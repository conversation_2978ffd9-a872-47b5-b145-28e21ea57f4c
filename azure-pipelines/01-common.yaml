
stages:
  - stage: 'Build'
    displayName: 'Build'
    jobs:
    - job: 'Build'
      steps:
      - task: NodeTool@0
        inputs:
          versionSpec: '16.x'
        displayName: 'Install Node.js'

      - script: |
          yarn install
          $(environmentFileCommand)
          yarn run build
          yarn install --production
        displayName: 'Yarn Install and Build'

      - task: ArchiveFiles@2
        inputs:
          rootFolderOrFile: '$(System.DefaultWorkingDirectory)'
          includeRootFolder: false
          archiveType: 'zip'
          archiveFile: '$(Build.ArtifactStagingDirectory)/api/$(Build.BuildId).zip'

      - task: PublishBuildArtifacts@1
        inputs:
          PathtoPublish: '$(Build.ArtifactStagingDirectory)/api'
          ArtifactName: 'api'

      - script: sed -i 's/CRON_JOB_HANDLERS=disabled/CRON_JOB_HANDLERS=enabled/g' .env
        displayName: 'Update .env for cron jobs'

      - script: sed -i 's/APP_TAG=APIS/APP_TAG=JOBS/g' .env
        displayName: 'Update .env for workers'

      - task: ArchiveFiles@2
        inputs:
          rootFolderOrFile: '$(System.DefaultWorkingDirectory)'
          includeRootFolder: false
          archiveType: 'zip'
          archiveFile: '$(Build.ArtifactStagingDirectory)/workers/$(Build.BuildId)-Workers.zip'  

      - task: PublishBuildArtifacts@1
        inputs:
          PathtoPublish: '$(Build.ArtifactStagingDirectory)/workers'
          ArtifactName: 'workers'

  - stage: 'Deploy'
    dependsOn: 'Build'
    displayName: 'Deploy'
    pool: 'azure-dev-rhel-agents'
    jobs:
      - deployment: 'DeployWebApp'
        displayName: 'Deploy WebApp'
        environment: $(buildEnvironment)
        strategy:
          runOnce:
            deploy:
              steps:
              - download: none
              - task: DownloadBuildArtifacts@0
                inputs:
                  buildType: 'current'
                  downloadType: 'single'
                  artifactName: 'api'
                  downloadPath: '$(System.ArtifactsDirectory)'
              - task: AzureWebApp@1
                displayName: 'Deploy Web App to Azure'
                inputs:
                  azureSubscription: $(azureSubscription)
                  appType: 'webApp'
                  appName: $(webAppName)
                  package: '$(System.ArtifactsDirectory)/api/$(Build.BuildId).zip'
                  deploymentMethod: 'zipDeploy'

      - deployment: 'DeployWorkers'
        dependsOn: 'DeployWebApp'
        displayName: 'Deploy Workers'
        environment: $(buildEnvironment)
        strategy:
          runOnce:
            deploy:
              steps:
              - download: none
              - task: DownloadBuildArtifacts@0
                inputs:
                  buildType: 'current'
                  downloadType: 'single'
                  artifactName: 'workers'
                  downloadPath: '$(System.ArtifactsDirectory)'
              - task: AzureWebApp@1
                displayName: 'Deploy Worker App to Azure'
                inputs:
                  azureSubscription: $(azureSubscription)
                  appType: 'webApp'
                  appName: $(workerAppName)
                  package: '$(System.ArtifactsDirectory)/workers/$(Build.BuildId)-Workers.zip'
                  deploymentMethod: 'zipDeploy'
