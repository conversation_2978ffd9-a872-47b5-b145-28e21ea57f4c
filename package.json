{"name": "mytm_oc", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"typeorm": "env-cmd ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js", "migration:generate": "npm run typeorm -- --dataSource=src/database/data-source.ts migration:generate", "migration:create": "npm run typeorm -- migration:create", "migration:run": "npm run typeorm -- --dataSource=src/database/data-source.ts migration:run", "migration:revert": "npm run typeorm -- --dataSource=src/database/data-source.ts migration:revert", "schema:drop": "npm run typeorm -- --dataSource=src/database/data-source.ts schema:drop", "seed:run": "ts-node -r tsconfig-paths/register ./src/database/seeds/run-seed.ts", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "format:check": "prettier --check \"src/**/*.ts\" \"test/**/*.ts\"", "start": "node main.js", "dev": "nest start --watch", "debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"src/**/*.ts\" --fix", "lint:check": "eslint \"src/**/*.ts\"", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "env-cmd jest --config ./test/jest-e2e.json", "prepare": "husky install"}, "dependencies": {"@bull-board/express": "^5.0.0", "@casl/ability": "^6.3.3", "@nestjs-modules/mailer": "1.8.1", "@nestjs/bull": "^0.6.2", "@nestjs/cache-manager": "^2.2.1", "@nestjs/common": "^9.2.0", "@nestjs/config": "2.2.0", "@nestjs/core": "^9.2.0", "@nestjs/jwt": "9.0.0", "@nestjs/passport": "9.0.0", "@nestjs/platform-express": "^9.2.0", "@nestjs/schedule": "^2.1.0", "@nestjs/swagger": "6.1.3", "@nestjs/typeorm": "9.0.1", "@nestjsx/crud": "^5.0.0-alpha.3", "@nestjsx/crud-typeorm": "^5.0.0-alpha.3", "@turf/turf": "^6.5.0", "@types/auth0": "^2.33.1", "@types/cache-manager": "^3.4.2", "@types/cache-manager-ioredis": "^2.0.3", "@types/secure-random": "^1.1.0", "apple-signin-auth": "1.7.4", "auth0": "^2.33.0", "autocannon": "^7.10.0", "axios": "^0.21.1", "bcryptjs": "2.4.3", "bull": "^4.10.2", "cache-manager": "^3.4.4", "cache-manager-ioredis": "^2.1.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "connect-ensure-login": "^0.1.1", "cookie-session": "^2.0.0", "crypto-js": "^4.1.1", "express": "^4.18.2", "fb": "2.0.0", "flexsearch": "^0.7.31", "geojson": "^0.5.0", "geolib": "^3.3.3", "google-auth-library": "8.6.0", "handlebars": "4.7.7", "ioredis": "5.2.4", "lodash": "^4.17.21", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "ms": "^2.1.3", "multer": "1.4.4", "multer-s3": "2.10.0", "nestjs-i18n": "9.2.2", "nestjs-typeorm-paginate": "^4.0.3", "node-klaviyo": "^1.1.3", "nodemailer": "6.8.0", "passport": "0.5.2", "passport-anonymous": "1.0.1", "passport-http": "0.3.0", "passport-jwt": "4.0.0", "passport-local": "1.0.0", "percentile": "^1.6.0", "pg": "8.8.0", "prettier": "^2.8.2", "qs": "^6.11.2", "reflect-metadata": "0.1.13", "rimraf": "3.0.2", "rxjs": "7.5.7", "secure-random": "^1.1.2", "source-map-support": "0.5.21", "swagger-ui-express": "4.5.0", "twitter": "1.7.1", "typeorm": "^0.3.10"}, "devDependencies": {"@nestjs/cli": "9.1.5", "@nestjs/schematics": "9.0.3", "@nestjs/testing": "9.1.6", "@trivago/prettier-plugin-sort-imports": "^4.0.0", "@types/bcryptjs": "2.4.2", "@types/connect-ensure-login": "^0.1.7", "@types/cookie-session": "^2.0.44", "@types/crypto-js": "^4.1.1", "@types/express": "4.17.14", "@types/facebook-js-sdk": "3.3.6", "@types/geojson": "^7946.0.10", "@types/jest": "29.0.3", "@types/lodash": "^4.14.191", "@types/ms": "^0.7.31", "@types/multer": "1.4.7", "@types/node": "^16.18.3", "@types/passport-anonymous": "1.0.3", "@types/passport-jwt": "3.0.7", "@types/passport-local": "^1.0.34", "@types/supertest": "2.0.12", "@types/twitter": "1.7.1", "@typescript-eslint/eslint-plugin": "^5.41.0", "@typescript-eslint/parser": "^5.41.0", "aws-sdk": "2.1243.0", "env-cmd": "10.1.0", "eslint": "^8.33.0", "eslint-config-custom": "*", "eslint-plugin-prettier": "^4.2.1", "husky": "^8.0.0", "is-ci": "3.0.1", "jest": "29.0.3", "supertest": "6.3.1", "ts-jest": "29.0.1", "ts-loader": "9.4.1", "ts-node": "10.9.1", "tsconfig": "*", "tsconfig-paths": "4.1.0", "tslib": "2.4.1", "typescript": "4.8.4"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "engines": {"node": ">=14.0.0"}}