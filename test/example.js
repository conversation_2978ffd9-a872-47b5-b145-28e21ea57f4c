queryBuilderRound.leftJoinAndSelect(
  (subQuery) => {
    return subQuery
      .from(HolePlayed, 'hole')
      .select('hole.id')
      .addSelect('round_id')
      .addSelect('name')
      .addSelect('par')
      .addSelect('handicap_index as handicap')
      .addSelect('score');
  },
  'hole',
  'hole.round_id = round.id'
);
queryBuilderRound.take(+pagingOptions.limit);
queryBuilderRound.skip((+pagingOptions.page - 1) * +pagingOptions.limit);
queryBuilderRound.orderBy({ played_on: 'DESC' });
const results = await queryBuilderRound.getRawMany();
