import { ApiProperty } from '@nestjs/swagger';
import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

@Entity({ name: 'average_scores_classic' })
export class AverageScoreClassic {
  @PrimaryGeneratedColumn()
  @ApiProperty({ example: 1 })
  id: number;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  user_id: number;

  @Column({ type: 'float' })
  @ApiProperty({ example: 1.0 })
  average_score: number;

  @Column({ type: 'float' })
  @ApiProperty({ example: 1.0 })
  lowest_score: number;

  @Column({ type: 'float' })
  @ApiProperty({ example: 1.0 })
  highest_score: number;

  @CreateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  created_at: Date;

  @UpdateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  updated_at: Date;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  total_rounds: number;
}
