import { <PERSON>, Get, Param } from '@nestjs/common';
import { ApiParam, ApiTags } from '@nestjs/swagger';
import { AverageScoresService } from './average-scores.service';

@Controller('average-scores')
@ApiTags('AverageScore')
export class AverageScoresController {
  constructor(private readonly averageScoresService: AverageScoresService) {}
  @Get('users/:id/calc-avg-score')
  @ApiParam({ name: 'id', example: 41428 })
  async calculateAverageScore(@Param('id') id: number) {
    const avg = await this.averageScoresService.calculateAverageScore(id);
    return {
      avg,
    };
  }
}
