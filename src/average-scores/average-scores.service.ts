import { InjectQueue } from '@nestjs/bull';
import { Inject, Injectable, Logger, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Queue } from 'bull';
import { isEmpty } from 'lodash';
import { In, Repository } from 'typeorm';
import { HolePlayed } from 'src/holes-played/entities/hole-played.entity';
import { HolesPlayedService } from 'src/holes-played/holes-played.service';
import { Round } from 'src/rounds/entities/round.entity';
import { OPTIONS_JOB_DEFAULT, ROUND } from 'src/rounds/round.const';
import { RoundService } from 'src/rounds/rounds.service';
import { StrokesStatsService } from 'src/strokes-stats/strokes-stats.service';
import { User } from 'src/users/entities/user.entity';
import { RoundScoreUtil } from 'src/utils/round-score';
import { SmartGolfStatsTypesOverall } from 'src/utils/smart-golf/stats/types/overall';
import { PROCESSORS, PROCESS_QUEUE_NAMES } from 'src/workers/jobs/job.constant';
import { AverageScoreClassic } from './entities/average-score.classic.entity';
import { AverageScore } from './entities/average-score.entity';

const MISSING_ROUND = 'MISSING_ROUND';

@Injectable()
export class AverageScoresService {
  private readonly logger = new Logger(AverageScoresService.name);
  constructor(
    @InjectRepository(AverageScore)
    private readonly averageScoreRepository: Repository<AverageScore>,
    @InjectRepository(AverageScoreClassic)
    private readonly averageScoreClassicRepository: Repository<AverageScoreClassic>,
    @InjectRepository(User)
    private readonly userRepo: Repository<User>,
    @InjectRepository(HolePlayed)
    private readonly holePlayedRepo: Repository<HolePlayed>,
    @InjectRepository(Round)
    private readonly roundRepo: Repository<Round>,
    @Inject(forwardRef(() => RoundService)) private roundService: RoundService,
    @Inject(forwardRef(() => HolesPlayedService)) private holePlayedService: HolesPlayedService,
    @Inject(forwardRef(() => StrokesStatsService)) private strokesStatsService: StrokesStatsService,
    @InjectQueue(PROCESSORS.CalculateAverageScoreClassicJob)
    private calculateAverageScoreClassicJobQueue: Queue
  ) {}

  async findOneByUserId(userId: number) {
    const avgScore = await this.averageScoreRepository.findOne({ where: { user_id: userId } });
    this.logger.log(`AVG_SCORE: ${JSON.stringify(avgScore)}`);
    return avgScore;
  }
  async findOneAvgClassicByUserId(userId: number) {
    const avgScore = await this.averageScoreClassicRepository.findOne({ where: { user_id: userId } });
    this.logger.log(`AVG_SCORE: ${JSON.stringify(avgScore)}`);
    return avgScore;
  }

  async updateTotalRounds(userId: number) {
    const totalRounds = await this.roundService.getUserTotalRounds(userId);
    const averageScore = await this.averageScoreRepository.findOne({ where: { user_id: userId } });
    if (!averageScore) {
      return this.averageScoreRepository.save(
        this.averageScoreRepository.create({ user_id: userId, total_rounds: totalRounds })
      );
    }
    return this.averageScoreRepository.update({ user_id: userId }, { total_rounds: totalRounds });
  }
  async getAllRounds(user) {
    const query = this.roundRepo.createQueryBuilder();
    query
      .where({
        user_id: user.id,
        completed: true,
      })
      .select(['id', 'round_mode', 'stats_completed'])
      .orderBy({ id: 'ASC' });
    const all_round_ids = await query.getRawMany();

    const roundsAdvanced = all_round_ids.filter((round) => round.round_mode == ROUND.ROUND_MODE_ADVANCED);
    const othersMode = all_round_ids.filter((round) => round.round_mode != ROUND.ROUND_MODE_ADVANCED);
    const roundValid = [...othersMode];
    for (const round of roundsAdvanced) {
      if (round.stats_completed == true) {
        roundValid.push(round);
      }
    }
    return RoundScoreUtil.formatRoundIds(roundValid);
  }
  async getRoundScore(round_id) {
    try {
      const round: any = await this.roundService.findOne({ id: round_id });
      let round_score = 0;
      let hole_numbers_players = 0;
      for (const hole of round.holes) {
        const hole_score = await this.holePlayedService.score(round.round_mode, hole);
        if (hole_score > 0) {
          round_score += hole_score;
          hole_numbers_players += 1;
        }
      }
      this.logger.log(
        `round id: ${round_id} - [round_score, hole_numbers_players] ${round_score}, ${hole_numbers_players}`
      );
      return [round_score, hole_numbers_players];
    } catch (error) {
      return [0, 0];
    }
  }
  async calculateAverageScore(userId: number) {
    const user = await this.userRepo.findOneBy({ id: userId });
    if (!user) {
      return;
    }

    const { round_multiplayer_ids, round_basic_ids, round_classic_ids, round_ids, round_simple_ids, total_rounds } =
      await this.getAllRounds(user);
    if (!total_rounds || total_rounds == 0) {
      await this.resetAvgScore(false, userId);
      await this.resetAvgScore(true, userId);
      return;
    }
    let total_score = 0;
    let holes_played_total = 0;
    const list_average_scores = [];
    let average_score = 0;

    // # List rounds advanced
    ({ holes_played_total, total_score } = await this.totalScoreAdvancedMode(
      round_ids,
      list_average_scores,
      holes_played_total,
      total_score
    ));

    // # List rounds simple
    ({ holes_played_total, total_score } = await this.totalScoreSimpleMode(
      round_simple_ids,
      list_average_scores,
      holes_played_total,
      total_score
    ));

    // # List rounds basic, classic
    const overall_basic = await this.listAvgScoreBasicClassicMode(
      round_basic_ids,
      // [...round_basic_ids, ...round_classic_ids],
      list_average_scores
    );
    // # List rounds  classic
    const overall_classic = await this.listAvgScoreBasicClassicMode(round_classic_ids, list_average_scores);

    // # List rounds multiplayer
    const overall_multiplayer: any = await this.listAvgScoreMultiplayerMode(round_multiplayer_ids, list_average_scores);

    if (overall_basic != MISSING_ROUND) {
      const totalBasic = await overall_basic.total_score_basic();
      total_score += totalBasic.total;
      holes_played_total += totalBasic.hole_numbers_players;
    }
    if (overall_classic != MISSING_ROUND) {
      const totalClassic = await overall_classic.total_score_basic();
      await this.calculateAverageScoreClassicJobQueue.add(
        PROCESS_QUEUE_NAMES.CALCULATE_AVERAGE_CLASSIC_SCORE,
        {
          total_classic_rounds: totalClassic,
          userId: userId,
          round_classic_ids,
        },
        OPTIONS_JOB_DEFAULT
      );
      total_score += totalClassic.total;
      holes_played_total += totalClassic.hole_numbers_players;
    } else {
      await this.resetAvgScore(true, userId);
    }

    if (overall_multiplayer != MISSING_ROUND) {
      const totalMultiplayer = await overall_multiplayer.total_score_multiplayer();
      total_score += totalMultiplayer.total;
      holes_played_total += totalMultiplayer.hole_numbers_players;
    }

    list_average_scores.sort((a, b) => a - b);
    if (holes_played_total != 0) {
      average_score = (total_score / holes_played_total) * 18;
    }

    this.logger.log(`AVERAGE_SCORE ${average_score}`);
    let average_data: any = await this.averageScoreRepository.findOne({
      where: { user_id: userId },
      select: ['id'],
    });
    const payload: any = {
      user_id: userId,
      average_score: total_rounds > 0 ? average_score.toFixed(2) : null,
      lowest_score: list_average_scores[0],
      highest_score: list_average_scores[list_average_scores.length - 1],
      total_rounds: total_rounds,
      updated_at: new Date(),
    };
    if (!average_data) {
      payload['created_at'] = new Date();
      average_data = await this.averageScoreRepository.save(this.averageScoreRepository.create(payload));
    } else {
      await this.averageScoreRepository.update({ id: average_data.id }, payload);
    }
    this.logger.log(`
=====================================================================================
AVERAGE_SCORE User: ${userId}
-------------------------------------------------------------------------------------
list_average_scores:      ${list_average_scores}
total_score:              ${total_score}
holes_played_total:       ${holes_played_total}
average_score:            ${average_score}
average_score_data:       ${JSON.stringify(average_data)}
=====================================================================================
   `);
    return await this.averageScoreRepository.findOneBy({ user_id: userId });
  }
  async resetAvgScore(isAvgScoreClassic: boolean, userId: number) {
    const repoAvgScore = isAvgScoreClassic ? this.averageScoreClassicRepository : this.averageScoreRepository;
    let average_data: any = await repoAvgScore.findOne({
      where: { user_id: userId },
      select: ['id'],
    });
    const payload: any = {
      user_id: userId,
      average_score: null,
      lowest_score: null,
      highest_score: null,
      total_rounds: 0,
      updated_at: new Date(),
    };
    if (!average_data) {
      payload['created_at'] = new Date();
      average_data = await repoAvgScore.save(this.averageScoreRepository.create(payload));
    } else {
      await repoAvgScore.update({ id: average_data.id }, payload);
    }
  }
  async calculateAverageScoreClassic(total_classic_rounds, round_classic_ids, userId) {
    if (round_classic_ids?.length == 0) {
      return;
    }
    const total_rounds = round_classic_ids.length;
    const total_score = total_classic_rounds.total;
    const holes_played_total = total_classic_rounds.hole_numbers_players;
    let average_score = (total_score / holes_played_total) * 18;
    if (isNaN(average_score)) {
      average_score = null;
    }
    const list_average_scores = [];
    await this.listAvgScoreBasicClassicMode(round_classic_ids, list_average_scores);
    list_average_scores.sort((a, b) => a - b);
    let average_data: any = await this.averageScoreClassicRepository.findOne({
      where: { user_id: userId },
      select: ['id'],
    });
    let payload: any = {
      user_id: userId,
      average_score: average_score != null ? average_score.toFixed(2) : null,
      lowest_score: list_average_scores[0] ?? null,
      highest_score: list_average_scores[list_average_scores.length - 1] ?? null,
      total_rounds: total_rounds,
      updated_at: new Date(),
    };
    if (total_rounds > 0 && average_score == null) {
      payload = {
        ...payload,
        average_score: 0,
        lowest_score: 0,
        highest_score: 0,
      };
    }

    if (!average_data) {
      payload['created_at'] = new Date();
      average_data = await this.averageScoreClassicRepository.save(this.averageScoreClassicRepository.create(payload));
    } else {
      await this.averageScoreClassicRepository.update({ id: average_data.id }, payload);
    }
    this.logger.log(`
=====================================================================================
AVERAGE_SCORE_CLASSIC User: ${userId}
-------------------------------------------------------------------------------------
list_average_scores_classic:      ${list_average_scores}
total_score_classic:              ${total_score}
holes_played_total_classic:       ${holes_played_total}
average_score_classic:            ${average_score}
average_score_data_classic:       ${JSON.stringify(average_data)}
=====================================================================================
   `);
  }

  private async listAvgScoreMultiplayerMode(round_multiplayer_ids: any, list_average_scores: any[]) {
    let overall_multiplayer: any = MISSING_ROUND;
    if (!isEmpty(round_multiplayer_ids)) {
      // let holes_played = round_multiplayer_ids.map {|round_id| HolePlayed.where(:round_id => round_id)}.flatten
      const holes_played = await this.holePlayedService.findHolesInRounds(round_multiplayer_ids);
      overall_multiplayer = new SmartGolfStatsTypesOverall(
        [],
        [],
        holes_played,
        this.holePlayedService,
        this.roundService
      );

      for (const round_id of round_multiplayer_ids) {
        const holesOfRound = holes_played.filter((hole) => +hole.round_id == +round_id);

        const score_data = RoundScoreUtil.getRoundScoreByHoles(holesOfRound);
        const round_average_score = RoundScoreUtil.getAverageScore(score_data[0], score_data[1]);
        if (round_average_score) {
          list_average_scores.push(round_average_score);
        }
      }
    } else {
      overall_multiplayer = MISSING_ROUND;
    }
    return overall_multiplayer;
  }

  private async listAvgScoreBasicClassicMode(round_basic_ids: any[], list_average_scores: any[]) {
    let overall_basic: any = MISSING_ROUND;
    if (!isEmpty(round_basic_ids)) {
      const holes_played_basic = await this.holePlayedService.findHolesInRounds(round_basic_ids);

      overall_basic = new SmartGolfStatsTypesOverall(
        [],
        [],
        holes_played_basic,
        this.holePlayedService,
        this.roundService
      );

      for (const round_id of round_basic_ids) {
        const holesOfRound = holes_played_basic.filter((hole) => +hole.round_id == +round_id);

        const score_data = RoundScoreUtil.getRoundScoreByHoles(holesOfRound);
        // this.getRoundScore(round_id);
        this.logger.log(`BASIC SCORE: ${score_data[0]}, ${score_data[1]}`);
        const round_average_score = RoundScoreUtil.getAverageScore(score_data[0], score_data[1]);
        if (round_average_score) {
          list_average_scores.push(round_average_score);
        }
      }
    } else {
      overall_basic = MISSING_ROUND;
    }
    return overall_basic;
  }
  async totalScoreAdvancedMode(
    round_ids: any,
    list_average_scores: any[],
    holes_played_total: number,
    total_score: number
  ) {
    if (!isEmpty(round_ids)) {
      const holesOfRounds = await this.holePlayedService.getHolesOfRoundIds(round_ids);
      for (const round_id of round_ids) {
        const holes = holesOfRounds.filter((hole) => hole.round_id == round_id);
        const scoresHoles: any = await this.holePlayedService.getScoreListHole(
          ROUND.ROUND_MODE_ADVANCED,
          holes,
          round_id
        );
        holes.map((hole) => {
          const scoreHole = scoresHoles.find((s) => s.id == hole.id);
          hole.score = scoreHole?.score || 0;
        });
        const score_data = this.roundService.scoreHolesPlayed({
          holes,
        });
        let round_average_score = 0;
        if (score_data) {
          round_average_score = RoundScoreUtil.getAverageScore(score_data[0], score_data[1]);
          total_score += score_data[0];
          holes_played_total += score_data[1];
          if (round_average_score) {
            list_average_scores.push(round_average_score);
          }
        }
        this.logger.log(
          `ROUND_AVERAGE_SCORE ADVANCED : ${round_average_score} - ROUND_ID: ${round_id} - ROUND SCORE: ${score_data[0]} - HOLE_PLAYED: ${score_data[1]}`
        );
      }
    }
    return { holes_played_total, total_score };
  }

  async totalScoreSimpleMode(
    round_simple_ids: any,
    list_average_scores: any[],
    holes_played_total: number,
    total_score: number
  ) {
    if (!isEmpty(round_simple_ids)) {
      const queryRoundSimples = this.roundRepo.createQueryBuilder();
      queryRoundSimples.where({ id: In(round_simple_ids) });
      queryRoundSimples.select([
        'id',
        'number_of_holes_played',
        'front_9_score',
        'back_9_score',
        'eighteen_holes_score',
      ]);
      queryRoundSimples.orderBy({ id: 'ASC' });
      const roundsSimples = await queryRoundSimples.getRawMany();
      for (const round_id of round_simple_ids) {
        const round_simple = roundsSimples.find((round) => round.id == round_id);
        let round_average_score = 0;
        let round_score = 0;
        if (round_simple.number_of_holes_played == 9) {
          round_score = round_simple.front_9_score || round_simple.back_9_score;
          round_average_score = RoundScoreUtil.getAverageScore(round_score, 9);
          if (round_average_score) {
            list_average_scores.push(round_average_score);
          }
          holes_played_total += 9;
        } else {
          round_score = round_simple.eighteen_holes_score;
          round_average_score = RoundScoreUtil.getAverageScore(round_score, 18);
          this.logger.log(`ROUND_AVERAGE_SCORE SIMPLE: ${round_average_score}`);
          if (round_average_score) {
            list_average_scores.push(round_average_score);
          }
          holes_played_total += 18;
        }
        // # 18 holes
        this.logger.log(`SIMPLE SCORE: ${round_score}`);
        total_score += +round_score;
      }
    }
    return { holes_played_total, total_score };
  }
}
