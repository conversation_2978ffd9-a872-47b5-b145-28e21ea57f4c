import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { isEmpty } from 'lodash';
import { In, IsNull, Not, Repository, SelectQueryBuilder } from 'typeorm';
import { Club } from 'src/clubs/entities/club.entity';
import { HolePlayed } from 'src/holes-played/entities/hole-played.entity';
import { MISSING_ROUND } from 'src/stats/stats.const';
import { StrokePlayed } from 'src/strokes-played/entities/stroke-played.entity';
import { SmartGolfStats, Stats } from 'src/utils/smart-golf/stats';
import { includeStr } from 'src/utils/utils';
import { StrokeStat } from './entities/stroke-stat.entity';

const SELECT_STATS_FIELDS = [
  'id',
  'user_id',
  'ending_distance_to_pin',
  'short',
  'long',
  'left',
  'right',
  'landed_on_fairway',
  'starting_lie',
  'ending_lie',
  'hole_name',
  'stroke_ordinal',
  'fairway_location_from_center',
  'fairway_distance_from_center',
  'shot_distance',
  'green_distances',
  'stroke_played_id',
  'starting_distance_to_pin',
  'starting_as_penalty_shot',
  'starting_as_recovery_shot',
  'ending_as_penalty_shot',
  'stroke_ordinal',
  'hole_played_id',
  'strokes_gained',
  'strokes_gained_pro',
  'strokes_gained_five',
  'strokes_gained_ten',
  'strokes_gained_fifteen',
  'strokes_gained_twenty',
];
@Injectable()
export class StrokesStatsService {
  constructor(
    @InjectRepository(StrokeStat)
    private strokeStatRepo: Repository<StrokeStat>
  ) {}

  findAll() {
    return `This action returns all strokesStats`;
  }
  async countStrokesBy(option: any) {
    console.log('start countStrokesBy....');
    return await this.strokeStatRepo.countBy(option);
  }
  findOne(id: number) {
    return `This action returns a #${id} strokesStat`;
  }

  remove(id: number) {
    return this.strokeStatRepo.delete({ id });
  }

  removeByUserId(userId: number) {
    return this.strokeStatRepo.delete({ user_id: userId });
  }

  async stats_for_rounds(round_ids) {
    const queryBuilder = this.strokeStatRepo.createQueryBuilder('SS');
    queryBuilder.where({
      round_id: In(round_ids),
    });
    queryBuilder.select([
      '"SS".id AS id',
      '"SS".stroke_played_id AS stroke_played_id',
      'long_tee',
      '"SS".round_id',
      '"SS".user_id as user_id',
      'holes_par',
      'hole_name',
      'ending_lie',
      'starting_lie',
      'club_id',
      'club_type',
      'starting_distance_to_pin',
      'starting_as_penalty_shot',
      'starting_as_recovery_shot',
      'ending_as_penalty_shot',
      'stroke_ordinal',
      '"SP".hole_played_id',
      'shot_distance',
      'strokes_gained',
      'strokes_gained_pro',
      'strokes_gained_five',
      'strokes_gained_ten',
      'strokes_gained_fifteen',
      'strokes_gained_twenty',
      'ending_distance_to_pin',
      'ends_in_hole',
      'approach',
      '"left"',
      '"right"',
      'short',
      'long',
    ]);
    queryBuilder.innerJoin(
      (subQuery) => {
        return subQuery
          .from(StrokePlayed, 'SP')
          .select(['id', 'hole_played_id'])
          .where({ round_id: In(round_ids) });
      },
      'SP',
      '"SP".id = "SS".stroke_played_id'
    );
    queryBuilder.innerJoin(
      (subQuery) => {
        return subQuery
          .from(HolePlayed, 'HP')
          .select(['id'])
          .where({ round_id: In(round_ids) });
      },
      'HP',
      '"SP".hole_played_id = "HP".id'
    );
    return await queryBuilder.getRawMany();
  }

  async stats_for_club(round_ids, club_id) {
    // joins(:round, :hole_played, :user, stroke_played: :hole_played).
    // where(round_id: round_ids).
    // includes(:round, :hole_played, :club, :user, stroke_played: :hole_played).
    // where("clubs.id = #{club_id} AND (clubs.disabled IS NULL OR clubs.disabled IS FALSE)").references(:club)

    const queryBuilder = this.strokeStatRepo.createQueryBuilder('SS');
    queryBuilder.where({
      round_id: In(round_ids),
      club_id,
      club_type: Not(IsNull()),
    });
    queryBuilder.select([
      '"SS".id AS id',
      'long_tee',
      'round_id',
      '"SS".user_id as user_id',
      'ending_as_penalty_shot',
      'holes_par',
      'ending_lie',
      'club_id',
      'club_type',
    ]);
    queryBuilder.innerJoin(
      (subQuery) => {
        return subQuery
          .from(StrokePlayed, 'SP')
          .select(['id', 'hole_played_id'])
          .where({ round_id: In(round_ids) });
      },
      'SP',
      '"SP".id = "SS".stroke_played_id'
    );
    queryBuilder.innerJoin(
      (subQuery) => {
        return subQuery
          .from(HolePlayed, 'HP')
          .select(['id'])
          .where({ round_id: In(round_ids) });
      },
      'HP',
      '"SP".hole_played_id = "HP".id'
    );
    return await queryBuilder.getRawMany();
  }

  async stats_for_users(user_ids) {
    return await this.strokeStatRepo.find({
      where: {
        user_id: In(user_ids),
      },
      select: ['round_id', 'id', 'user_id'],
    });
  }

  dispersion_graph(round_ids, user_id) {
    const queryBuilder = this.strokeStatRepo.createQueryBuilder();
    queryBuilder.where(`long_tee = TRUE AND stroke_ordinal = 1 AND holes_par > 3 AND starting_as_penalty_shot = FALSE`);
    // queryBuilder.where(`lower(starting_lie) = 'tee' AND stroke_ordinal = 1 AND holes_par > 3`);
    queryBuilder.andWhere({ user_id: user_id, round_id: In(round_ids) });
    queryBuilder.select([
      'round_id',
      'id',
      'user_id',
      'ending_distance_to_pin',
      'short',
      'long',
      'long_tee',
      '"left"',
      '"right"',
      'starting_lie',
      'ending_lie',
      'hole_name',
      'stroke_ordinal',
      'fairway_location_from_center',
      'fairway_distance_from_center',
      'shot_distance',
      'green_distances',
      'stroke_played_id',
      'holes_par',
    ]);
    return queryBuilder;
  }
  async driver(queryBuilder: SelectQueryBuilder<StrokeStat>) {
    queryBuilder.andWhere(`lower(club_type) = 'driver'`);
    return await queryBuilder.getRawMany();
  }

  async three_wood(queryBuilder: SelectQueryBuilder<StrokeStat>) {
    queryBuilder.andWhere("lower(club_type) IN('3-fw','3fw')");
    return await queryBuilder.getRawMany();
  }

  async no_woods(queryBuilder: SelectQueryBuilder<StrokeStat>) {
    queryBuilder.andWhere("lower(club_type) NOT IN('driver','3fw','3-fw')");
    return await queryBuilder.getRawMany();
  }

  // #
  // # specifically for approach & short
  // #
  async within_distance_range(start_distance, end_distance) {
    const queryBuilder = this.strokeStatRepo.createQueryBuilder();
    queryBuilder.where('starting_distance_to_pin BETWEEN :start AND :end', {
      start: start_distance * Stats.YARDS_TO_METERS,
      end: end_distance * Stats.YARDS_TO_METERS,
    });
    queryBuilder.select(['round_id', 'id', 'user_id']);
    return await queryBuilder.getRawMany();
  }

  // #
  // # Scope to specific rounds
  // #
  async for_rounds(round_ids) {
    const queryBuilder = this.strokeStatRepo.createQueryBuilder();
    queryBuilder.where({ round_id: In(round_ids) });
    queryBuilder.select(['round_id', 'id', 'user_id']);
    return await queryBuilder.getRawMany();
  }

  // #
  // # Scope for a single/multiple user id(s)
  // #
  async for_user(user_id) {
    const queryBuilder = this.strokeStatRepo.createQueryBuilder();
    queryBuilder.where({ user_id: In(user_id) });
    queryBuilder.select(SELECT_STATS_FIELDS);
    return await queryBuilder.getRawMany();
  }

  // #
  // # Ending distance, specifically for approach & short graphs
  // #
  async within_ending_distance(distance_in_yards) {
    const queryBuilder = this.strokeStatRepo.createQueryBuilder();
    queryBuilder.where('ending_distance_to_pin < :distance', {
      distance: distance_in_yards * Stats.YARDS_TO_METERS,
    });
    queryBuilder.select(SELECT_STATS_FIELDS);
    return await queryBuilder.getRawMany();
  }

  // #
  // # Shots that are considered approach shots
  // #
  async approach() {
    const queryBuilder = this.strokeStatRepo.createQueryBuilder();
    queryBuilder.where(
      `
     lower(starting_lie) <> 'green' AND 
     starting_distance_to_pin >= :start AND
     starting_as_recovery_shot <> true AND
     starting_as_penalty_shot <> true
    `,
      {
        start: 100 * Stats.YARDS_TO_METERS,
      }
    );

    queryBuilder.select([
      'id',
      'user_id',
      'ending_distance_to_pin',
      'short',
      'long',
      'left',
      'right',
      'landed_on_fairway',
      'starting_lie',
      'ending_lie',
      'hole_name',
      'stroke_ordinal',
      'fairway_location_from_center',
      'fairway_distance_from_center',
      'shot_distance',
      'green_distances',
      'stroke_played_id',
    ]);
    return await queryBuilder.getRawMany();
  }
  async stats_proximity_to_hole(round_ids, start_distance, end_distance, distance_in_yards) {
    const queryBuilder = this.strokeStatRepo.createQueryBuilder('SS');
    queryBuilder.where({
      round_id: In(round_ids),
    });
    queryBuilder.andWhere(
      `
     lower(starting_lie) <> 'green' AND 
     starting_distance_to_pin >= :start AND
     starting_as_recovery_shot <> true AND
     starting_as_penalty_shot <> true
    `,
      {
        start: 100 * Stats.YARDS_TO_METERS,
      }
    );
    queryBuilder.andWhere('ending_distance_to_pin < :distance', {
      distance: distance_in_yards * Stats.YARDS_TO_METERS,
    });
    queryBuilder.andWhere('starting_distance_to_pin BETWEEN :start AND :end', {
      start: start_distance * Stats.YARDS_TO_METERS,
      end: end_distance * Stats.YARDS_TO_METERS,
    });
    queryBuilder.select([
      '"SS".id AS id',
      '"SS".stroke_played_id AS stroke_played_id',
      'long_tee',
      'round_id',
      '"SS".user_id as user_id',
      'holes_par',
      'ending_lie',
      'starting_lie',
      'club_id',
      'club_type',
      'starting_distance_to_pin',
      'starting_as_penalty_shot',
      'starting_as_recovery_shot',
      'ending_as_penalty_shot',
      'stroke_ordinal',
      'hole_played_id',
      'shot_distance',
      'hole_name',
      'strokes_gained',
      'strokes_gained_pro',
      'strokes_gained_five',
      'strokes_gained_ten',
      'strokes_gained_fifteen',
      'strokes_gained_twenty',
      'ending_distance_to_pin',
      'approach',
      '"left"',
      '"right"',
      'short',
      'long',
      'bearing_to_dogleg',
      'green_distances',
    ]);
    queryBuilder.innerJoin(
      (subQuery) => {
        return subQuery
          .from(StrokePlayed, 'SP')
          .select(['id'])
          .where({ round_id: In(round_ids) });
      },
      'SP',
      '"SP".id = "SS".stroke_played_id'
    );
    return await queryBuilder.getRawMany();
  }

  async stats_short_proximity_to_hole(round_ids, start_distance, end_distance, distance_in_yards) {
    const queryBuilder = this.strokeStatRepo.createQueryBuilder('SS');
    queryBuilder.where({
      round_id: In(round_ids),
    });
    queryBuilder.andWhere(
      `
     lower(starting_lie) <> 'green' AND 
     starting_distance_to_pin < :short_start_distance AND
     ending_distance_to_pin < :short_end_distance AND
     starting_as_recovery_shot <> true AND
     starting_as_penalty_shot <> true
    `,
      {
        short_start_distance: 100 * Stats.YARDS_TO_METERS,
        short_end_distance: 30 * Stats.YARDS_TO_METERS,
      }
    );
    queryBuilder.andWhere('ending_distance_to_pin < :distance', {
      distance: distance_in_yards * Stats.YARDS_TO_METERS,
    });
    queryBuilder.andWhere('starting_distance_to_pin BETWEEN :start AND :end', {
      start: start_distance * Stats.YARDS_TO_METERS,
      end: end_distance * Stats.YARDS_TO_METERS,
    });
    queryBuilder.select([
      '"SS".id AS id',
      '"SS".stroke_played_id AS stroke_played_id',
      'long_tee',
      'round_id',
      '"SS".user_id as user_id',
      'holes_par',
      'hole_name',
      'ending_lie',
      'starting_lie',
      'club_id',
      'club_type',
      'starting_distance_to_pin',
      'starting_as_penalty_shot',
      'starting_as_recovery_shot',
      'ending_as_penalty_shot',
      'stroke_ordinal',
      'hole_played_id',
      'shot_distance',
      'strokes_gained',
      'strokes_gained_pro',
      'strokes_gained_five',
      'strokes_gained_ten',
      'strokes_gained_fifteen',
      'strokes_gained_twenty',
      'ending_distance_to_pin',
      'approach',
      '"left"',
      '"right"',
      'short',
      'long',
      'bearing_to_dogleg',
      'green_distances',
    ]);
    queryBuilder.innerJoin(
      (subQuery) => {
        return subQuery
          .from(StrokePlayed, 'SP')
          .select(['id'])
          .where({ round_id: In(round_ids) });
      },
      'SP',
      '"SP".id = "SS".stroke_played_id'
    );
    return await queryBuilder.getRawMany();
  }
  // #
  // #
  // #
  async short() {
    const queryBuilder = this.strokeStatRepo.createQueryBuilder();
    queryBuilder.where(
      `
     lower(starting_lie) <> 'green' AND 
     starting_distance_to_pin < :start AND
     ending_distance_to_pin < :end AND
     starting_as_recovery_shot <> true AND
     starting_as_penalty_shot <> true
    `,
      {
        start: 100 * Stats.YARDS_TO_METERS,
        end: 30 * Stats.YARDS_TO_METERS,
      }
    );
    queryBuilder.select(SELECT_STATS_FIELDS);
    return await queryBuilder.getRawMany();
  }
  async disable_club() {
    // joins(:club).
    // includes(:club).
    // where('(clubs.in_bag IS NULL OR clubs.in_bag IS TRUE) AND (clubs.disabled IS NULL OR clubs.disabled IS FALSE)').references(:club)

    const queryBuilder = this.strokeStatRepo.createQueryBuilder('SS');
    queryBuilder.select(['SS.id', 'round_id', 'user_id']);
    queryBuilder.leftJoinAndSelect(
      (subQuery) => {
        return subQuery
          .from(Club, 'clubs')
          .where(
            '(clubs.in_bag IS NULL OR clubs.in_bag IS TRUE) AND (clubs.disabled IS NULL OR clubs.disabled IS FALSE)'
          );
      },
      'clubs',
      'clubs.id = SS.club_id'
    );
    return await queryBuilder.getRawMany();
  }
  calculate(round_ids = []) {
    // return MissingRounds if round_ids.blank?
    const stroke_stats = this.stats_for_rounds(round_ids);
    return new SmartGolfStats().calculate(stroke_stats);
  }
  async calculate_for_club(round_ids = [], club_id) {
    // return MissingRounds if round_ids.blank?
    // return MissingClub if club_id.nil?
    if (isEmpty(round_ids) || !club_id) {
      return MISSING_ROUND;
    }
    const stroke_stats = await this.stats_for_club(round_ids, club_id);
    return new SmartGolfStats().calculate(stroke_stats);
  }
  within_yards(stroke, amount_in_yards) {
    return this.ending_distance_in_yards(stroke) < amount_in_yards;
  }
  convert_ending_distance_units(stroke) {
    if (includeStr(stroke.ending_lie, 'green')) {
      return this.ending_distance_in_feet(stroke);
    }
    return this.ending_distance_in_yards(stroke);
  }

  ending_distance_in_feet(stroke) {
    return this.ending_distance_in_yards(stroke) * 3;
  }

  ending_distance_in_yards(stroke) {
    return stroke.ending_distance_to_pin * Stats.METERS_TO_YARDS;
  }

  convert_starting_distance_units(stroke) {
    if (includeStr(stroke.starting_lie, 'green')) {
      return this.starting_distance_in_feet(stroke);
    }

    return this.starting_distance_in_yards(stroke);
  }

  starting_distance_in_feet(stroke) {
    return this.starting_distance_in_yards(stroke) * 3;
  }

  starting_distance_in_yards(stroke) {
    return stroke.starting_distance_to_pin * Stats.METERS_TO_YARDS;
  }
  static landed_on_fairway(stroke) {
    return (
      includeStr(stroke.ending_lie, 'fairway') ||
      includeStr(stroke.ending_lie, 'green') ||
      includeStr(stroke.ending_lie, 'tee')
    );
  }

  next_stroke_landing_coords(stroke) {
    console.log(stroke);

    // return [] if stroke.blank?
    // coords = stroke.next_stroke.try(:coords)
    // if coords.try(:x)
    //   [coords.y, coords.x]
    // else
    //   stroke.hole_played.flag rescue []
    // end
  }
}
