import { ApiProperty } from '@nestjs/swagger';
import { Exclude } from 'class-transformer';
import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { HolePlayed } from 'src/holes-played/entities/hole-played.entity';

@Entity({ name: 'stroke_stats' })
export class StrokeStat {
  @PrimaryGeneratedColumn()
  @ApiProperty({ example: 1 })
  @Index()
  id: number;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  facility_id: number;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  @Index()
  round_id: number;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  club_id: number;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  user_id: number;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  @Index()
  hole_played_id: number;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  stroke_played_id: number;

  @Column({ type: 'boolean' })
  @ApiProperty({ example: false })
  long_tee: boolean;

  @Column({ type: 'boolean' })
  @ApiProperty({ example: false })
  short_tee: boolean;

  @Column({ type: 'boolean' })
  @ApiProperty({ example: false })
  approach: boolean;

  @Column({ type: 'boolean' })
  @ApiProperty({ example: false })
  putting: boolean;

  @Column({ type: 'boolean' })
  @ApiProperty({ example: false })
  short: boolean;

  @Column({ type: 'boolean' })
  @ApiProperty({ example: false })
  long: boolean;

  @Column({ type: 'boolean' })
  @ApiProperty({ example: false })
  right: boolean;

  @Column({ type: 'boolean' })
  @ApiProperty({ example: false })
  left: boolean;

  @Column({ type: 'boolean' })
  @ApiProperty({ example: false })
  ends_in_hole: boolean;

  @Column({ type: 'boolean' })
  @ApiProperty({ example: false })
  first_stroke: boolean;

  @Column({ type: 'boolean' })
  @ApiProperty({ example: false })
  greens_in_regulation: boolean;

  @Column({ type: 'boolean' })
  @ApiProperty({ example: false })
  fairways_in_regulation: boolean;

  @Column({ type: 'boolean' })
  @ApiProperty({ example: false })
  sand_saved_opportunity: boolean;

  @Column({ type: 'boolean' })
  @ApiProperty({ example: false })
  sand_saved: boolean;

  @Column({ type: 'boolean' })
  @ApiProperty({ example: false })
  starting_as_penalty_shot: boolean;

  @Column({ type: 'boolean' })
  @ApiProperty({ example: false })
  ending_as_penalty_shot: boolean;

  @Column({ type: 'boolean' })
  @ApiProperty({ example: false })
  starting_as_difficult_shot: boolean;

  @Column({ type: 'boolean' })
  @ApiProperty({ example: false })
  ending_as_difficult_shot: boolean;

  @Column({ type: 'boolean' })
  @ApiProperty({ example: false })
  starting_as_recovery_shot: boolean;

  @Column({ type: 'boolean' })
  @ApiProperty({ example: false })
  ending_as_recovery_shot: boolean;

  @Column({ type: 'float' })
  @ApiProperty({ example: 1.0 })
  shot_distance: number;

  @Column({ type: 'float' })
  @ApiProperty({ example: 1.0 })
  starting_distance_to_pin: number;

  @Column({ type: 'float' })
  @ApiProperty({ example: 1.0 })
  ending_distance_to_pin: number;

  @Column({ type: 'float' })
  @ApiProperty({ example: 1.0 })
  distance_to_dogleg: number;

  @Column({ type: 'float' })
  @ApiProperty({ example: 1.0 })
  distance_to_pin_with_dogleg: number;

  @Column({ type: 'float' })
  @ApiProperty({ example: 1.0 })
  total_dogleg_distance: number;

  @Column({ type: 'float' })
  @ApiProperty({ example: 1.0 })
  starting_strokes_gained: number;

  @Column({ type: 'float' })
  @ApiProperty({ example: 1.0 })
  ending_strokes_gained: number;

  @Column({ type: 'float' })
  @ApiProperty({ example: 1.0 })
  strokes_gained: number;

  @Column({ type: 'float' })
  @ApiProperty({ example: 1.0 })
  x_axis: number;

  @Column({ type: 'float' })
  @ApiProperty({ example: 1.0 })
  y_axis: number;

  @Column({ type: 'float' })
  @ApiProperty({ example: 1.0 })
  z_axis: number;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  starting_lie: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  ending_lie: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  round_type: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  club_type: string;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  holes_par: number;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  holes_yardage: number;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  holes_handicap: number;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  stroke_ordinal: number;

  @Column({ type: 'timestamp' })
  @ApiProperty({ example: new Date().toISOString() })
  recorded_on: Date;

  @Column({ type: 'timestamp' })
  @ApiProperty({ example: new Date().toISOString() })
  round_played_on: Date;

  @CreateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  @Exclude()
  created_at: Date;

  // @Expose()
  // stated_on: () =>{
  //    this.created_at;
  // };

  @UpdateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  @Exclude()
  updated_at: Date;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  course_name: string;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  hole_name: number;

  @Column({ type: 'float' })
  @ApiProperty({ example: 1.0 })
  starting_strokes_gained_pro: number;

  @Column({ type: 'float' })
  @ApiProperty({ example: 1.0 })
  ending_strokes_gained_pro: number;

  @Column({ type: 'float' })
  @ApiProperty({ example: 1.0 })
  strokes_gained_pro: number;

  @Column({ type: 'json' })
  @ApiProperty({ example: 'string' })
  green_map: string;

  @Column({ type: 'float' })
  @ApiProperty({ example: 1.0 })
  latitude: number;

  @Column({ type: 'float' })
  @ApiProperty({ example: 1.0 })
  longitude: number;

  @Column({
    type: 'hstore',
    nullable: true,
  })
  green_distances: string;

  @Column({ type: 'float' })
  @ApiProperty({ example: 1.0 })
  starting_strokes_gained_five: number;

  @Column({ type: 'float' })
  @ApiProperty({ example: 1.0 })
  ending_strokes_gained_five: number;

  @Column({ type: 'float' })
  @ApiProperty({ example: 1.0 })
  strokes_gained_five: number;

  @Column({ type: 'float' })
  @ApiProperty({ example: 1.0 })
  starting_strokes_gained_ten: number;

  @Column({ type: 'float' })
  @ApiProperty({ example: 1.0 })
  ending_strokes_gained_ten: number;

  @Column({ type: 'float' })
  @ApiProperty({ example: 1.0 })
  strokes_gained_ten: number;

  @Column({ type: 'float' })
  @ApiProperty({ example: 1.0 })
  starting_strokes_gained_fifteen: number;

  @Column({ type: 'float' })
  @ApiProperty({ example: 1.0 })
  ending_strokes_gained_fifteen: number;

  @Column({ type: 'float' })
  @ApiProperty({ example: 1.0 })
  strokes_gained_fifteen: number;

  @Column({ type: 'float' })
  @ApiProperty({ example: 1.0 })
  starting_strokes_gained_twenty: number;

  @Column({ type: 'float' })
  @ApiProperty({ example: 1.0 })
  ending_strokes_gained_twenty: number;

  @Column({ type: 'float' })
  @ApiProperty({ example: 1.0 })
  strokes_gained_twenty: number;

  @Column({ type: 'float' })
  @ApiProperty({ example: 1.0 })
  shot_distance_to_fairway_centerline: number;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  bearing_to_dogleg: string;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  course_id: number;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  pin_location: string;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  round_status: number;

  @Column({ type: 'float' })
  @ApiProperty({ example: 1.0 })
  landed_latitude: number;

  @Column({ type: 'float' })
  @ApiProperty({ example: 1.0 })
  landed_longitude: number;

  @Column({ type: 'float' })
  @ApiProperty({ example: 1.0 })
  fairway_distance_from_center: number;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  fairway_location_from_center: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  igolf_course_id: string;

  @OneToOne(() => HolePlayed, (holePlayed) => holePlayed.id, {
    eager: false,
  })
  @JoinColumn({ name: 'hole_played_id', referencedColumnName: 'id' })
  hole_played?: HolePlayed;
}
