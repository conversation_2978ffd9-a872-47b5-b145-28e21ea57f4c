import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { IPaginationOptions, Pagination, paginateRaw } from 'nestjs-typeorm-paginate';
import { Repository } from 'typeorm';
import { transformDataPaging } from 'src/utils/infinity-pagination';
import { CreateCountryDto } from './dto/create-country.dto';
import { Country } from './entities/country.entity';

@Injectable()
export class CountriesService {
  constructor(
    @InjectRepository(Country)
    private readonly countryRepository: Repository<Country>
  ) {}

  create(createCountryDto: CreateCountryDto) {
    console.log(createCountryDto);

    return 'This action adds a new country';
  }

  async findAll(options: IPaginationOptions): Promise<Pagination<Country>> {
    const queryBuilder = this.countryRepository.createQueryBuilder('c');
    queryBuilder.select(['id', 'name']);
    queryBuilder.orderBy('name');
    const countries = await paginateRaw<Country>(queryBuilder, options);
    return transformDataPaging(countries);
  }

  async findAllCountries(options: IPaginationOptions): Promise<Pagination<Country>> {
    const queryBuilder = this.countryRepository.createQueryBuilder('c');
    queryBuilder.select(['id', 'name', 'iso_code', 'alpha_2', 'dial_code']);
    queryBuilder.orderBy('name');
    const countries = await paginateRaw<Country>(queryBuilder, options);
    return transformDataPaging(countries);
  }

  findOne(id: number) {
    return this.countryRepository.findOneBy({ id });
  }
}
