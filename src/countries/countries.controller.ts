import { Body, Controller, DefaultValuePipe, Get, Param, ParseIntPipe, Post, Query } from '@nestjs/common';
import { ApiQuery, ApiTags } from '@nestjs/swagger';
import { CountriesService } from './countries.service';
import { CreateCountryDto } from './dto/create-country.dto';

@ApiTags('Country')
@Controller('countries')
export class CountriesController {
  constructor(private readonly countriesService: CountriesService) {}

  @Post()
  create(@Body() createCountryDto: CreateCountryDto) {
    return this.countriesService.create(createCountryDto);
  }

  @Get()
  @ApiQuery({ name: 'page', example: 1, required: false })
  @ApiQuery({ name: 'per', example: 25, required: false })
  findAll(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page = 1,
    @Query('per', new DefaultValuePipe(25), ParseIntPipe) limit = 25
  ) {
    limit = limit > 100 ? 100 : limit;
    return this.countriesService.findAll({ page, limit, countQueries: true });
  }

  @Get('get/all')
  @ApiQuery({ name: 'page', example: 1, required: false })
  @ApiQuery({ name: 'per', example: 25, required: false })
  findAllCountries(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page = 1,
    @Query('per', new DefaultValuePipe(100), ParseIntPipe) limit = 300
  ) {
    return this.countriesService.findAllCountries({ page, limit, countQueries: true });
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.countriesService.findOne(+id);
  }
}
