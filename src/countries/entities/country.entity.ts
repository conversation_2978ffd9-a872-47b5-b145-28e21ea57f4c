import { ApiProperty } from '@nestjs/swagger';
import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

@Entity('countries')
export class Country {
  @PrimaryGeneratedColumn()
  @ApiProperty({ example: 1 })
  'id': number;

  @Column()
  @ApiProperty({ example: 'Montserrat' })
  'name': string;

  @Column()
  @ApiProperty({ example: 'MS' })
  'iso_code': string;

  @Column()
  @ApiProperty({ example: 'MSR' })
  'alpha_2': string;

  @Column()
  @ApiProperty({ example: '1-664' })
  'dial_code': string;

  @Column()
  @ApiProperty({ example: 'MSR' })
  'fifa': string;

  @Column()
  @ApiProperty({ example: 'MH' })
  'fips': string;

  @Column()
  @ApiProperty({ example: 'MNT' })
  'ioc': string;

  @Column()
  @ApiProperty({ example: 'Territory of GB' })
  'is_independent': string;

  @Column()
  @ApiProperty({
    example: `"{""currency_code"": ""XCD"", ""currency_name"": ""East Caribbean Dollar"", ""currency_minor_unit"": ""2"", ""currency_country_name"": ""MONTSERRAT"", ""currency_numeric_code"": ""951""}"`,
  })
  'currency': string;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 2 })
  'status': number;

  @Column({ type: 'integer', default: 0 })
  @ApiProperty({ example: 0 })
  'states_count': number;

  @Column({ type: 'integer', default: 0 })
  @ApiProperty({ example: 0 })
  'courses_count': number;

  @CreateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  'created_at': Date;

  @UpdateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  'updated_at': Date;
}
