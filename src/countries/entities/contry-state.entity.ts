import { ApiProperty } from '@nestjs/swagger';
import { Column, CreateDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

@Entity('states')
export class CountryState {
  @PrimaryGeneratedColumn()
  @ApiProperty({ example: 1 })
  'id': number;

  @Index()
  @Column()
  @ApiProperty({ example: 5 })
  'country_id': number;

  @Column()
  @ApiProperty({ example: 'Canillo' })
  'name': string;

  @Column()
  @ApiProperty({ example: 'AD-02' })
  'iso_code': string;

  @Column()
  @ApiProperty({ example: '' })
  'alt_name': string;

  @Column()
  @ApiProperty({ example: '' })
  'alt_name_2': string;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 2 })
  'status': number;

  @Column({ type: 'integer', default: 0 })
  @ApiProperty({ example: 0 })
  'courses_count': number;

  @CreateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  'created_at': Date;

  @UpdateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  'updated_at': Date;
}
