import { ApiProperty } from '@nestjs/swagger';
import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

@Entity('countries_iso')
export class CountryIso {
  @PrimaryGeneratedColumn()
  @ApiProperty({ example: 1 })
  'id': number;

  @Column()
  @ApiProperty({ example: 'United States' })
  'name': string;

  @Column()
  @ApiProperty({ example: 'us' })
  'iso_3166_1_alpha_2': string;

  @Column()
  @ApiProperty({ example: 'usa' })
  'iso_3166_1_alpha_3': string;

  @Column()
  @ApiProperty({ example: '840' })
  'iso_3166_1_numeric': number;

  @Column()
  @ApiProperty({ example: false })
  'disabled': boolean;

  @CreateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  'created_at': Date;

  @UpdateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  'updated_at': Date;
}
