import { Controller, DefaultValuePipe, Get, Param, ParseIntPipe, Query } from '@nestjs/common';
import { ApiQuery } from '@nestjs/swagger';
import { throwNotFoundError } from 'src/utils/exception';
import { AvailableClubsService } from './available-clubs.service';

@Controller('available_clubs')
export class AvailableClubsController {
  constructor(private readonly availableClubsService: AvailableClubsService) {}

  @Get()
  @ApiQuery({ name: 'page', example: 1, required: false })
  @ApiQuery({ name: 'per', example: 25, required: false })
  @ApiQuery({ name: 'brand', example: '', required: false })
  @ApiQuery({ name: 'club_type', example: '', required: false })
  @ApiQuery({ name: 'family', example: '', required: false })
  findAll(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page = 1,
    @Query('per', new DefaultValuePipe(25), ParseIntPipe) limit = 25,
    @Query('brand') brand: string,
    @Query('club_type') club_type: string,
    @Query('family') family: string
  ) {
    return this.availableClubsService.findAll(page, limit, brand, club_type, family);
  }

  @Get(':id')
  async findOne(@Param('id') id: number) {
    const club = await this.availableClubsService.findOne(+id);
    if (!club) {
      return throwNotFoundError();
    }
    return {
      data: AvailableClubsService.transformAvailableClubResponse(club),
    };
  }
}
