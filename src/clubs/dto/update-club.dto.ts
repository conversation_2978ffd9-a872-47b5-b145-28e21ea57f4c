import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsDefined, IsNotEmptyObject, IsObject, IsOptional, ValidateNested } from 'class-validator';
import { UserClubDto } from '../../users/dto/create-user-club.dto';

export class UpdateUserClubDto {
  @IsOptional()
  @IsDefined()
  @IsNotEmptyObject()
  @IsObject()
  @ValidateNested()
  @Type(() => UserClubDto)
  club: UserClubDto;

  @ApiProperty({ example: 1 })
  @IsOptional()
  user_id: number;
}
