import { ApiProperty } from '@nestjs/swagger';
import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

@Entity({ name: 'clubs' })
export class Club {
  @PrimaryGeneratedColumn()
  @ApiProperty({ example: 1 })
  id: number;

  @Column({ type: 'boolean' })
  @ApiProperty({ example: false })
  in_bag: boolean;

  @Column({ type: 'boolean' })
  @ApiProperty({ example: false })
  disabled: boolean;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  shaft_length: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  shaft_flex: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  face_lie_adjustment: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  face_loft_adjustment: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  loft: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  uuid: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  manufacturer: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  modelname: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  club_type: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  club_family: string;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  position: number;

  @CreateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  created_at: Date;

  @UpdateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  updated_at: Date;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  user_id: number;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  club_name: string;

  @Column({ type: 'date' })
  @ApiProperty({ example: new Date().toISOString() })
  release_date: Date;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  cdm_witb_id: string;
}
