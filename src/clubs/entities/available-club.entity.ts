import { ApiProperty } from '@nestjs/swagger';
import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

@Entity({ name: 'available_clubs' })
export class AvailableClubEntity {
  @PrimaryGeneratedColumn()
  @ApiProperty({ example: 1 })
  id: number;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  club_name: string;

  @Column('character varying', { array: true, default: [] })
  @ApiProperty({ example: [] })
  shaft_length: string[];

  @Column('character varying', { array: true, default: [] })
  @ApiProperty({ example: [] })
  shaft_flex: string[];

  @Column('character varying', { array: true, default: [] })
  @ApiProperty({ example: [] })
  lie_adjustment: string[];

  @Column('character varying', { array: true, default: [] })
  @ApiProperty({ example: [] })
  loft_adjustment: string[];

  @Column('character varying', { array: true, default: [] })
  @ApiProperty({ example: [] })
  loft: string[];

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  brand: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  modelname: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  club_type: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  family: string;

  @CreateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  created_at: Date;

  @UpdateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  updated_at: Date;

  @Column({ type: 'date' })
  @ApiProperty({ example: new Date().toISOString() })
  release_date: Date;
}
