import { Inject, Injectable, Request, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectRepository } from '@nestjs/typeorm';
import moment from 'moment';
import { paginateRaw } from 'nestjs-typeorm-paginate';
import { Repository } from 'typeorm';
import { transformDataPaging } from '../utils/infinity-pagination';
import { AvailableClubEntity } from './entities/available-club.entity';

@Injectable({ scope: Scope.REQUEST })
export class AvailableClubsService {
  constructor(
    @InjectRepository(AvailableClubEntity)
    private readonly availableClubRepository: Repository<AvailableClubEntity>,
    @Inject(REQUEST) private request: Request
  ) {}

  async findAll(page: number, limit: number, brand: string, club_type: string, family: string) {
    const currentPathName = this.request.url?.split('?')[0];
    const queryBuilder = this.availableClubRepository.createQueryBuilder();
    queryBuilder.select([
      'id',
      'club_name',
      'shaft_length',
      'shaft_flex',
      'lie_adjustment',
      'loft_adjustment',
      'loft',
      'brand',
      'modelname',
      'club_type',
      'family',
      'release_date',
    ]);
    if (brand?.trim()) {
      queryBuilder.andWhere('LOWER(brand) = LOWER(:brand)', { brand });
    }
    if (family?.trim()) {
      queryBuilder.andWhere('LOWER(family) = LOWER(:family)', { family });
    }
    if (club_type?.trim()) {
      queryBuilder.andWhere('LOWER(club_type) = LOWER(:club_type)', { club_type });
    }
    if (brand?.trim() || club_type?.trim()) {
      queryBuilder.orderBy('release_date', 'DESC');
      queryBuilder.orderBy('modelname', 'ASC');
    }
    const clubs = await paginateRaw<AvailableClubEntity>(queryBuilder, {
      page,
      limit,
      countQueries: true,
    });
    const results: any = { ...clubs };
    results.items = clubs.items.map((item) => AvailableClubsService.transformAvailableClubResponse(item));
    const pagingExtraQueries: any = { per: limit };
    if (brand) {
      pagingExtraQueries.brand = brand;
    }
    if (club_type) {
      pagingExtraQueries.club_type = club_type;
    }
    return transformDataPaging(results, currentPathName, pagingExtraQueries);
  }

  findOne(id: number) {
    return this.availableClubRepository.findOne({ where: { id } });
  }

  static transformAvailableClubResponse(availableClub: AvailableClubEntity) {
    delete availableClub.created_at;
    delete availableClub.updated_at;
    return {
      ...availableClub,
      release_date: availableClub.release_date ? moment(availableClub.release_date).format('YYYY-MM-DD') : null,
    };
  }
}
