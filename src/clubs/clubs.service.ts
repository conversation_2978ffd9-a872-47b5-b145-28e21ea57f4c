import { Inject, Injectable, Request, Scope, forwardRef } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { REQUEST } from '@nestjs/core';
import { InjectRepository } from '@nestjs/typeorm';
import { plainToClass, plainToClassFromExist } from 'class-transformer';
import { isEmpty } from 'lodash';
import capitalize from 'lodash/capitalize';
import { paginateRaw } from 'nestjs-typeorm-paginate';
import { In, Repository } from 'typeorm';
import { CdmCacheService } from 'src/cdm/cdm.cache.service';
import { CdmService } from 'src/cdm/cdm.service';
import { MytmService } from 'src/mytm/mytm.service';
import { CreateUserClubDto, UserClubDto } from 'src/users/dto/create-user-club.dto';
import { WitbDTO } from 'src/users/dto/update-witbs.dto';
import { User } from 'src/users/entities/user.entity';
import { UsersService } from 'src/users/users.service';
import { isNumeric } from 'src/utils/helpers';
import { transformDataPaging } from 'src/utils/infinity-pagination';
import { deleteValueBlank } from 'src/utils/utils';
import { UpdateUserClubDto } from './dto/update-club.dto';
import { Club } from './entities/club.entity';

@Injectable({ scope: Scope.REQUEST })
export class ClubsService {
  constructor(
    @InjectRepository(Club)
    private readonly clubRepository: Repository<Club>,
    @Inject(REQUEST) private request: Request,
    private cdmCacheService: CdmCacheService,
    private mytmService: MytmService,
    @Inject(forwardRef(() => CdmService)) private cdmService: CdmService,
    private config: ConfigService,
    @Inject(forwardRef(() => UsersService)) private userService: UsersService
  ) {}

  create(club: Club) {
    return this.clubRepository.save(club);
  }

  findAll() {
    return `This action returns all clubs`;
  }

  findOne(id: number) {
    return this.clubRepository.findOne({ where: { id } });
  }

  remove(id: number) {
    return `This action removes a #${id} club`;
  }

  updateClubWithCdmWITBId(clubId: number, witbId: string) {
    return this.clubRepository.update({ id: clubId }, { cdm_witb_id: witbId });
  }

  getAllUserClubs(userId: number) {
    return this.clubRepository.find({ where: { user_id: userId } });
  }

  async createUserClub(userId: number, data: CreateUserClubDto | UserClubDto) {
    let club = null;
    let dto: any = null;
    dto = { ...data };
    const params = dto.club != undefined ? dto.club : dto;
    club = plainToClass(Club, params);
    club.user_id = userId;
    if (typeof params.in_bag === 'undefined') {
      club.in_bag = true;
    }
    if (typeof params.disabled === 'undefined') {
      club.disabled = false;
    }
    club.created_at = new Date();
    club.updated_at = new Date();
    const { club_family, position } = this.setClubPosition(club.club_type);
    club.club_family = club_family;
    club.position = position;
    const payload = await this.transformClubToWITBParams(club);
    const witb = await this.cdmService.createWITB(payload);

    if (!witb?.id) {
      return null;
    }
    club.cdm_witb_id = witb.id;
    return this.clubRepository.save(club);
  }
  setClubPosition(club_type) {
    let club_family = '';
    let position = 0;
    if (isEmpty(club_type)) {
      return { club_family, position };
    }
    // # Iron Selection
    const clubType = club_type?.toLowerCase();
    if (clubType == 'ball') {
      return {
        club_family: 'ball',
        position: 0,
      };
    }
    let positionClub = '';
    if (clubType.includes('-pw')) {
      positionClub = clubType.replace('-pw', '')?.trim();
      position = +`14.${positionClub}`;
      club_family = 'iron';
      return { club_family, position };
    }
    if (clubType.includes('-sw')) {
      positionClub = clubType.replace('-sw', '')?.trim();
      position = +`15.${positionClub}`;
      club_family = 'iron';
      return { club_family, position };
    }
    switch (club_type?.toLowerCase()) {
      case 'driver':
        position = 1;
        club_family = 'driver';
        break;

      // # Woods
      case '2-fw':
        position = 2;
        club_family = 'fairway';
        break;
      case '3-fw':
        position = 3;
        club_family = 'fairway';
        break;
      case '3fw':
        position = 3;
        club_family = 'fairway';
        break;
      case '4-fw':
        position = 4;
        club_family = 'fairway';
        break;
      case '5-fw':
        position = 5;
        club_family = 'fairway';
        break;
      case '7-fw':
        position = 6;
        club_family = 'fairway';
        break;
      case '9-fw':
        position = 7;
        club_family = 'fairway';
        break;

      // # Hybrids
      case '2h':
        position = 8;
        club_family = 'hybrid';
        break;
      case '3h':
        position = 9;
        club_family = 'hybrid';
        break;
      case '4h':
        position = 10;
        club_family = 'hybrid';
        break;
      case '5h':
        position = 11;
        club_family = 'hybrid';
        break;
      case '6h':
        position = 12;
        club_family = 'hybrid';
        break;
      case '7h':
        position = 13;
        club_family = 'hybrid';
        break;
      // # Irons
      case '1i':
        position = 16;
        club_family = 'iron';
        break;
      case '2i':
        position = 17;
        club_family = 'iron';
        break;
      case '3i':
        position = 18;
        club_family = 'iron';
        break;
      case '4i':
        position = 19;
        club_family = 'iron';
        break;
      case '5i':
        position = 20;
        club_family = 'iron';
        break;
      case '6i':
        position = 21;
        club_family = 'iron';
        break;
      case '7i':
        position = 22;
        club_family = 'iron';
        break;
      case '8i':
        position = 23;
        club_family = 'iron';
        break;
      case '9i':
        position = 24;
        club_family = 'iron';
        break;

      // # Wedge
      // # order: PW, AW, SW, LW
      case 'pw':
        position = 25;
        club_family = 'wedge';
        break;
      case 'aw':
        position = 26;
        club_family = 'wedge';
        break;
      case 'sw':
        position = 27;
        club_family = 'wedge';
        break;
      case 'lw':
        position = 28;
        club_family = 'wedge';
        break;
      case 'wedge':
        position = 29;
        club_family = 'wedge';
        break;
      // # Putter
      case 'putter':
        position = 30;
        club_family = 'putter';
        break;
    }
    return { club_family, position };
  }
  async markClubAsDisabled(clubId: number) {
    await this.clubRepository.update({ id: clubId }, { disabled: true });
  }
  async markClubCDMAsDisabled(cdmWitbId: string) {
    await this.clubRepository.update({ cdm_witb_id: cdmWitbId }, { disabled: true });
  }

  async updateClub(clubId: number, data) {
    data['updated_at'] = new Date();
    return this.clubRepository.update({ id: clubId }, data);
  }
  async updateClubByCdmWitbId(cdmWitbId: string, data) {
    data['updated_at'] = new Date();
    return this.clubRepository.update({ cdm_witb_id: cdmWitbId }, data);
  }

  async updateUserClub(clubId: number, data: UpdateUserClubDto | UserClubDto, userId: number): Promise<[string, Club]> {
    let club = await this.clubRepository.findOne({ where: { id: clubId } });
    if (!club) {
      return ['Club not found!', null];
    }
    if (club.user_id !== userId) {
      return ['Club is not belong to this user!', null];
    }
    let dto: any = null;
    dto = { ...data };
    const params = dto.club != undefined ? dto.club : dto;
    club = plainToClassFromExist(club, params);
    club.updated_at = new Date();
    if (params.club_type) {
      const { club_family, position } = this.setClubPosition(club.club_type);
      club.club_family = club_family;
      club.position = position;
    }

    club = await this.clubRepository.save(club);
    const payload = await this.transformClubToWITBParams(club);
    const witb = await this.cdmService.updateWITB(club.cdm_witb_id, payload);
    this.triggerUserCollectSaleForceData(userId);
    if (!witb?.id) {
      return ['Error when update cdm WITB!', null];
    }
    return [null, club];
  }

  async activateUserClubs(userId: number, clubIds: number[], deactivate = false) {
    for (const clubId of clubIds) {
      const club = await this.clubRepository.findOne({ where: { id: clubId } });
      if (club && club.user_id === userId) {
        if (deactivate) {
          club.disabled = true;
          club.in_bag = false;
        } else {
          club.disabled = false;
        }
        const isRecover = !club.disabled;
        const payload = await this.transformClubToWITBParams(club);
        if (isRecover) {
          await this.cdmService.recoverWITB(club.cdm_witb_id);
        } else {
          await this.cdmService.updateWITB(club.cdm_witb_id, payload);
        }

        await this.clubRepository.save(club);
      }
    }
    this.triggerUserCollectSaleForceData(userId);
    return {
      msg: `Action deactivate: ${deactivate} clubs successfully`,
      data: clubIds,
    };
  }

  async activeUserClubs(userId: number, clubIds: number[], inactive = false) {
    if (inactive == undefined) {
      inactive = false;
    }

    inactive = [true, 'true'].includes(inactive);
    for (const clubId of clubIds) {
      const club = await this.clubRepository.findOne({ where: { id: clubId } });
      if (club && club.user_id === userId) {
        club.in_bag = !inactive;
        const payload = await this.transformClubToWITBParams(club);
        await this.cdmService.updateWITB(club.cdm_witb_id, payload);
        await this.clubRepository.save(club);
      }
    }
    this.triggerUserCollectSaleForceData(userId);
    return {
      msg: `Action inactive: ${inactive} clubs successfully`,
      data: clubIds,
    };
  }
  async triggerUserCollectSaleForceData(userId) {
    try {
      const user = await this.userService.findOne({ id: userId });
      if (user) {
        this.mytmService.triggerCollectSaleForceData(user.email);
      }
    } catch (error) {
      console.log(error);
    }
  }
  async getUserDeactivatedClubs(userId: number) {
    return this.clubRepository.find({ where: { user_id: userId, disabled: true } });
  }

  async findClubWithCdmWITBId(cdmWITBId: string) {
    return this.clubRepository.findOne({ where: { cdm_witb_id: cdmWITBId } });
  }
  async findClubWithCdmWITBIds(cdmWITBIds: WitbDTO[]) {
    const witbIds = cdmWITBIds.map((witb) => witb.witbId);
    return this.clubRepository.find({ where: { cdm_witb_id: In(witbIds) }, select: ['id', 'cdm_witb_id'] });
  }

  async transformClubToWITBParams(club: Club, user?: User) {
    await this.cdmCacheService.initialCaches();
    const category = await this.transformClubToCategory(club);
    const brand = await this.transformClubToBrand(club, category);
    const model = await this.transformClubToModel(club, brand, category);
    const categoryType = await this.transformClubToCategoryType(club, category);
    const loft = await this.transformClubToLoft(club);
    const shaftFlex = await this.transformClubToShaftFlex(club);
    const shaftLength = await this.transformClubToShaftLength(club, category);
    const lie = await this.transformClubToLie(club);
    const loftAdjustment = await this.transformClubToLoftAdjustment(club);
    if (!user) {
      user = await this.userService.findOne({ id: club.user_id });
    }
    return {
      systemId: this.config.get('app.cdmMRPSystemId'),
      categoryId: category?.id,
      clubCategoryId: category?.id,
      brandId: brand?.id,
      modelId: model?.id,
      consumerId: user.cdm_id,
      clubLoftId: loft?.id,
      clubShaftFlexId: shaftFlex?.id,
      clubShaftLengthId: shaftLength?.id,
      faceLieAdjustmentId: lie?.id,
      faceLoftAdjustmentId: loftAdjustment?.id,
      clubCategoryTypeId: categoryType?.id || '',
      active: !club.disabled,
      inBag: club.in_bag,
      deleted: club.disabled ? new Date().toISOString() : '',
    };
  }

  async transformClubToCategory(club: Club) {
    let family = club.club_family?.toLowerCase();
    if (family === 'iron') {
      family = 'irons';
    }
    if (family === 'hybrid') {
      family = 'rescue';
    }
    const clubCategories: any = await this.getClubCategories();
    return clubCategories.find((item) => item.name?.toLowerCase() === family);
  }

  private async getClubCategories() {
    let clubCategories: any = this.cdmCacheService.caches.clubCategories;
    if (!clubCategories || isEmpty(clubCategories)) {
      clubCategories = await this.cdmService.cacheClubCategories();
      clubCategories = this.cdmCacheService.caches.clubCategories;
    }
    return clubCategories;
  }

  async transformClubToBrand(club: Club, category) {
    const ironCategory = this.cdmCacheService.caches.clubCategories.find(
      (item) => item?.name?.toLowerCase() === 'irons'
    );
    const filterByCategoryId = club.club_family?.toLowerCase() === 'wedge' ? ironCategory?.id : category?.id;
    const manufacturer = club.manufacturer?.toLowerCase();
    const clubBrands = await this.getClubBrands();
    let is_wedge = false;
    if (club?.club_family?.toLowerCase() == 'wedge') {
      is_wedge = true;
    }
    let brand = null;
    if (is_wedge) {
      const clubIron = new Club();
      clubIron.club_family = 'iron';
      const categoryIron = await this.transformClubToCategory(clubIron);
      brand = clubBrands.find(
        (item) => item.name?.toLowerCase() === manufacturer?.toLowerCase() && item.clubCategoryId === categoryIron['id']
      );
    } else {
      brand = clubBrands.find(
        (item) => item.name?.toLowerCase() === manufacturer?.toLowerCase() && item.clubCategoryId === filterByCategoryId
      );
    }

    if (!brand) {
      brand = clubBrands.find((item) => item.name === '');
    }
    return brand;
  }
  private async getClubBrands() {
    let clubBrands: any = this.cdmCacheService.caches.clubBrands;
    if (!clubBrands || isEmpty(clubBrands)) {
      clubBrands = await this.cdmService.cacheClubBrands();
      clubBrands = this.cdmCacheService.caches.clubBrands;
    }
    return clubBrands;
  }
  async transformClubToModel(club: Club, brand, category?: any) {
    const modelName = club.modelname;
    const clubModels = await this.getClubModels();
    let is_wedge = false;
    if (club?.club_family?.toLowerCase() == 'wedge') {
      is_wedge = true;
    }

    let model = clubModels.find(
      (item) => item.name?.toLowerCase() === modelName?.toLowerCase() && item.brandId === brand?.id
    );
    // not found model with irons -> find with wedge
    if (is_wedge && !model) {
      const manufacturer = club.manufacturer?.toLowerCase();
      const clubBrands = await this.getClubBrands();
      brand = clubBrands.find(
        (item) => item.name?.toLowerCase() === manufacturer?.toLowerCase() && item.clubCategoryId === category?.id
      );
      if (!brand) {
        brand = clubBrands.find((item) => item.name == '');
      }
      model = clubModels.find(
        (item) => item.name?.toLowerCase() === modelName?.toLowerCase() && item.brandId === brand?.id
      );
    }
    if (!model) {
      model = clubModels.find((item) => item.name?.toLowerCase() === '');
    }
    return model;
  }
  private async getClubModels() {
    let clubModels: any = this.cdmCacheService.caches.clubModels;
    if (!clubModels || isEmpty(clubModels)) {
      clubModels = await this.cdmService.cacheClubModels();
      clubModels = this.cdmCacheService.caches.clubModels;
    }
    return clubModels;
  }
  async transformClubToCategoryType(club: Club, category) {
    const categoriesTypes: any = await this.getClubCategoriesTypes();
    const clubCategoryTypes = categoriesTypes.filter((item) => item.clubCategoryId === category?.id);
    let clubType = club.club_type?.toLowerCase();
    if (!clubType) {
      return null;
    }
    if (clubType.length === 2 && clubType.includes('i')) {
      clubType = clubType.replace('i', '');
    } else if (clubType.length === 2 && clubType.includes('h')) {
      clubType = clubType.replace('h', ' rescue');
    } else if (clubType.includes('-fw')) {
      clubType = clubType.replace('-fw', 'fw');
    } else if (clubType.includes('lob wedge')) {
      clubType = clubType.replace('lob wedge', 'lw');
    } else if (clubType.includes('pitching wedge')) {
      clubType = clubType.replace('pitching wedge', 'pw');
    } else if (clubType.includes('specialist wedge')) {
      clubType = clubType.replace('specialist wedge', 'sw');
    } else if (clubType.includes('sand wedge')) {
      clubType = clubType.replace('sand wedge', 'sw');
    } else if (clubType.includes('approach wedge')) {
      clubType = clubType.replace('approach wedge', 'aw');
    }
    return clubCategoryTypes.find((item) => item.type?.toLowerCase() === clubType);
  }
  private async getClubCategoriesTypes() {
    let clubCategoriesTypes: any = this.cdmCacheService.caches.clubCategoriesTypes;
    if (!clubCategoriesTypes || isEmpty(clubCategoriesTypes)) {
      clubCategoriesTypes = await this.cdmService.cacheClubCategoriesTypes();
      clubCategoriesTypes = this.cdmCacheService.caches.clubCategoriesTypes;
    }
    return clubCategoriesTypes;
  }

  async transformClubToLoft(club: Club) {
    const loftValue = club.loft?.replace('°', '').replace('.0', '');
    const clubLofts = await this.getClubLofts();
    const loft = clubLofts.find((item) => item.value?.toLowerCase() === loftValue?.toLowerCase() && item.inUse);
    if (loft) {
      return loft;
    }
    return clubLofts.find((item) => item.value === '');
  }
  private async getClubLofts() {
    let clubLofts: any = this.cdmCacheService.caches.clubLofts;
    if (!clubLofts || isEmpty(clubLofts)) {
      clubLofts = await this.cdmService.cacheClubLofts();
      clubLofts = this.cdmCacheService.caches.clubLofts;
    }
    return clubLofts;
  }
  async transformClubToShaftFlex(club: Club) {
    const shaftFlexValue = club.shaft_flex?.toLowerCase();
    const shaftFlexs = await this.getClubShaftFlex();
    const shaftFlex = shaftFlexs.find((item) => item.value?.toLowerCase() === shaftFlexValue && item.inUse);
    if (shaftFlex) {
      return shaftFlex;
    }
    return shaftFlexs.find((item) => item.value === '');
  }
  private async getClubShaftFlex() {
    let clubShaftFlex: any = this.cdmCacheService.caches.clubShaftFlex;
    if (!clubShaftFlex || isEmpty(clubShaftFlex)) {
      clubShaftFlex = await this.cdmService.cacheClubShaftFlex();
      clubShaftFlex = this.cdmCacheService.caches.clubShaftFlex;
    }
    return clubShaftFlex;
  }

  async transformClubToLie(club: Club) {
    let clubLie = club.face_lie_adjustment || '';
    if (clubLie.toLowerCase() === 'std') {
      clubLie = 'Standard';
    }
    if (clubLie.toLowerCase().includes('up')) {
      clubLie = clubLie.replace('up', 'upright');
    }
    if (clubLie.toLowerCase().includes('°')) {
      clubLie = clubLie.replace('°', '*');
    }
    const clubLies = await this.getClubLies();
    const lie = clubLies.find((item) => item.value?.toLowerCase() === clubLie.toLowerCase() && item.inUse);
    if (lie) {
      return lie;
    }
    return clubLies.find((item) => item.value === '');
  }
  private async getClubLies() {
    let clubLies: any = this.cdmCacheService.caches.clubLies;
    if (!clubLies || isEmpty(clubLies)) {
      clubLies = await this.cdmService.cacheClubLies();
      clubLies = this.cdmCacheService.caches.clubLies;
    }
    return clubLies;
  }

  async transformClubToLoftAdjustment(club: Club) {
    let clubLoftAdjustment = club.face_loft_adjustment || '';
    if (clubLoftAdjustment.toLowerCase() === 'std') {
      clubLoftAdjustment = 'Std';
    }
    const clubLoftAdjustments = await this.getClubLoftAdjustment();
    const loftAdjustment = clubLoftAdjustments.find(
      (item) => item.value?.toLowerCase() === clubLoftAdjustment.toLowerCase() && item.inUse
    );
    if (loftAdjustment) {
      return loftAdjustment;
    }
    return clubLoftAdjustments.find((item) => item.value === '');
  }

  private async getClubLoftAdjustment() {
    let clubLoftAdjustment: any = this.cdmCacheService.caches.clubLoftAdjustment;
    if (!clubLoftAdjustment || isEmpty(clubLoftAdjustment)) {
      clubLoftAdjustment = await this.cdmService.cacheClubLoftAdjustment();
      clubLoftAdjustment = this.cdmCacheService.caches.clubLoftAdjustment;
    }
    return clubLoftAdjustment;
  }

  async transformClubToShaftLength(club: Club, category) {
    let shaftLengthValue = club.shaft_length || '';
    if (shaftLengthValue.toLowerCase() === 'std') {
      shaftLengthValue = 'Std';
    }
    if (isNumeric(shaftLengthValue) && shaftLengthValue.includes('.0')) {
      shaftLengthValue = shaftLengthValue.replace('.0', '');
    }
    if (shaftLengthValue.toLowerCase().includes('short')) {
      shaftLengthValue = `-${shaftLengthValue
        .toLowerCase()
        .replace('short', '')
        .replace('\\', '')
        .replace('"', '')
        .trim()}`;
    }
    if (shaftLengthValue.toLowerCase().includes('long')) {
      shaftLengthValue = `+${shaftLengthValue
        .toLowerCase()
        .replace('long', '')
        .replace('\\', '')
        .replace('"', '')
        .trim()}`;
    }
    const clubShaftLengths: any = await this.getClubShaftLength();
    const shaftLength = clubShaftLengths.find(
      (item) =>
        item.value?.toLowerCase() === shaftLengthValue?.toLowerCase() &&
        item.inUse &&
        item.clubCategoryId === category?.id
    );
    if (shaftLength) {
      return shaftLength;
    }
    return clubShaftLengths.find((item) => item.value === '');
  }
  private async getClubShaftLength() {
    let clubShaftLength: any = this.cdmCacheService.caches.clubShaftLength;
    if (!clubShaftLength || isEmpty(clubShaftLength)) {
      clubShaftLength = await this.cdmService.cacheClubShaftLength();
      clubShaftLength = this.cdmCacheService.caches.clubShaftLength;
    }
    return clubShaftLength;
  }

  async getUserClubs(userId: number, page: number, limit: number, excludeRegistedClubs: boolean, inactive: boolean) {
    const currentPathName = this.request.url?.split('?')[0];
    const queryBuilder = this.clubRepository.createQueryBuilder();
    const user = await this.userService.findOne({ id: userId });
    const witb = await this.cdmService.getWITBs(user.email);
    queryBuilder
      .select([
        'id',
        'in_bag',
        'disabled',
        'shaft_length',
        'shaft_flex',
        'face_lie_adjustment',
        'face_loft_adjustment',
        'loft',
        'manufacturer',
        'modelname',
        'club_type',
        'club_family',
        'cdm_witb_id',
      ])
      .where('user_id = :userId', { userId })
      .andWhere(`club_type is not NULL AND club_type != ''`)
      .andWhere('disabled = false');
    if (!inactive) {
      queryBuilder.andWhere('in_bag = true');
    } else {
      queryBuilder.andWhere('in_bag = false');
    }
    const clubs = await paginateRaw<Club>(queryBuilder, { page, limit, countQueries: true });
    const results: any = { ...clubs };
    results.items = this.transformClubsData(clubs.items, userId, witb);
    const pagingExtraQueries: any = { per: limit };
    if (inactive) {
      pagingExtraQueries.inactive = true;
    }
    if (excludeRegistedClubs) {
      pagingExtraQueries.excludeRegistedClubs = true;
    }
    return transformDataPaging(results, currentPathName, pagingExtraQueries);
  }

  transformClubsData(clubs: Club[], userId: number, WITBs: any[]) {
    return clubs.map((club) => this.transformClubData(club, userId, WITBs));
  }

  transformClubData(club: Club, userId: number, WITBs: any[]) {
    if (!WITBs) {
      return {};
    }
    const witb = WITBs.find((item) => item.id === club.cdm_witb_id);
    let clubType = club.club_type;
    let clubFamily = club.club_family;
    if (clubFamily?.toLowerCase() === 'driver') {
      clubType = 'Driver';
    }
    if (clubFamily?.toLowerCase() === 'putter') {
      clubType = 'Putter';
    }
    if (clubFamily?.toLowerCase() === 'rescue') {
      clubFamily = 'Hybrid';
    }
    if (clubFamily?.toLowerCase() === 'irons') {
      clubFamily = 'Iron';
    }
    if (clubType.length === 1 && clubFamily === 'Iron') {
      clubType = `${clubType}i`;
    } else if (clubType.toLowerCase()?.includes('rescue')) {
      clubType = clubType.replace(' rescue', 'h');
    }
    if (clubType.length === 2) {
      if (!(clubType.toLowerCase().includes('i') || clubType.toLowerCase().includes('h'))) {
        clubType = clubType.toUpperCase();
      }
    } else {
      if (
        clubType.toLowerCase().includes('-fw') ||
        clubType.toLowerCase().includes('-pw') ||
        clubType.toLowerCase().includes('-sw') ||
        clubType.toLowerCase().includes('-aw')
      ) {
        clubType = clubType.toUpperCase();
      } else if (clubType.toLowerCase().includes('fw')) {
        clubType = clubType.toLowerCase().replace(/fw/g, '-fw').toUpperCase();
      } else {
        clubType = clubType
          .split(/ |\_/)
          .map((item) => capitalize(item))
          .join(' ');
      }
    }
    let data = {
      ...club,
      image_thumb: witb?.imageThumb || '',
      image_small: witb?.imageSmall || '',
      club_type: clubType,
      club_family: clubFamily,
      image_reg: witb?.imageReg || '',
      image_large: witb?.imageLarge || '',
      self: `${process.env.BACKEND_DOMAIN}/api/clubs/${club.id}`,
      user: `${process.env.BACKEND_DOMAIN}/api/users/${userId}`,
    };
    data['manufacturer'] = data['manufacturer'] || '';
    data['modelname'] = data['modelname'] || '';
    data = deleteValueBlank(data, false);
    return data;
  }

  async findCdmWitbId(id: any) {
    try {
      const club = await this.clubRepository.find({ where: { cdm_witb_id: id }, select: ['cdm_witb_id'] });
      return club[0]['cdm_witb_id'];
    } catch (error) {
      console.log(`ERROR notFound club with clubId: ${id}`);
      return id;
    }
  }

  async findClubBy(options: any) {
    try {
      return this.clubRepository.find(options);
    } catch (error) {
      console.log(`ERROR notFound club with options: ${options}`);
      return null;
    }
  }

  async findOneByCdmWITBId(cdmWITBId: string) {
    return this.clubRepository.findOne({ where: { cdm_witb_id: cdmWITBId } });
  }

  async findLstCdmWitbId(ids: any) {
    try {
      return this.clubRepository.find({
        where: { id: In(ids) },
        select: ['id', 'cdm_witb_id'],
      });
    } catch (error) {
      console.log(`ERROR notFound club with clubIds: ${ids}`);
      return ids;
    }
  }
}
