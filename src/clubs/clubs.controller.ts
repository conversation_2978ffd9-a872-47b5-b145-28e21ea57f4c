import { Body, Controller, Delete, Get, Param, Put, Request, UseGuards } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { AuthGuard } from 'src/guards/auth.guard';
import { MytmService } from 'src/mytm/mytm.service';
import { UserClubDto } from 'src/users/dto/create-user-club.dto';
import { UsersService } from 'src/users/users.service';
import { throwBadRequestError, throwNotFoundError } from 'src/utils/exception';
import { BaseRequest } from 'src/utils/types/request';
import { CdmService } from '../cdm/cdm.service';
import { ClubsService } from './clubs.service';
import { UpdateUserClubDto } from './dto/update-club.dto';
import { Club } from './entities/club.entity';

@ApiTags('Club')
@Controller('clubs')
@UseGuards(AuthGuard)
export class ClubsController {
  constructor(
    private readonly clubsService: ClubsService,
    private readonly usersService: UsersService,
    private readonly cdmService: CdmService,
    private readonly mytmService: MytmService
  ) {}

  @Get()
  findAll() {
    return this.clubsService.findAll();
  }
  @Get(':id')
  async findOne(@Param('id') id: number) {
    const club = await this.clubsService.findOne(+id);
    if (!club) {
      return throwNotFoundError();
    }
    const user = await this.usersService.findOne({ id: club.user_id });
    const witb = await this.cdmService.getWITBs(user.email);
    return {
      data: this.clubsService.transformClubData(club as Club, club.user_id, witb),
    };
  }

  @Put(':id')
  async updateUserClubs(
    @Param('id') id: number,
    @Body() clubDto: UpdateUserClubDto | UserClubDto,
    @Request() request: BaseRequest
  ) {
    const [error, club] = await this.clubsService.updateUserClub(id, clubDto, request.user?.id || clubDto.user_id);

    if (error && !club) {
      return throwBadRequestError(error as string);
    }
    const user = await this.usersService.findOne({ id: club.user_id });
    const witbs = await this.cdmService.getWITBs(user.email);
    return {
      data: this.clubsService.transformClubData(club as Club, club.user_id, witbs),
    };
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.clubsService.remove(+id);
  }
}
