import { Module } from '@nestjs/common';
import { SharedModule } from 'src/shared/shared.module';
import { AvailableClubsController } from './available-clubs.controller';
import { AvailableClubsService } from './available-clubs.service';
import { ClubsController } from './clubs.controller';
import { ClubsService } from './clubs.service';

@Module({
  imports: [SharedModule],
  controllers: [ClubsController, AvailableClubsController],
  providers: [ClubsService, AvailableClubsService],
})
export class ClubsModule {}
