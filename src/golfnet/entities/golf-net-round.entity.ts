import { ApiProperty } from '@nestjs/swagger';
import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

@Entity({ name: 'golf_net_rounds' })
export class GolfNetRoundEntity {
  @PrimaryGeneratedColumn()
  @ApiProperty({ example: 1 })
  id: number;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  user_id: number;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  canada_card_id: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  golfnet_round_id: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  golfnet_course_id: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  golfnet_course_name: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  golfnet_tee_set_id: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  golfnet_tee_set_name: string;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  score: number;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  score_to_par: number;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  generated_by: string;

  @Column({ type: 'float' })
  @ApiProperty({ example: 1.0 })
  course_rating: number;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  slope_rating: number;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  course_yards: number;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  course_par: number;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  number_of_holes_played: number;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  user_timezone: number;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  score_type: string;

  @ApiProperty({ example: new Date().toString() })
  @Column({ type: 'timestamptz' })
  played_on: Date;

  @ApiProperty({ example: new Date().toISOString() })
  @Column({ type: 'timestamptz' })
  played_on_utc: Date;

  @CreateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  created_at: Date;

  @UpdateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  updated_at: Date;
}
