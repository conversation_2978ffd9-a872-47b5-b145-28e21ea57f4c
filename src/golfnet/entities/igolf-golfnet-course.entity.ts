import { ApiProperty } from '@nestjs/swagger';
import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

@Entity({ name: 'igolf_golfnet_courses' })
export class IGolfGolfNetCourse {
  @PrimaryGeneratedColumn()
  @ApiProperty({ example: 1 })
  id: number;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  igolf_course_id: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  golfnet_course_id: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  golfnet_course_name: string;

  @CreateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  created_at: Date;

  @UpdateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  updated_at: Date;
}
