import { ApiProperty } from '@nestjs/swagger';
import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

@Entity({ name: 'golfnet_tee_ratings' })
export class GolfNetTeeRating {
  @PrimaryGeneratedColumn()
  @ApiProperty({ example: 1 })
  id: number;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  golfnet_course_id: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  tee_set_rating_name: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  golfnet_tee_rating_id: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  gender: string;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  holes_number: number;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  total_yard_age: number;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  total_par: number;

  @Column({ type: 'jsonb' })
  @ApiProperty({ example: 'string' })
  tee_rating_detail: string;

  @CreateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  created_at: Date;

  @UpdateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  updated_at: Date;
}
