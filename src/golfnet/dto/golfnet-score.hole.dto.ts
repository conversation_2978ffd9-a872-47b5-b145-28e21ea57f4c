import { IsNotEmpty, IsOptional } from 'class-validator';

export class GolfNetScoreHoleDto {
  @IsNotEmpty()
  number: number;

  @IsOptional()
  gross: number;

  @IsOptional()
  adjScore: number;

  @IsOptional()
  putts: number;

  @IsOptional()
  puttLength: number;

  @IsOptional()
  golfClub: string;

  @IsOptional()
  drive: number;

  @IsOptional()
  fir: string;

  @IsOptional()
  upDown: boolean;

  @IsOptional()
  sandSave: boolean;

  @IsOptional()
  penalty: number;

  @IsOptional()
  gir: boolean;
}
