import { IsOptional } from 'class-validator';
import { GolfNetScoreHoleDto } from './golfnet-score.hole.dto';

export class GolfNetScoreDto {
  @IsOptional()
  id: string;

  @IsOptional()
  networkId: string;

  @IsOptional()
  golfCanadaCardId: string;

  @IsOptional()
  originalScoreId: string;

  @IsOptional()
  date: string;

  @IsOptional()
  adjScore: number;

  @IsOptional()
  gross: number;

  @IsOptional()
  courseId: string;

  @IsOptional()
  teeId: string;

  @IsOptional()
  gender: string;

  @IsOptional()
  holesPlayed: string;

  @IsOptional()
  differential: string;

  @IsOptional()
  holeScores?: GolfNetScoreHoleDto[];

  @IsOptional()
  isHoleByHole: boolean;

  @IsOptional()
  isTrackingStats: boolean;

  @IsOptional()
  isTournament: boolean;

  @IsOptional()
  isPenalty: boolean;

  @IsOptional()
  isPlayedAlone: boolean;

  @IsOptional()
  isNGL: boolean;

  @IsOptional()
  scoreType: string;

  @IsOptional()
  isManualEntry: boolean;

  @IsOptional()
  courseName: string;

  @IsOptional()
  teeName: string;

  @IsOptional()
  rating: string;

  @IsOptional()
  slope: string;

  @IsOptional()
  gcGameScoreId: string;

  @IsOptional()
  formatPlayedType: string;

  @IsOptional()
  saveDefaultTee: boolean;
}
