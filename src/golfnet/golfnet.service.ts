import {
  BadRequestException,
  CACHE_MANAGER,
  Inject,
  Injectable,
  Logger,
  UnauthorizedException,
  forwardRef,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import axios from 'axios';
import { Cache } from 'cache-manager';
import { plainToClass } from 'class-transformer';
import * as geolib from 'geolib';
import { getDistance } from 'geolib';
import { isEmpty } from 'lodash';
import ms from 'ms';
import qs from 'qs';
import { Repository } from 'typeorm';
import { IGolfCourse } from 'src/ghin/ghin.types';
import { GolfNetQueryDto } from 'src/golfnet/dto/golf-net.query.dto';
import { User } from 'src/users/entities/user.entity';
import { isValidId, similarity } from 'src/utils/utils';
import { ThreePartyCourseService } from '../igolf/threePartyCourse.service';
import { CreateIGolfGolfNetCourseDto } from './dto/create-igolf-canada-cousre.dto';
import { SearchCanadaCourseByKeywordDto } from './dto/search-canada-course-by-keyword.dto';
import { SearchCanadaCourseDto } from './dto/search-canada-course.dto';
import { VerifyMemberDto as VerifyMemberGolfNetDto } from './dto/verify-member.dto';
import { GolfNetTeeRating } from './entities/golfnet-tee-rating.entity';
import { IGolfGolfNetCourse } from './entities/igolf-golfnet-course.entity';
import { GOLF_NET_APIS, GOLF_NET_CONST, GOLF_NET_ERROR_CODES } from './golfnet.consts';
import { GolfNetCourseMapped } from './golfnet.types';

@Injectable()
export class GolfNetService {
  private readonly logger = new Logger(GolfNetService.name);
  private golfNetEndpoint: string;
  private golfNetUserName: string;
  private golfNetPassword: string;
  constructor(
    private readonly config: ConfigService,
    @Inject(forwardRef(() => ThreePartyCourseService)) private threePartyCourseService: ThreePartyCourseService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    @InjectRepository(User)
    private userRepo: Repository<User>,
    @InjectRepository(GolfNetTeeRating)
    private golfNetTeeRatingRepo: Repository<GolfNetTeeRating>,
    @InjectRepository(IGolfGolfNetCourse)
    private iGolfGolfNetCourseRepo: Repository<IGolfGolfNetCourse>
  ) {
    this.golfNetEndpoint = config.get('golfnet.endpoint');
    this.golfNetUserName = config.get('golfnet.username');
    this.golfNetPassword = config.get('golfnet.password');
  }
  async handleRefreshToken() {
    const urlLogin = `${this.golfNetEndpoint}/${GOLF_NET_APIS.API_LOGIN}`;
    const payload = {
      grant_type: 'password',
      username: this.golfNetUserName,
      password: this.golfNetPassword,
    };
    try {
      const response = await axios.post(urlLogin, qs.stringify(payload));
      const token = response.data?.access_token;
      const cacheTTL: string = this.config.get('golfnet.cacheTTL');
      this.logger.debug(`GOLF NET TOKEN: ${token}`);
      const ttl = ms(cacheTTL) / 1000;
      await this.cacheManager.set(GOLF_NET_CONST.GOLF_NET_CACHE_TOKEN, token, { ttl });

      return token;
    } catch (error) {
      console.error(`ERROR HANDLE REFRESH TOKEN: ${error.message}`);
      console.log(error);
      return null;
    }
  }
  /**
   * getHeaderConfig
   * @returns token
   */
  async getHeaderConfig() {
    let token = await this.getToken();
    if (!token) {
      token = await this.handleRefreshToken();
    }
    return {
      headers: { Authorization: `Bearer ${token}` },
    };
  }

  async getToken() {
    return await this.cacheManager.get(GOLF_NET_CONST.GOLF_NET_CACHE_TOKEN);
  }
  async removeToken() {
    return await this.cacheManager.del(GOLF_NET_CONST.GOLF_NET_CACHE_TOKEN);
  }

  /**
   *
   * @param searchDto SearchCanadaCourseDto
   * @param isRetry
   * @returns
   */
  async searchGolfNetCourseByLocation(searchDto: SearchCanadaCourseDto, isRetry = true) {
    let uriSearch = `${this.golfNetEndpoint}/${GOLF_NET_APIS.API_SEARCH_COURSE_BY_LOCATION_AND_KEYWORD}`;
    const payload = new URLSearchParams(searchDto as any).toString();
    uriSearch = `${uriSearch}?${payload}`;
    this.logger.debug(`SEARCH CANADA COURSE BY LOCATION`);
    console.log({ uriSearch });
    try {
      const response = await axios.get(uriSearch, await this.getHeaderConfig());
      if (response.data && !isEmpty(response?.data?.data)) {
        return this.getDistanceMiles(response.data, searchDto.latitude, searchDto.longitude);
      }
      return { total: 0, data: [] };
    } catch (error) {
      if (error.response.status == 401 && isRetry) {
        await this.removeToken();
        return await this.searchGolfNetCourseByLocation(searchDto, false);
      }
      console.error(error);
      return { total: 0 };
    }
  }

  /**
   * searchCanadaCourseByKeyword
   *
   * @param searchDto
   * @param isRetry
   * @returns
   */
  async searchCanadaCourseByKeyword(searchDto: SearchCanadaCourseByKeywordDto, isRetry = true) {
    let uriSearch = `${this.golfNetEndpoint}/${GOLF_NET_APIS.API_SEARCH_COURSE_BY_KEYWORD}`;
    const payload = new URLSearchParams(searchDto as any).toString();
    uriSearch = `${uriSearch}?${payload}`;
    try {
      const response = await axios.get(uriSearch, await this.getHeaderConfig());
      return response.data;
    } catch (error) {
      if (error.response.status == 401 && isRetry) {
        await this.removeToken();
        return await this.searchCanadaCourseByKeyword(searchDto, false);
      }
      this.logger.error(error);
      return [];
    }
  }

  /**
   * getCourseDetail
   *
   * @param courseId
   * @param isRetry
   * @returns
   */
  async getCourseDetail(courseId: string, isRetry = true) {
    const uri = `${this.golfNetEndpoint}/${GOLF_NET_APIS.API_GET_COURSE_DETAIL}${courseId}`;
    try {
      const response = await axios.get(uri, await this.getHeaderConfig());
      return response.data;
    } catch (error) {
      if (error.response.status == 401 && isRetry) {
        await this.removeToken();
        return await this.getCourseDetail(courseId, false);
      }
      this.logger.error(error);
      return [];
    }
  }

  /**
   * getCourseByFacility
   *
   * @param courseId
   * @param isRetry
   * @returns
   */
  async getCourseByFacility(facilityId: string, isRetry = true) {
    const uri = `${this.golfNetEndpoint}/${GOLF_NET_APIS.API_GET_COURSE_DETAIL_BY_FACILITY}${facilityId}`;
    try {
      const response = await axios.get(uri, await this.getHeaderConfig());
      return response.data;
    } catch (error) {
      if (error.response.status == 401 && isRetry) {
        await this.removeToken();
        return await this.getCourseByFacility(facilityId, false);
      }
      this.logger.error(error);
      return [];
    }
  }

  /**
   * verifyMemberGolfNet
   *
   * @param verifyDto
   * @param isRetry
   * @returns
   */
  async verifyMemberGolfNet(verifyDto: VerifyMemberGolfNetDto, isRetry = true) {
    const uriVerify = `${this.golfNetEndpoint}/${GOLF_NET_APIS.API_GET_TMS_MEMBERS}`;
    try {
      const response = await axios.post(
        uriVerify,
        [{ golfCanadaCardId: verifyDto.canadaCardId }],
        await this.getHeaderConfig()
      );
      const { data } = response;
      if (data) {
        const golfer = data[0];
        return verifyDto.email?.trim()?.toLowerCase() == golfer.email?.trim()?.toLowerCase();
      }
      return false;
    } catch (error) {
      if (error.response.status == 401 && isRetry) {
        await this.removeToken();
        return await this.verifyMemberGolfNet(verifyDto, false);
      }
      this.logger.error(error);
      return false;
    }
  }

  /**
   * requestAccessWHS
   *
   * @param verifyDto
   * @param isRetry
   * @returns
   */
  async requestAccessWHS(userId: any, verifyDto: VerifyMemberGolfNetDto) {
    if (isValidId(userId)) {
      // check canada card id has map with user
      const user = await this.userRepo.findOne({
        where: {
          canada_card_id: verifyDto.canadaCardId,
        },
      });
      if (user) {
        if (user.id == userId) {
          return { success: true };
        }

        throw new BadRequestException({
          message: 'Golf Canada Card Id has already connected to other user!',
          errorCode: GOLF_NET_ERROR_CODES.ERROR_REQUEST_SYNC_WHS,
          canadaCardId: verifyDto.canadaCardId,
        });
      }
      const resultRequestGPA = await this.verifyMemberGolfNet(verifyDto);
      if (resultRequestGPA) {
        await this.userRepo.update(
          { id: userId },
          {
            canada_card_id: verifyDto.canadaCardId?.trim(),
          }
        );
        return { success: true };
      } else {
        throw new BadRequestException({
          message: 'Golf Canada Card Id not found!',
          errorCode: GOLF_NET_ERROR_CODES.ERROR_HANDICAP_ID_NOT_FOUND,
          canadaCardId: verifyDto.canadaCardId,
        });
      }
    }
    this.throwExceptionUserNotFound();
  }

  /**
   * revokeAccessToWHS
   *
   * @param userId
   * @returns
   */
  async revokeAccessToWHS(userId) {
    if (isValidId(userId)) {
      await this.getUser(userId);
      try {
        await this.userRepo.update({ id: userId }, { canada_card_id: null });
        return { success: true };
      } catch (error) {
        console.log(`ERROR REVOKE ACCESS TO WHS`);
        console.log(error);
        return { success: false };
      }
    }
    this.throwExceptionUserNotFound();
  }
  /**
   * getHandicapIndex
   * @param userId
   * @param isRetry
   * @returns
   */
  async getHandicapIndex(userId: number, isRetry = true) {
    const user = await this.getUser(userId);
    const uriHandicapIndex = `${this.golfNetEndpoint}/${GOLF_NET_APIS.API_GET_HI_BY_CARD_ID}${user.canada_card_id}`;

    try {
      const response = await axios.get(uriHandicapIndex, await this.getHeaderConfig());
      const handicapIndex = response.data?.data[0] || {};
      let uriClubDetail = '';

      const uriHandicapDetail = `${this.golfNetEndpoint}/${GOLF_NET_APIS.API_GET_HI_DETAIL}${user.canada_card_id}`;
      const datas = [axios.get(uriHandicapDetail, await this.getHeaderConfig())];
      if (handicapIndex && handicapIndex.clubId) {
        uriClubDetail = `${this.golfNetEndpoint}/${GOLF_NET_APIS.API_GET_CLUB_DETAIL}${handicapIndex.clubId}`;
        datas.push(axios.get(uriClubDetail, await this.getHeaderConfig()));
      }
      const [handicapDetailResponse, clubDetailResponse] = await Promise.all(datas);
      if (clubDetailResponse) {
        handicapIndex['club'] = clubDetailResponse?.data || null;
      }
      handicapIndex['hiDetail'] = handicapDetailResponse?.data;
      return handicapIndex;
    } catch (error) {
      if (error?.response?.status == 401 && isRetry) {
        await this.removeToken();
        return await this.getHandicapIndex(userId, false);
      }
      this.logger.error(error);
      return [];
    }
  }

  /**
   * getHandicapIndexByCardIdAndNetworkId
   *
   * @param cardId
   * @param networkId
   * @param isRetry
   * @returns
   */
  async getHandicapIndexByCardIdAndNetworkId(cardId: string, networkId: string, isRetry = true) {
    let uriHandicapIndex = `${this.golfNetEndpoint}/${GOLF_NET_APIS.API_GET_HI_BY_CARD_ID_AND_NETWORK_ID}${cardId}`;
    uriHandicapIndex = uriHandicapIndex.replace('NETWORK_ID', networkId);
    try {
      const response = await axios.get(uriHandicapIndex, await this.getHeaderConfig());
      return response.data;
    } catch (error) {
      if (error.response.status == 401 && isRetry) {
        await this.removeToken();
        return await this.getHandicapIndexByCardIdAndNetworkId(cardId, networkId, false);
      }
      this.logger.error(error);
      return [];
    }
  }

  /**
   * getScoreById
   *
   * @param scoreId
   * @param isRetry
   * @returns
   */
  async getScoreById(scoreId: string, isRetry = true) {
    const uriScore = `${this.golfNetEndpoint}/${GOLF_NET_APIS.API_GET_SCORE_BY_ID}${scoreId}`;
    try {
      const response = await axios.get(uriScore, await this.getHeaderConfig());
      return response.data;
    } catch (error) {
      if (error.response.status == 401 && isRetry) {
        await this.removeToken();
        return await this.getScoreById(scoreId, false);
      }
      this.logger.error(error);
      return [];
    }
  }

  /**
   * postScore
   * @param payload
   * @param isRetry
   * @returns
   */
  async postScore(payload: any, isRetry = true) {
    const uriPostScore = `${this.golfNetEndpoint}/${GOLF_NET_APIS.API_POST_SCORE}`;
    try {
      const response = await axios.post(uriPostScore, payload, await this.getHeaderConfig());
      return response.data;
    } catch (error) {
      if (error.response.status == 401 && isRetry) {
        await this.removeToken();
        return await this.postScore(payload, false);
      }
      this.logger.error(error);
      let message = error?.response?.data?.message;
      try {
        message = JSON.stringify(error?.response?.data);
      } catch (error) {}
      const errorCode = message.includes('duplicate score')
        ? GOLF_NET_ERROR_CODES.ERROR_POST_DUPLICATE_SCORE
        : GOLF_NET_ERROR_CODES.ERROR_POST_SCORE;
      throw new BadRequestException({
        message,
        errorCode,
      });
    }
  }

  /**
   * updateScore
   * @param payload
   * @param isRetry
   * @returns
   */
  async updateScore(payload: any, isRetry = true) {
    const uriUpdateScore = `${this.golfNetEndpoint}/${GOLF_NET_APIS.API_UPDATE_SCORE}`;
    try {
      const response = await axios.post(uriUpdateScore, payload, await this.getHeaderConfig());
      return response.data;
    } catch (error) {
      if (error.response.status == 401 && isRetry) {
        await this.removeToken();
        return await this.updateScore(payload, false);
      }
      this.logger.error(error);
      return [];
    }
  }

  /**
   * getUser
   *
   * @param userId
   * @returns
   */
  private async getUser(userId: any) {
    const user = await this.userRepo.findOne({
      where: { id: userId },
      select: ['id', 'canada_card_id'],
    });
    if (!user) {
      this.throwExceptionUserNotFound();
    }
    if (!user.canada_card_id) {
      throw new UnauthorizedException({
        message: `User don't have golf Canada Card Id`,
        errorCode: GOLF_NET_ERROR_CODES.ERROR_USER_MISSED_CANADA_CARD_ID,
      });
    }
    return user;
  }
  private throwExceptionUserNotFound() {
    throw new BadRequestException('User not found');
  }

  /**
   * mappingIGolfGolfNetCourse
   * @param iGolfCourse
   * @returns
   */
  async mappingIGolfGolfNetCourse(iGolfCourse: IGolfCourse): Promise<GolfNetCourseMapped> {
    this.logger.debug(`MAPPING IGOLF GOLF NET COURSE`);
    const iGolfGolfNetCourse = await this.iGolfGolfNetCourseRepo.findOneBy({
      igolf_course_id: iGolfCourse.igolf_course_id?.trim(),
    });
    if (iGolfGolfNetCourse) {
      const lstTeeSet = await this.golfNetTeeRatingRepo.findBy({
        golfnet_course_id: iGolfGolfNetCourse.golfnet_course_id,
      });
      if (!lstTeeSet || lstTeeSet.length == 0) {
        throw new BadRequestException({
          message: 'Tee set rating for course not found!',
          errorCode: GOLF_NET_ERROR_CODES.ERROR_TEE_NOT_FOUND,
          golfNetCourseId: null,
        });
      }
      const teeSetRating = lstTeeSet.find((tee) => {
        const teeGender = tee.gender.toLowerCase().trim() == 'mens' ? 'male' : 'female';
        const iGolfGender = ['men', `men's`, 'male'].includes(iGolfCourse.gender.toLowerCase().trim())
          ? 'male'
          : 'female';
        return tee.tee_set_rating_name?.toLowerCase() == iGolfCourse.tee?.toLowerCase() && teeGender == iGolfGender;
      });
      if (!teeSetRating) {
        throw new BadRequestException({
          message: 'Tee set rating for course not found!',
          errorCode: GOLF_NET_ERROR_CODES.ERROR_TEE_NOT_FOUND,
          golfNetCourseId: iGolfGolfNetCourse.golfnet_course_id,
        });
      }
      return { igolf_golfnet: iGolfGolfNetCourse, tee_set_rating: teeSetRating };
    }
    // eslint-disable-next-line @typescript-eslint/no-var-requires
    const FlexSearch = require('flexsearch');
    const searchIndex = new FlexSearch.Index('score');
    const iGolfCourseDetail = await this.threePartyCourseService.getCourseDetail(iGolfCourse.igolf_course_id);
    const searchDto: SearchCanadaCourseDto = {
      latitude: iGolfCourseDetail.latitude,
      longitude: iGolfCourseDetail.longitude,
      proximity: 10,
      skip: 0,
      take: 10,
    };
    const golfNetCoursesResult = await this.searchGolfNetCourseByLocation(searchDto);
    if (golfNetCoursesResult.total == 0) {
      throw new BadRequestException({
        message: 'GolfNet course not found!',
        errorCode: GOLF_NET_ERROR_CODES.ERROR_COURSE_NOT_FOUND,
        golfNetCourseId: null,
      });
    }
    console.log({ iGolfCourseDetail });

    const golfNetCourses = golfNetCoursesResult.data;
    golfNetCourses.map((gnCourse) => {
      const start = { longitude: iGolfCourseDetail.longitude, latitude: iGolfCourseDetail.latitude };
      const end = { longitude: gnCourse.longitude, latitude: gnCourse.latitude };
      gnCourse.distance = geolib.getDistance(start, end) / 1000;
      return gnCourse;
    });
    let idxBets = 0;
    for (const idx in golfNetCourses) {
      searchIndex.add(idx, golfNetCourses[idx].name.toLowerCase());
    }
    let resultIds = await searchIndex.search({
      query: iGolfCourseDetail.courseName?.trim().toLowerCase(),
      suggest: false,
    });

    if (resultIds.length > 0) {
      idxBets = resultIds[0];
    } else {
      resultIds = await searchIndex.search({
        query: iGolfCourseDetail.courseName?.trim().toLowerCase(),
        suggest: true,
      });
      idxBets = resultIds[0] ?? 0;
    }
    const courseGolfNetBase = golfNetCourses[idxBets];
    const percentMap = similarity(courseGolfNetBase.name?.toLowerCase(), iGolfCourse.course_name?.toLowerCase());
    if (courseGolfNetBase.distance > 3 && percentMap < 0.8) {
      // throw new BadRequestException({
      //   message: 'GolfNet course not found !',
      //   errorCode: GOLF_NET_ERROR_CODES.ERROR_COURSE_NOT_FOUND,
      //   golfNetCourseId: null,
      // });
    }

    const courseGolfNetDetail = await this.getCourseDetail(courseGolfNetBase.id);
    const golfNetCourseTees = courseGolfNetDetail.tees; //.filter((t) => t.isActive == true);
    let teeMap = null;
    if (golfNetCourseTees.length > 0) {
      teeMap = golfNetCourseTees.find((tee) => {
        const teeGender = tee.type.toLowerCase().trim() == 'mens' ? 'male' : 'female';
        const iGolfGender = ['men', `men's`, 'male'].includes(iGolfCourse.gender.toLowerCase().trim())
          ? 'male'
          : 'female';
        return tee.name.toLowerCase().trim().includes(iGolfCourse.tee.toLowerCase().trim()) && teeGender == iGolfGender;
      });
    }
    if (!teeMap) {
      throw new BadRequestException({
        message: 'Tee set rating for course not found!',
        golfNetCourseId: courseGolfNetDetail.CourseId,
        errorCode: GOLF_NET_ERROR_CODES.ERROR_TEE_NOT_FOUND,
      });
    }
    // if (courseGolfNetBase.DistanceMiles >= 4) {
    //   throw new BadRequestException({
    //     message: 'GolfNet course not found !',
    //     errorCode: GOLF_NET_ERROR_CODES.ERROR_COURSE_NOT_FOUND,
    //     golfNetCourseId: null,
    //   });
    // }
    const golfNetCourseDto: CreateIGolfGolfNetCourseDto = {
      igolf_course_id: iGolfCourse.igolf_course_id,
      golfnet_course_id: courseGolfNetBase.id,
      golfnet_course_name: courseGolfNetDetail.name,
    };
    const igolfGolfNet = await this.create(golfNetCourseDto);
    let teeSetRatingMapping = null;
    await Promise.all(
      golfNetCourseTees.map(async (teeRatting) => {
        teeRatting['golfnet_course_id'] = courseGolfNetBase.id;
        teeRatting['tee_set_rating_name'] = teeRatting.name;
        teeRatting['golfnet_tee_rating_id'] = teeRatting.id;
        teeRatting['gender'] = teeRatting.type;
        teeRatting['holes_number'] = teeRatting.holes?.length;
        teeRatting['total_yard_age'] = teeRatting?.yardage;
        teeRatting['total_par'] = teeRatting?.par;
        teeRatting['tee_rating_detail'] = JSON.stringify(teeRatting);
        const teeRattingDto = plainToClass(GolfNetTeeRating, teeRatting);
        const result = await this.golfNetTeeRatingRepo.save(this.golfNetTeeRatingRepo.create(teeRattingDto));
        if (teeRatting.id == teeMap.golfnet_tee_rating_id) {
          teeSetRatingMapping = result;
        }
      })
    );

    return { igolf_golfnet: igolfGolfNet, tee_set_rating: teeSetRatingMapping };
  }

  create(createDto: CreateIGolfGolfNetCourseDto) {
    return this.iGolfGolfNetCourseRepo.save(this.iGolfGolfNetCourseRepo.create(createDto));
  }
  /**
   * getScoreHistory
   *
   * @param userId
   * @param query GolfNetQueryDto
   */
  async getScoreHistory(userId: number, query: GolfNetQueryDto, isRetry = true) {
    const user = await this.getUser(userId);
    query.cardId = user.canada_card_id;
    const params = new URLSearchParams(query as any);
    const uriScoreHistory = `${this.golfNetEndpoint}/${GOLF_NET_APIS.API_SCORE_HISTORY}?${params}`;
    this.logger.debug(`uriScoreHistory: ${uriScoreHistory}`);
    try {
      const response = await axios.get(uriScoreHistory, await this.getHeaderConfig());
      return response.data;
    } catch (error) {
      if (error.response.status == 401 && isRetry) {
        await this.removeToken();
        return await this.getScoreHistory(userId, query, false);
      }
      this.logger.error(error);
      return [];
    }
  }

  getDistanceMiles(datas, lat, long) {
    let { data } = datas;
    const from = {
      latitude: lat,
      longitude: long,
    };
    data = data.map((value) => {
      if (!value.distanceMiles && value.latitude && value.longitude) {
        const to = {
          latitude: value.latitude,
          longitude: value.longitude,
        };
        value.distanceMiles = getDistance(from, to) * 0.000621371192;
        return value;
      }
    });
    return { data, total: datas?.total };
  }
}
