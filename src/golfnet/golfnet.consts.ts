export const GOLF_NET_CONST = {
  GOLF_NET_CACHE_TOKEN: 'GOLF_NET_CACHE_TOKEN',
};
export const GOLF_NET_APIS = {
  // Authentication
  API_LOGIN: 'connect/token',

  // Courses
  API_GET_COURSE_DETAIL_BY_FACILITY: 'course/getFacilityDetail?facilityId=',
  API_GET_COURSE_DETAIL: 'course/get?courseId=',
  API_SEARCH_COURSE_BY_LOCATION_AND_KEYWORD: 'course/getByKeywordAndLocation', //skip=0&take=20&latitude=29.9721273&longitude=-95.5328782&proximity=25
  API_SEARCH_COURSE_BY_KEYWORD: 'course/getByKeywords', //skip=0&take=25&keywords=Carl&includeNonAssociation=true

  // Scores
  API_GET_SCORE_BY_ID: 'score/get?scoreId=',
  API_POST_SCORE: 'score/create',
  API_UPDATE_SCORE: 'score/update',
  API_SCORE_HISTORY: 'score/getHistory',

  // HandicapIndex
  API_GET_HI_BY_CARD_ID: 'handicap/getByCardId?cardId=',
  API_GET_HI_BY_CARD_ID_AND_NETWORK_ID: 'handicap/getDetail?networkId=NETWORK_ID&golfCanadaCardId=',
  API_GET_HI_DETAIL: 'handicap/getDetail?golfCanadaCardId=',

  // Members
  API_GET_TMS_MEMBERS: 'member/getTMSMembers',

  // Club
  API_GET_CLUB_DETAIL: 'club/get?clubId=',
};
export const GOLF_NET_ERROR_CODES = {
  ERROR_UNAUTHORIZED: 'UNAUTHORIZED',
  ERROR_GET_SCORE: 'ERROR_GET_SCORE',
  ERROR_REQUEST_SYNC_WHS: 'ERROR_REQUEST_SYNC_WHS',
  ERROR_HANDICAP_ID_NOT_FOUND: 'ERROR_HANDICAP_ID_NOT_FOUND',
  ERROR_POST_SCORE: 'ERROR_POST_SCORE',
  ERROR_POST_DUPLICATE_SCORE: 'ERROR_POST_DUPLICATE_SCORE',
  ERROR_UPDATE_SCORE: 'ERROR_UPDATE_SCORE',
  ERROR_GET_HANDICAP_INDEX: 'ERROR_GET_HANDICAP_INDEX',
  ERROR_USER_MISSED_CANADA_CARD_ID: 'ERROR_USER_MISSED_CANADA_CARD_ID',
  ERROR_SEARCH_COURSE: 'ERROR_SEARCH_COURSE',
  ERROR_COURSE_NOT_FOUND: 'ERROR_COURSE_NOT_FOUND',
  ERROR_TEE_NOT_FOUND: 'ERROR_TEE_NOT_FOUND',
  ERROR_INVALID_DATA: 'ERROR_INVALID_DATA',
  ERROR_MAP_TEE_SET_RATINGS: 'ERROR_MAP_TEE_SET_RATINGS',
};
