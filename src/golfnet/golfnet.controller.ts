import { Body, Controller, Get, HttpCode, HttpStatus, Param, Post, Query } from '@nestjs/common';
import { ApiBody } from '@nestjs/swagger';
import { MappingCourseDto } from 'src/ghin/dto/mapping-course.dto';
import { SearchCanadaCourseByKeywordDto } from './dto/search-canada-course-by-keyword.dto';
import { SearchCanadaCourseDto } from './dto/search-canada-course.dto';
import { GolfNetService } from './golfnet.service';

@Controller('golf-net')
export class GolfNetController {
  constructor(private readonly golfNetService: GolfNetService) {}

  @Get('get-token')
  getToken() {
    return this.golfNetService.handleRefreshToken();
  }

  @Get('search')
  @HttpCode(HttpStatus.OK)
  searchByName(
    @Query('name') course_name: string,
    @Query('latitude') latitude: number,
    @Query('longitude') longitude: number
  ) {
    if (latitude && longitude) {
      const searchDto: SearchCanadaCourseDto = {
        latitude,
        longitude,
        skip: 0,
        take: 20,
        proximity: 25,
      };
      return this.golfNetService.searchGolfNetCourseByLocation(searchDto);
    }
    const searchKeyWordDto: SearchCanadaCourseByKeywordDto = {
      keywords: course_name,
      skip: 0,
      take: 20,
      includeNonAssociation: false,
    };
    return this.golfNetService.searchCanadaCourseByKeyword(searchKeyWordDto);
  }

  @Get('course_detail/:id')
  @HttpCode(HttpStatus.OK)
  getCourseDetail(@Param('id') id: string) {
    return this.golfNetService.getCourseDetail(id);
  }

  @Post('mapping')
  @HttpCode(HttpStatus.OK)
  @ApiBody({ type: MappingCourseDto })
  mappingCourse(@Body() mappingCourseDto: MappingCourseDto) {
    return this.golfNetService.mappingIGolfGolfNetCourse(mappingCourseDto);
  }
}
