import { Controller, Get, HttpCode, HttpStatus } from '@nestjs/common';
import { BullDashboardService } from './bull-dashboard.service';

@Controller('bull-dashboard')
export class BullDashboardController {
  constructor(private readonly bullBoardService: BullDashboardService) {}

  @Get('check-jobs-oc')
  @HttpCode(HttpStatus.OK)
  async checkJobFailed() {
    return this.bullBoardService.checkJobFailed();
  }
}
