import { createBullBoard } from '@bull-board/api';
import { BullAdapter } from '@bull-board/api/bullAdapter';
import { ExpressAdapter } from '@bull-board/express';
import Queue from 'bull';
import { ensureLoggedIn } from 'connect-ensure-login';
import cookieSession from 'cookie-session';
import ms from 'ms';
import passport from 'passport';
import { Strategy as LocalStrategy } from 'passport-local';
import { redisConfig } from 'src/utils/redis';
import { CDM_CONSUMER_SYNCING_QUEUE_NAME } from './cdm/cdm.constant';
import { PROCESSORS } from './jobs/job.constant';
import { KLAVIYO_TRACK_QUEUE_NAME } from './klaviyo/klaviyo.constant';
import { MAIL_QUEUE_NAME } from './mail/mail.constant';

passport.use(
  new LocalStrategy(function (username: string, password: string, cb: any) {
    if (username === process.env.BULL_BOARD_EMAIL && password === process.env.BULL_BOARD_PASSWORD) {
      return cb(null, { user: 'bull-board-admin' });
    }
    return cb(null, false);
  })
);
passport.serializeUser((user, cb) => {
  cb(null, user);
});
passport.deserializeUser((user, cb) => {
  cb(null, user);
});

export default function (app) {
  const serverAdapter = new ExpressAdapter();
  app.use(
    cookieSession({
      name: 'session',
      keys: [process.env.SESSION_SECRET],
      maxAge: ms('30 days'),
    })
  );
  app.use(passport.initialize());
  app.use(passport.session());
  createBullBoard({
    queues: [
      new BullAdapter(new Queue(PROCESSORS.RoundAuditImportJob, { redis: redisConfig })),
      new BullAdapter(new Queue(PROCESSORS.IGolfRoundCompleteJob, { redis: redisConfig })),
      new BullAdapter(new Queue(PROCESSORS.ForceRoundCompleteJob, { redis: redisConfig })),
      new BullAdapter(new Queue(PROCESSORS.IGolfRoundDrivingDispersionJob, { redis: redisConfig })),
      new BullAdapter(new Queue(PROCESSORS.LogRequestIGolfSI, { redis: redisConfig })),
      new BullAdapter(new Queue(PROCESSORS.CalculateAverageScoreJob, { redis: redisConfig })),
      new BullAdapter(new Queue(PROCESSORS.CalculateAverageScoreClassicJob, { redis: redisConfig })),
      new BullAdapter(new Queue(PROCESSORS.CalcHandicapJob, { redis: redisConfig })),
      new BullAdapter(new Queue(PROCESSORS.SyncUpdateMrpIdJob, { redis: redisConfig })),
      new BullAdapter(new Queue(PROCESSORS.SyncUpdateGhinNumberJob, { redis: redisConfig })),
      new BullAdapter(new Queue(PROCESSORS.MyTmEventsJob, { redis: redisConfig })),
      new BullAdapter(new Queue(PROCESSORS.SyncUpdateClubsFromCdmJob, { redis: redisConfig })),
      new BullAdapter(new Queue(PROCESSORS.SyncUpdateUserFromCdmJob, { redis: redisConfig })),
      new BullAdapter(new Queue(PROCESSORS.SyncClubsToCDM, { redis: redisConfig })),
      new BullAdapter(new Queue(PROCESSORS.ClearShotNotInHole, { redis: redisConfig })),
      new BullAdapter(new Queue(PROCESSORS.SimpleScoreToParJob, { redis: redisConfig })),
      new BullAdapter(new Queue(PROCESSORS.PostScoreToGHIN, { redis: redisConfig })),
      new BullAdapter(new Queue(PROCESSORS.SyncGhinHandicapIndexJob, { redis: redisConfig })),
      new BullAdapter(new Queue(PROCESSORS.RoundUpdateJob, { redis: redisConfig })),
      new BullAdapter(new Queue(MAIL_QUEUE_NAME, { redis: redisConfig })),
      new BullAdapter(new Queue(KLAVIYO_TRACK_QUEUE_NAME, { redis: redisConfig })),
      new BullAdapter(new Queue(CDM_CONSUMER_SYNCING_QUEUE_NAME, { redis: redisConfig })),
      new BullAdapter(new Queue(PROCESSORS.RemoveUserLogCourse, { redis: redisConfig })),
    ],
    serverAdapter,
    options: {
      uiConfig: {
        boardTitle: 'OC Queues',
      },
    },
  });

  app.use('/admin/login', (req: any, res: any, next: any) => {
    if (req.method === 'POST') {
      return passport.authenticate('local', function (err, user) {
        if (err) {
          return next(err);
        }
        if (!user) {
          return res.redirect('/admin/login');
        }
        req.logIn(user, function (err) {
          if (err) {
            return next(err);
          }
          setTimeout(() => {
            res.redirect('/admin/queues');
          }, 500);
        });
      })(req, res, next);
    }
    res.sendFile(process.cwd() + '/src/views/login.html');
  });
  serverAdapter.setBasePath('/admin/queues');
  app.use('/admin/queues', ensureLoggedIn({ redirectTo: '/admin/login' }), serverAdapter.getRouter());
}
