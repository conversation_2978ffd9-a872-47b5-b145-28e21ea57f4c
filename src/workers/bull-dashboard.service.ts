import { InjectQueue } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import { Queue } from 'bull';
import { MytmService } from '../mytm/mytm.service';
import { CDM_CONSUMER_SYNCING_QUEUE_NAME } from './cdm/cdm.constant';
import { PROCESSORS } from './jobs/job.constant';
import { KLAVIYO_TRACK_QUEUE_NAME } from './klaviyo/klaviyo.constant';
import { MAIL_QUEUE_NAME } from './mail/mail.constant';

@Injectable()
export class BullDashboardService {
  private readonly logger = new Logger(BullDashboardService.name);

  constructor(
    private mytmService: MytmService,
    @InjectQueue(PROCESSORS.RoundAuditImportJob) private roundAuditImportJobQueue: Queue,
    @InjectQueue(PROCESSORS.IGolfRoundCompleteJob) private iGolfRoundCompleteJobQueue: Queue,
    @InjectQueue(PROCESSORS.ForceRoundCompleteJob) private forceRoundCompleteJobQueue: Queue,
    @InjectQueue(PROCESSORS.IGolfRoundDrivingDispersionJob) private iGolfRoundDrivingDispersionJobQueue: Queue,
    @InjectQueue(PROCESSORS.LogRequestIGolfSI) private logRequestIGolfSIQueue: Queue,
    @InjectQueue(PROCESSORS.CalculateAverageScoreJob) private calculateAverageScoreJobQueue: Queue,
    @InjectQueue(PROCESSORS.CalculateAverageScoreClassicJob) private calculateAverageScoreClassicJobQueue: Queue,
    @InjectQueue(PROCESSORS.CalcHandicapJob) private calcHandicapJobQueue: Queue,
    @InjectQueue(PROCESSORS.SyncUpdateMrpIdJob) private syncUpdateMrpIdJobQueue: Queue,
    @InjectQueue(PROCESSORS.SyncUpdateGhinNumberJob) private syncUpdateGhinNumberJobQueue: Queue,
    @InjectQueue(PROCESSORS.MyTmEventsJob) private myTmEventsJobQueue: Queue,
    @InjectQueue(PROCESSORS.SyncUpdateClubsFromCdmJob) private syncUpdateClubsFromCdmJobQueue: Queue,
    @InjectQueue(PROCESSORS.SyncUpdateUserFromCdmJob) private syncUpdateUserFromCdmJobQueue: Queue,
    @InjectQueue(PROCESSORS.SyncClubsToCDM) private syncClubsToCDMQueue: Queue,
    @InjectQueue(PROCESSORS.ClearShotNotInHole) private clearShotNotInHoleQueue: Queue,
    @InjectQueue(PROCESSORS.SimpleScoreToParJob) private simpleScoreToParJobQueue: Queue,
    @InjectQueue(PROCESSORS.PostScoreToGHIN) private postScoreToGHINQueue: Queue,
    @InjectQueue(PROCESSORS.SyncGhinHandicapIndexJob) private syncGhinHandicapIndexJobQueue: Queue,
    @InjectQueue(PROCESSORS.RoundUpdateJob) private roundUpdateJobQueue: Queue,
    @InjectQueue(MAIL_QUEUE_NAME) private mailQueue: Queue,
    @InjectQueue(KLAVIYO_TRACK_QUEUE_NAME) private klaviyoTrackQueue: Queue,
    @InjectQueue(CDM_CONSUMER_SYNCING_QUEUE_NAME) private cdmConsumerSyncingQueue: Queue,
    @InjectQueue(PROCESSORS.RemoveUserLogCourse) private removeUserLogCourseQueue: Queue
  ) {}

  @Cron(CronExpression.EVERY_DAY_AT_1AM)
  async checkJobFailed() {
    try {
      const queues = [
        { name: PROCESSORS.RoundAuditImportJob, queue: this.roundAuditImportJobQueue },
        { name: PROCESSORS.IGolfRoundCompleteJob, queue: this.iGolfRoundCompleteJobQueue },
        { name: PROCESSORS.ForceRoundCompleteJob, queue: this.forceRoundCompleteJobQueue },
        { name: PROCESSORS.IGolfRoundDrivingDispersionJob, queue: this.iGolfRoundDrivingDispersionJobQueue },
        { name: PROCESSORS.LogRequestIGolfSI, queue: this.logRequestIGolfSIQueue },
        { name: PROCESSORS.CalculateAverageScoreJob, queue: this.calculateAverageScoreJobQueue },
        { name: PROCESSORS.CalculateAverageScoreClassicJob, queue: this.calculateAverageScoreClassicJobQueue },
        { name: PROCESSORS.CalcHandicapJob, queue: this.calcHandicapJobQueue },
        { name: PROCESSORS.SyncUpdateMrpIdJob, queue: this.syncUpdateMrpIdJobQueue },
        { name: PROCESSORS.SyncUpdateGhinNumberJob, queue: this.syncUpdateGhinNumberJobQueue },
        { name: PROCESSORS.MyTmEventsJob, queue: this.myTmEventsJobQueue },
        { name: PROCESSORS.SyncUpdateClubsFromCdmJob, queue: this.syncUpdateClubsFromCdmJobQueue },
        { name: PROCESSORS.SyncUpdateUserFromCdmJob, queue: this.syncUpdateUserFromCdmJobQueue },
        { name: PROCESSORS.SyncClubsToCDM, queue: this.syncClubsToCDMQueue },
        { name: PROCESSORS.ClearShotNotInHole, queue: this.clearShotNotInHoleQueue },
        { name: PROCESSORS.SimpleScoreToParJob, queue: this.simpleScoreToParJobQueue },
        { name: PROCESSORS.PostScoreToGHIN, queue: this.postScoreToGHINQueue },
        { name: PROCESSORS.SyncGhinHandicapIndexJob, queue: this.syncGhinHandicapIndexJobQueue },
        { name: PROCESSORS.RoundUpdateJob, queue: this.roundUpdateJobQueue },
        { name: MAIL_QUEUE_NAME, queue: this.mailQueue },
        { name: KLAVIYO_TRACK_QUEUE_NAME, queue: this.klaviyoTrackQueue },
        { name: CDM_CONSUMER_SYNCING_QUEUE_NAME, queue: this.cdmConsumerSyncingQueue },
        { name: PROCESSORS.RemoveUserLogCourse, queue: this.removeUserLogCourseQueue },
      ];

      const failedJobsCount = {};

      for (const { name, queue } of queues) {
        const failedJobs = await queue.getFailed();
        failedJobsCount[name] = failedJobs.length;
      }

      await this.mytmService.triggerSendEmailCheckJobsDaily(failedJobsCount, true);

      return failedJobsCount;
    } catch (err) {
      this.logger.error('Error checking failed jobs', err);
      throw err;
    }
  }
}
