import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Job } from 'bull';
import { CdmService } from 'src/cdm/cdm.service';
import { ClubsService } from 'src/clubs/clubs.service';
import { UsersService } from 'src/users/users.service';
import { CDM_CONSUMER_SYNCING_PROCESS_NAME, CDM_CONSUMER_SYNCING_QUEUE_NAME } from './cdm.constant';

@Processor(CDM_CONSUMER_SYNCING_QUEUE_NAME)
export class SyncConsumerProcessor {
  private readonly logger = new Logger(SyncConsumerProcessor.name);

  constructor(
    private readonly config: ConfigService,
    private usersService: UsersService,
    private cdmService: CdmService,
    private clubsService: ClubsService
  ) {}

  @Process(CDM_CONSUMER_SYNCING_PROCESS_NAME)
  async sync(job: Job): Promise<any> {
    const { userId } = job.data;
    this.logger.log(`Syncing user ${userId} to CDM...`);
    const user = await this.usersService.findOne({ id: userId });
    const consumer = await this.cdmService.updateConsumer(user);
    if (!consumer) {
      throw new Error('Update consumer failed!');
    }
    await this.usersService.updateUserCdmConsumerId(user.id, consumer.id);
    user.cdm_id = consumer.id;
    const region = await this.cdmService.getRegionFromUserCountry(user.country);
    const consumerOptInParams = {
      regionId: region?.id,
      consumerId: consumer.id,
      key: 'NEWS_TAYLORMADE',
      value: user.tmag_email_opt_in,
    };
    await this.cdmService.addOrUpdateAccountOptIn(consumerOptInParams);
    const clubs = await this.clubsService.getAllUserClubs(user.id);
    if (clubs.length > 0) {
      for (const club of clubs) {
        if (!club.cdm_witb_id) {
          const payload = await this.clubsService.transformClubToWITBParams(club, user);
          const witb = await this.cdmService.createWITB(payload);
          if (witb?.id) {
            await this.clubsService.updateClubWithCdmWITBId(club.id, witb?.id);
          }
        }
      }
    }
    this.logger.debug(`SYNC USER TO CDM ${userId} DONE...`);
  }
}
