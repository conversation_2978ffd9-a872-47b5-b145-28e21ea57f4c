import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import { Job } from 'bull';
import Klaviyo from 'node-klaviyo';
import { KLAVIYO_TRACK_PROCESS_NAME, KLAVIYO_TRACK_QUEUE_NAME } from './klaviyo.constant';

@Processor(KLAVIYO_TRACK_QUEUE_NAME)
export class KlaviyoTrackProcessor {
  private readonly logger = new Logger(KlaviyoTrackProcessor.name);
  constructor(private readonly config: ConfigService) {}

  @Process(KLAVIYO_TRACK_PROCESS_NAME)
  async track(job: Job): Promise<any> {
    if (job.data.withMyTM) {
      const res = await axios.post(`${this.config.get('mytm.endpoint')}${job.data.url}`, job.data.body, {
        headers: { clientId: this.config.get('mytm.clientId') },
      });
      if (res.status === 200) {
        this.logger.log('Klavi<PERSON> tracked with MyTM!');
      }
      return;
    }
    const KlaviyoClient = new Klaviyo({
      publicToken: this.config.get('klaviyo.publicToken'),
      privateToken: this.config.get('klaviyo.privateToken'),
    });
    return KlaviyoClient.public.track(job.data);
  }
}
