import { IGolfRoundCompleteJobProcessor } from 'src/workers/jobs/igolf-round-completed-job.processor';
import { SyncConsumerProcessor } from './cdm/sync-consumer.processor';
import { CalculateAverageScoreClassicJobProcessor } from './jobs/calculate-average-score-classic-job.processor';
import { CalculateAverageScoreJobProcessor } from './jobs/calculate-average-score-job.processor';
import { CalculateHandicapJobProcessor } from './jobs/calculate-handicap-job.processor';
import { ClearShotNotInHoleJobProcessor } from './jobs/clear-shot-not-in-hole-job.processor';
import { ForceRoundCompleteJobProcessor } from './jobs/force-round-completed-job.processor';
import { IGolfRoundDrivingDispersionJobProcessor } from './jobs/igolf-round-driving-dispersion-job.processor';
import { LogRequestIGolfSIJobProcessor } from './jobs/log-request-igolf-si-job.processor';
import { MyTMEventsJobProcessor } from './jobs/mytm-events-job.processor';
import { PostScoreToGHINJobProcessor } from './jobs/post-score-to-ghin-job.processor';
import { RemoveUserLogCourseDailyProcessor } from './jobs/remove-user-log-course-daily.processor';
import { RoundAuditImportJobProcessor } from './jobs/round-audit-import-job.processor';
import { RoundUpdateJobProcessor } from './jobs/round-update-job.processor';
import { SimpleScoreToParJobProcessor } from './jobs/simple-score-to-par-job.processor';
import { SyncClubsToCDMProcessor } from './jobs/sync-clubs-to-cdm.processor';
import { SyncGHINHandicapJobProcessor } from './jobs/sync-ghin-handicap-job.processor';
import { SyncUpdateClubFromCDMProcessor } from './jobs/sync-update-club-from-cdm.processor';
import { SyncUpdateGhinNumberProcessor } from './jobs/sync-update-ghin-number.processor';
import { SyncUpdateMrpIdProcessor } from './jobs/sync-update-mrp-id.processor';
import { SyncUpdateUserFromCDMProcessor } from './jobs/sync-update-user-from-cdm.processor';
import { KlaviyoTrackProcessor } from './klaviyo/track.processor';
import { MailProcessor } from './mail/mail.processor';

const processors =
  process.env.APP_TAG === 'JOBS'
    ? [
        MailProcessor,
        KlaviyoTrackProcessor,
        SyncConsumerProcessor,
        IGolfRoundCompleteJobProcessor,
        ForceRoundCompleteJobProcessor,
        IGolfRoundDrivingDispersionJobProcessor,
        RoundAuditImportJobProcessor,
        CalculateAverageScoreJobProcessor,
        CalculateAverageScoreClassicJobProcessor,
        CalculateHandicapJobProcessor,
        SimpleScoreToParJobProcessor,
        SyncUpdateMrpIdProcessor,
        SyncUpdateClubFromCDMProcessor,
        SyncUpdateUserFromCDMProcessor,
        SyncClubsToCDMProcessor,
        MyTMEventsJobProcessor,
        ClearShotNotInHoleJobProcessor,
        PostScoreToGHINJobProcessor,
        SyncUpdateGhinNumberProcessor,
        SyncGHINHandicapJobProcessor,
        RoundUpdateJobProcessor,
        LogRequestIGolfSIJobProcessor,
        RemoveUserLogCourseDailyProcessor,
      ]
    : [];

export default processors;
