import { BullModule } from '@nestjs/bull';
import { Module } from '@nestjs/common';
import { PROCESSORS } from 'src/workers/jobs/job.constant';
import { CDM_CONSUMER_SYNCING_QUEUE_NAME } from './cdm/cdm.constant';
import { KLAVIYO_TRACK_QUEUE_NAME } from './klaviyo/klaviyo.constant';
import { MAIL_QUEUE_NAME } from './mail/mail.constant';

const processors = Object.values(PROCESSORS);

const defaultJobOptions = {
  removeOnComplete: true,
  removeOnFail: false,
  attempts: 10,
  backoff: 300000,
};

const queues = [
  BullModule.registerQueue({
    name: MAIL_QUEUE_NAME,
    defaultJobOptions,
  }),
  BullModule.registerQueue({
    name: KLAVIYO_TRACK_QUEUE_NAME,
    defaultJobOptions,
  }),
  BullModule.registerQueue({
    name: CDM_CONSUMER_SYNCING_QUEUE_NAME,
    defaultJobOptions,
  }),
];
processors.forEach((processor) => {
  queues.push(
    BullModule.registerQueue({
      name: processor,
      defaultJobOptions: {
        removeOnComplete: false,
      },
    })
  );
});

@Module({
  imports: queues,
  exports: queues,
})
export class WorkersRegistryModule {}
