import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { MytmService } from 'src/mytm/mytm.service';
import { APP_CONCURRENCY, PROCESSORS, PROCESS_QUEUE_NAMES } from './job.constant';
import { JobMyTMEvent } from './job.types';

@Processor(PROCESSORS.MyTmEventsJob)
export class MyTMEventsJobProcessor {
  private readonly logger = new Logger(MyTMEventsJobProcessor.name);
  constructor(private myTmService: MytmService) {}
  @Process({ name: PROCESS_QUEUE_NAMES.MY_TM_EVENTS, concurrency: APP_CONCURRENCY })
  async performJob(job: JobMyTMEvent) {
    this.logger.log(`START JOB MY_TM_EVENTS: ${job.data.roundId}....`);
    return await this.myTmService.sendEventNewRound(job.data.roundId);
  }
}
