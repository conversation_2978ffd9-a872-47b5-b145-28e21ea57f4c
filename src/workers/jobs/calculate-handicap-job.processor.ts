import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CdmService } from 'src/cdm/cdm.service';
import { RoundService } from 'src/rounds/rounds.service';
import { UsersService } from 'src/users/users.service';
import { APP_CONCURRENCY, PROCESSORS, PROCESS_QUEUE_NAMES } from './job.constant';
import { JobCalculateHandicapIndex } from './job.types';

@Processor(PROCESSORS.CalcHandicapJob)
export class CalculateHandicapJobProcessor {
  private readonly logger = new Logger(CalculateHandicapJobProcessor.name);

  constructor(
    private readonly config: ConfigService,
    private cdmService: CdmService,
    private roundService: RoundService,
    private usersService: UsersService
  ) {}

  @Process({ name: PROCESS_QUEUE_NAMES.CALC_HANDICAP, concurrency: APP_CONCURRENCY })
  async performJob(job: JobCalculateHandicapIndex) {
    this.logger.log(`START CALCULATE HANDICAP INDEX FOR USER: ${job.data.userId}`);
    const user = await this.usersService.findOne({ id: job.data.userId });
    if (!user) return;
    const handicapIndex = await this.roundService.calculatePlayerHandicapIndex(job.data.userId);
    try {
      await this.cdmService.updateConsumerHandicapIndex(user, handicapIndex);
    } catch (error) {
      this.logger.error(`UPDATE HANDICAP ERROR`);
      this.logger.error(error);
    }
  }
}
