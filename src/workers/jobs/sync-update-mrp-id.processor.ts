import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CdmService } from 'src/cdm/cdm.service';
import { UsersService } from 'src/users/users.service';
import { APP_CONCURRENCY, PROCESSORS, PROCESS_QUEUE_NAMES } from './job.constant';

@Processor(PROCESSORS.SyncUpdateMrpIdJob)
export class SyncUpdateMrpIdProcessor {
  private readonly logger = new Logger(SyncUpdateMrpIdProcessor.name);
  constructor(
    private readonly config: ConfigService,
    private cdmService: CdmService,
    private usersService: UsersService
  ) {}

  @Process({ name: PROCESS_QUEUE_NAMES.SYNC_UPDATE_MRP_ID, concurrency: APP_CONCURRENCY })
  async performJob(job) {
    const { userId } = job.data;
    this.logger.log(`${userId} Start JOB ${PROCESSORS.SyncUpdateMrpIdJob}....`);
    const user = await this.usersService.findOne({ id: userId });
    if (!user) {
      return;
    }
    await this.cdmService.updateConsumerMRPUserId(user);
  }
}
