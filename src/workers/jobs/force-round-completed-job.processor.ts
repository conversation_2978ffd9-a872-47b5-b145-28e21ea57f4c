import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { Club } from 'src/clubs/entities/club.entity';
import { HolePlayed } from 'src/holes-played/entities/hole-played.entity';
import { IGolfService } from 'src/igolf/igolf.service';
import { Round } from 'src/rounds/entities/round.entity';
import { RoundService } from 'src/rounds/rounds.service';
import { StrokePlayed } from 'src/strokes-played/entities/stroke-played.entity';
import { StrokesPlayedService } from 'src/strokes-played/strokes-played.service';
import { StrokeStat } from 'src/strokes-stats/entities/stroke-stat.entity';
import { StrokeRollup } from 'src/utils/concers/stroke_rollup';
// import { sleeps } from 'src/utils/utils';
import { APP_CONCURRENCY, PROCESSORS, PROCESS_QUEUE_NAMES } from './job.constant';
import { JobIGolfRoundCompleteData } from './job.types';

// const RETRY_MAX = 150;
@Processor(PROCESSORS.ForceRoundCompleteJob)
export class ForceRoundCompleteJobProcessor {
  private readonly logger = new Logger(ForceRoundCompleteJobProcessor.name);
  constructor(
    @InjectRepository(Round) private readonly roundRepo: Repository<Round>,
    @InjectRepository(Club) private readonly clubRepo: Repository<Club>,
    @InjectRepository(StrokeStat) private readonly strokeStatRepo: Repository<StrokeStat>,
    @InjectRepository(HolePlayed) private readonly holePlayedRepo: Repository<HolePlayed>,
    @InjectRepository(StrokePlayed) private readonly strokePlayerRepo: Repository<StrokePlayed>,
    private readonly configService: ConfigService,
    private readonly roundService: RoundService,
    private readonly iGolfService: IGolfService,
    private readonly strokePlayedService: StrokesPlayedService
  ) {}
  @Process({ name: PROCESS_QUEUE_NAMES.FORCE_ROUND_COMPLETE, concurrency: APP_CONCURRENCY })
  async performJob(job: JobIGolfRoundCompleteData) {
    this.logger.log(`${JSON.stringify(job.data)}`);

    const { roundId, holesPlayedModify } = job.data;
    this.logger.log('START JOB FORCE ROUND COMPLETE....');
    const round = await this.roundRepo.findOneBy({ id: roundId });
    if (!round) {
      return;
    }
    if (job.data.isCheckStats) {
      if (round.stats_completed) {
        return;
      }
    }
    // Clear strokeStats if exits
    await this.strokeStatRepo.delete({ round_id: roundId });

    let holePlayedIds = null;
    if (!holesPlayedModify) {
      // holesPlayed = await this.holePlayedRepo.find({
      //   where: {
      //     id: In(holesPlayedModify),
      //     round_id: roundId,
      //   },
      // });
      const holeQueryBuilder = this.holePlayedRepo.createQueryBuilder();
      holeQueryBuilder.where({ round_id: roundId });
      holeQueryBuilder.select(['id']);
      holePlayedIds = await holeQueryBuilder.getRawMany();
      if (holePlayedIds.length > 0) {
        holePlayedIds = holePlayedIds.map((hole: any) => hole.id);
      }
    } else {
      holePlayedIds = holesPlayedModify;
    }
    // let isCreateStrokeDone = false;
    let strokes = [];
    // let retryMax = RETRY_MAX;
    // while (!isCreateStrokeDone) {
    //   this.logger.debug(`GET STROKES.....`);
    //   const countStrokes = await this.countStrokes(holePlayedIds);
    //   retryMax--;
    //   if (!totalStrokes && countStrokes == 0) {
    //     isCreateStrokeDone = true;
    //     return;
    //   }
    //   this.logger.debug(`STROKES SIZE: ${countStrokes}`);
    //   if (countStrokes < totalStrokes && retryMax > 0) {
    //     this.logger.debug(`WAIT TO STROKE CREATED DONE!....`);
    //     await sleeps(3);
    //   } else {
    //     if (countStrokes >= totalStrokes || !totalStrokes) {
    //       this.logger.debug(`CREATED STROKES DONE!....`);
    //       retryMax = RETRY_MAX;
    //       isCreateStrokeDone = true;
    //       await this.updateIGolfStats(roundId);
    //       strokes = await this.getStrokes(strokes, holePlayedIds);
    //     } else {
    //       this.logger.debug(`WAIT TO STROKE CREATED DONE!....`);
    //       await sleeps(3);
    //     }
    //   }
    //   if (retryMax == 0) {
    //     this.logger.debug(`REACHED MAX TIME WAIT!....`);
    //     isCreateStrokeDone = true;
    //     return;
    //   }
    // }

    await this.updateIGolfStats(roundId);
    strokes = await this.getStrokes(strokes, holePlayedIds);
    // # Fix 0,0 Coordinates

    // ::Strokes::MissingCoordinates.new(strokes).move!

    // # Update missing Lie Conditions
    // ::Strokes::MissingLies.new(strokes).assign!
    strokes = strokes.map((s) => {
      if (!s.lie) {
        s.lie = 'Hole Boundary';
      }
      return s;
    });

    // # generate stroke stats
    const strokeRollup = new StrokeRollup(
      round,
      this.roundRepo,
      this.strokeStatRepo,
      this.clubRepo,
      this.holePlayedRepo,
      this.strokePlayedService,
      this.iGolfService,
      this.roundService
    );
    await strokeRollup.iGolfGenerateStats(round, strokes);
  }
  private async updateIGolfStats(roundId: number) {
    await this.roundService.iGolfStatSql(roundId);
    await this.roundService.statSql(roundId);
  }

  private async getStrokes(strokes: any[], holePlayedIds: any) {
    strokes = await this.strokePlayerRepo.find({
      where: { hole_played_id: In(holePlayedIds) },
      select: [
        'id',
        'hole_played_id',
        'difficult',
        'lie',
        'coords',
        'round_id',
        'club_id',
        'azimuth_to_pin',
        'result_from_pin',
        'shot_azimuth',
        'result_angle_from_pin',
        'ordinal',
      ],
    });
    return strokes;
  }
  private async countStrokes(holePlayedIds: any) {
    const count = await this.strokePlayerRepo.count({
      where: { hole_played_id: In(holePlayedIds) },
    });
    return count;
  }
}
