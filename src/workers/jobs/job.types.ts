import { Job } from 'bull';
import { HolePlayed } from 'src/holes-played/entities/hole-played.entity';
import { PlayerScoreDto } from 'src/players/dto/player-score.dto';
import { PlayerDto } from 'src/players/dto/player.dto';

export type JobIGolfRoundCompleteData = Job<{
  roundId?: number;
  holesPlayedModify?: any;
  totalStrokes?: number;
  isCheckStats?: boolean;
}>;
export type JobRoundAuditImport = Job<{
  auditId?: number;
}>;
export type JobCalculateHandicapIndex = Job<{
  userId?: number;
  ghinHandicapIndex?: number;
}>;
export type JobSimpleScoreToPar = Job<{
  userId?: number;
  roundId?: number;
}>;
export type JobAverageScore = Job<{
  userId?: number;
}>;
export type JobAverageClassicScore = Job<{
  userId?: number;
  total_classic_rounds?: any;
  round_classic_ids?: any;
}>;
export type JobMyTMEvent = Job<{
  roundId?: number;
}>;
export type JobCreatePlayerScore = Job<{
  roundId?: number;
  holePlayed?: HolePlayed;
  playerScores?: PlayerScoreDto[];
}>;
export type JobCreatePlayer = Job<{
  roundId?: number;
  players?: PlayerDto[];
}>;
export type JobRoundUpdate = Job<{
  round?: any;
  params?: any;
  id?: any;
}>;
export type JobLogRequestIGolf = Job<{
  actionType: string;
  igolfCourseId: string;
  client?: string;
}>;

export type RemoveUserLogCourseType = Job<{
  ids: any[];
}>;
