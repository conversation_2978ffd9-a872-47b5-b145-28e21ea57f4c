import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import _, { reject } from 'lodash';
import { In, IsNull, Not, Repository } from 'typeorm';
import { Club } from 'src/clubs/entities/club.entity';
import { HolePlayed } from 'src/holes-played/entities/hole-played.entity';
import { Round } from 'src/rounds/entities/round.entity';
import { ROUND } from 'src/rounds/round.const';
import { StrokePlayed } from 'src/strokes-played/entities/stroke-played.entity';
import { StrokeStat } from 'src/strokes-stats/entities/stroke-stat.entity';
import { PROCESSORS, PROCESS_QUEUE_NAMES } from './job.constant';
import { JobIGolfRoundCompleteData } from './job.types';

@Processor(PROCESSORS.ClearShotNotInHole)
export class ClearShotNotInHoleJobProcessor {
  private readonly logger = new Logger(ClearShotNotInHoleJobProcessor.name);
  constructor(
    @InjectRepository(Round) private readonly roundRepo: Repository<Round>,
    @InjectRepository(Club) private readonly clubRepo: Repository<Club>,
    @InjectRepository(StrokeStat) private readonly strokeStatRepo: Repository<StrokeStat>,
    @InjectRepository(HolePlayed) private readonly holePlayedRepo: Repository<HolePlayed>,
    @InjectRepository(StrokePlayed) private readonly strokePlayerRepo: Repository<StrokePlayed>
  ) {}
  @Process({ name: PROCESS_QUEUE_NAMES.CLEAR_SHOT_NOT_IN_HOLE })
  async performJob(job: JobIGolfRoundCompleteData) {
    this.logger.log('START JOB ROUND CLEAR_SHOT_NOT_IN_HOLE....');
    this.logger.log(`DATA: ${JSON.stringify(job.data)}`);
    const { roundId } = job.data;
    if (isNaN(+roundId)) {
      return `RoundID: ${roundId} is invalid!`;
    }
    const round = await this.roundRepo.findOne({
      where: {
        id: roundId,
        deleted_at: IsNull(),
      },
      select: ['id', 'round_mode'],
    });
    if (!round) {
      return;
    }
    // get holeId in round
    const holes = await this.holePlayedRepo.find({
      where: {
        round_id: roundId,
      },
      select: ['id', 'name'],
    });

    if (holes && holes.length > 0) {
      const lstHole = holes?.map((hole) => {
        return { id: hole.id, name: hole.name };
      });
      const groupHoles = _.groupBy(lstHole, 'name');
      const holesChecked = await this.checkDuplicateHole(groupHoles);
      const ids = holesChecked.map((h: any) => h.id);
      if (!round || round.round_mode != ROUND.ROUND_MODE_ADVANCED) {
        return;
      }
      const strokePlayeds = await this.strokePlayerRepo.find({
        where: {
          hole_played_id: Not(In(ids)),
          round_id: roundId,
        },
        select: ['id'],
      });
      if (strokePlayeds && strokePlayeds.length > 0) {
        // await this.strokePlayerRepo.delete({ id: In(strokePlayeds.map((s) => s.id)) });
        const listStrokesId = strokePlayeds.map((s) => s.id);
        const splitStrokeIds = _.chunk(listStrokesId, 30);

        await Promise.all(
          splitStrokeIds.map(async (ids) => {
            try {
              await this.strokePlayerRepo.delete({ id: In(ids) });
              console.log(`DELETE LIST STROKES: ${ids}`);
            } catch (error) {
              reject(error);
            }
          })
        )
          .then()
          .catch((error) => console.log(error));
      }
      const strokeStats = await this.strokeStatRepo.find({
        where: {
          hole_played_id: Not(In(ids)),
          round_id: roundId,
        },
        select: ['id'],
      });

      if (strokeStats && strokeStats.length > 0) {
        const listStatsId = strokeStats.map((s) => s.id);
        const splitIds = _.chunk(listStatsId, 30);

        await Promise.all(
          splitIds.map(async (ids) => {
            try {
              await this.strokeStatRepo.delete({ id: In(ids) });
              console.log(`DELETE LIST STATS: ${ids}`);
            } catch (error) {
              reject(error);
            }
          })
        )
          .then()
          .catch((error) => console.log(error));
      }
    }
  }
  private async checkDuplicateHole(groupByHoleName: any) {
    let isDuplicateHole = false;
    const lstHolesName = Object.keys(groupByHoleName);
    const lstHoleIds = Object.values(groupByHoleName)?.flat();
    const listHolesIdRemoved = [];
    let holesIds = lstHoleIds;
    for (const holeName of lstHolesName) {
      if (groupByHoleName[holeName].length > 1) {
        isDuplicateHole = true;
        this.logger.debug(`DUPLICATE HOLE FOUND: ${holeName}`);
        for (let i = 1; i < groupByHoleName[holeName].length; i++) {
          const holesId = groupByHoleName[holeName][i]['id'];
          try {
            this.logger.debug(`REMOVE SUCCESS HOLE: ${holesId}`);
            listHolesIdRemoved.push(+holesId);
          } catch (error) {
            this.logger.error(`REMOVE FAIL HOLE: ${holesId}`);
          }
        }
      }
    }
    if (isDuplicateHole) {
      await this.holePlayedRepo.delete({ id: In(listHolesIdRemoved) });
      holesIds = lstHoleIds.filter((hole: any) => !listHolesIdRemoved.includes(hole.id));
    }
    return holesIds;
  }
}
