import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AverageScoresService } from 'src/average-scores/average-scores.service';
import { PROCESSORS, PROCESS_QUEUE_NAMES } from './job.constant';
import { JobAverageClassicScore } from './job.types';

@Processor(PROCESSORS.CalculateAverageScoreClassicJob)
export class CalculateAverageScoreClassicJobProcessor {
  private readonly logger = new Logger(CalculateAverageScoreClassicJobProcessor.name);
  constructor(private readonly config: ConfigService, private averageScoreService: AverageScoresService) {}
  @Process({ name: PROCESS_QUEUE_NAMES.CALCULATE_AVERAGE_CLASSIC_SCORE, concurrency: 1 })
  async performJob(job: JobAverageClassicScore) {
    this.logger.log(`START JOB CALCULATE_AVERAGE_CLASSIC_SCORE USER: ${job.data.userId}....`);
    const { userId, round_classic_ids, total_classic_rounds } = job.data;
    await this.averageScoreService.calculateAverageScoreClassic(total_classic_rounds, round_classic_ids, userId);
  }
}
