import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CdmService } from 'src/cdm/cdm.service';
import { UpdateUserDto } from 'src/users/dto/update-user.dto';
import { UsersService } from 'src/users/users.service';
import { APP_CONCURRENCY, PROCESSORS, PROCESS_QUEUE_NAMES } from './job.constant';

@Processor(PROCESSORS.SyncUpdateUserFromCdmJob)
export class SyncUpdateUserFromCDMProcessor {
  private readonly logger = new Logger(SyncUpdateUserFromCDMProcessor.name);
  constructor(
    private readonly config: ConfigService,
    private cdmService: CdmService,
    private usersService: UsersService
  ) {}

  @Process({ name: PROCESS_QUEUE_NAMES.SYNC_UPDATE_USER_FROM_CDM, concurrency: APP_CONCURRENCY })
  async performJob(job) {
    const { cdmId } = job.data;
    this.logger.log(`${cdmId} Start JOB ${PROCESSORS.SyncUpdateUserFromCdmJob}....`);
    const user = await this.usersService.findOne({ cdm_id: cdmId });
    if (!user) {
      return 'User not found!';
    }
    const consumer = await this.cdmService.getConsumer(user.email, true);
    if (!consumer) {
      return 'Consumer not found!';
    }
    try {
      const dataMap = CdmService.mapConsumerToUser(consumer);
      delete dataMap?.country;
      const payload = new UpdateUserDto();
      payload.user = dataMap;

      await this.usersService.update(user.id, payload);
      return payload;
    } catch (error) {
      this.logger.error(`SYNC UPDATE USER FROM CDM ERROR`);
      this.logger.error(error);
      return error.message;
    }
  }
}
