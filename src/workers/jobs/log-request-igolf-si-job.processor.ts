import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { ThreePartyCourseService } from '../../igolf/threePartyCourse.service';
import { PROCESSORS, PROCESS_QUEUE_NAMES } from './job.constant';
import { JobLogRequestIGolf } from './job.types';

@Processor(PROCESSORS.LogRequestIGolfSI)
export class LogRequestIGolfSIJobProcessor {
  private readonly logger = new Logger(LogRequestIGolfSIJobProcessor.name);
  constructor(private threePartyCourseService: ThreePartyCourseService) {}
  @Process({ name: PROCESS_QUEUE_NAMES.LOG_REQUEST_IGOLF_SI, concurrency: 1 })
  async performJob(job: JobLogRequestIGolf) {
    this.logger.log(`START JOB LOG REQUEST IGOLF....`);
    try {
      await this.threePartyCourseService.addLogRequest(job.data);
      return { success: true };
    } catch (error) {
      return { msg: error.message, success: false };
    }
  }
}
