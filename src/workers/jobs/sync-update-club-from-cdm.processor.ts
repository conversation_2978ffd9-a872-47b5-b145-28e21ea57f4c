import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { plainToClass } from 'class-transformer';
import { CdmService } from 'src/cdm/cdm.service';
import { ClubsService } from 'src/clubs/clubs.service';
import { Club } from 'src/clubs/entities/club.entity';
import { UsersService } from 'src/users/users.service';
import { APP_CONCURRENCY, PROCESSORS, PROCESS_QUEUE_NAMES } from './job.constant';

@Processor(PROCESSORS.SyncUpdateClubsFromCdmJob)
export class SyncUpdateClubFromCDMProcessor {
  private readonly logger = new Logger(SyncUpdateClubFromCDMProcessor.name);
  constructor(
    private readonly config: ConfigService,
    private cdmService: CdmService,
    private clubService: ClubsService,
    private usersService: UsersService
  ) {}

  @Process({ name: PROCESS_QUEUE_NAMES.SYNC_UPDATE_CLUBS_FROM_CDM, concurrency: APP_CONCURRENCY })
  async performJob(job) {
    const { userId } = job.data;
    this.logger.log(`${userId} Start JOB ${PROCESSORS.SyncUpdateClubsFromCdmJob}....`);
    const user = await this.usersService.findOne({ id: userId });
    if (!user || !user.cdm_id) {
      return;
    }
    // TODO: CDM V1
    const consumer = await this.cdmService.getConsumer(user.email, false, true);
    const witbs = consumer?.golferProfile?.whatsInTheBags || [];

    // if (witbs.length === 0) {
    //   return;
    // }

    //TODO: CDM V2
    // const witbs = await this.cdmService.getWitbsV2(user.email, true);

    if (witbs == null || witbs.length == 0) {
      return;
    }
    const clubs: Club[] = witbs.map((item) =>
      plainToClass(Club, { ...CdmService.convertWITBItemToClubParams(item), user_id: userId })
    );
    for (const club of clubs) {
      const existingClub = await this.clubService.findOneByCdmWITBId(club.cdm_witb_id);
      if (existingClub) {
        if (existingClub.disabled != club.disabled) {
          const clubUpdate = await this.clubService.updateClub(
            existingClub.id,
            CdmService.convertWITBItemToClubParams(club)
          );
          this.logger.debug(`UPDATE CLUBS: ${JSON.stringify(clubUpdate)}`);
        }
      } else {
        club['created_at'] = new Date();
        club['updated_at'] = new Date();
        const createNewClub = await this.clubService.create(club);
        this.logger.debug(`CREATE CLUBS: ${JSON.stringify(createNewClub)}`);
      }
    }
  }
}
