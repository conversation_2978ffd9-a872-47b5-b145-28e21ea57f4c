import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RoundService } from 'src/rounds/rounds.service';
import { APP_CONCURRENCY, PROCESSORS, PROCESS_QUEUE_NAMES } from './job.constant';
import { JobSimpleScoreToPar } from './job.types';

@Processor(PROCESSORS.PostScoreToGHIN)
export class PostScoreToGHINJobProcessor {
  private readonly logger = new Logger(PostScoreToGHINJobProcessor.name);
  constructor(private readonly config: ConfigService, private readonly roundService: RoundService) {}
  @Process({ name: PROCESS_QUEUE_NAMES.POST_SCORE_TO_GHIN, concurrency: APP_CONCURRENCY })
  async performJob(job: JobSimpleScoreToPar) {
    this.logger.log(`POST SCORE TO GHIN: ${job.data.roundId}`);
    await this.roundService.postScoreToGHIN(job.data.roundId);
  }
}
