import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CdmService } from 'src/cdm/cdm.service';
import { UsersService } from 'src/users/users.service';
import { APP_CONCURRENCY, PROCESSORS, PROCESS_QUEUE_NAMES } from './job.constant';
import { JobCalculateHandicapIndex } from './job.types';

@Processor(PROCESSORS.SyncGhinHandicapIndexJob)
export class SyncGHINHandicapJobProcessor {
  private readonly logger = new Logger(SyncGHINHandicapJobProcessor.name);

  constructor(
    private readonly config: ConfigService,
    private cdmService: CdmService,
    private usersService: UsersService
  ) {}

  @Process({ name: PROCESS_QUEUE_NAMES.SYNC_GHIN_HANDICAP_INDEX, concurrency: APP_CONCURRENCY })
  async performJob(job: JobCalculateHandicapIndex) {
    this.logger.log(`START CALCULATE HANDICAP INDEX FOR USER: ${job.data.userId}`);
    const user = await this.usersService.findOne({ id: job.data.userId });
    if (!user) return;
    try {
      await this.cdmService.updateConsumerGHINHandicapIndex(user, job.data.ghinHandicapIndex);
    } catch (error) {
      this.logger.error(`UPDATE GHIN HANDICAP ERROR`);
      this.logger.error(error);
    }
  }
}
