import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AverageScoresService } from 'src/average-scores/average-scores.service';
import { APP_CONCURRENCY, PROCESSORS, PROCESS_QUEUE_NAMES } from './job.constant';
import { JobAverageScore } from './job.types';

@Processor(PROCESSORS.CalculateAverageScoreJob)
export class CalculateAverageScoreJobProcessor {
  private readonly logger = new Logger(CalculateAverageScoreJobProcessor.name);
  constructor(private readonly config: ConfigService, private averageScoreService: AverageScoresService) {}
  @Process({ name: PROCESS_QUEUE_NAMES.CALCULATE_AVERAGE_SCORE, concurrency: APP_CONCURRENCY })
  async performJob(job: JobAverageScore) {
    this.logger.log(`START JOB CALCULATE_AVERAGE_SCORE USER: ${job.data.userId}....`);
    await this.averageScoreService.calculateAverageScore(job.data.userId);
  }
}
