import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CdmService } from 'src/cdm/cdm.service';
import { ClubsService } from 'src/clubs/clubs.service';
import { UsersService } from 'src/users/users.service';
import { PROCESSORS, PROCESS_QUEUE_NAMES } from './job.constant';

@Processor(PROCESSORS.SyncClubsToCDM)
export class SyncClubsToCDMProcessor {
  private readonly logger = new Logger(SyncClubsToCDMProcessor.name);
  constructor(
    private readonly config: ConfigService,
    private cdmService: CdmService,
    private usersService: UsersService,
    private clubsService: ClubsService
  ) {}

  @Process({ name: PROCESS_QUEUE_NAMES.SYNC_CLUBS_TO_CDM, concurrency: 2 })
  async performJob(job) {
    const { userId } = job.data;
    this.logger.log(`START SYNC CLUBS OF USER: ${userId} TO CDM...`);
    const user = await this.usersService.findOne({ id: userId });
    if (!user) {
      return 'User not found!';
    }
    const clubs = await this.clubsService.getAllUserClubs(user.id);
    const clubsSync = [];
    if (clubs.length > 0) {
      for (const club of clubs) {
        if (!club.cdm_witb_id) {
          const payload = await this.clubsService.transformClubToWITBParams(club, user);
          const witb = await this.cdmService.createWITB(payload);
          if (witb?.id) {
            await this.clubsService.updateClubWithCdmWITBId(club.id, witb?.id);
          }
          clubsSync.push({ witb: witb.id, clubId: club.id });
        }
      }
    }
    this.logger.debug(`SYNC CLUBS TO CDM ${userId} DONE...`);
    console.log(`LIST CLUBS: ${JSON.stringify(clubsSync)}`);
    return clubsSync;
  }
}
