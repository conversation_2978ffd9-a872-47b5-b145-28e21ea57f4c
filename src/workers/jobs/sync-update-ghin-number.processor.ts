import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CdmService } from 'src/cdm/cdm.service';
import { UsersService } from 'src/users/users.service';
import { APP_CONCURRENCY, PROCESSORS, PROCESS_QUEUE_NAMES } from './job.constant';

@Processor(PROCESSORS.SyncUpdateGhinNumberJob)
export class SyncUpdateGhinNumberProcessor {
  private readonly logger = new Logger(SyncUpdateGhinNumberProcessor.name);
  constructor(
    private readonly config: ConfigService,
    private cdmService: CdmService,
    private usersService: UsersService
  ) {}

  @Process({ name: PROCESS_QUEUE_NAMES.SYNC_UPDATE_GHIN_NUMBER, concurrency: APP_CONCURRENCY })
  async performJob(job) {
    const { userId } = job.data;
    this.logger.log(`${userId} Start JOB ${PROCESSORS.SyncUpdateGhinNumberJob}....`);
    const user = await this.usersService.findOne({ id: userId });
    if (!user) {
      return;
    }
    try {
      await this.cdmService.updateConsumerGHINNumber(user);
    } catch (error) {
      this.logger.error(`ERROR SYNC GHIN NUMBER`);
      console.log(error);
    }
  }
}
