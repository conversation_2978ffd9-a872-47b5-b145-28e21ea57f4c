import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { RoundAuditUpdateService } from 'src/round-audit-update/round-audit-update.service';
import { Round } from 'src/rounds/entities/round.entity';
import { APP_CONCURRENCY, PROCESSORS, PROCESS_QUEUE_NAMES } from './job.constant';
import { JobRoundUpdate } from './job.types';

@Processor(PROCESSORS.RoundUpdateJob)
export class RoundUpdateJobProcessor {
  private readonly logger = new Logger(RoundUpdateJobProcessor.name);
  constructor(
    private readonly configService: ConfigService,
    private readonly roundAuditUpdate: RoundAuditUpdateService,
    @InjectRepository(Round)
    private roundRepo: Repository<Round>
  ) {}
  @Process({ name: PROCESS_QUEUE_NAMES.ROUND_UPDATE, concurrency: APP_CONCURRENCY })
  async performJob(job: JobRoundUpdate) {
    this.logger.log('START JOB ROUND UPDATE....');
    const { round, params, id } = job.data;
    let roundData = round;
    if (!round) {
      roundData = await this.roundRepo.findOneBy({ id });
    }

    if (roundData) {
      await this.roundAuditUpdate.updateRound(roundData, params);
      this.logger.log(`DONE ROUND UPDATE....`);
      return { success: true };
    } else {
      this.logger.log(`ROUND UPDATE FAIL....`);
      return {
        success: false,
        id,
        round,
      };
    }
  }
}
