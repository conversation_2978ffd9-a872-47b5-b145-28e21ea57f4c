import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { ThreePartyCourseService } from '../../igolf/threePartyCourse.service';
import { PROCESSORS, PROCESS_QUEUE_NAMES } from './job.constant';
import { RemoveUserLogCourseType } from './job.types';

@Processor(PROCESSORS.RemoveUserLogCourse)
export class RemoveUserLogCourseDailyProcessor {
  private readonly logger = new Logger(RemoveUserLogCourseDailyProcessor.name);
  constructor(private threePartyCourseService: ThreePartyCourseService) {}
  @Process({ name: PROCESS_QUEUE_NAMES.REMOVE_USER_LOG_COURSE, concurrency: 1 })
  async performJob(job: RemoveUserLogCourseType) {
    this.logger.log(`START QUEUE REMOVE USER LOG COURSE....`);
    try {
      await this.threePartyCourseService.removeUserLogCourse(job?.data?.ids);
      return { success: true };
    } catch (error) {
      return { msg: error.message, success: false };
    }
  }
}
