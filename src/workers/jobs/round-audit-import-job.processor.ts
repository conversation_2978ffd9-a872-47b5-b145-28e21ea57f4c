import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RoundService } from 'src/rounds/rounds.service';
import { APP_CONCURRENCY, PROCESSORS, PROCESS_QUEUE_NAMES } from './job.constant';
import { JobRoundAuditImport } from './job.types';

@Processor(PROCESSORS.RoundAuditImportJob)
export class RoundAuditImportJobProcessor {
  private readonly logger = new Logger(RoundAuditImportJobProcessor.name);
  constructor(private readonly configService: ConfigService, private readonly roundService: RoundService) {}
  @Process({ name: PROCESS_QUEUE_NAMES.ROUND_AUDIT_IMPORT, concurrency: APP_CONCURRENCY })
  async performJob(job: JobRoundAuditImport) {
    this.logger.log('START JOB JOB ROUND AUDIT IMPORT....');
    this.logger.log(`${JSON.stringify(job.data)}`);
    const { auditId } = job.data;
    await this.roundService.processCreateRound(auditId);
    this.logger.log(`DONE ROUND AUDIT IMPORT JOB CREATE ROUND.....`);
  }
}
