import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RoundService } from 'src/rounds/rounds.service';
import { APP_CONCURRENCY, PROCESSORS, PROCESS_QUEUE_NAMES } from './job.constant';
import { JobSimpleScoreToPar } from './job.types';

@Processor(PROCESSORS.SimpleScoreToParJob)
export class SimpleScoreToParJobProcessor {
  private readonly logger = new Logger(SimpleScoreToParJobProcessor.name);
  constructor(private readonly config: ConfigService, private readonly roundService: RoundService) {}
  @Process({ name: PROCESS_QUEUE_NAMES.SIMPLE_SCORE_TO_PAR, concurrency: APP_CONCURRENCY })
  async performJob(job: JobSimpleScoreToPar) {
    this.logger.log(`Start Calculate Simple Score To Par: ${job.data.roundId}`);
    await this.roundService.simpleScoreToPar(job.data.roundId);
  }
}
