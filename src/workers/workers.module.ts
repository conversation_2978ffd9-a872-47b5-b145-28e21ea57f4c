import { Module } from '@nestjs/common';
import { SharedModule } from 'src/shared/shared.module';
import { BullDashboardController } from './bull-dashboard.controller';
import { BullDashboardService } from './bull-dashboard.service';
import processors from './processors';

@Module({
  imports: [SharedModule],
  controllers: [BullDashboardController],
  providers: [...processors, BullDashboardService],
})
export class WorkersModule {}
