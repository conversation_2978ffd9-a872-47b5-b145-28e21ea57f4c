import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from 'src/users/entities/user.entity';
import { MAIL_QUEUE_NAME, SEND_MAIL_PROCESS_NAME } from './mail.constant';
import { SendEmailJob } from './mail.type';

@Processor(MAIL_QUEUE_NAME)
export class MailProcessor {
  private readonly logger = new Logger(MailProcessor.name);
  constructor(@InjectRepository(User) private readonly userRepo: Repository<User>) {}

  @Process({
    name: SEND_MAIL_PROCESS_NAME,
    concurrency: 1,
  })
  sendEmail(job: SendEmailJob) {
    console.log(job);
    this.logger.debug(`Sending email...`);
    return true;
  }
}
