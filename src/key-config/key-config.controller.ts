import { Body, Controller, Get, Post } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { KeyConfigDto } from './dto/key-config.dto';
import { KeyConfigService } from './key-config.service';

@ApiTags('KeyConfigs')
@Controller('key-config')
export class KeyConfigController {
  constructor(private readonly keyConfigService: KeyConfigService) {}

  @Post()
  create(@Body() createKeyConfigDto: KeyConfigDto) {
    return this.keyConfigService.createOrUpdateKeyConfig(createKeyConfigDto);
  }

  @Get('/all')
  findAll() {
    return this.keyConfigService.findAll();
  }
}
