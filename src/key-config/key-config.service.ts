import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { KeyConfigDto } from './dto/key-config.dto';
import { KeyConfigEntity } from './entities/key-config.entity';

@Injectable()
export class KeyConfigService {
  constructor(
    @InjectRepository(KeyConfigEntity)
    private keyConfigRepository: Repository<KeyConfigEntity>
  ) {}

  async createOrUpdateKeyConfig(createKeyConfigDto: KeyConfigDto) {
    const { key } = createKeyConfigDto;
    const config = await this.keyConfigRepository.findOne({
      where: { key },
    });
    if (config && config?.id) {
      await this.keyConfigRepository.update({ id: config?.id }, createKeyConfigDto);
      return true;
    }
    await this.keyConfigRepository.save(this.keyConfigRepository.create(createKeyConfigDto));
    return true;
  }

  findAll() {
    return this.keyConfigRepository.find();
  }

  remove(id: number) {
    return `This action removes a #${id} player`;
  }
}
