import { ApiProperty } from '@nestjs/swagger';
import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

@Entity({ name: 'key_config' })
export class KeyConfigEntity {
  @PrimaryGeneratedColumn()
  @ApiProperty({ example: 1 })
  id: number;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  key: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  value: string;

  @CreateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  created_at: Date;

  @UpdateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  updated_at: Date;
}
