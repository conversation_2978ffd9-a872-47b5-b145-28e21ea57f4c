import { registerAs } from '@nestjs/config';

export default registerAs('app', () => ({
  nodeEnv: process.env.NODE_ENV,
  name: process.env.APP_NAME,
  workingDirectory: process.env.PWD || process.cwd(),
  frontendDomain: process.env.FRONTEND_DOMAIN,
  backendDomain: process.env.BACKEND_DOMAIN,
  port: parseInt(process.env.APP_PORT || process.env.PORT, 10) || 3000,
  apiPrefix: process.env.API_PREFIX || 'api',
  fallbackLanguage: process.env.APP_FALLBACK_LANGUAGE || 'en',
  headerLanguage: process.env.APP_HEADER_LANGUAGE || 'x-custom-lang',
  redisHost: process.env.REDIS_HOST,
  redisPort: parseInt(process.env.REDIS_PORT, 10),
  redisPassword: process.env.REDIS_PASSWORD,
  tmInternalSecretKey: process.env.TM_INTERNAL_SECRET_KEY,
  cacheTTL: process.env.CACHE_TTL || '1 days',
  cdmEndpoint: process.env.CDM_ENDPOINT,
  cdmUsername: process.env.CDM_USERNAME,
  cdmPassword: process.env.CDM_PASSWORD,
  cdmMRPSystemId: process.env.CDM_MRP_SYSTEM_ID,
}));
