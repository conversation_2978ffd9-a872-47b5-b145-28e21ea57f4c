import { registerAs } from '@nestjs/config';

export default registerAs('auth0', () => ({
  domain: process.env.AUTH0_DOMAIN,
  apiDomain: process.env.AUTH0_API_DOMAIN,
  clientId: process.env.AUTH0_CLIENT_ID,
  clientSecret: process.env.AUTH0_CLIENT_SECRET,
  connection: process.env.AUTH0_CLIENT_CONNECTION,
  managementSecret: process.env.AUTH0_CLIENT_MANAGEMENT_SECRET,
  managementAudience: process.env.AUTH0_CLIENT_MANAGEMENT_AUDIENCE,
  managementScope: process.env.AUTH0_CLIENT_MANAGEMENT_SCOPE,
}));
