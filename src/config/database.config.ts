import { registerAs } from '@nestjs/config';

const databaseCredentials = {
  port: parseInt(process.env.DATABASE_PORT, 10) || 5432,
  username: process.env.DATABASE_USERNAME,
  password: process.env.DATABASE_PASSWORD,
  database: process.env.DATABASE_NAME,
};

export default registerAs('database', () => ({
  type: process.env.DATABASE_TYPE,
  keepConnectionAlive: true,
  host: process.env.DATABASE_HOST,
  ...databaseCredentials,
  migrationsRun: process.env.NODE_ENV === 'production',
  synchronize: process.env.DATABASE_SYNCHRONIZE === 'true',
  maxConnections: parseInt(process.env.DATABASE_MAX_CONNECTIONS, 10) || 100,
  sslEnabled: process.env.DATABASE_SSL_ENABLED === 'true',
  rejectUnauthorized: process.env.DATABASE_REJECT_UNAUTHORIZED === 'true',
  ca: process.env.DATABASE_CA,
  key: process.env.DATABASE_KEY,
  cert: process.env.DATABASE_CERT,
  replication: {
    master: {
      host: process.env.DATABASE_HOST,
      ...databaseCredentials,
    },
    slaves: process.env.DATABASE_SLAVE_HOSTS.split(',').map((host, i) => ({
      host,
      ...databaseCredentials,
      username: process.env.DATABASE_SLAVE_USERNAMES.split(',')[i],
    })),
  },
}));
