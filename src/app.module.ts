import { BullModule } from '@nestjs/bull';
import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MailerModule } from '@nestjs-modules/mailer';
import { AcceptLanguageResolver, CookieResolver, HeaderResolver, I18nModule, QueryResolver } from 'nestjs-i18n';
import * as path from 'path';
import { DataSource } from 'typeorm';
import { AuthModule } from './auth/auth.module';
import { AverageScoresModule } from './average-scores/average-scores.module';
import { CaslModule } from './casl/casl.module';
import { CdmModule } from './cdm/cdm.module';
import { ClientsModule } from './clients/clients.module';
import { ClubsModule } from './clubs/clubs.module';
import appConfig from './config/app.config';
import auth0Config from './config/auth0.config';
import authConfig from './config/auth.config';
import databaseConfig from './config/database.config';
import ghinConfig from './config/ghin.config';
import glxConfig from './config/glx.config';
import golfnetConfig from './config/golfnet.config';
import igolfConfig from './config/igolf.config';
import klaviyoConfig from './config/klaviyo.config';
import mailConfig from './config/mail.config';
import mytmConfig from './config/mytm.config';
import { CountriesModule } from './countries/countries.module';
import { TypeOrmConfigService } from './database/typeorm-config.service';
import { GhinTeeRatingModule } from './ghin-tee-rating/ghin-tee-rating.module';
import { GhinModule } from './ghin/ghin.module';
import { GlxModule } from './glx/glx.module';
import { GolfNetModule } from './golfnet/golfnet.module';
import { HealthModule } from './health/health.module';
import { HolesPlayedModule } from './holes-played/holes-played.module';
import { HomeModule } from './home/<USER>';
import { IGolfModule } from './igolf/igolf.module';
import { JobModule } from './job/job.module';
import { KeyConfigModule } from './key-config/key-config.module';
import { MailConfigService } from './mail/mail-config.service';
import { MailModule } from './mail/mail.module';
import { AppLoggerMiddleware } from './middleware/app-logger-middleware';
import { MytmModule } from './mytm/mytm.module';
import { PlayersModule } from './players/players.module';
import { RoundAuditImportMobileModule } from './round-audit-import-mobile/round-audit-import-mobile.module';
import { RoundAuditUpdateModule } from './round-audit-update/round-audit-update.module';
import { RoundAuditUtilModule } from './round-audit-util/round-audit-util.module';
import { RoundAuditModule } from './round-audit/round-audit.module';
import { RoundModule } from './rounds/rounds.module';
import { SharedModule } from './shared/shared.module';
import { StatesModule } from './states/states.module';
import { StaticModule } from './static/static.module';
import { StatsModule } from './stats/stats.module';
import { StrokeBaselinesModule } from './stroke-baselines/stroke-baselines.module';
import { StrokesPlayedModule } from './strokes-played/strokes-played.module';
import { StrokesStatsModule } from './strokes-stats/strokes-stats.module';
import { UsersModule } from './users/users.module';
import { redisConfig } from './utils/redis';
import { WhitelistIpModule } from './whitelist-ip/whitelist-ip.module';
import { WorkersModule } from './workers/workers.module';

@Module({
  imports: [
    ScheduleModule.forRoot(),
    ConfigModule.forRoot({
      isGlobal: true,
      load: [
        databaseConfig,
        authConfig,
        appConfig,
        mailConfig,
        igolfConfig,
        auth0Config,
        klaviyoConfig,
        mytmConfig,
        glxConfig,
        ghinConfig,
        golfnetConfig,
      ],
      envFilePath: ['.env'],
    }),
    BullModule.forRootAsync({
      useFactory: () => ({
        redis: redisConfig,
        prefix: 'MYTM_OC',
      }),
      inject: [ConfigService],
    }),
    TypeOrmModule.forRootAsync({
      useClass: TypeOrmConfigService,
      dataSourceFactory: async (options) => {
        return await new DataSource(options).initialize();
      },
    }),
    MailerModule.forRootAsync({
      useClass: MailConfigService,
    }),
    I18nModule.forRootAsync({
      useFactory: () => {
        return {
          fallbackLanguage: 'en',
          loaderOptions: { path: path.join(__dirname, '/i18n/'), watch: true },
        };
      },
      resolvers: [
        new QueryResolver(['lang', 'l']),
        new HeaderResolver(['x-custom-lang']),
        new CookieResolver(),
        AcceptLanguageResolver,
      ],
      imports: [ConfigModule],
      inject: [ConfigService],
    }),
    UsersModule,
    AuthModule,
    MailModule,
    HomeModule,
    CaslModule,
    WorkersModule,
    SharedModule,
    IGolfModule,
    RoundModule,
    CountriesModule,
    StatesModule,
    HolesPlayedModule,
    StrokesPlayedModule,
    StrokesStatsModule,
    StrokeBaselinesModule,
    ClientsModule,
    PlayersModule,
    AverageScoresModule,
    ClubsModule,
    RoundAuditModule,
    RoundAuditUtilModule,
    RoundAuditImportMobileModule,
    CdmModule,
    RoundAuditUpdateModule,
    MytmModule,
    StatsModule,
    StaticModule,
    JobModule,
    HealthModule,
    GhinModule,
    GhinTeeRatingModule,
    GolfNetModule,
    KeyConfigModule,
    GlxModule,
    WhitelistIpModule,
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(AppLoggerMiddleware).forRoutes('*');
  }
}
