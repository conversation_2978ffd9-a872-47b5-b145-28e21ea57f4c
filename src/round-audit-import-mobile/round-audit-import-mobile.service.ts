import { InjectQueue } from '@nestjs/bull';
import { Inject, Injectable, Logger, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Queue } from 'bull';
import moment from 'moment';
import { Repository } from 'typeorm';
import { GhinService } from 'src/ghin/ghin.service';
import { HolesPlayedService } from 'src/holes-played/holes-played.service';
import { PlayersService } from 'src/players/players.service';
import { Round } from 'src/rounds/entities/round.entity';
import { MAP_TYPE, OPTIONS_JOB_DEFAULT, ROUND } from 'src/rounds/round.const';
import { StrokesPlayedService } from 'src/strokes-played/strokes-played.service';
import { coordPutts, deleteValueBlank, includeStr, isGear } from 'src/utils/utils';
import { PROCESSORS, PROCESS_QUEUE_NAMES } from 'src/workers/jobs/job.constant';
import { ThreePartyCourseService } from '../igolf/threePartyCourse.service';
import { RoundAuditUtilsService } from '../round-audit-util/round-audit-util.service';

@Injectable()
export class RoundAuditImportMobileService {
  private readonly logger = new Logger(RoundAuditImportMobileService.name);
  public holesPlayedModify = [];
  public round = null;
  constructor(
    @InjectRepository(Round)
    private roundRepository: Repository<Round>,
    private roundAuditUtilService: RoundAuditUtilsService,
    private playerService: PlayersService,
    @Inject(forwardRef(() => ThreePartyCourseService)) private threePartyCourseService: ThreePartyCourseService,
    @Inject(forwardRef(() => GhinService)) private ghinService: GhinService,
    private strokesPlayerService: StrokesPlayedService,
    private holePlayerService: HolesPlayedService,
    @InjectQueue(PROCESSORS.CalculateAverageScoreJob) private calculateAverageScoreQueue: Queue
  ) {}
  //
  // generates a new round
  //
  async processImportRound(data: any, forceMapCourse = null) {
    const round: any = await this.initializeRound(data, forceMapCourse);
    delete round.holes_completed;
    return await this.buildRoundData(data, round, forceMapCourse);
  }

  // initialize(data) {
  //   // let @data   = data
  //   // let @round  = this.initialize_round()
  //   // let @holesPlayedModify = []
  // }

  //
  // generates the new round
  //
  async buildRoundData(data: any, round: any, forceMapCourse = null) {
    const result = await this.processRound(data, round, forceMapCourse);
    if (result.error) {
      this.roundRepository.delete({ id: result.id });
    }
    return result;
  }

  //
  // private methods
  //

  //
  // generate a new round and it's holes/strokes
  //
  async processRound(data: any, round: any, forceMapCourse = null) {
    try {
      const list_hole = [];
      const holePlayedIdModify = [];
      let totalStrokes = 0;
      if (data['holes']) {
        // for (const hole of data['holes']) {
        //   totalStrokes = await this.processCreateHoles(round, hole, data, list_hole, holePlayedIdModify, totalStrokes);
        // }
        await Promise.all(
          data['holes'].map(async (hole) => {
            totalStrokes = await this.processCreateHoles(
              round,
              hole,
              data,
              list_hole,
              holePlayedIdModify,
              totalStrokes,
              forceMapCourse
            );
          })
        );
      }
      if (data['round_mode'] == ROUND.ROUND_MODE_MULTIPLAYER) {
        await this.createRoundPlayer(data, round.id);
      }
      if (data['round_mode'] == ROUND.ROUND_MODE_ADVANCED) {
        this.logger.debug(`===============QUEUE COMPLETE ROUND==================`);
        this.roundAuditUtilService.queueCompleteRound(round, holePlayedIdModify, totalStrokes);
      }
      if ([ROUND.ROUND_MODE_SIMPLE, ROUND.ROUND_MODE_CLASSIC].includes(data['round_mode'])) {
        try {
          await this.calculateAverageScoreQueue.add(
            PROCESS_QUEUE_NAMES.CALCULATE_AVERAGE_SCORE,
            {
              userId: round.user_id,
            },
            OPTIONS_JOB_DEFAULT
          );
        } catch (error) {
          console.log(error);
        }
      }
      return round;
    } catch (error) {
      this.logger.error('PROCESS ROUND');
      console.error(error);
      return { id: round.id, error: error.message };
    }
  }

  private async processCreateHoles(
    round: any,
    hole: any,
    data: any,
    list_hole: any[],
    holePlayedIdModify: any[],
    totalStrokes: number,
    forceMapCourse = null
  ) {
    this.logger.debug(`============ START CREATE HOLE FOR ROUND ${round.id} ============`);
    let iGolfPinLocation = [0, 0];
    if (!hole.pin_location) {
      if (round.round_mode === ROUND.ROUND_MODE_ADVANCED || round.round_mode === ROUND.ROUND_MODE_MULTIPLAYER) {
        iGolfPinLocation = (await this.threePartyCourseService.pinLocation(+hole.number, round.igolf_course_id)) || [
          0, 0,
        ];
        delete hole.pin_location;
        hole.pin_location = iGolfPinLocation[0]; // lon, lat
      } else {
        hole.pin_location = null;
      }
    } else {
      iGolfPinLocation = [hole.pin_location[1], hole.pin_location[0]]; // lon, lat
      hole.pin_location = iGolfPinLocation;
    }
    // CREATES A NEW HOLE
    const newHolePlayer: any = await this.createHole(hole, round, forceMapCourse);

    const generated_by = data['generated_by'];

    const isSamSungGear = isGear(generated_by) && !forceMapCourse;

    list_hole.push(newHolePlayer.id);

    // FOR GEAR
    let greenFront = null;
    const pinLocation = hole.pin_location; //[iGolfPinLocation[1], iGolfPinLocation[0]];
    let putts = null;

    if (hole['strokes']) {
      holePlayedIdModify.push(newHolePlayer.id);
      if (isSamSungGear) {
        greenFront = await this.threePartyCourseService.greenFrontFor(newHolePlayer.name, round.igolf_course_id);
      }
      putts = this.listPutts(hole['strokes']);
    }
    if (hole['strokes']) {
      this.logger.debug(` ============ START CREATE STROKES FOR HOLE ${newHolePlayer.id}  ============ `);

      await Promise.all(
        hole['strokes'].map(async (stroke) => {
          if (isSamSungGear) {
            stroke['coords'] = coordPutts(pinLocation, greenFront, stroke, putts);
          }
          try {
            await this.createStroke(stroke, round.id, newHolePlayer.id, pinLocation);
            totalStrokes++;
          } catch (e) {
            console.error(`ERROR CREATE STROKE`);
            console.error(e);
          }
        })
      );
      this.logger.debug(` ============ DONE CREATE STROKES FOR HOLE ${newHolePlayer.id}  ============ `);

      // // creates strokes
      // for (const stroke of hole['strokes']) {
      //   if (isSamSungGear) {
      //     stroke['coords'] = coordPutts(pinLocation, greenFront, stroke, putts);
      //   }
      //   await this.createStroke(stroke, round.id, newHolePlayer.id, pinLocation);
      //   totalStrokes++;
      // }
    }
    if (data['round_mode'] == ROUND.ROUND_MODE_MULTIPLAYER) {
      await this.createPlayerScoreRound(hole, newHolePlayer, round.id);
    }
    return totalStrokes;
  }

  private async createPlayerScoreRound(hole: any, new_hole: any, roundId) {
    if (hole['player_score']) {
      await Promise.all(
        hole['player_score'].map(async (player_score) => {
          try {
            await this.createPlayerScore(new_hole, player_score, roundId);
          } catch (e) {
            console.log(`ERROR CREATE PLAYER SCORE`);
            console.error(e);
          }
        })
      );
      // for (const player_score of hole['player_score']) {
      //   await this.createPlayerScore(new_hole, player_score, roundId);
      // }
    }
  }

  private async createRoundPlayer(data: any, roundId) {
    if (data['players']) {
      await Promise.all(
        data['players'].map(async (player) => {
          this.logger.debug(`CREATE PLAYER ${player.player_name}`);
          await this.createPlayer(player, roundId);
        })
      );
      // for (const player of data['players']) {
      //   this.logger.debug(`CREATE PLAYER ${player.player_name}`);
      //   await this.createPlayer(player, roundId);
      // }
    }
  }

  listPutts(strokes) {
    const putts = strokes?.filter((stroke: any) => includeStr(stroke['starting_lie'], 'green'));
    return putts;
  }

  //
  // Initializes a round
  //
  async initializeRound(data: any, forceMapCourse = null) {
    let completed = !!data['completed'];
    let inprogress = data['inprogress'];
    let isSimpleRound = false;
    let isParOut,
      isParIn = false;
    if (data['round_mode'] == ROUND.ROUND_MODE_SIMPLE) {
      isSimpleRound = true;
      completed = true;
      inprogress = false;
      data['total_score'] = 0;
      if (data['front_9_score']) {
        data['total_score'] += +data['front_9_score'];
        isParOut = true;
      }
      if (data['back_9_score']) {
        data['total_score'] += +data['back_9_score'];
        isParIn = true;
      }
    }
    // let [course_yards, course_par] = [0, 0];
    let course_yards = data['course_yards'] ? data['course_yards'] : 0;
    let course_par = data['course_par'] ? data['course_par'] : 0;
    try {
      if (!course_par || !course_yards) {
        [course_yards, course_par] = await Promise.all([
          this.threePartyCourseService.totalYardage(
            data['tee_name'],
            data['igolf_course_id'],
            isSimpleRound,
            isParOut,
            isParIn
          ),
          this.threePartyCourseService.totalPar(
            data['tee_name'],
            data['igolf_course_id'],
            isSimpleRound,
            isParOut,
            isParIn
          ),
        ]);
      }
    } catch (error) {
      console.log(`GET TOTAL YARD, PAR IGOLF FAIL: ${error.message}`);
    }

    const isValidDatePlayedOn = moment(data['played_on']).isValid();
    if (!isValidDatePlayedOn) {
      data['played_on'] = new Date();
    }
    const service = await this.threePartyCourseService.getCacheService();
    let mapId = service === 'IGOLF' ? MAP_TYPE.MAP_TYPE_IGOLF : MAP_TYPE.MAP_TYPE_GLX;
    if (forceMapCourse) {
      switch (forceMapCourse) {
        case 'IGOLF':
          mapId = MAP_TYPE.MAP_TYPE_IGOLF;
          break;
        case 'TAG':
          mapId = MAP_TYPE.MAP_TYPE_TAG;
          break;
      }
    }
    let round_data: any = {
      user_id: data['user_id'],
      duration: data['duration'],
      generated_by: data['generated_by'],
      total_score: data['total_score'],
      event_id: data['event_id'],
      lowest_heartrate: data['lowest_heartrate'],
      average_heartrate: data['avg_heartrate'],
      peak_heartrate: data['peak_heartrate'],
      calories_burned: data['calories_burned'],
      temperature: data['temperature'],
      ground_conditions: data['ground_conditions'],
      course_yards,
      course_par,
      completed: completed,
      played_on: data['played_on'] || new Date(),
      round_type: data['round_type'] || '',
      steps_count: data['steps_count'] || 0,
      holes_completed: JSON.stringify(data['holes']) || {},
      updated_stroke: true,
      updated_missing_coords: true,
      inprogress: inprogress,
      round_mode: data['round_mode'] || ROUND.ROUND_MODE_ADVANCED,
      map_id: mapId,
      team: data['team'] || null,
      multiplayer_game_type: data['multiplayer_game_type'] || null,
      device_token: data['device_token'] || null,
      // Simple round
      number_of_holes_played: data['number_of_holes_played'] || 0,
      front_9_score: data['front_9_score'] || '',
      back_9_score: data['back_9_score'] || '',
      eighteen_holes_score: data['eighteen_holes_score'] || '',
      play_service: data['play_service'] || '',
      play_client: data['play_client'] || '',
      source_type: data['source_type'] || '',
      ghin_course_id: data['ghin_course_id'] || '',
      ghin_course_name: data['ghin_course_name'] || '',
      ghin_tee_set_id: data['ghin_tee_set_id'] || '',
      ghin_tee_set_name: data['ghin_tee_set_name'] || '',
      // Arccos
      arccos_round_id: data['arccos_round_id'] || null,
      created_at: new Date(),
      updated_at: new Date(),
    };

    // if (data['map_id'] == MAP_TYPE.MAP_TYPE_IGOLF) {
    round_data = {
      ...round_data,
      igolf_course_id: data['igolf_course_id'],
      tee_name: data['tee_name'],
      user_timezone: data['user_timezone'],
      course_name: data['course_name'],
    };
    // }
    round_data = deleteValueBlank(round_data);
    // Save round
    return await this.roundRepository.save(this.roundRepository.create(round_data));
  }

  //
  // create a hole for the round
  //
  async createHole(hole: any, round, forceMapCourse = null) {
    // round.holes_played.create!( create_hole_attributes(hole) )
    const holeAttr = await this.roundAuditUtilService.createHoleAttributes(hole, round, forceMapCourse);
    return await this.holePlayerService.create(holeAttr);
  }

  //
  // creates a new stroke for the given hole
  //
  async createStroke(stroke, roundId, holePlayerId, pinLocation) {
    // new_hole.strokes_played.create!( create_stroke_attributes(stroke) )
    this.logger.debug(`CREATE STROKE FOR HOLE ${holePlayerId}`);
    const strokeAttr = await this.roundAuditUtilService.createStrokeAttributes(
      stroke,
      roundId,
      holePlayerId,
      pinLocation
    );
    return await this.strokesPlayerService.create(strokeAttr);
  }

  //
  // create a player_score for the round
  //
  async createPlayerScore(hole, player_score, roundId) {
    // hole.player_score.create!(
    //   this.roundAuditUtilService.create_player_score_attributes(hole, player_score)
    // );
    return await this.playerService.createPlayerScore(
      this.roundAuditUtilService.createPlayerScoreAttributes(hole, player_score, roundId)
    );
  }

  //
  // create a player  for the round
  //
  async createPlayer(player, roundId) {
    // round.player.create!(this.roundAuditUtilService.create_player_attributes(player));
    return await this.playerService.createPlayer(this.roundAuditUtilService.createPlayerAttributes(player, roundId));
  }

  // delete round when post score to ghin error
  async forceDeleteRound(roundId: number) {
    try {
      await this.roundRepository.delete({ id: roundId });
      await this.holePlayerService.deleteHolesPlayedByRoundId(roundId);
      return true;
    } catch (error) {
      console.error(`DELETE ROUND ERROR: ${error.message}`);
    }
  }
}
