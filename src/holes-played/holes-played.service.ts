import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import _ from 'lodash';
import { In, Repository } from 'typeorm';
import { ROUND } from 'src/rounds/round.const';
import { StrokesPlayedService } from 'src/strokes-played/strokes-played.service';
import { HolePlayedDto } from './dto/hole-played.dto';
import { HolePlayed } from './entities/hole-played.entity';

@Injectable()
export class HolesPlayedService {
  constructor(
    @InjectRepository(HolePlayed)
    private holePlayedRepo: Repository<HolePlayed>,
    private strokesPlayedService: StrokesPlayedService
  ) {}

  async create(holePlayedDto: HolePlayedDto) {
    return await this.holePlayedRepo.save(this.holePlayedRepo.create(holePlayedDto));
  }

  async find(option: any) {
    return this.holePlayedRepo.find(option);
  }
  async findRaw(option: any) {
    const query = this.holePlayedRepo.createQueryBuilder();
    query.where(option.where);
    query.select(option.select);
    return await query.getRawMany();
  }

  findAll() {
    return `This action returns all holesPlayed`;
  }

  findOne(id: number) {
    return `This action returns a #${id} holesPlayed`;
  }

  remove(id: number) {
    return `This action removes a #${id} holesPlayed`;
  }

  async strokesPlayed({ isCount = false, holePlayedId }) {
    if (isCount) {
      return await this.strokesPlayedService.countBy({ hole_played_id: holePlayedId });
    }
    return await this.strokesPlayedService.findBy({ hole_played_id: holePlayedId });
  }

  async getStrokesPlayedListHole(holePlayedIds: any, getClub = true, getStat = true) {
    return await this.strokesPlayedService.findRawBy({
      where: { hole_played_id: In(holePlayedIds) },
      select: ['stroke.id as id', 'stroke.hole_played_id as hole_played_id'],
      getClub,
      getStat,
    });
  }
  async getStrokesPlayedByRoundIds(roundIds: any) {
    return await this.strokesPlayedService.findRawBy({
      where: { round_id: In(roundIds) },
      select: ['stroke.id as id', 'hole_played_id'],
    });
  }
  async getHolesOfRoundIds(roundIds: any) {
    const query = this.holePlayedRepo.createQueryBuilder();
    query.where({ round_id: In(roundIds) });
    query.select(['round_id', 'id', 'score', 'shots_removed']);
    query.orderBy({ round_id: 'ASC' });
    return query.getRawMany();
  }

  async score(round_mode = ROUND.ROUND_MODE_ADVANCED, hole: HolePlayed) {
    let score = 0;
    if (round_mode == ROUND.ROUND_MODE_SIMPLE) return 0;
    if (
      round_mode == ROUND.ROUND_MODE_BASIC ||
      round_mode == ROUND.ROUND_MODE_MULTIPLAYER ||
      round_mode == ROUND.ROUND_MODE_CLASSIC
    ) {
      return +hole.score;
    } else {
      const countStrokesPlayed: any = await this.strokesPlayed({
        isCount: true,
        holePlayedId: hole.id,
      });
      score = countStrokesPlayed == 0 ? 0 : countStrokesPlayed - parseInt(hole.shots_removed?.toString() || '0', 10);
    }
    return score;
  }

  async getScoreListHole(round_mode: any, holes: any, roundId) {
    let lstScores = [];
    const holeIds = [];
    const shotsRemove = [];
    holes.forEach((hole: any) => {
      holeIds.push(hole['id']);
      shotsRemove[hole['id']] = +hole['shots_removed'];
    });
    if (round_mode == ROUND.ROUND_MODE_SIMPLE) {
      return 0;
    }
    if (
      round_mode == ROUND.ROUND_MODE_BASIC ||
      round_mode == ROUND.ROUND_MODE_MULTIPLAYER ||
      round_mode == ROUND.ROUND_MODE_CLASSIC
    ) {
      const query = this.holePlayedRepo.createQueryBuilder();
      query.where({ id: In(holeIds) });
      query.select(['id', 'score']);
      query.cache(`LIST_SCORE_HOLE_ROUND_${roundId}`, 100000);
      // lstScores = await this.holePlayedRepo.find({
      //   where: { id: In(holeIds) },
      //   select: ['id', 'score'],
      // });
      lstScores = await query.getRawMany();
      return lstScores;
    } else {
      let countStrokesPlayed: any = await this.getStrokesPlayedListHole(holeIds, false, false);
      countStrokesPlayed = _.groupBy(countStrokesPlayed, 'hole_played_id');
      const keyHoles = Object.keys(countStrokesPlayed);

      keyHoles.forEach((hole) => {
        lstScores.push({ id: hole, score: countStrokesPlayed[hole].length || 0 });
      });

      for (const s of lstScores) {
        s['score'] -= shotsRemove[s['id']];
      }
      return lstScores;
    }
  }
  async findHolesInRounds(roundIds) {
    const queryBuilder = this.holePlayedRepo.createQueryBuilder();
    queryBuilder.where({ round_id: In(roundIds) });
    queryBuilder.select([
      'id',
      'round_id',
      'score',
      'shots_removed',
      'gr_stats',
      'fw_stats',
      'bunker_hit',
      'penalty_hit',
      'putts_number',
      'par',
    ]);
    return await queryBuilder.getRawMany();
  }

  async deleteHolesPlayedByRoundId(roundId: number) {
    return this.holePlayedRepo.delete({ round_id: roundId });
  }
}
