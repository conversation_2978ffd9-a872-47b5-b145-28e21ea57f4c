import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { Point } from 'geojson';
import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { PlayerScore } from 'src/players/entities/player-score.entity';
import { Round } from 'src/rounds/entities/round.entity';
import { StrokePlayed } from 'src/strokes-played/entities/stroke-played.entity';
import { StrokeStat } from 'src/strokes-stats/entities/stroke-stat.entity';

@Entity({ name: 'holes_played' })
export class HolePlayed {
  @PrimaryGeneratedColumn()
  @ApiProperty({ example: 1 })
  @Index()
  id: number;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  round_id: number;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  score: number;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  par: number;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  name: string;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  lowest_heartrate: number;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  average_heartrate: number;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  peak_heartrate: number;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  shots_removed: number;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  calories_burned: number;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  image_url: string;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  stroke_index: number;

  @CreateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  created_at: Date;

  @UpdateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  updated_at: Date;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  handicap_index: number;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  hole_id: number;

  @Column({
    type: 'geography',
    spatialFeatureType: 'Point',
    srid: 4326,
    nullable: true,
  })
  @Transform(({ value }) => value?.coordinates?.reverse())
  pin_location: Point;

  @Column({
    type: 'geography',
    spatialFeatureType: 'Point',
    srid: 4326,
    nullable: true,
  })
  @Transform(({ value }) => value?.coordinates?.reverse())
  fairway_center: Point;

  @Column({
    type: 'geography',
    spatialFeatureType: 'Point',
    srid: 4326,
    nullable: true,
  })
  @Transform(({ value }) => value?.coordinates?.reverse())
  tee_center: Point;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  scorecard_id: number;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  yards: number;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  svg_image_url: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  image_version: string;

  @Column({ type: 'timestamp' })
  @ApiProperty({ example: new Date().toISOString() })
  image_generated_at: Date;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  image_public_id: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  image_signature: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  shotless_svg_image_url: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  shotless_svg_image_version: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  shotless_svg_image_public_id: string;

  @Column({
    type: 'geography',
    spatialFeatureType: 'Point',
    srid: 4326,
    nullable: true,
  })
  @Transform(({ value }) => value?.coordinates?.reverse())
  green_center: Point;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  steps_count: number;

  @Column({ type: 'boolean' })
  @ApiProperty({ example: false })
  is_stroke_modify: boolean;

  @Column({ type: 'boolean' })
  @ApiProperty({ example: false })
  bunker_hit: boolean;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  putts_number: number;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  gr_stats: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  fw_stats: string;

  @Column({ type: 'boolean' })
  @ApiProperty({ example: false })
  penalty_hit: boolean;

  @OneToMany(() => StrokePlayed, (stroke) => stroke.hole_played, {
    eager: true,
  })
  strokes?: StrokePlayed;

  @OneToMany(() => StrokeStat, (stats) => stats.hole_played, {
    eager: true,
  })
  stats?: StrokeStat;

  @OneToMany(() => PlayerScore, (playerScore) => playerScore.hole_played, {
    eager: false,
  })
  playerScores?: PlayerScore;

  @OneToOne(() => Round, (round) => round.id, {
    eager: false,
  })
  @JoinColumn({ name: 'round_id', referencedColumnName: 'id' })
  round?: Round;
}
