export const formatGLXCourseListData = (apiData = []) => {
  return apiData.map((course) => ({
    id_course: course?.ID,
    active: 1,
    address1: course?.Address,
    address2: '',
    city: course?.City,
    id_country: 0,
    countryFull: '',
    countryShort: course?.Country,
    courseName: course.Name,
    email: '',
    gpsAvailable: 1,
    layoutHoles: course.Holes,
    layoutTotalHoles: course.Holes,
    layoutName: `${course.Holes} Hole`,
    otherState: course.State,
    zipCode: '',
    scorecardAvailable: 1,
    syncOutputAvailable: 1,
    vectorAvailable: 1,
    elevationAvailable: 0,
    conditionRating: 0,
    recommendRating: 0,
    latitude: course.Lat,
    longitude: course.Lon,
    teeTimesAvailable: 0,
    classification: '',
    id_language: 1,
    favoriteCourse: 0,
    ghinId: course?.gh_id && course?.gh_id !== '0' ? course?.gh_id : null,
    '1meterElevationAvailable': 0,
    '3metersElevationAvailable': 0,
    distance: 0,
  }));
};
export const formatGLXCourseDetail = (input) => {
  return {
    id_course: input?.id,
    active: 1,
    address1: input?.address,
    address2: '',
    city: input?.city,
    id_countryDefaultLanguage: 1,
    countryShort: input?.country,
    courseName: input?.name,
    dressCode: '',
    email: '',
    gpsAvailable: 1,
    layoutHoles: input?.holes,
    layoutTotalHoles: input?.holes,
    layoutName: `${input?.holes} Hole`,
    otherState: '',
    phone: '',
    seasonal: 0,
    stateShort: input?.state,
    url: '',
    zipCode: input?.zip,
    courseCreated: '',
    courseModified: '',
    dayClosed: '',
    directorName: '',
    fax: '',
    fivesome: 1,
    gpsModified: '',
    professionalName: '',
    proShopOpen: '',
    proShopClose: '',
    scorecardAvailable: 1,
    scorecardModified: '',
    seasonEnd: 0,
    seasonStart: 0,
    superintendentName: '',
    syncOutputAvailable: 1,
    syncOutputModified: '',
    vectorOutputModified: '',
    vectorAvailable: 1,
    vectorModified: '',
    weekend9: 0,
    weekend18: 0,
    weekday9: 0,
    weekday18: 0,
    twilight: 0,
    recommendRating: 0,
    latitude: input?.latitude,
    longitude: input?.longitude,
    id_language: 1,
    favoriteCourse: 0,
    class: 'private',
    ghinId: input?.gh_id && input?.gh_id !== '0' ? input?.gh_id : null,
    Status: 1,
    ErrorMessage: 'Action successful',
  };
};
