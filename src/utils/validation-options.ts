import { HttpException, HttpStatus, ValidationError, ValidationPipeOptions } from '@nestjs/common';

const validationOptions: ValidationPipeOptions = {
  transform: true,
  whitelist: true,
  errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
  exceptionFactory: (errors: ValidationError[]) => {
    return new HttpException(
      {
        status: HttpStatus.UNPROCESSABLE_ENTITY,
        errors: errors.reduce((accumulator, currentValue) => {
          if (currentValue.children.length > 0) {
            return {
              ...accumulator,
              [currentValue.children[0].property]: Object.values(currentValue.children[0].constraints),
            };
          }
          return {
            ...accumulator,
            [currentValue.property]: Object.values(currentValue.constraints),
          };
        }, {}),
      },
      HttpStatus.UNPROCESSABLE_ENTITY
    );
  },
};

export default validationOptions;
