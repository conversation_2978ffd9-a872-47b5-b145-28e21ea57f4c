import dotenv from 'dotenv';
import Redis from 'ioredis';

dotenv.config();

const host = process.env.REDIS_HOST;
const password = process.env.REDIS_PASSWORD;
const port = parseInt(process.env.REDIS_PORT, 10) || 6379;

export const redisConfig: any = {
  host,
  password,
  port,
  tls: port === 6380 ? {} : undefined,
  connectTimeout: 100000,
  disconnectTimeout: 20000,
  keyPrefix: 'MYTM_OC',
};

export const redisConnectionString = `redis://${password}@${host}:${port}/0`;
const redisInstance = new Redis(redisConfig);

export default redisInstance;
