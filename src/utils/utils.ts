// import { Logger } from '@nestjs/common';
// import * as turf from '@turf/turf';
// import * as geolib from 'geolib';
// import _ from 'lodash';
import _, { isEmpty } from 'lodash';
import { isArray } from 'lodash';
import moment from 'moment';
import { SG_BASELINES } from 'src/users/entities/user.entity';
import { Stats } from './smart-golf/stats';
import { SmartGolfStatsCalculationsBearing } from './smart-golf/stats/calculations/bearing';
import { GeocoderCalculations } from './smart-golf/stats/calculations/geocoder';

// const logger = new Logger('UTILS');
export const deleteValueBlank = (data: any, removeEmpty = true) => {
  const keys = Object.keys(data);
  for (const k of keys) {
    if (data[k] == undefined || data[k] == null) {
      delete data[k];
      continue;
    }
    if (removeEmpty && data[k] === '') {
      delete data[k];
      continue;
    }
  }
  return data;
};
export const range = (min, max) => {
  const list = [];
  for (let i = min; i <= max; i++) {
    list.push(i);
  }
  return list;
};
export const isPointEmpty = (arr: any) => {
  if (!arr) {
    return true;
  }
  if (isArray(arr)) {
    if (arr.length != 2) {
      return true;
    }
    if (!arr[0] || !arr[1]) {
      return true;
    }
  }
  return false;
};
export const getDistancePoints = (from: any, to: any, unit?: any) => {
  let fromPoint: any;
  let toPoint: any;
  if (isArray(from)) {
    fromPoint = convertArrToLocation(from);
    toPoint = convertArrToLocation(to);
  }
  unit = unit ? unit : Stats.MILES_TO_METERS;
  return GeocoderCalculations.distanceBetween(fromPoint, toPoint) * unit;
};

export const getBearingBetweenPoints = (from: any, to: any) => {
  let originPoint: any;
  let destPoint: any;
  if (isArray(from)) {
    originPoint = convertArrToLocation(from);
    destPoint = convertArrToLocation(to);
  }
  return GeocoderCalculations.bearingBetween(originPoint, destPoint);
};
export const isGear = (generatedBy: any) => {
  if (!generatedBy) {
    return false;
  }
  generatedBy = generatedBy.toString().trim().toLowerCase();
  if (generatedBy.includes('ios')) {
    return false;
  }
  if (generatedBy.includes('android')) {
    return false;
  }
  return true;
};
export const convertArrToLocation = (point: any) => {
  // let result: any;
  // logger.debug(`CONVERT ARR TO LOCATION`);
  // logger.debug({ point });
  if (!point) {
    return null;
  }
  // if (Math.abs(+point[0]) <= 90) {
  //   result = { lat: +point[0], lon: +point[1] };
  // } else {
  // result = { lat: +point[1], lon: +point[0] };
  // }
  // return { lat: +point[0], lon: +point[1] };
  return { lat: +point[1], lon: +point[0] };
  // return result;
};
export const buildCoordsToPointObject = (arrPoint: any) => {
  if (isArray(arrPoint)) {
    return arrPoint.map((arr) => {
      return convertArrToLocation(arr);
    });
  } else {
    return null;
  }
};

export const getCenterPoint = (points: any) => {
  const firstPoint = points[0];
  const lastPoint = points[points.length - 1];
  if (firstPoint[0] != lastPoint[0] || firstPoint[1] != lastPoint[1]) {
    points.push(points[0]);
  }
  const point = centroid(points);
  return point || null;
  // return [+point.latitude, +point.longitude];
};
export const getBounds = (points) => {
  if (Array.isArray(points) === false || points.length === 0) {
    throw new Error('No points were given.');
  }
  return points.reduce(
    function (stats, point) {
      let latitude = 0;
      let longitude = 0;
      if (isArray(point)) {
        point = convertArrToLocation(point);
      }
      latitude = point.lat || point.latitude;
      longitude = point.lon || point.longitude;

      return {
        maxLat: Math.max(latitude, stats.maxLat),
        minLat: Math.min(latitude, stats.minLat),
        maxLng: Math.max(longitude, stats.maxLng),
        minLng: Math.min(longitude, stats.minLng),
      };
    },
    { maxLat: -Infinity, minLat: Infinity, maxLng: -Infinity, minLng: Infinity }
  );
};
export const coordsEqual = (point1, point2) => {
  return point1[0] == point2[0] && point1[1] == point2[1];
};
export const coordPutts = (pin_location, greenFront, stroke, putts) => {
  if (
    includeStr(stroke['starting_lie'], 'green') &&
    stroke.coords &&
    stroke?.coords[0] == 0 &&
    stroke?.coords[1] == 0
  ) {
    const bear_new_location = SmartGolfStatsCalculationsBearing.get_bearing(pin_location, greenFront);
    const nbPutters = putts.length;
    // calculate pinlocation move up 3 feet
    const lastDistance = 0.000568182;
    const newPinLocation = GeocoderCalculations.endpoint(pin_location, bear_new_location, lastDistance);
    //putter = 1
    if (nbPutters == 1) {
      return GeocoderCalculations.geographic_center([pin_location, greenFront]);
    }
    if (nbPutters > 1) {
      const putt_idx = putts.indexOf(stroke) + 1;
      const putt_x = greenFront[1] + (putt_idx * (newPinLocation[1] - greenFront[1])) / nbPutters;
      const putt_y = greenFront[0] + (putt_idx * (newPinLocation[0] - greenFront[0])) / nbPutters;
      if (putt_idx == nbPutters) {
        return [newPinLocation[1], newPinLocation[0]];
      }

      return [putt_x, putt_y];
    }
  } else {
    return stroke['coords'];
  }
};
// export const centroid = (coords) => {
//   const bounds = getBounds(coords);
//   const latitude = bounds.minLat + (bounds.maxLat - bounds.minLat) / 2;
//   const longitude = bounds.minLng + (bounds.maxLng - bounds.minLng) / 2;
//   return [latitude, longitude];
//   // return {
//   //   latitude: parseFloat(latitude),
//   //   longitude: parseFloat(longitude),
//   // };
// };

export const centroid = (polygon) => {
  let centroid_lat = 0.0;
  let centroid_lng = 0.0;
  let signed_area = 0.0;
  const firstPoint = polygon[0];
  const lastPoint = polygon[polygon.length - 1];
  if (firstPoint[0] != lastPoint[0] || firstPoint[1] != lastPoint[1]) {
    polygon.push(polygon[0]);
  }
  _.map(polygon, function (p0, i) {
    if (i + 1 < polygon.length) {
      const p1 = polygon[i + 1];
      const area = p0[0] * p1[1] - p1[0] * p0[1];
      signed_area += area;
      centroid_lng += (p0[0] + p1[0]) * area;
      centroid_lat += (p0[1] + p1[1]) * area;
    }
  });
  signed_area *= 0.5;
  centroid_lng /= 6.0 * signed_area;
  centroid_lat /= 6.0 * signed_area;
  const centroid = [centroid_lng, centroid_lat];
  return centroid;
};
export const includeStr = (source, str) => {
  if (!source) {
    return false;
  }
  return source.toLowerCase().trim().includes(str);
};

export const determine_units = (unit_selection) => {
  const unit = unit_selection?.toString().toLowerCase().trim();
  switch (unit) {
    case unit.includes('meter'):
      return 'meters';
    case unit.includes('feet'):
      return 'feet';
    default:
      return 'yards';
  }
};

export const sleeps = async (seconds = 1) => {
  return new Promise((r) => setTimeout(r, seconds * 1000));
};
export const coordsDistance = (start, end) => {
  const [xStart, yStart] = start;
  const [xEnd, yEnd] = end;

  const dx = xStart - xEnd;
  const dy = yStart - yEnd;
  const result = Math.sqrt(dx * dx + dy * dy);
  return result;
};
export const calculateAverageValues = (value1, value2) => {
  if (!isEmptyValue(value1) && !isEmptyValue(value2)) {
    return (value1 + value2) / 2;
  }
  if (isEmptyValue(value1) && !isEmptyValue(value2)) {
    return value2;
  }
  if (!isEmptyValue(value1) && isEmptyValue(value2)) {
    return value1;
  }
  return value1;
};
export const detectBaseline = (strokes_gained_baseline) => {
  if (strokes_gained_baseline == '' || isEmpty(strokes_gained_baseline)) {
    return 'scratch';
  }
  const sgBaseLineVal = Object.values(SG_BASELINES).map((k) => k.toString().toLowerCase());
  const sgBaseLineKeys = Object.keys(SG_BASELINES);
  const userBaseLineVal = sgBaseLineVal.find((val) => val.toLowerCase() == strokes_gained_baseline.toLowerCase());
  if (userBaseLineVal) {
    const indexVal = sgBaseLineVal.indexOf(userBaseLineVal);
    return sgBaseLineKeys[indexVal];
  }
  const userBaseLineKey = sgBaseLineKeys.find((key) => key.toLowerCase() == strokes_gained_baseline.toLowerCase());
  if (userBaseLineKey) {
    return userBaseLineKey;
  }
  return 'scratch';
};
export const isEmptyValue = (value) => {
  return value == null || isNaN(value);
};
export const percentRound = (ipt, precision) => {
  if (!precision) {
    precision = 0;
  }
  if (!Array.isArray(ipt)) {
    throw new Error('percentRound input should be an Array');
  }
  const iptPercents = ipt.slice();
  const length = ipt.length;
  const out = new Array(length);

  let total = 0;
  for (let i = length - 1; i >= 0; i--) {
    if (typeof iptPercents[i] === 'string') {
      iptPercents[i] = Number.parseFloat(iptPercents[i]);
    }
    total += iptPercents[i] * 1;
  }
  if (isNaN(total)) {
    throw new Error('percentRound invalid input');
  }

  if (total === 0) {
    out.fill(0);
  } else {
    const powPrecision = Math.pow(10, precision);
    const pow100 = 100 * powPrecision;
    let check100 = 0;
    for (let i = length - 1; i >= 0; i--) {
      iptPercents[i] = (100 * iptPercents[i]) / total;
      check100 += out[i] = Math.round(iptPercents[i] * powPrecision);
    }

    if (check100 !== pow100) {
      const totalDiff = check100 - pow100;
      const roundGrain = 1;
      let grainCount = Math.abs(totalDiff);
      const diffs = new Array(length);

      for (let i = 0; i < length; i++) {
        diffs[i] = Math.abs(out[i] - iptPercents[i] * powPrecision);
      }

      while (grainCount > 0) {
        let idx = 0;
        let maxDiff = diffs[0];
        for (let i = 1; i < length; i++) {
          if (maxDiff < diffs[i]) {
            // avoid negative result
            if (check100 > pow100 && out[i] - roundGrain < 0) {
              continue;
            }
            idx = i;
            maxDiff = diffs[i];
          }
        }
        if (check100 > pow100) {
          out[idx] -= roundGrain;
        } else {
          out[idx] += roundGrain;
        }
        diffs[idx] -= roundGrain;
        grainCount--;
      }
    }

    if (powPrecision > 1) {
      for (let i = 0; i < length; i++) {
        out[i] = out[i] / powPrecision;
      }
    }
  }

  return out;
};

export const parseStrDate = (strDate) => {
  const isFormatDDMMYY = moment(strDate, 'DD/MM/YYYY', true).isValid();
  if (isFormatDDMMYY) {
    return moment(strDate, 'DD/MM/YYYY').toDate();
  } else {
    return moment(strDate, 'YYYY-MM-DD').toDate();
  }
};

const editDistance = (s1, s2) => {
  s1 = s1.toLowerCase();
  s2 = s2.toLowerCase();

  const costs = [];
  for (let i = 0; i <= s1.length; i++) {
    let lastValue = i;
    for (let j = 0; j <= s2.length; j++) {
      if (i == 0) costs[j] = j;
      else {
        if (j > 0) {
          let newValue = costs[j - 1];
          if (s1.charAt(i - 1) != s2.charAt(j - 1)) newValue = Math.min(Math.min(newValue, lastValue), costs[j]) + 1;
          costs[j - 1] = lastValue;
          lastValue = newValue;
        }
      }
    }
    if (i > 0) costs[s2.length] = lastValue;
  }
  return costs[s2.length];
};
export const similarity = (s1, s2) => {
  let longer = s1;
  let shorter = s2;
  if (s1.length < s2.length) {
    longer = s2;
    shorter = s1;
  }
  const longerLength = longer.length;
  if (longerLength == 0) {
    return 1.0;
  }
  return (longerLength - editDistance(longer, shorter)) / parseFloat(longerLength);
};
export const isValidId = (id) => {
  return id && ![undefined, 'undefined', 'null', '(null)'].includes(id);
};
