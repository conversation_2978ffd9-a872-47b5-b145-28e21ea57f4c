import { HttpException, HttpStatus } from '@nestjs/common';

export const throwForbiddenError = () => {
  throw new HttpException(
    {
      status: HttpStatus.FORBIDDEN,
      message: 'Permission denied!',
    },
    HttpStatus.FORBIDDEN
  );
};
export const throwUnauthorizedError = (message) => {
  throw new HttpException(
    {
      status: HttpStatus.UNAUTHORIZED,
      message: message || 'Unauthorized!',
    },
    HttpStatus.UNAUTHORIZED
  );
};

export const throwBadRequestError = (message?: string) => {
  throw new HttpException(
    {
      status: HttpStatus.BAD_REQUEST,
      message,
      error: message,
    },
    HttpStatus.BAD_REQUEST
  );
};
export const throwServerError = (message?: string) => {
  throw new HttpException(
    {
      status: HttpStatus.INTERNAL_SERVER_ERROR,
      message,
    },
    HttpStatus.INTERNAL_SERVER_ERROR
  );
};

export const throwNotFoundError = (message?: string) => {
  throw new HttpException(
    {
      status: HttpStatus.NOT_FOUND,
      message,
      error: message,
    },
    HttpStatus.NOT_FOUND
  );
};

export const throwEntityUniqueError = (column: string) => {
  throw new HttpException(
    {
      status: HttpStatus.UNPROCESSABLE_ENTITY,
      errors: {
        [column]: 'existed',
      },
    },
    HttpStatus.UNPROCESSABLE_ENTITY
  );
};

export const throwUnprocessableEntityError = (message) => {
  throw new HttpException(
    {
      status: HttpStatus.UNPROCESSABLE_ENTITY,
      message,
      error: message,
    },
    HttpStatus.UNPROCESSABLE_ENTITY
  );
};
