export class ClubUtil {
  static normalize_type(type) {
    if (!type) {
      return '';
    }
    type = type.toString().toLowerCase();
    const woodsRegex = /(\d+)\-?f?w/i;
    const iRonsRegex = /(\d+)\-?i/i;
    const hybridsRegex = /(\d+)-?h/i;
    if (this.isClubDriver(type)) {
      return 'Driver';
    }
    if (this.isClubPutter(type)) {
      return 'Putter';
    }
    if (this.isClubSpecialist(type)) {
      return 'SPW';
    }
    if (this.isClubWoods(type)) {
      return this.getValueMatch(woodsRegex, type, 'FW');
    }
    if (this.isClubIrons(type)) {
      return this.getValueMatch(iRonsRegex, type, 'i');
    }
    if (this.isClubHybrids(type)) {
      return this.getValueMatch(hybridsRegex, type, 'H');
    }
    if (this.isClubNA(type)) {
      return 'N/A';
    }
    return type.toUpperCase();
  }
  static isClubDriver(type) {
    return ['d', 'driver'].includes(type);
  }
  static isClubPutter(type) {
    return ['p', 'putter'].includes(type);
  }
  static isClubSpecialist(type) {
    return type.includes('specialist');
  }
  static isClubWoods(type) {
    const woodsRegex = /(\d+)\-?f?w/i;
    return woodsRegex.test(type);
  }
  static isClubIrons(type) {
    const iRonsRegex = /(\d+)\-?i/i;
    return iRonsRegex.test(type);
  }
  static isClubHybrids(type) {
    const hybridsRegex = /(\d+)-?h/i;
    const recuseRegex = /(\d+)-?r/i;
    return hybridsRegex.test(type) || recuseRegex.test(type);
  }
  static isClubNA(type) {
    return ['na', 'unknown'].includes(type);
  }

  static getValueMatch(regex, str, suffix) {
    const match = str.match(regex);
    if (match) {
      return match[1] + suffix;
    } else {
      return '';
    }
  }
}
