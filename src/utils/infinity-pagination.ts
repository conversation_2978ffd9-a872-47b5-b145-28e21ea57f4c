import { isEmpty } from 'lodash';
import { IPaginationOptions } from './types/pagination-options';

export const infinityPagination = <T>(data: T[], options: IPaginationOptions) => {
  return {
    data,
    hasNextPage: data.length === options.limit,
  };
};

export const transformDataPaging = (data: any, link?: string, extraLinkQueries?: any) => {
  data['total_count'] = data?.meta['totalItems'];
  data['page_count'] = data?.meta['totalPages'];
  data['page'] = data?.meta['currentPage'];
  data['per_page'] = data?.meta['itemsPerPage'];
  data['data'] = data['items'];
  if (link) {
    data['links'] = {
      self: `${process.env.BACKEND_DOMAIN}${link}?page=${data.page}&${new URLSearchParams(
        extraLinkQueries
      ).toString()}`,
      first: `${process.env.BACKEND_DOMAIN}${link}?page=1&${new URLSearchParams(extraLinkQueries).toString()}`,
      last: `${process.env.BACKEND_DOMAIN}${link}?page=${data.page_count}&${new URLSearchParams(
        extraLinkQueries
      ).toString()}`,
      next: `${process.env.BACKEND_DOMAIN}${link}?page=${data.page + 1}&${new URLSearchParams(
        extraLinkQueries
      ).toString()}`,
    };
    if (data.page === data.page_count) {
      delete data['links']['next'];
    }
  }
  delete data['meta'];
  if (!data['items'] || isEmpty(data['items'])) {
    return { data: [] };
  }

  delete data['items'];
  return data;
};
export const getOptionsPaging = (page: any, limit: any) => {
  page = getDefaultNumber(page, 1);
  limit = getDefaultNumber(limit, 10);
  if (limit > 100) {
    limit = 100;
  }
  return { page, limit };
};
const getDefaultNumber = (value: any, defaultValue: any) => {
  if (value) {
    value = parseInt(value, 10);
    if (isNaN(value)) {
      value = defaultValue;
    }
  } else {
    value = defaultValue;
  }
  return value;
};
