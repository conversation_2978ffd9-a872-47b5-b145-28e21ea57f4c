import { Point } from 'geoj<PERSON>';
import { isArray } from 'lodash';
import { isPointEmpty } from '../utils';

export class Coordinates {
  // extend ActiveSupport::Concern

  static coordinatesMissing(coordinates: any) {
    // return true if coordinates.blank?
    // coordinates = convert_to_rgeo(coordinates) if coordinates.is_a? Array
    // coordinates.x.zero?           ||
    //   coordinates.y.zero?         ||
    //   coordinates.x == -20        ||
    //   coordinates.y == 20
    // console.log(`coordinates_missing....`);
    // console.log({ coordinates });
    if (isPointEmpty(coordinates)) {
      return true;
    }
    if (isArray(coordinates)) {
      coordinates = this.convert_to_rgeo(coordinates);
    }

    return [0, -20].includes(+coordinates?.coordinates[0]) || [0, 20].includes(+coordinates?.coordinates[1]);
  }

  pin_location_missing(pin) {
    // pin.blank?            ||
    //   pin.try(:x).blank?  ||
    //   pin.try(:y).blank?  ||
    //   pin.x.zero?         ||
    //   pin.y.zero?

    return isPointEmpty(pin);
  }

  static convert_to_rgeo(coordinates) {
    if (isArray(coordinates)) {
      const pointObject: Point = {
        type: 'Point',
        coordinates: [coordinates[1], coordinates[0]],
      };
      return pointObject;
    }
  }

  convert_to_array(coords) {
    if (isPointEmpty(coords)) {
      return null;
    }
    if (isArray(coords)) {
      return coords;
    }
    if (coords['coordinates']) {
      return coords['coordinates'];
    }
    console.log({ coords });

    return [coords[1], coords[0]];
  }

  factory() {
    // @factory ||= ::RGeo::Geos.factory(srid: 4326)
  }

  spherical_factory() {
    // @spherical_factory ||= ::RGeo::Geographic.spherical_factory(srid: 4326)
  }
}
