import { Logger } from '@nestjs/common';
import _ from 'lodash';
import { In, Repository } from 'typeorm';
import { Club } from 'src/clubs/entities/club.entity';
import { HolePlayed } from 'src/holes-played/entities/hole-played.entity';
import { IGolfService } from 'src/igolf/igolf.service';
import { Round } from 'src/rounds/entities/round.entity';
import { RoundService } from 'src/rounds/rounds.service';
import { StrokesPlayedService } from 'src/strokes-played/strokes-played.service';
import { StrokeStat } from 'src/strokes-stats/entities/stroke-stat.entity';
import { StrokeAutoMove } from '../stroke-auto-move';
import { isPointEmpty } from '../utils';
import { Coordinates } from './coordinates';

enum ROUND_STATUS {
  open = 0,
  completed = 1,
  deleted = 2,
}
export class StrokeRollup {
  // extend self
  round: any;
  strokeStatRepo: Repository<StrokeStat>;
  roundRepo: Repository<Round>;
  clubRepo: Repository<Club>;
  holePlayedRepo: Repository<HolePlayed>;
  strokePlayerService: StrokesPlayedService;
  iGolfService: IGolfService;
  roundService: RoundService;
  private readonly logger = new Logger(StrokeRollup.name);
  constructor(
    round: any,
    roundRepo: Repository<Round>,
    strokeStatRepo: Repository<StrokeStat>,
    clubRepo: Repository<Club>,
    holePlayedRepo: Repository<HolePlayed>,
    strokePlayerService: StrokesPlayedService,
    iGolfService: IGolfService,
    roundService?: RoundService
  ) {
    this.round = round;
    this.strokeStatRepo = strokeStatRepo;
    this.roundRepo = roundRepo;
    this.clubRepo = clubRepo;
    this.holePlayedRepo = holePlayedRepo;
    this.strokePlayerService = strokePlayerService;
    this.iGolfService = iGolfService;
    this.roundService = roundService;
  }

  getNextStrokeLandingCoords(stroke, next_stroke, hole_played) {
    if (isPointEmpty(stroke)) {
      return [0, 0];
    }
    if (!next_stroke) {
      return hole_played?.pin_location?.coordinates;
    }
    const coords = next_stroke?.coords?.coordinates;
    if (coords && coords[0]) {
      return coords;
    } else {
      return hole_played?.pin_location?.coordinates;
    }
  }

  async iGolfGenerateStats(round, strokes) {
    this.round = round;
    this.logger.log(`IGOLF GENERATE STATS.......`);
    const stroke_stat_ids = [];
    const allHolesSet = new Set();
    if (strokes && strokes.length > 0) {
      strokes.forEach((stroke) => allHolesSet.add(stroke.hole_played_id));
    }
    const allHoles = Array.from(allHolesSet);
    this.logger.debug(`HOlES ${allHoles}`);

    const cache = [];
    // initCacheHole
    await Promise.all(
      allHoles.map(async (holeId) => {
        const holePlayed = await this.getHolePlayedById({ hole_played_id: holeId });
        if (holePlayed) {
          const pin_location = holePlayed?.pin_location?.coordinates;
          this.logger.debug(`STROKE PLAYER PIN LOCATION: ${pin_location}`);
          holePlayed['igolf_course_id'] = round.igolf_course_id;
          const allStrokes = this.getAllStrokesOfHole(strokes, holePlayed.id);

          const lstClubOfHole = await this.getClubIds(allStrokes);
          cache.push({ hole_id: holeId, hole_played: holePlayed, lstClubOfHole, allStrokes });
          await this.updateStrokeMissingCoords(allStrokes, holePlayed);
        }
      })
    );
    const userSkillLevel = await this.strokePlayerService.getUserSkillLevel(round.user_id);
    let totalStrokeStatsCreated = 0;

    await Promise.all(
      strokes.map(async (stroke) => {
        const hole_cache = cache.find((hole) => hole.hole_id == stroke.hole_played_id);
        if (hole_cache) {
          const holePlayed = hole_cache['hole_played'];
          const allStrokes = hole_cache['allStrokes'];
          const lstClubOfHole = hole_cache['lstClubOfHole'];
          let club_id = null;
          let strokeClub = null;
          if (stroke.club_id && stroke.club_id != 'penalty') {
            club_id = stroke.club_id;
            strokeClub = lstClubOfHole.find((c) => c.id == club_id);
            if (!strokeClub) {
              club_id = null;
            }
          }
          const nextStroke = this.strokePlayerService.generateNextStroke(allStrokes, stroke);

          const sand_saved = this.strokePlayerService.generateSandSave(stroke, allStrokes, holePlayed);
          const nextStrokeLanding = this.getNextStrokeLandingCoords(stroke, nextStroke, holePlayed);
          const strokeXY = this.strokePlayerService.xy(stroke);
          // let stat = null;
          const shotsDistance = await this.buildShotDistances(allStrokes, nextStroke, stroke, holePlayed);
          const coords = stroke?.coords?.coordinates;
          const latitude = coords ? coords[1] : 0;
          const longitude = coords ? coords[0] : 0;
          const dataStat: any = {
            // # track associations
            ...this.buildTrackAssociations(round),
            // # what type of club was used for this shot
            club_id: club_id,
            club_type: strokeClub ? strokeClub.club_type : null,
            stroke_played_id: stroke.id,
            // # categorize where the shot/stroke is and landed
            ...this.buildCategorizeShot(allStrokes, nextStroke, stroke, holePlayed),
            // # ball compared to pin
            ...this.buildBallCompared(allStrokes, nextStroke, stroke, holePlayed),
            // # lie conditions
            ...this.buildLieConditions(nextStroke, stroke),
            // # shot distances - all in meters
            ...shotsDistance,
            // # type of shot as it starts and as it ends
            ...this.buildTypeOfShot(nextStroke, stroke),
            ...(await this.buildStrokeGain(
              allStrokes,
              nextStroke,
              stroke,
              round.user_id,
              holePlayed,
              userSkillLevel,
              shotsDistance
            )),
            // # doglegs
            ...this.buildDogLegs(stroke, nextStroke, holePlayed),
            // # hole information
            ...this.buildHoleInformation(holePlayed),
            sand_saved_opportunity: sand_saved['opportunity'],
            sand_saved: sand_saved['saved'],
            // # shot location
            latitude: latitude, // stroke.coords.y,
            longitude: longitude, // stroke.coords.x,
            landed_latitude: nextStrokeLanding[1],
            landed_longitude: nextStrokeLanding[0],
            ...this.buildEndsInHole(allStrokes, stroke),

            // # stat specific
            fairways_in_regulation: this.strokePlayerService.generateFairwaysInRegulation(nextStroke, holePlayed),
            // fairway_distance_from_center: await this.strokePlayerService.shot_distance_from_center(
            //   nextStroke,
            //   stroke,
            //   allStrokes,
            //   holePlayed
            // ),
            // fairway_location_from_center: await this.strokePlayerService.shot_direction(stroke, nextStroke, holePlayed),
            // # for graphing
            x_axis: strokeXY[0], //#stroke.x,
            y_axis: strokeXY[1], //#stroke.y,
            z_axis: 0.0,

            // # using for debug purposes
            stroke_ordinal: +stroke.ordinal,
            // # when the stroke took place
            recorded_on: stroke.timestamp || new Date(),

            // # green calculations
            ...(await this.buildGreenCalculations(allStrokes, nextStroke, stroke, holePlayed)),

            pin_location: [holePlayed?.pin_location?.coordinates[1], holePlayed?.pin_location?.coordinates[0]],
            created_at: new Date(),
            updated_at: new Date(),
          };

          try {
            this.strokeStatRepo.save(this.strokeStatRepo.create(dataStat)).then(async (result) => {
              if (result) {
                totalStrokeStatsCreated++;
              }
              this.logger.debug(`TOTAL STROKE STATS CREATED: ${totalStrokeStatsCreated} / ${strokes.length}`);

              if (totalStrokeStatsCreated == strokes.length) {
                this.logger.debug(`UPDATE STATS COMPLETED = TRUE`);
                this.logger.debug(`START CALCULATE ROUND DRIVING DISPERSION....`);
                try {
                  this.roundService.triggerCalculateDrivingDispersion({ roundId: round.id });
                  this.roundService.triggerCalculateAvgScore({ userId: round.user_id });
                } catch (error) {
                  console.log(`ADD QUEUE IGOLF_ROUND_DRIVING_DISPERSION FAIL`);
                  console.log(error);
                }

                await this.roundRepo.update({ id: round.id }, { stats_completed: true });
              }
            });
          } catch (error) {
            this.logger.error(`ERROR CREATE STATS FOR STROKE: ${stroke.id}`);
            this.logger.error(error.message);
          }
        }
      })
    );

    if (strokes?.length == 0 && round?.completed && round.total_score > 0) {
      this.roundRepo.update({ id: round.id }, { stats_completed: true, driving_stat_complete: true });
    }
    this.logger.log(`STROKE_STAT_IDS ${stroke_stat_ids} `);

    return stroke_stat_ids;
  }

  private async updateStrokeMissingCoords(allStrokes: any, holePlayed: any) {
    for (const s of allStrokes) {
      if (Coordinates.coordinatesMissing(s?.coords?.coordinates) || s?.coords == null) {
        try {
          s.coords = {};
          const strokeAutoMove = new StrokeAutoMove(s, allStrokes, holePlayed, this.holePlayedRepo, this.iGolfService);
          s.coords['coordinates'] = await strokeAutoMove.get_coords();
          await this.strokePlayerService.update_stroke_coordinate(s.id, s.coords['coordinates']);
          //Update coord current stroke in allStroke
        } catch (error) {
          this.logger.error(error.message);
        }
      }
    }
  }

  private async getClubIds(allStrokes: any) {
    let lstClubId = [];
    let lstClubOfHole = [];
    for (const s of allStrokes) {
      lstClubId.push(s.club_id);
    }
    lstClubId = _.uniq(lstClubId);
    lstClubOfHole = await this.clubRepo.find({
      where: { id: In(lstClubId) },
      select: ['cdm_witb_id', 'club_type', 'id'],
    });
    return lstClubOfHole;
  }

  private getAllStrokesOfHole(strokes: any, hole_played_id: any) {
    let allStrokes = strokes?.filter((stroke) => stroke.hole_played_id == hole_played_id) || [];
    allStrokes = allStrokes.sort((a, b) => +a.ordinal - +b.ordinal);
    return allStrokes;
  }

  buildTrackAssociations(round: any) {
    return {
      course_id: null,
      facility_id: null,
      igolf_course_id: round.igolf_course_id,
      course_name: round.course_name,
      round_id: round.id,
      round_status: ROUND_STATUS.completed,
      round_played_on: round.played_on || new Date(),
      user_id: round.user_id,
      round_type: round.round_type,
    };
  }

  buildEndsInHole(allStrokes: any, stroke: any) {
    this.logger.log(`START BUILD ENDS IN HOLE`);
    return {
      // # helps with % holed in the putting graphs
      ends_in_hole: this.strokePlayerService.isLastStroke([...allStrokes], stroke),
      // # first valid stroke taken for it's hole
      first_stroke: this.strokePlayerService.isFirstStroke([...allStrokes], stroke),
    };
  }

  async buildGreenCalculations(allStrokes: any, next_stroke: any, stroke: any, holePlayed: any) {
    console.log(`START BUILD GREEN CALCULATIONS....`);
    return {
      greens_in_regulation: this.strokePlayerService.isGreensInRegulation([...allStrokes], holePlayed, stroke),
      green_map: await this.strokePlayerService.generateBuildGreenMapping([...allStrokes], holePlayed, stroke),
      green_distances: await this.strokePlayerService.generateGreenDistances(
        [...allStrokes],
        next_stroke,
        holePlayed,
        stroke
      ),
    };
  }
  buildHoleInformation(hole_played: any) {
    console.log(`START BUILD HOLE INFORMATION...`);
    return {
      hole_played_id: hole_played.id,
      hole_name: hole_played.name,
      holes_par: hole_played.par,
      holes_yardage: hole_played.yards,
      holes_handicap: hole_played.handicap_index || hole_played.stroke_index,
    };
  }
  buildDogLegs(stroke: any, next_stroke: any, holePlayed) {
    return {
      distance_to_dogleg: this.strokePlayerService.stat_distance_to_dogleg(stroke),
      distance_to_pin_with_dogleg: this.strokePlayerService.stat_dogleg_to_pin_distance(stroke),
      total_dogleg_distance: this.strokePlayerService.stat_total_dogleg_hole_distance(stroke),
      shot_distance_to_fairway_centerline:
        this.strokePlayerService.generate_shot_distance_to_fairway_centerline_in_meters(
          stroke,
          next_stroke,
          holePlayed
        ),
      bearing_to_dogleg: this.strokePlayerService.generate_bearing_to_dogleg(next_stroke, stroke),
    };
  }
  buildLieConditions(next_stroke: any, stroke: any) {
    console.log(`START BUILD LIE CONDITIONS...`);
    return {
      starting_lie: this.strokePlayerService.lie(stroke)?.toString()?.toLowerCase() || '',
      ending_lie: this.strokePlayerService.generateEndLie(next_stroke)?.toString()?.toLowerCase() || '',
    };
  }
  buildCategorizeShot(all_strokes: any, next_stroke: any, stroke: any, hole_played: any) {
    console.log(`START BUILD CATEGORIZE SHOT...`);

    return {
      long_tee: this.strokePlayerService.isLongTee([...all_strokes], hole_played, stroke),
      short_tee: this.strokePlayerService.isShort(stroke, hole_played),
      approach: this.strokePlayerService.isApproach(stroke, hole_played),
      putting: this.strokePlayerService.putting(stroke),
    };
  }
  buildBallCompared(all_strokes: any, next_stroke: any, stroke: any, hole_played: any) {
    console.log(`START BUILD BALL COMPARED...`);
    return {
      short: this.strokePlayerService.isShortOfGreen(next_stroke, [...all_strokes], stroke, hole_played),
      long: this.strokePlayerService.isLongOfGreen(next_stroke, [...all_strokes], stroke, hole_played),
      right: this.strokePlayerService.isRightOfCenter(next_stroke, [...all_strokes], stroke, hole_played),
      left: this.strokePlayerService.isLeftOfCenter(next_stroke, [...all_strokes], stroke, hole_played),
    };
  }
  buildTypeOfShot(next_stroke: any, stroke: any) {
    this.logger.log(`START BUILD TYPE OF SHOT...`);

    return {
      starting_as_penalty_shot: this.strokePlayerService.penalty(stroke),
      ending_as_penalty_shot: this.strokePlayerService.isStatNextStrokePenalty(next_stroke),
      starting_as_recovery_shot: this.strokePlayerService.recovery(stroke),
      ending_as_recovery_shot: this.strokePlayerService.isStatNextStrokeRecovery(next_stroke),
      starting_as_difficult_shot: this.strokePlayerService.difficult(stroke),
      ending_as_difficult_shot: this.strokePlayerService.isStatNextStrokeDifficult(next_stroke),
    };
  }
  async buildShotDistances(all_strokes, next_stroke, stroke, hole_played) {
    this.logger.log(`START BUILD SHOT DISTANCES...`);
    return {
      shot_distance: await this.strokePlayerService.generateStatDistanceShot(next_stroke, hole_played, stroke),
      starting_distance_to_pin: await this.strokePlayerService.generateStatDistanceToPin(
        next_stroke,
        hole_played,
        stroke
      ),
      ending_distance_to_pin: this.strokePlayerService.generateStatEndingDistanceToPin(
        next_stroke,
        [...all_strokes],
        stroke,
        hole_played
      ),
    };
  }
  async buildStrokeGain(all_strokes, next_stroke, stroke, user_id, hole_played, user_skill_level, shotsDistance) {
    this.logger.log(`START BUILD STROKE GAIN...`);

    const lstStrokeGain = ['', 'pga', 'five', 'ten', 'fifteen', 'twenty'];
    let dataStrokeGain = {};
    for (let sg of lstStrokeGain) {
      sg = sg == 'pga' ? 'pro' : sg;
      const keyStarting = sg == '' ? `starting_strokes_gained` : `starting_strokes_gained_${sg}`;
      const keyEnding = sg == '' ? `ending_strokes_gained` : `ending_strokes_gained_${sg}`;
      const keyStrokeGain = sg == '' ? `strokes_gained` : `strokes_gained_${sg}`;
      const options = {
        skill_level: sg,
        next_stroke,
        stroke,
        user_id,
        all_strokes,
        hole_played,
        user_skill_level,
        shotsDistance,
      };
      const [start, ending, strokeGain] = await Promise.all([
        this.strokePlayerService.getStartSg(options),
        this.strokePlayerService.getEndSg(options),
        this.strokePlayerService.getSg(options),
      ]);
      dataStrokeGain = {
        ...dataStrokeGain,
        [keyStarting]: start,
        [keyEnding]: ending,
        [keyStrokeGain]: strokeGain,
      };
    }
    return dataStrokeGain;
  }

  async updateDrivingDispersion(round, strokes, strokesStats) {
    const cache = [];
    //initCache
    const allHolesSet = new Set();
    if (strokes && strokes.length > 0) {
      strokes.forEach((stroke) => allHolesSet.add(stroke.hole_played_id));
    }
    const allHoles = Array.from(allHolesSet);

    // initCacheHole
    await Promise.all(
      allHoles.map(async (holeId) => {
        this.logger.log(`INIT HOLE ${holeId} CACHE....`);
        const holePlayed = await this.getHolePlayedById({ hole_played_id: holeId });
        if (holePlayed) {
          holePlayed['igolf_course_id'] = round.igolf_course_id;
          const allStrokes = this.getAllStrokesOfHole(strokes, holePlayed.id);
          cache.push({ hole_id: holeId, hole_played: holePlayed, allStrokes });
        }
      })
    );

    await Promise.all(
      strokes.map(async (stroke) => {
        const hole_cache = cache.find((hole) => hole.hole_id == stroke.hole_played_id);
        const holePlayed = hole_cache['hole_played'];
        if (holePlayed) {
          let allStrokes = hole_cache['allStrokes'];
          const stats = strokesStats.find((ss) => ss.stroke_played_id == stroke.id);
          let statsId = null;
          if (stats) {
            statsId = stats.id;
          }
          allStrokes = _.sortBy(allStrokes, 'ordinal');
          // await this.updateStrokeMissingCoords(allStrokes, holePlayed);
          try {
            const nextStroke = this.strokePlayerService.generateNextStroke(allStrokes, stroke);
            const fairway_distance_from_center = await this.strokePlayerService.shot_distance_from_center(
              nextStroke,
              stroke,
              allStrokes,
              holePlayed
            );

            let location = stats?.left ? 'left' : 'right';
            if (location == 'right') {
              location = stats?.right ? 'right' : 'center';
            }
            console.log({ location });

            // const fairway_location_from_center = await this.strokePlayerService.shot_direction(
            //   stroke,
            //   nextStroke,
            //   holePlayed
            // );
            const fairway_location_from_center = location;
            this.strokeStatRepo.update({ id: statsId }, { fairway_distance_from_center, fairway_location_from_center });
          } catch (error) {
            this.logger.error(`ERROR UPDATE STROKE STATS ${statsId}`);
            this.logger.error(error.message);
          }
        }
      })
    );
    await this.roundRepo.update({ id: round.id }, { driving_stat_complete: true, stats_completed: true });
    this.logger.log(`UPDATE_DRIVING_DISPERSION ROUND: ${round.id} DONE...`);
    return round.id;
  }
  async update_driving_dispersionV2(round, strokes, strokesStats) {
    const cache = [];
    for (const stroke of strokes) {
      let holePlayed = null;
      let allStrokes = null;
      const hole_cache = cache.find((hole) => hole.hole_id == stroke.hole_played_id);

      const stats = strokesStats.find((ss) => ss.stroke_played_id == stroke.id);
      let statsId = null;
      if (stats) {
        statsId = stats.id;
      }
      if (hole_cache != undefined) {
        console.log(`============LOAD CACHE==============`);
        holePlayed = hole_cache['hole_played'];
        allStrokes = this.getAllStrokesOfHole(strokes, holePlayed.id);
      } else {
        this.logger.log(`LOAD HOLE DATA AND CACHE....`);
        // stroke.reload
        holePlayed = await this.getHolePlayedById(stroke);
        if (!holePlayed) {
          this.logger.error(`NOTFOUND HOLE_PLAYED FOR STROKE: ${stroke?.id}`);
          continue;
        }
        holePlayed['igolf_course_id'] = round.igolf_course_id;
        allStrokes = this.getAllStrokesOfHole(strokes, holePlayed.id);

        cache.push({ hole_id: stroke.hole_played_id, hole_played: holePlayed });
      }
      allStrokes = _.sortBy(allStrokes, 'ordinal');
      // await this.updateStrokeMissingCoords(allStrokes, holePlayed);
      try {
        const nextStroke = this.strokePlayerService.generateNextStroke(allStrokes, stroke);
        const fairway_distance_from_center = await this.strokePlayerService.shot_distance_from_center(
          nextStroke,
          stroke,
          allStrokes,
          holePlayed
        );
        let location = stats.left ? 'left' : 'right';
        if (location == 'right') {
          location = stats.right ? 'right' : 'center';
        }

        // const fairway_location_from_center = await this.strokePlayerService.shot_direction(
        //   stroke,
        //   nextStroke,
        //   holePlayed
        // );
        const fairway_location_from_center = location;
        this.strokeStatRepo.update({ id: statsId }, { fairway_distance_from_center, fairway_location_from_center });
      } catch (error) {
        this.logger.error(`ERROR UPDATE STROKE STATS ${statsId}`);
        this.logger.error(error.message);
      }
    }
    await this.roundRepo.update({ id: round.id }, { driving_stat_complete: true, stats_completed: true });
    this.logger.log(`UPDATE_DRIVING_DISPERSION ROUND: ${round.id} DONE...`);
    return round.id;
    //     round_ids.uniq.each do |round_id|
    //       Round.find(round_id).update(:driving_stat_complete => true)
    //     end
    //     hole_played_ids.uniq.each do |hole_played_id|
    //       HolePlayed.find(hole_played_id).update(:is_stroke_modify => false)
    //     end
  }
  private async getHolePlayedById(stroke: any) {
    return await this.holePlayedRepo
      .createQueryBuilder()
      .where({ id: stroke.hole_played_id })
      .select([
        'id',
        'is_stroke_modify',
        'round_id',
        'par',
        'name',
        'hole_id',
        'ST_AsGeoJSON("tee_center")::json AS tee_center',
        'ST_AsGeoJSON("pin_location")::json AS pin_location',
        'yards',
        'stroke_index AS handicap_index',
      ])
      .getRawOne();
  }

  stat_update(list_stroke_stat) {
    console.log({ list_stroke_stat });

    // ActiveRecord::Base.transaction do
    //   holeStrokes = []
    //   round_ids = []
    //   hole_played_ids = []
    //   start_run_update = Time.now
    //   puts "BEGIN stat_update: #{(Time.now - start_run_update).inspect}" if !Rails.env.production?
    //   list_stroke_stat.each do |stat_id|
    //     begin
    //       stat = StrokeStat.find_by_id(stat_id.to_i)
    //       unless stat&.persisted?
    //         File.open("#{Rails.root}/log/stat_update_error_message.log", 'a') { |f| f.write("#{Time.now} - stat is not persisted - #{stat_id}\n") }
    //         next
    //       end
    //       #stroke = stroke_stat[:stroke]
    //       stroke = StrokePlayed.find_by_id(stat.stroke_played_id)
    //       holePlayed = stroke&.hole_played
    //       unless holePlayed.try(:id)
    //         File.open("#{Rails.root}/log/stat_update_error_message.log", 'a') { |f| f.write("#{Time.now} - holePlayed not found #{stat_id}\n") }
    //         next
    //       end
    //       # get all stroke of hole
    //       allStroke = holeStrokes[holePlayed.try(:id)]
    //       unless allStroke.present?
    //         allStroke ||= holePlayed.strokes_played.order("ordinal asc") || []
    //         holeStrokes[holePlayed.try(:id)] = allStroke
    //       end
    //       nextstroke = stroke.next_stroke(allstrokes: allStroke)#stroke_stat[:nextstroke]
    //       center                   = nextstroke.blank? ? nil : ::FairwayCenterline.new(stroke, nextstroke: nextstroke, holeplayed: holePlayed )
    //       # fairway distance from center
    //       fw_distance_from_center  =  center.try(:shot_distance_from_center)
    //       # fairway localtion from center
    //       fw_location_from_center  = center.try(:shot_direction)
    //       params_update = {
    //           fairway_distance_from_center: fw_distance_from_center,
    //           fairway_location_from_center: fw_location_from_center
    //       }
    //       stat.update(params_update)
    //       round_ids.push(stat.round_id)
    //       hole_played_ids.push(stat.hole_played_id)
    //       puts "TIME update stat for stroke id #{stat.stroke_played_id}: #{(Time.now - start_run_update).inspect}" if !Rails.env.production?
    //     rescue Exception => e
    //       Rails.logger.error "RoundStatJob stat_update #{e.message}"
    //       File.open("#{Rails.root}/log/stat_update_error_message.log", 'a') { |f| f.write("#{Time.now} - RoundStatJob stat_update #{stat_id} - #{e.message}\n") }
    //       next
    //     end
    //   end#each
    //   round_ids.uniq.each do |round_id|
    //     Round.find(round_id).update(:driving_stat_complete => true)
    //   end
    //   hole_played_ids.uniq.each do |hole_played_id|
    //     HolePlayed.find(hole_played_id).update(:is_stroke_modify => false)
    //   end
    //   puts "END stat_update: #{(Time.now - start_run_update).inspect}"
    // end
    // rescue Exception => e
    //   Rails.logger.error "RoundStatJob stat_update #{e.message}"
    //   File.open("#{Rails.root}/log/stat_update_error_message.log", 'a') { |f| f.write("#{Time.now} - RoundStatJob stat_update transction - #{e.message} - #{e.backtrace}\n") }
  }
}
