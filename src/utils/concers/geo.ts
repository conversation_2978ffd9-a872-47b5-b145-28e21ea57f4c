export class Geo {
  // extend ::ActiveSupport::Concern
  SRID = 4326;

  //
  // mercator factory
  //
  mercator_factory() {
    // @mercator_factory ||= ::RGeo::Geographic.simple_mercator_factory( srid: SRID )
  }

  //
  // simple factory
  //
  factory() {
    // @factory ||= ::RGeo::Geos.factory(srid: SRID)
  }

  //
  // generate an rgeo point
  //
  to_point(coords) {
    console.log(coords);
    // coords.map { |coord| factory.point( coord[0], coord[1] ) }
  }

  //
  // draw a line from point_x to point_y
  //
  to_line(starting, ending) {
    console.log(starting, ending);

    // factory.line(starting, ending)
  }

  //
  // generate a linear ring
  //
  to_linear_ring(points) {
    console.log(points);
    // factory.linear_ring( points )
  }

  //
  // generate an rgeo polygon
  //
  to_polygon(coords) {
    console.log(coords);

    // points  = to_point(coords)
    // ring    = to_linear_ring(points)
    // factory.polygon( ring )
  }

  //
  // get the centroid of a given polygon
  //
  get_centroid(polygon) {
    console.log(polygon);
    // polygon.try(:centroid)
  }

  //
  // convert coordinates to a RGEO polygon
  //
  convert_to_polygon(hole_number, lie) {
    // this.coordinates_for( hole_number, lie ).map do |coords|
    //   to_polygon( coords )
    // end
    return this.coordinates_for(hole_number, lie).map((coords) => this.to_polygon(coords));
  }

  //
  // get the centroid of a polygon
  //
  get_polygon_center(hole_number, lie) {
    const coords = this.convert_to_polygon(hole_number, lie)?.pop();
    return this.get_centroid(coords);
  }

  //
  // get the center coordinate for a specific hole number and lie
  //
  get_center_for(hole_number, lie) {
    const center_point: any = this.get_polygon_center(hole_number, lie.downcase);
    if (center_point) {
      return [center_point.y, center_point.x];
    }
    return []; //if center_point.blank? || center_point.try(:y).nil?
  }

  find_lie(hole_number, shot_coords) {
    console.log(hole_number, shot_coords);
    //   lies = get_geojson( hole_number ).select do |feature|
    //   coords = feature.fetch("geometry", {}).fetch("coordinates", [])
    //   to_polygon(coords.last).contains?(shot_coords)
    // end
    // lies = lies.map { |lie| lie.fetch("properties", {}).fetch("type", "") }.
    //   reject  { |lie| %w(tee-boundary trees).include?(lie) }.
    //   map     { |lie| lie =~ /hole-boundary/i ? "rough" : lie }
    // if lies.count > 1
    //   lies.delete('rough')
    //   lies = lies.sort
    // end
    // lies.last
    // rescue
    //   nil
  }

  //
  // Get a random point on a given shape's exterior ring
  //
  get_random_point_on_shape(shape) {
    console.log(shape);

    // rand_point = shape.points[ rand(shape.points.size) ]
    // [ rand_point.y, rand_point.x ]
  }

  //
  // Get a random point on a line string
  //
  get_midpoint(start_point, end_point) {
    console.log(start_point, end_point);

    // ::Geocoder::Calculations.geographic_center([start_point, end_point])
  }

  //
  // Get the outer ring of a given shape/polygon for a specific hole
  //
  get_outer_ring_for_shape(lie, hole_number) {
    console.log(lie, hole_number);

    // shape = convert_to_polygon( hole_number, lie ).try(:first)
    // shape.try(:exterior_ring)
  }

  //
  // Get a random point within a given shape/polygon for a specific hole
  //
  get_random_point_within_shape(lie, hole_number) {
    console.log(lie, hole_number);
    try {
      // center      = get_center_for( hole_number, lie.downcase )
      // ring_shape  = get_outer_ring_for_shape( lie.downcase, hole_number )
      // ring_point  = get_random_point_on_shape( ring_shape )
      // get_midpoint( ring_point, center )
    } catch (error) {
      console.log(
        `===============================================================================
        Method: get_random_point_within_shape(#{lie}, #{hole_number})
        -------------------------------------------------------------------------------
        
        Center:       #{center.inspect}
        
        Ring Shape:   #{ring_shape.inspect}
        
        Ring Point:   #{ring_point.inspect}
        
        Point:        #{point.inspect}
        ===============================================================================
            EOF
            raise e
        `
      );
    }
  }
  //
  // Lies for a single hole
  //
  lies_for(hole_number) {
    console.log(hole_number);
    // get_geojson( hole_number ).collect do |feature|
    // feature["properties"]["type"]
    // end.uniq
  }

  //
  // get coordinates for a specific hole and lie
  //
  coordinates_for(hole_number, lie) {
    // this.get_geojson_for( hole_number, lie ).map do |feature|
    //   feature.fetch("geometry", {}).fetch("coordinates", []).try(:last)
    // end
    return this.get_geojson_for(hole_number, lie).map((feature) => {
      return feature?.geometry?.coordinates?.pop();
    });
  }

  //
  // get geojson for a specific hole
  //
  get_geojson(hole_number) {
    //   this.geojson["features"].select do |feature|
    //   feature.fetch("properties", {}).
    //           fetch("hole-number", nil) == hole_number.to_i
    // end
    const features = []; // geojson['features']
    features.filter((feature) => {
      return feature?.properties['hole-number'] == +hole_number;
    });

    return features || [];
  }
  // alias_method :get_features_for_hole, :get_geojson

  //
  // get geojson for a specific hole and lie condition
  //
  //   e.g. get_geojson_for(1, "green")
  //
  get_geojson_for(hole_number, lie) {
    //   this.get_geojson( hole_number ).select do |feature|
    //   feature.fetch("properties", {}).fetch("type", nil) == lie.to_s
    // end
    this.get_geojson(hole_number).filter((feature) => {
      return feature?.properties['type'] == lie;
    });
    return [];
  }
  // alias_method :get_feature, :get_geojson_for

  distance_between(startpoint, endpoint) {
    console.log(startpoint, endpoint);

    // ::Geocoder::Calculations.distance_between( startpoint, endpoint )
  }
  /*   
 
  ##########################################################################
  # Course Specific Geo Stuff

  #
  #
  #
  */
  tees_with_distances(hole_number) {
    console.log(hole_number);

    // green = get_polygon_center( hole_number, 'green' )
    // tees  = convert_to_polygon( hole_number, 'tee' )
    // tees  = tees.map do |tee|
    //   tee_center = tee.try(:centroid)
    //   {
    //     distance: ( ::Geocoder::Calculations.distance_between([tee_center.y, tee_center.x], [green.y, green.x]) * 1760 ),
    //     tee:      tee,
    //   }
    // end
    // tees.sort_by { |m| m[:distance] }
  }

  default_tee_by_distance(hole_number) {
    console.log(hole_number);

    // tees_with_distances( hole_number ).last[:tee]
  }

  default_tee_center(hole_number) {
    console.log(hole_number);
    // center = default_tee_by_distance( hole_number ).try(:centroid)
    // [center.y, center.x]
  }

  //
  // get tee centroid
  //
  tee_center(hole_number) {
    this.get_center_for(hole_number, 'tee');
  }

  // alias_method :middle_of_tee, :tee_center

  //
  // get teebox centroid
  //
  teebox_center(hole_number) {
    this.get_center_for(hole_number, 'tee-boundary');
  }

  //
  // the middle of the green
  //
  green_center(hole_number) {
    this.get_center_for(hole_number, 'green');
  }

  // alias_method :middle_of_green, :green_center
  // alias_method :green_middle_for, :green_center

  //
  // get the green's outer ring
  //
  green_outer_ring(hole_number) {
    console.log(hole_number);
    // green_polygon = convert_to_polygon( hole_number, "green" ).try(:first)
    // green_polygon.try(:exterior_ring)
  }

  //
  // Get the midpoint between the fairway center and the green front
  //
  midpoint_between_fairway_center_and_green_front(hole_number) {
    console.log(hole_number);
    //  center  = fairway_center( hole_number )
    //  front   = green_front_for( hole_number )
    //  get_midpoint(center, front)
  }

  //
  // create a line string from tee center to green center
  //
  tee_to_green_line(hole_number) {
    console.log(hole_number);
    // tcenter = get_polygon_center( hole_number, "tee-boundary" )
    // gcenter = get_polygon_center( hole_number, "green" )
    // to_line( tcenter, gcenter )
  }

  //
  // intersection point
  //
  tee_to_green_intersection(hole_number) {
    console.log(hole_number);
    // line = tee_to_green_line( hole_number )
    // return [] if green_outer_ring( hole_number ).blank? || line.blank?
    // line.intersection( green_outer_ring( hole_number ) )
  }

  //
  // get the bearing from teebox center to green center, for a specific hole
  //
  teebox_to_green_bearing(hole_number) {
    console.log(hole_number);
    // ::Geocoder::Calculations.bearing_between(
    //   teebox_center(hole_number), green_center(hole_number)
    // )
  }

  //
  // get the distance of the green front to the green middle, for a specific
  // hole
  //
  distance_green_center_to_intersection(hole_number) {
    console.log(hole_number);
    // front = tee_to_green_intersection( hole_number ).try(:coordinates)
    // front ||= begin
    //   intersect_coord = tee_to_green_intersection( hole_number )
    //   if intersect_coord.is_a?(::RGeo::Geos::FFIMultiPointImpl)
    //   [ intersect_coord[1].y, intersect_coord[1].x ]
    //   else
    //     [ intersect_coord.y, intersect_coord.x ]
    //   end
    // end
    // middle = get_center_for( hole_number, "green" )
    // ::Geocoder::Calculations.distance_between(front, middle)
  }

  //
  // get the green back intersection
  //
  green_back_intersection(hole_number) {
    console.log(hole_number);
    // middle        = get_center_for( hole_number, "green" )
    // distance      = distance_green_center_to_intersection( hole_number )
    // bearing       = teebox_to_green_bearing( hole_number )
    // ending_point  = ::Geocoder::Calculations.endpoint(middle, bearing, (distance + 1) )
    // ending_point  = factory.point(ending_point[1], ending_point[0])
    // gcenter       = get_polygon_center( hole_number, "green" )
    // line          = to_line( ending_point, gcenter )
    // return [] if green_outer_ring( hole_number ).blank? || line.blank?
    // line.intersection( green_outer_ring( hole_number ) )
  }

  //
  // get the 4 points of the tee boundary
  //
  tee_boundary_coordinates_for(hole_number) {
    console.log(hole_number);
    // feature = get_geojson_for( hole_number, "tee-boundary" ).try(:first)
    // return [] if feature.blank?
    // coordinates = feature.fetch("geometry", {}).fetch("coordinates", []).try(:first)
    // if coordinates.length == 5
    //   coordinates.try(:pop)
    // end
    // coordinates
  }

  //
  // the front of the green
  //
  green_front_for(hole_number) {
    console.log(hole_number);
    // rgeo_point = tee_to_green_intersection( hole_number )
    // if rgeo_point.is_a?(::RGeo::Geos::FFIMultiPointImpl)
    //   [ rgeo_point[1].y, rgeo_point[1].x ]
    // else
    //   [ rgeo_point.y, rgeo_point.x ]
    // end
  }

  //
  // the back of the green
  //
  green_back_for(hole_number) {
    console.log(hole_number);
    // rgeo_point = green_back_intersection( hole_number )
    // if rgeo_point.is_a?(::RGeo::Geos::FFIMultiPointImpl)
    //   [ rgeo_point[1].y, rgeo_point[1].x ]
    // else
    //   [ rgeo_point.y, rgeo_point.x ]
    // end
  }

  //
  // fairway center
  //
  fairway_center(hole_number) {
    console.log(hole_number);
    // get_center_for( hole_number, "fairway" )
  }

  fairway_polygons(hole_number) {
    console.log(hole_number);
    // convert_to_polygon( hole_number, "fairway" )
  }

  //
  // get the polygon that contains the fairway shot
  //
  select_fairway_polygon(hole_number, shot_coords) {
    console.log(hole_number, shot_coords);
    // return nil if shot_coords.blank?
    // fairways = fairway_polygons( hole_number )
    // fairway  = fairways.select { |p| p.contains?(shot_coords) }.first
    // fairway  = if fairway.blank?
    //   # shot is not inside a fairway
    //   outside_fairway = fairways.map do |p|
    //     {
    //       distance: distance_between([shot_coords.y, shot_coords.x], [p.centroid.y, p.centroid.x]),
    //       fairway:  p
    //     }
    //   end.sort{|a,b| a[:distance] <=> b[:distance]}
    //   outside_fairway.blank? ? nil : outside_fairway.first[:fairway]
    // else
    //   fairway
    // end
    // fairway
  }
}
