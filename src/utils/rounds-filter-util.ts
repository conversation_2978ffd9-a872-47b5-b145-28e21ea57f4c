import { Injectable, Logger } from '@nestjs/common';
import { isEmpty, isString } from 'lodash';
import moment from 'moment';
import { Between, In, Repository } from 'typeorm';
import { ROUND } from 'src/rounds/round.const';
import { User } from 'src/users/entities/user.entity';
import { Round } from '../rounds/entities/round.entity';
import { RoundService } from '../rounds/rounds.service';
import { throwBadRequestError } from './exception';

const SELECT_ROUND_FIELDS = ['id', 'round_mode', 'igolf_course_id', 'stats_completed', 'driving_stat_complete'];
@Injectable()
export class RoundsFilterUtil {
  private readonly logger = new Logger(RoundsFilterUtil.name);
  roundRepo: Repository<Round>;
  user: User;
  roundService: RoundService;
  rounds: any;
  parameters: any;
  constructor(roundRepo, user, roundService) {
    this.roundRepo = roundRepo;
    this.user = user;
    this.roundService = roundService;
  }

  async search(params) {
    const options = Object.keys(params);
    switch (true) {
      case options.includes('start_date'):
        return await this.filter_by_time(params.start_date, params.end_date, params?.classic);
      case options.includes('round_ids'):
        let ids = null;
        if (isString(params['round_ids'])) {
          ids = params['round_ids'] = params['round_ids'].split(',').map((id) => parseInt(id));
        } else {
          ids = params['round_ids'];
        }
        return this.filter_round_ids(ids);
      case options.includes('igolf_course_ids'):
      case options.includes('course_ids'):
        if (isString(params.course_ids)) {
          params.course_ids = params.course_ids.split(',');
        }
        if (isString(params.igolf_course_ids)) {
          params.igolf_course_ids = params.igolf_course_ids.split(',');
        }
        const course_ids = params.course_ids || '';
        const igolf_course_ids = params.igolf_course_ids || '';
        return this.filter_by_course_id(course_ids, igolf_course_ids, params?.classic);
      case options.includes('round_type'):
        return this.filter_by_round_type(params.round_type);
      case options.includes('last_round'):
        return this.filter_by_last_round_played();
      case options.includes('round_mode'):
        return this.filter_by_round_mode(params.round_mode);
      default:
        return this.default_filter();
    }
    // @results    = case
  }

  async searchTAG(params) {
    const options = Object.keys(params);
    switch (true) {
      case options.includes('start_date'):
        return await this.filter_by_time(params.start_date, params.end_date, false, true);
      case options.includes('round_ids'):
        let ids = null;
        if (isString(params['round_ids'])) {
          ids = params['round_ids'] = params['round_ids'].split(',').map((id) => parseInt(id));
        } else {
          ids = params['round_ids'];
        }
        return this.filter_round_ids(ids);
      case options.includes('last_round'):
        return this.filter_by_last_round_played();
      default:
        return this.default_filter_advanced();
    }
  }

  //   filter_by(type, args) {
  //     // args.flatten!
  //     // case type.to_s
  //     // when 'round'
  //     //   filter_by_ids(args)
  //     // when 'range'
  //     //   filter_by_time(args[0], args[1])
  //     // when 'course'
  //     //   filter_by_course(args)
  //     // when 'round_type'
  //     //   filter_by_round_type(args)
  //     // when 'last_round'
  //     //   filter_by_last_round_played
  //     // when 'default'
  //     //   default_filter
  //     // end
  //   }

  async filter_by_ids(ids) {
    // @user.rounds.completed.find_ids(ids)
    console.log(`FILTER_BY_IDS: ${ids} `);
    const query = this.roundRepo.createQueryBuilder();
    query.where({ user_id: this.user.id, completed: true, id: In(ids) });
    query.select(SELECT_ROUND_FIELDS);
    return await query.getRawMany();
  }

  async filter_round_ids(ids) {
    console.log(`FILTER_ROUND_IDS: ${ids} `);
    const query = this.roundRepo.createQueryBuilder();
    query.where({ user_id: this.user.id, completed: true, id: In(ids) });
    query.select(SELECT_ROUND_FIELDS);
    query.orderBy({ id: 'DESC' });
    return await query.getRawMany();
  }

  async filter_by_time(start_date, end_date, isClassic = false, isAdvanced = false) {
    console.log(`FILTER_BY_TIME: ${start_date} - ${end_date}`);
    if (!start_date) {
      throwBadRequestError(`start_date Invalid`);
    }
    try {
      if (!end_date) {
        end_date = moment().format(`YYYY-MM-DD`) + ' 23:59:59';
      } else {
        end_date = moment(end_date, 'YYYY-MM-DD').format(`YYYY-MM-DD`) + ' 23:59:59';
      }
    } catch (error) {
      console.log(error);
    }
    const query = this.roundRepo.createQueryBuilder();
    const conditions = {
      user_id: this.user.id,
      completed: true,
      // stats_completed: true,
      played_on: Between(start_date, end_date),
    };
    if (isClassic) {
      conditions['round_mode'] = ROUND.ROUND_MODE_CLASSIC;
    } else if (isAdvanced) {
      conditions['round_mode'] = ROUND.ROUND_MODE_ADVANCED;
    }

    query.where(conditions);
    query.select(SELECT_ROUND_FIELDS);
    const rounds = await query.getRawMany();
    return this.filter_round_stats_completed(rounds);
  }

  async filter_by_course_id(facility_ids, igolf_course_ids, isClassic = false) {
    console.log(`FILTER_BY_COURSE_ID: ${facility_ids}, ${igolf_course_ids}`);

    const query = this.roundRepo.createQueryBuilder();
    query.where({
      user_id: this.user.id,
      completed: true,
    });
    if (facility_ids != '' && igolf_course_ids != '') {
      query.andWhere(`course_id IN (:...facility_ids) OR igolf_course_id IN (:...igolf_course_ids)`, {
        facility_ids,
        igolf_course_ids,
      });
    }
    if (facility_ids != '' && igolf_course_ids == '') {
      query.andWhere(`course_id IN (:...facility_ids)`, { facility_ids });
    }
    if (facility_ids == '' && igolf_course_ids != '') {
      query.andWhere(`igolf_course_id IN (:...igolf_course_ids)`, { igolf_course_ids });
    }
    if (isClassic) {
      query.andWhere(`round_mode IN (:round_mode)`, { round_mode: ROUND.ROUND_MODE_CLASSIC });
    }
    query.select(SELECT_ROUND_FIELDS);
    return await query.getRawMany();
  }

  async filter_by_course(facility_ids) {
    console.log('FILTER_BY_COURSE: ', facility_ids);
    const query = this.roundRepo.createQueryBuilder();
    query.where({
      user_id: this.user.id,
      completed: true,
      course_id: In(facility_ids),
    });
    query.select(SELECT_ROUND_FIELDS);
    return await query.getRawMany();
  }

  async filter_by_round_type(type) {
    console.log('FILTER_BY_ROUND_TYPE: ', type);
    if (isEmpty(type)) {
      return null;
    }
    type = type.split(',').map((t) => t.trim().toLowerCase());
    const query = this.roundRepo.createQueryBuilder();
    query.where({
      user_id: this.user.id,
      completed: true,
    });
    query.andWhere(`lower(round_type) IN (:...type)`, { type });
    query.select(SELECT_ROUND_FIELDS);
    return await query.getRawMany();
  }

  async filter_by_round_mode(mode) {
    console.log('FILTER_BY_ROUND_MODE: ', mode);

    if (isEmpty(mode)) {
      return null;
    }
    mode = mode.split(',').map((m) => m.trim().toLowerCase());
    const query = this.roundRepo.createQueryBuilder();
    query.where({
      user_id: this.user.id,
      completed: true,
    });
    query.andWhere(`lower(round_mode) IN (:...mode)`, { mode });
    query.select(SELECT_ROUND_FIELDS);
    return await query.getRawMany();
  }

  async filter_by_last_round_played() {
    console.log(`FILTER_BY_LAST_ROUND_PLAYED`);
    const query = this.roundRepo.createQueryBuilder();
    query.where({
      user_id: this.user.id,
      completed: true,
      // stats_completed: true,
    });
    query.orderBy({ played_on: 'DESC', created_at: 'DESC' });
    query.select(SELECT_ROUND_FIELDS);
    query.take(1);
    const rounds = await query.getRawOne();
    if (!rounds) {
      return {};
    }
    return this.filter_round_stats_completed([rounds]);
  }
  async default_filter() {
    this.logger.debug(`DEFAULT_FILTER`);
    const query = this.roundRepo.createQueryBuilder();
    query.where({
      user_id: this.user.id,
      completed: true,
      round_mode: ROUND.ROUND_MODE_CLASSIC,
    });
    query.orderBy({ played_on: 'DESC', created_at: 'DESC' });
    query.select(SELECT_ROUND_FIELDS);
    query.take(5);
    const rounds = await query.getRawMany();

    if (!rounds) {
      return {};
    }
    return rounds;
  }

  async default_filter_advanced() {
    this.logger.debug(`DEFAULT_FILTER`);
    const query = this.roundRepo.createQueryBuilder();
    query.where({
      user_id: this.user.id,
      completed: true,
      round_mode: ROUND.ROUND_MODE_ADVANCED,
    });
    query.orderBy({ played_on: 'DESC', created_at: 'DESC' });
    query.select(SELECT_ROUND_FIELDS);
    query.take(5);
    const rounds = await query.getRawMany();

    if (!rounds) {
      return {};
    }
    return rounds;
  }

  private async getRoundClassicRecent(numberOfRounds: number) {
    const query5RoundClassic = this.roundRepo.createQueryBuilder();
    query5RoundClassic.where({
      user_id: this.user.id,
      completed: true,
      round_mode: ROUND.ROUND_MODE_CLASSIC,
    });
    query5RoundClassic.orderBy({ played_on: 'DESC', created_at: 'DESC' });
    query5RoundClassic.select(SELECT_ROUND_FIELDS);
    query5RoundClassic.take(numberOfRounds);
    const roundsClassic = await query5RoundClassic.getRawMany();
    return roundsClassic;
  }

  filter_round_stats_completed(rounds) {
    return rounds?.filter(
      async (round) => await this.roundService.statsCompletedProcessing(round, round?.stats_completed == null)
    );
  }
}
