import { Logger } from '@nestjs/common';
import { isEmpty } from 'lodash';
import { ROUND } from 'src/rounds/round.const';

export class RoundScoreUtil {
  static formatRoundIds(all_round_ids) {
    const logger = new Logger(RoundScoreUtil.name);
    if (isEmpty(all_round_ids)) {
      return {};
    }

    const total_rounds = all_round_ids.length;
    const rounds = all_round_ids;

    const round_multiplayer_ids = RoundScoreUtil.filterRoundId(ROUND.ROUND_MODE_MULTIPLAYER, rounds);

    const round_basic_ids = RoundScoreUtil.filterRoundId(ROUND.ROUND_MODE_BASIC, rounds);
    const round_classic_ids = RoundScoreUtil.filterRoundId(ROUND.ROUND_MODE_CLASSIC, rounds);
    let round_ids = this.filterRoundId(ROUND.ROUND_MODE_ADVANCED, rounds);
    if (round_ids) {
      round_ids = round_ids.sort((a, b) => +a.id - +b.id);
    }
    const round_simple_ids = RoundScoreUtil.filterRoundId(ROUND.ROUND_MODE_SIMPLE, rounds);

    logger.log(`
    =======================================================================================================================
    FORMAT_ROUND_IDS:
    
    round_ids:             ${round_ids}
    round_simple_ids:      ${round_simple_ids}
    round_basic_ids:       ${round_basic_ids}
    round_classic_ids:     ${round_classic_ids}
    round_multiplayer_ids: ${round_multiplayer_ids}
    ========================================================================================================================
    `);
    return {
      round_multiplayer_ids,
      round_basic_ids,
      round_classic_ids,
      round_ids,
      round_simple_ids,
      total_rounds,
      all_round_ids,
    };
  }
  static filterRoundId(round_mode: any, rounds: any) {
    if (!rounds || rounds.length == 0) {
      return [];
    }
    return rounds.filter((round) => round?.round_mode == round_mode)?.map((r) => r.id);
  }
  static getAverageScore(score, holes) {
    if (holes == 0 || score == 0 || score == null) {
      return null;
    }
    return +((score / holes) * 18).toFixed(2);
  }
  static getRoundScoreByHoles(holes) {
    const logger = new Logger(RoundScoreUtil.name);
    try {
      let round_score = 0;
      let hole_numbers_players = 0;
      for (const hole of holes) {
        const hole_score = parseInt(hole.score);
        if (hole_score && hole_score > 0) {
          round_score += hole_score;
          hole_numbers_players += 1;
        }
      }
      return [round_score, hole_numbers_players];
    } catch (error) {
      logger.error(error.message);
      return [0, 0];
    }
  }
}
