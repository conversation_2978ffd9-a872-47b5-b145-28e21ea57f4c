import { ApiProperty } from '@nestjs/swagger';
import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

@Entity({ name: 'regions' })
export class Region {
  @PrimaryGeneratedColumn()
  @ApiProperty({ example: 1 })
  id: number;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  name: string;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  facilities_count: number;

  @CreateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  created_at: Date;

  @UpdateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  updated_at: Date;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  iso_short: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  iso_long: string;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  status: number;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  states_count: number;
}
