const fs = require('fs');
const entityClassName = 'IGolfGhinCourse';
const tableName = 'igolf_ghin_courses';
const fileName = 'ghin.entity.ts';

const entity = `
"id" int4 NOT NULL DEFAULT nextval('igolf_ghin_course_id_seq' :: regclass),
            "igolf_course_id" character varying(60) NOT NULL, 
            "ghin_course_id" character varying(30) NOT NULL, 
            "ghin_course_name" character varying(255) NOT NULL, 
            "created_at" TIMESTAMP NOT NULL DEFAULT now(), 
            "updated_at" TIMESTAMP NOT NULL DEFAULT now()
`;

let fields = entity.split(',');
fields = fields.map((f) => f.replace('\n', ''));
console.log(fields);
let columns = [];

fields.map((f) => {
  let dataField = f.trim().split(' ');
  columns.push({ columns: dataField[0].replace(/\"/gi, ''), type: dataField[1] });
});
console.log({ columns });
let attrs = '';

let strFile = `
import {
    Column,
    CreateDateColumn,
    DeleteDateColumn,
    Entity,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
  } from 'typeorm';
  import { ApiProperty } from '@nestjs/swagger';
  
  @Entity({ name: '${tableName}' })
  export class ${entityClassName} {
    
`;
const parseType = (column, type) => {
  type = type.toLowerCase();
  if (type.includes('int')) {
    if (column == 'id') {
      return `
        @PrimaryGeneratedColumn()
        @ApiProperty({ example: 1 })
        ${column}:number;
        `;
    }
    return `
        @Column({type:"integer"})
        @ApiProperty({ example: 1 })
        ${column}:number;
    `;
  }
  if (type.includes('float')) {
    return `
        @Column({type:"float"})
        @ApiProperty({ example: 1.0 })
        ${column}:number;
    `;
  }
  if (type.includes('varchar') || type.includes('character')) {
    return `
        @Column({type:"varchar"})
        @ApiProperty({ example: "string" })
        ${column}:string;
    `;
  }
  if (type.includes('jsonb')) {
    return `
        @Column({type:"jsonb"})
        @ApiProperty({ example: "string" })
        ${column}:string;
    `;
  }
  if (type.includes('json')) {
    return `
        @Column({type:"json"})
        @ApiProperty({ example: "string" })
        ${column}:string;
    `;
  }
  if (type.includes('text')) {
    return `
        @Column({type:"text"})
        @ApiProperty({ example: "string" })
        ${column}:string;
    `;
  }
  if (type.includes('timestamp')) {
    let columnName = '@Column({type:"timestamp"})';
    if (column == 'updated_at') {
      columnName = `@UpdateDateColumn()`;
    }
    if (column == 'created_at') {
      columnName = `@CreateDateColumn()`;
    }
    if (column == 'deleted_at') {
      columnName = `@DeleteDateColumn()`;
    }
    return `
        ${columnName}
        @ApiProperty({ example: new Date().toISOString() })
        ${column}:Date;
    `;
  }
  if (type.includes('date')) {
    let columnName = '@Column({type:"date"})';
    return `
        ${columnName}
        @ApiProperty({ example: new Date().toISOString() })
        ${column}:Date;
    `;
  }
  if (type.includes('bool')) {
    return `
        @Column({type:"boolean"})
        @ApiProperty({ example: false })
        ${column}:boolean;
    `;
  }
  if (type.includes('geography')) {
    return `
     @Column({
        type: 'geography',
        spatialFeatureType: 'Point',
        srid: 4326,
        nullable: true,
      })
     ${column}:Point;
    `;
  }
  if (type.includes('hstore')) {
    return `
     @Column({
        type: 'hstore',
        nullable: true,
      })
     ${column}:string;
    `;
  }
};
columns.forEach((c) => {
  attrs += parseType(c.columns, c.type) + '\n';
});
strFile += attrs + '}';
fs.writeFileSync('./gen/' + fileName, strFile);
