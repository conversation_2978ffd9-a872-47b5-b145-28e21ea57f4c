import { Logger } from '@nestjs/common';
import { isNumberString } from 'class-validator';
import _, { isEmpty } from 'lodash';
import { StrokesStatsService } from 'src/strokes-stats/strokes-stats.service';
import { Stats } from '../smart-golf/stats';
import { includeStr } from '../utils';

// const COUNTRIES = [
//   ['United States', 'US'],
//   ['United Kingdom', 'GB'],
//   ['Canada', 'CA'],
//   ['Australia', 'AU'],
// ];
// const GENDER_MALE = 'male';
// const GENDER_FEMALE = 'female';
// const REGEX_PASSWORD = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[\s\S]{8,}$/;

const SG_BASELINES = {
  pga: 'Pro',
  scratch: 'Scratch',
  five: 5,
  ten: 10,
  fifteen: 15,
  twenty: 20,
};

export class StatsHelper {
  private readonly logger = new Logger(StatsHelper.name);
  static to_baseline_number(baseline) {
    baseline = baseline ? baseline.toString().toLowerCase() : 'scratch';
    if (isNumberString(baseline)) {
      return baseline;
    }
    return SG_BASELINES[baseline];
  }

  get_pga_median(dist, units = 'yards') {
    switch (this.determine_units(units)) {
      case 'meters':
        return this.get_pga_median_in_meters(dist);
      case 'yards':
        return this.get_pga_median_in_yards(dist);
    }
  }

  //
  // PGA Median for Yards
  //
  get_pga_median_in_yards(dist: any) {
    const medians = {
      25: 4.6,
      50: 10,
      75: 14.3,
      100: 15.1,
      150: 21,
      200: 29.9,
      250: 41.4,
    };
    const keysMedians = Object.keys(medians);
    for (const k of keysMedians) {
      if (+k > +dist) {
        return medians[k];
      }
    }
    return 56.1;
  }

  //
  // PGA Median for Meters
  //
  get_pga_median_in_meters(dist) {
    const medians = {
      25: 2.3,
      50: 4.7,
      75: 5.9,
      100: 6.3,
      150: 8.1,
      200: 11.2,
      250: 15.5,
    };
    const keysMedians = Object.keys(medians);
    for (const k of keysMedians) {
      if (+k > +dist) {
        return medians[k];
      }
    }
    return 19.3;
  }

  calculate_percentage_long(strokes) {
    if (isEmpty(strokes)) {
      return 0;
    }
    const total = this.calculate_long(strokes) / strokes.length;
    return total * 100;
  }

  calculate_percentage_short(strokes) {
    if (isEmpty(strokes)) {
      return 0;
    }
    const total = this.calculate_short(strokes) / strokes.length;
    return total * 100;
  }

  calculate_percentage_left(strokes) {
    if (isEmpty(strokes)) {
      return 0;
    }
    const total = this.calculate_left(strokes) / strokes.length;
    return +(total * 100).toFixed(2);
  }

  calculate_percentage_fairway_hit(strokes) {
    if (isEmpty(strokes)) {
      return 0;
    }
    const totalFirstLongTee = this.calculate_fairway_hit(strokes);
    const total = totalFirstLongTee / strokes.length;
    return +(total * 100).toFixed(2);
  }

  calculate_percentage_right(strokes) {
    if (isEmpty(strokes)) {
      return 0;
    }
    const total = this.calculate_right(strokes) / strokes.length;
    return +(total * 100).toFixed(2);
  }

  //
  // Pass in StrokeStats
  //
  calculate_left(strokes) {
    if (isEmpty(strokes)) {
      return 0;
    }
    return strokes.filter((stroke) => stroke.left && !StrokesStatsService.landed_on_fairway(stroke)).length || 0;
  }
  calculate_fairway_hit(strokes) {
    if (isEmpty(strokes)) {
      return 0;
    }
    return (
      strokes.filter(
        (stroke) =>
          stroke.long_tee &&
          !stroke.starting_as_penalty_shot &&
          stroke.stroke_ordinal == 1 &&
          StrokesStatsService.landed_on_fairway(stroke)
      ).length || 0
    );
  }

  //
  // Pass in StrokeStats
  //
  calculate_right(strokes) {
    if (isEmpty(strokes)) {
      return 0;
    }
    return strokes.filter((stroke) => stroke.right && !StrokesStatsService.landed_on_fairway(stroke)).length || 0;
  }

  //
  // Pass in StrokeStats
  //
  calculate_long(strokes) {
    if (isEmpty(strokes)) {
      return 0;
    }
    return strokes.filter((stroke) => !!stroke.long && !StrokesStatsService.landed_on_fairway(stroke)).length || 0;
  }

  //
  // Pass in StrokeStats
  //
  calculate_short(strokes) {
    if (isEmpty(strokes)) {
      return 0;
    }
    return strokes.filter((stroke) => !!stroke.short && !StrokesStatsService.landed_on_fairway(stroke)).length || 0;
  }

  //
  // Pass in StrokeStats to get the average / mean
  //
  calculate_mean(strokes) {
    if (isEmpty(strokes)) {
      return 0;
    }
    //     return 0 if strokes.blank?
    // ending_distances = strokes.map{ |m| m.ending_distance_to_pin * ::SmartGolf::Stats::METERS_TO_YARDS }
    // ending_distances.sort.mean # rescue 0
    const ending_distances = strokes.map((s) => s.ending_distance_to_pin * Stats.METERS_TO_YARDS);
    try {
      return _.sum(_.sortBy(ending_distances)) / ending_distances.length;
    } catch (error) {
      this.logger.error(`CALCULATE_MEAN`);
      this.logger.error(error.message);
      return 0;
    }
  }

  //
  // Pass in StrokeStats to get the average / mean
  //
  calculate_mean_in_feet(strokes) {
    if (isEmpty(strokes)) {
      return 0;
    }
    // return 0 if strokes.blank?
    // ending_distances = strokes.map{ |m| m.ending_distance_to_pin * ::SmartGolf::Stats::METERS_TO_FEET }
    // ending_distances.sort.mean # rescue 0
    const ending_distances = strokes.map((s) => s.ending_distance_to_pin * Stats.METERS_TO_FEET);
    try {
      return _.sum(_.sortBy(ending_distances)) / ending_distances.length;
    } catch (error) {
      this.logger.error(`CALCULATE_MEAN_IN_FEET`);
      this.logger.error(error.message);
      return 0;
    }
  }

  lie_color(lie) {
    // case lie
    // when /Hazard/i
    //   '#45BBD4' #Blue
    // when /Bunker/i
    //   '#FFCC00' #Grey
    // when /Hole Boundary/i, /Facility Boundary/i, /Trees/i, /Rough/i
    //   '#FF0000' #Red
    // else
    //   '#3ABA56' #Green
    // end
    lie = lie.toString().toLowerCase().trim();

    if (includeStr(lie, 'hazard')) {
      return '#45BBD4'; // #Blue
    }
    if (includeStr(lie, 'bunker')) {
      return '#FFCC00'; // #Grey
    }

    if (['hole boundary', 'facility boundary', 'trees', 'rough'].includes(lie)) {
      return '#FF0000'; //#Red
    }
    return '#3ABA56'; //#Green
  }

  calculate_mean_with_units(strokes, convert_to_unit) {
    if (isEmpty(strokes)) {
      return 0;
    }
    // mean = calculate_mean(strokes) * 3
    // a = determine_units(convert_to_unit)
    // unit = a == "yards" ? "feet" : a
    // calculate_with_units(unit, :feet, mean)
    const mean = this.calculate_mean(strokes) * 3;
    const a = this.determine_units(convert_to_unit);
    const unit = a == 'yards' ? 'feet' : a;
    return this.calculate_with_units(unit, 'feet', mean);
  }

  calculate_x_axis(green_distances, convert_to_unit?) {
    //     distance = if green_distances["left"].zero?
    //     green_distances["right"]
    //   else
    //     green_distances["left"] * -1
    //   end
    //   a = determine_units(convert_to_unit)
    //   unit = a == "yards" ? "feet" : a
    //   calculate_with_units(unit, :feet, distance)
    if (green_distances.includes('=>')) {
      green_distances = '{' + green_distances.replace(/=>/g, ':') + '}';
      green_distances = JSON.parse(green_distances);
    }
    let distance = 0;
    if (!green_distances['left'] || green_distances['left'] == 0) {
      distance = +green_distances['right'];
    } else {
      distance = +green_distances['left'] * -1;
    }
    const a = this.determine_units(convert_to_unit);
    const unit = a == 'yards' ? 'feet' : a;
    return this.calculate_with_units(unit, 'feet', distance);
  }

  calculate_y_axis(green_distances, convert_to_unit = 'yards') {
    console.log(convert_to_unit, green_distances);
    //     distance = if green_distances["short"].zero?
    //     green_distances["long"]
    //   else
    //     green_distances["short"] * -1
    //   end
    //   # distance
    //   a = determine_units(convert_to_unit)
    //   unit = a == "yards" ? "feet" : a
    //   calculate_with_units(unit, :feet, distance)
    if (green_distances.includes('=>')) {
      green_distances = '{' + green_distances.replace(/=>/g, ':') + '}';
      green_distances = JSON.parse(green_distances);
    }
    let distance = 0;
    if (!green_distances['short'] || green_distances['short'] == 0) {
      distance = +green_distances['long'];
    } else {
      distance = +green_distances['short'] * -1;
    }
    const a = this.determine_units(convert_to_unit);
    const unit = a == 'yards' ? 'feet' : a;
    return this.calculate_with_units(unit, 'feet', distance);
  }

  //
  // determine_units :meters
  //
  determine_units(unit_selection) {
    const unit = unit_selection?.toString().toLowerCase().trim() || '';
    if (unit.includes('meter')) {
      return 'meters';
    }
    if (unit.includes('feet')) {
      return 'feet';
    }
    return 'yards';
  }

  //
  // calculate_with_units(:meters, :feet, 21.2)
  //
  calculate_with_units(convert_to_unit, unit_in_use, value) {
    // current_unit    = unit_in_use.to_s.downcase
    // units           = determine_units(convert_to_unit.to_s.downcase)
    // return value if units.to_s == current_unit.to_s
    // if (units == "meters")    && (current_unit == "yards")
    //   value * ::SmartGolf::Stats::YARDS_TO_METERS
    // elsif (units == "yards")  && (current_unit == "meters")
    //   value * ::SmartGolf::Stats::METERS_TO_YARDS
    // elsif (units == "meters") && (current_unit == "feet")
    //   value * ::SmartGolf::Stats::FEET_TO_METERS
    // elsif (units == "yards") && (current_unit == "feet")
    //   value * ::SmartGolf::Stats::FEET_TO_YARDS
    // end
    const current_unit = unit_in_use.toString().toLowerCase();
    const units = this.determine_units(convert_to_unit.toString().toLowerCase());
    if (units == current_unit) {
      return value;
    }
    if (units == 'meters' && current_unit == 'yards') {
      return value * Stats.YARDS_TO_METERS;
    }
    if (units == 'yards' && current_unit == 'meters') {
      return value * Stats.METERS_TO_YARDS;
    }
    if (units == 'meters' && current_unit == 'feet') {
      return value * Stats.FEET_TO_METERS;
    }
    if (units == 'yards' && current_unit == 'feet') {
      return value * Stats.FEET_TO_YARDS;
    }
  }

  //
  // Overall Stats Page Strokes Gained Badges
  //
  stat_badge_item(classname, sg) {
    console.log(classname, sg);

    // content_tag(:strong, sg.round(2), class: "#{classname} #{sg_color(sg)}")
  }

  //
  // Stat paths
  //
  overall_path(query) {
    console.log(query);

    // query.present? ? my_stats_path(query) : my_stats_path
  }

  driving_path(query) {
    console.log(query);
    // query.present? ? my_stats_driving_path(query) : my_stats_driving_path
  }

  approach_path(query) {
    console.log(query);
    // query.present? ? my_stats_approach_path(query) : my_stats_approach_path
  }

  short_path(query) {
    console.log(query);
    // query.present? ? my_stats_short_path(query) : my_stats_short_path
  }

  putting_path(query) {
    console.log(query);
    // query.present? ? my_stats_putting_path(query) : my_stats_putting_path
  }

  format_avg_score_for_par(score, decimal_points = 2) {
    console.log(score, decimal_points);

    // avg = score.round(decimal_points)
    // avg.zero? ? 0 : avg.to_s.ljust(4, '0')
  }

  format_percentages(score, decimal_points = 2) {
    console.log(score, decimal_points);
    // score.round(decimal_points).to_s + '%'
  }

  percentage_progress_bar_class(percentage) {
    console.log(percentage);
    // percentage > 50.0 ? 'progress-bar-success' : 'progress-bar-danger'
  }

  round_filter_message(query) {
    console.log(query);

    // case
    // when query[:start_date].present?
    //   "<p>You are viewing stats for rounds that were played between <strong>#{query[:start_date].to_date.strftime('%B %d, %Y')}</strong> and <strong>#{(query[:end_date] || Date.today).to_date.strftime('%B %d, %Y') }</strong>.</p>"
    // when query[:round_ids].present?
    //   "<p>You are viewing stats for rounds you played on</p>" + "<ul style='list-style:none;'>" +
    //   Round.where(id: query[:round_ids]).map { |r| "<li>#{r.played_on.strftime('%B %d, %Y')} at #{r.facility.name}</li>"}.join("") + "</ul>"
    // when query[:course_ids].present?
    //   "<p>You are viewing stats for rounds that were played at the following Courses</p>" + "<ul style='list-style:none;'>" +
    //   Facility.where(id: query[:course_ids]).pluck(:name).map { |m| "<li>#{m}</li>" }.join("") + "</ul>"
    // else
    //   "<p>You are viewing stats for your last 5 completed rounds.</p>"
    // end.html_safe
  }

  short_ranges() {
    // Hash[
    //     '< 25'   => (0..25),
    //     '25-50'  => (25..50),
    //     '50-75'  => (50..75),
    //     '75-100' => (75..100)
    //   ]
  }

  approach_ranges() {
    // Hash[
    //     '100-150' => (100..150),
    //     '150-200' => (150..200),
    //     '200-250' => (200..250),
    //     '>250'    => (250..2000)
    //   ]
  }

  //
  // Display a NaN number as 0.
  //
  displayable_nan(number) {
    console.log(number);

    // number.is_a?(Float) && number.nan? ? 0 : number
  }

  full_progress_bar(stat) {
    console.log(stat);
    // full_display_progress_bar(stat, "")
  }

  progress_bar(stat) {
    console.log(stat);
    // display_progress_bar(stat, "")
  }

  yardage_progress_bar(stat) {
    console.log(stat);
    // display_progress_bar(stat, " YDS")
  }

  percentage_progress_bar(stat) {
    console.log(stat);
    // display_progress_bar(stat, "%")
  }

  display_progress_bar(stat, units, div_styles = '') {
    console.log(stat, units, div_styles);

    //     content_tag(:div, class: "progress list-item-progress-bar") do
    //     content_tag(:div, class: "progress-bar #{div_styles}", style: "width: #{stat}%;") do
    //       "#{stat}#{units}"
    //     end
    //   end
  }

  full_display_progress_bar(stat, units, div_styles = '') {
    console.log(stat, units, div_styles);
    //     content_tag(:div, class: "progress list-item-progress-bar") do
    //     content_tag(:div, class: "progress-bar #{div_styles}", style: "width: 100%;") do
    //       "#{stat}#{units}"
    //     end
    //   end
  }

  sg_progress_bar(stat) {
    console.log(stat);
    // content_tag(:div, class: "progress list-item-progress-bar") do
    // content_tag(:div, class: "progress-bar #{sg_background_color(stat)}", style: "width: 100%;") do
    //   "#{stat}"
    // end
    //  end
  }

  sg_background_color(stroke_gained) {
    console.log(stroke_gained);
    // return "stat-sg-plus" if stroke_gained > 0
    // return "stat-sg-zero" if stroke_gained == 0
    // return "stat-sg-minus" if stroke_gained < 0
  }
}
