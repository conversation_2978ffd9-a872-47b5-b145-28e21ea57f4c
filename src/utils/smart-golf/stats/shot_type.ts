//
// ShotType
//
// This is meant to be included into either the +StrokeStat+
// class or the +StrokePlayed+ class.
//
// A shot/stroke can be a:
//
//   * Penalty Shot
//   * Recovery Shot
//   * Difficult Shot
//   * Long Tee Shot
//   * Short Tee Shot
//   * Approach Shot
//   * Putting Shot
//
//
export class SmartGolfStatsShotType {
  //
  // LongTee
  //
  // ==== What makes it a Long Tee?
  //
  // LongTee is Tee Shots on Holes that are either Par 4 or Par 5.
  // Tee Shots are the very first shots only.
  //
  //
  long_tee?() {
    // self.holes_par.to_i >= 4 &&
    // ( self.ordinal.to_i == 1        ||
    //   self.starting_lie =~ /tee/i
    // )
  }

  //
  // Short
  //
  // ==== What makes it a Short Tee?
  //
  //   * Can start from any lie condition except for on the Green
  //   * Starting distance to pin must be < 100 yds
  //   * Ending distance to pin must be <= 30 yds
  //   * Cannot be a Recovery shot
  //   * Cannot be a Penalty shot
  //   * Strokes that fall within the above specific bucket range. (e.g. 25-50)
  //
  short_tee?() {
    // return false if penalty?
    // return false if recovery?
    // return false if self.starting_lie =~ /green/i
    // return false if self.starting_distance_to_pin >= ONE_HUNDRED_YARDS
    // return false if self.ending_distance_to_pin   > THIRTY_YARDS
    // true
  }

  //
  // Approach
  //
  // ==== What makes it an Approach Shot?
  //
  // The approach is when a stroke is on hole where it is par <= 3,
  // and it is at least 100 yards away from the hole. Also, the stroke
  // cannot be either a recovery shot or a penalty shot.
  //
  // Starting distance needs to be >= 100 yards
  // Dont include Tee Shots on holes that are Par 4 or 5
  // Cannot be a Recovery or Penalty Shot
  //
  //   * No Penalty Shots
  //   * No Recovery Shots
  //   * No Tee Shots on a hole that is Par >= 4
  //   * Stroke's distance to the Hole is >= 100 yards
  //   * Stroke's ending distance to be <= 30 yds
  //
  approach?() {
    // return false if penalty?
    // return false if recovery?
    // return false if self.starting_lie =~ /tee/i &&
    //                 self.holes_par.to_i >= 4
    // return false if self.starting_distance_to_pin < ONE_HUNDRED_YARDS
    // return false if self.ending_distance_to_pin   > THIRTY_YARDS
    // true
  }

  //
  // Putting
  //
  // ==== About
  //
  // Starts on the green.
  //
  putting?() {
    // self.starting_lie =~ /green/i
  }

  //
  // Penalty Shot?
  //
  penalty?() {
    // !!self.penalty_shot
  }

  //
  // Recovery Shot?
  //
  recovery?() {
    // !!self.recovery_shot
  }

  //
  // Difficult Shot?
  //
  difficult?() {
    // !!self.difficult_shot
  }

  //
  // Last Shot?
  //
  last?() {
    // !!self.ends_in_hole
  }
}
