// //
// // endpoint  = SmartGolf::Stats::Formatters::Point.new( 35.2077182261098, -114.04586183163273 )
// // point     = SmartGolf::Stats::Formatters::Point.new( 35.20669139318023, -114.04277919521085 )
// //
// // point.distance_to(*endpoint.to_array)
// // point.get_endpoint( )
// //
// //
// // SmartGolf::Stats::Calculations::Bearing.get_bearing(point.to_array, endpoint.to_array)
// //
// const MILES_TO_YARDS = 1760;
// export class SmartGolfStatsFormattersPoint {
//   // attr_reader :factory, :point, :latitude, :longitude

//   //
//   // Lat: Float
//   // Lng: Float
//   //
//   initialize(lat, lng) {
//     // @factory    = ::RGeo::Geos.factory(srid: 4326)
//     // @point      = to_point(lat, lng)
//     // @latitude   = lat
//     // @longitude  = lng
//   }

//   //
//   // converts point to an array: [Lat, Lng]
//   //
//   to_array() {
//     // [ point.y, point.x ]
//   }

//   //
//   // Get the bearing between the self.point to an endpoint
//   //
//   bearing_to(lat, lng) {
//     // ::SmartGolf::Stats::Calculations::Bearing.get_bearing(to_array, [lat, lng])
//   }

//   //
//   // Generates an endpoint
//   //
//   //   * Bearing: Direction on where the endpoint will be
//   //   * How far away from the self.point to create the new point
//   //
//   get_endpoint(bearing, distance = 0.5) {
//     // ::Geocoder::Calculations.endpoint(to_array, bearing, distance)
//   }

//   //
//   // Calculate the distance between 2 points. Returns distance in feet.
//   //
//   //   * Latitude
//   //   * Longitude
//   //
//   distance_to(lat, lng) {
//     // distance_in_miles = ::Geocoder::Calculations.distance_between(to_array, [lat, lng])
//     // distance_in_miles * MILES_TO_YARDS
//   }

//   //
//   // Get an intersection point
//   //
//   //   * Endpoint: Array: [Lat, Lng]
//   //   * polygon: Polygon Shape
//   //
//   get_intersect_point(endpoint, polygon) {
//     // line          = to_line(*endpoint)
//     // intersection  = clean_intersection( line.intersection(polygon), line ) rescue nil
//     // return nil unless intersection.present?
//     // return nil if intersection.to_s =~ /GEOMETRYCOLLECTION EMPTY/i
//     // intersection  = handle_multiline_string(intersection, line)
//     // if intersection.respond_to?(:y)
//     //   [ intersection.y.round(8), intersection.x.round(8) ]
//     // else
//     //   # line string
//     //   point = clean_intersection( intersection, line )
//     //   [ point.y.round(8), point.x.round(8) ]
//     // end
//   }

//   handle_multiline_string(intersection, line) {
//     //   if intersection.to_s =~ /MULTILINESTRING/i
//     //   intersection.to_a.select { |point| !line.points.include?(point) }.first
//     // else
//     //   intersection
//     // end
//   }

//   //
//   // getting a line string... when we want a point
//   //
//   clean_intersection(intersection, line) {
//     // if intersection.respond_to?(:points)
//     // points = intersection.points.select { |point| !line.points.include?(point) }
//     // points.last
//     // else
//     // intersection
//     // end
//   }

//   //
//   //   * Bearing: Integer / Float
//   //   * Shape: A Geo Shape - Polygon
//   //
//   get_intersect_point_using_computed_endpoint(bearing, polygon) {
//     // endpoint = get_endpoint(bearing, 0.5)
//     // get_intersect_point(endpoint, polygon)
//     let endpoint = this.get_endpoint(bearing, 0.5);
//     return this.get_intersect_point(endpoint, polygon);
//   }

//   //
//   // Get a random point on a line string
//   //
//   get_midpoint(lat, lng) {
//     // ::Geocoder::Calculations.geographic_center( [to_array, [lat, lng]] )
//   }

//   //
//   // generate a line
//   //
//   //   * lat: Latitude   - Float
//   //   * lng: Longitude  - Float
//   //
//   to_line(lat, lng) {
//     //  end_point = to_point(lat, lng)
//     //  factory.line(self.point, end_point)
//   }

//   //
//   // generate a linear ring
//   //
//   to_linear_ring(points) {
//     // factory.linear_ring( points )
//   }

//   //
//   // generate an rgeo polygon
//   //
//   to_polygon(coords) {
//     // points  = to_point(coords)
//     // ring    = to_linear_ring(points)
//     // factory.polygon( ring )
//   }

//   //
//   // generate a point
//   //
//   //   * lat: Float - Latitude
//   //   * lng: Float - Longitude
//   //
//   to_point(lat, lng) {
//     // factory.point(lng, lat)
//   }
// }
