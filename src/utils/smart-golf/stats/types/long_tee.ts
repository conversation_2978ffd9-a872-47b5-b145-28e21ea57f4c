import { SmartGolfStatsTypesBase } from './base';

export class SmartGolfStatsTypesLongTee extends SmartGolfStatsTypesBase {
  //
  // Override SG:
  //
  // Calculates the Strokes Gained for a group of strokes
  //
  // SG/Round
  //
  // calculate_strokes_gained(sg_total = strokes_gained_total) {
  calculate_strokes_gained(sg_total) {
    console.log(sg_total);
    return 0;

    //           return 0 if sg_total.to_f.zero?               ||
    //           all_holes_played_total.zero?      ||
    //           all_rounds_played_total.zero?
    // avg_holes_played    = all_holes_played_total / all_rounds_played_total
    // avg_strokes_gained  = sg_total.to_f / all_holes_played_total
    // avg_strokes_gained * avg_holes_played
  }
}
