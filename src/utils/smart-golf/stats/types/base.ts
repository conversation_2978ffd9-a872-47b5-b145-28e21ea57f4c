import { Logger } from '@nestjs/common';
import _, { isEmpty } from 'lodash';
import { HolesPlayedService } from 'src/holes-played/holes-played.service';
import { RoundService } from 'src/rounds/rounds.service';
import { StrokesPlayedService } from 'src/strokes-played/strokes-played.service';
import { Stats } from '../../stats';
import { SmartGolfStatsCollections } from '../collections';

//  stats = StrokeStat.calculate(["44522"])
export class SmartGolfStatsTypesBase extends SmartGolfStatsCollections {
  private readonly loggerBase = new Logger(SmartGolfStatsTypesBase.name);
  // include SmartGolf::Stats::Calculations::AverageScore
  // include SmartGolf::Stats::Calculations::Sums
  // include SmartGolf::Stats::Calculations::Regulations

  // extend Forwardable

  // # def_delegators :@strokes, :map, :select, :each

  //
  // Accessors for StrokeStat objects and it's associated HolePlayed
  // objects.
  //
  // attr_reader :strokes, :holes, :all_strokes, :course_ids, :holes_played
  strokes: any;
  all_strokes: any;
  holes: any;
  course_ids: any;
  holes_played: any;
  holePlayerService: HolesPlayedService;
  roundService: RoundService;
  strokePlayedService: StrokesPlayedService;
  userSkill: any;
  constructor(
    strokes?,
    all_rounds_strokes?,
    holes_played?,
    holePlayerService?: HolesPlayedService,
    roundService?: RoundService,
    strokePlayedService?: StrokesPlayedService,
    userSkill?: any
  ) {
    super(_.compact([strokes].flat()), strokePlayedService, userSkill, all_rounds_strokes);
    this.strokes = _.compact([strokes].flat());
    this.all_strokes = isEmpty(all_rounds_strokes) ? strokes : [all_rounds_strokes].flat();
    // this.holes        = strokes?.flat_map { |stroke| stroke.hole_played }.uniq
    // console.log({ strokes });

    this.course_ids = strokes?.map((s) => s.course_id);
    this.holes_played = holes_played;
    if (holePlayerService) {
      this.holePlayerService = holePlayerService;
    }
    if (roundService) {
      this.roundService = roundService;
    }
    this.userSkill = userSkill;
  }

  //
  // all round ids
  //
  round_ids() {
    // strokes.group_by(&:round_id).keys.uniq
  }

  //
  // Total number of Strokes
  //
  strokes_total() {
    // strokes.group_by(&:stroke_played_id).keys.uniq.size.to_f
    const group = _.groupBy(this.strokes, 'stroke_played_id');
    const strokePlayedIds = Object.keys(group);
    return _.uniq(strokePlayedIds).length;
  }

  // alias_method :count,                    :strokes_total
  // alias_method :size,                     :strokes_total
  // alias_method :number_of_strokes_taken,  :strokes_total

  //
  // Get clubs from strokes
  //
  clubs() {
    // @clubs ||= strokes.map(&:club)
    const clubs = this.strokes.map((s) => {
      return { name: s.club_name, club_id: s.club_id };
    });
    // const names = Object.keys(clubNames);
    return clubs;
  }

  //
  // Get a specific club by name
  //
  select_club(name) {
    this.loggerBase.debug(`SELECT_CLUB`);
    if (isEmpty(name)) {
      return [];
    }
    const clubs = this.clubs();
    return clubs.filter((c) => c.name == name);
    // return [] if name.to_s.empty?
    // clubs.select { |club| club.name =~ /#{name}/i }
  }

  //
  // select shots for a specific user
  //
  select_by_user(user_id) {
    console.log(user_id);

    // return [] if user_id.blank?
    // user_strokes = strokes.select { |stroke| stroke.user_id == user_id }
    // ::SmartGolf::Stats::Types::User.new(user_strokes)
  }

  // alias_method :for_user, :select_by_user

  users() {
    // return [] if strokes.blank?
    // strokes.uniq { |stroke| stroke.user_id }
    if (!isEmpty(this.strokes)) {
      return [];
    }
    return _.groupBy(this.strokes, 'user_id');
  }

  //
  // Total Score for all strokes
  //
  // The size of all strokes minus the total number of strokes that
  // have been removed by the user/system.
  //
  total_score() {
    if (isEmpty(this.strokes)) {
      return 0;
    }
    const strokeTotal = this.strokes_total();
    const number_of_shots_removed = this.number_of_shots_removed();
    this.loggerBase.debug({ strokeTotal, number_of_shots_removed });
    return strokeTotal - number_of_shots_removed;
    // return 0 if strokes.empty?
    // @total_score ||= strokes_total - number_of_shots_removed
  }

  total_score_basic() {
    // return 0 if holes_played.empty?
    // holes_played.sum {|hole| hole.score(Round::ROUND_MODE_BASIC).to_i}
    this.loggerBase.log(`TOTAL_SCORE_BASIC`);
    if (!this.holes_played || isEmpty(this.holes_played)) {
      return 0;
    }
    let total = 0;
    let hole_numbers_players = 0;
    for (const hole of this.holes_played) {
      const hole_score = +hole.score;
      if (hole_score > 0) {
        total += hole_score;
        if (+hole.score > 0) {
          hole_numbers_players += 1;
        }
        // total += await this.holePlayerService.score(ROUND.ROUND_MODE_BASIC, hole);
      }
    }
    this.loggerBase.log({ total });

    return { total, hole_numbers_players };
  }

  total_score_multiplayer() {
    // return 0 if holes_played.empty?
    // holes_played.sum {|hole| hole.score(Round::ROUND_MODE_MULTIPLAYER).to_i}
    this.loggerBase.log(`TOTAL_SCORE_MULTIPLAYER`);

    if (!this.holes_played || isEmpty(this.holes_played)) {
      return 0;
    }
    let total = 0;
    // total += await this.holePlayerService.score(ROUND.ROUND_MODE_MULTIPLAYER, hole);
    let hole_numbers_players = 0;
    for (const hole of this.holes_played) {
      if (hole.score) {
        total += +hole.score;
        if (+hole.score > 0) {
          hole_numbers_players += 1;
        }
        // total += await this.holePlayerService.score(ROUND.ROUND_MODE_BASIC, hole);
      }
    }

    return { total, hole_numbers_players };
    // holes_played.sum {|hole| hole.score(Round::ROUND_MODE_MULTIPLAYER).to_i}
  }

  //
  // Total Shots removed by the user is used to substract from
  // the total shots to get the total score for round(s).
  //
  number_of_shots_removed() {
    // return 0 if holes.blank?
    // @number_of_shots_removed ||= holes.inject(0) do |sum, hole|
    //   sum += hole.shots_removed.to_i
    // end
    return 0;
  }

  //
  // Putts per Round for all shot/stroke types
  //
  putts_per_round() {
    const holeTotal = this.holes_played_total();
    if (holeTotal == 0) {
      return 0;
    }
    const nbPutts = this.number_of_putts();
    return (nbPutts / holeTotal) * 18;
    // return 0 if holes_played_total.zero?
    // number_of_putts.to_f / holes_played_total * 18
  }

  //
  // Number of times where only a single putt was hit on a hole
  //
  number_of_single_putts_per_hole() {
    const group = _.groupBy(this.starting_on_green(), 'hole_played_id');
    const holeIds = Object.keys(group);
    let count = 0;
    for (const holeId of holeIds) {
      if (group[holeId].length == 1) {
        count += 1;
      }
    }
    return count;
    // group = starting_on_green.group_by { |stroke| stroke.hole_played_id }
    // group.select do |hole_id, strokes|
    //   strokes.size == 1
    // end.size
  }

  //
  // How many holes had a single putt
  // returns a percentage
  //
  one_putt_per_hole() {
    // return 0 if number_of_single_putts_per_hole.zero? || number_of_putts.zero?
    // number_of_single_putts_per_hole.to_f / holes_played_total * 100
    const numberSinglePuttPerHole = this.number_of_single_putts_per_hole();
    const numberOfPutts = this.number_of_putts();
    if (numberSinglePuttPerHole == 0 || numberOfPutts == 0) {
      return 0;
    }
    const holesPlayedTotal = this.holes_played_total();
    return (numberSinglePuttPerHole / holesPlayedTotal) * 100;
  }
  holes_played_total(): any {
    this.loggerBase.log('HOLES_PLAYED_TOTAL');
    const groupHoleId = _.groupBy(this.strokes, 'hole_played_id');
    const holedIds = Object.keys(groupHoleId);
    return holedIds.length;
    // strokes.flat_map { |stroke| stroke.hole_played_id }.uniq.size.to_f
    // return _.groupBy(this.strokes, 'hole_played_id').length;
  }
  number_of_putts() {
    // return 0 if starting_on_green.empty?
    // starting_on_green.size.to_f
    const startOnGreen = this.starting_on_green();
    if (isEmpty(startOnGreen)) {
      return 0;
    }
    return startOnGreen.length;
  }
  //
  // get shot distance percentile. defaults to 75th percentile
  //
  get_percentile(quadrant = 75, unit_in_use = 'yards') {
    this.loggerBase.log({ quadrant, unit_in_use });

    //   if unit_in_use == 'yards'
    //   return calculate_percentile(quadrant) * METERS_TO_YARDS
    // elsif unit_in_use == 'meters'
    //   return calculate_percentile(quadrant)
    // end
    // 0

    if (unit_in_use == 'yards') {
      return this.calculate_percentile(quadrant) * Stats.METERS_TO_YARDS;
    }

    if (unit_in_use == 'meters') {
      return this.calculate_percentile(quadrant);
    }

    return 0;
  }

  //
  // All strokes that were hit with a driver club
  //
  //   e.g.
  //     stats = StrokeStat.calculate([40128, 25034, 33528, 44801, 16889])
  //     stats.strokes_shot_with_driver.strokes_gained
  //     stats.strokes_shot_with_driver.size
  //
  with_driver_club() {
    // @strokes_shot_with_driver ||= ::SmartGolf::Stats::Types::DriverClub.new(all_driver_clubs, all_strokes)
    return new SmartGolfStatsTypesBase(
      this.all_driver_clubs(),
      this.all_strokes,
      this.holes,
      this.holePlayerService,
      this.roundService,
      this.strokePlayedService,
      this.userSkill
    );
  }

  //
  // All strokes that were hit with a three wood club
  //
  //   e.g.
  //     stats = StrokeStat.calculate([40128, 25034, 33528, 44801, 16889])
  //     stats.strokes_shot_with_three_wood.strokes_gained
  //     stats.strokes_shot_with_three_wood.size
  //
  with_three_wood_club() {
    // @strokes_shot_with_three_wood ||= ::SmartGolf::Stats::Types::ThreeWoodClub.new(all_three_wood_clubs, all_strokes)
    return new SmartGolfStatsTypesBase(
      this.all_three_wood_clubs(),
      this.all_strokes,
      this.holes,
      this.holePlayerService,
      this.roundService,
      this.strokePlayedService,
      this.userSkill
    );
  }

  //
  // All strokes that were hit with all clubs BUT a driver and 3-fw
  //
  //   e.g.
  //     stats = StrokeStat.calculate([40128, 25034, 33528, 44801, 16889])
  //     stats.strokes_shot_with_other.strokes_gained
  //     stats.strokes_shot_with_other.size
  //
  with_other_clubs() {
    // @strokes_shot_with_other ||= ::SmartGolf::Stats::Types::OtherClubs.new(all_other_clubs, all_strokes)
    return new SmartGolfStatsTypesBase(
      this.all_other_clubs(),
      this.all_strokes,
      this.holes,
      this.holePlayerService,
      this.roundService,
      this.strokePlayedService,
      this.userSkill
    );
  }
}
