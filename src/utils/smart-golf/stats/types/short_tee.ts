// import { range } from 'lodash';
import _ from 'lodash';
import { isEmpty, range } from 'lodash';
import { includeStr } from 'src/utils/utils';
import { Stats } from '../../stats';
import { SmartGolfStatsTypesBase } from './base';
import { SmartGolfStatsTypesProximity } from './proximity';

const VALID_DISTANCE_BUCKETS = {
  BUCKET_ONE: range(0, 25),
  BUCKET_TWO: range(25, 50),
  BUCKET_THREE: range(50, 75),
  BUCKET_FOUR: range(75, 100),

  METERS_BUCKET_ONE: range(0, 25),
  METERS_BUCKET_TWO: range(25, 50),
  METERS_BUCKET_THREE: range(50, 75),
  METERS_BUCKET_FOUR: range(75, 100),
};
export class SmartGolfStatsTypesShortTee extends SmartGolfStatsTypesBase {
  //
  // Shots for a given range
  //
  // e.g.
  //
  //   shots_for_range( :bucket_one )
  //
  calculateDistanceBucket(bucketType) {
    const shots = this.shots_for_range(bucketType);

    return new SmartGolfStatsTypesProximity(
      shots,
      this.all_strokes,
      this.holes,
      this.holePlayerService,
      this.roundService,
      this.strokePlayedService,
      this.userSkill
    );
  }
  shots_for_range(range_key) {
    if (isEmpty(this.strokes)) {
      return [];
    }
    const range = VALID_DISTANCE_BUCKETS[range_key];

    return this.strokes.filter((stroke) => {
      const [min, max] = this.determine_min_max(range, range_key);
      const distance_in_yards = +stroke.starting_distance_to_pin * Stats.METERS_TO_YARDS;
      const isValid = distance_in_yards >= min && distance_in_yards < max;
      return isValid;
    });
  }

  //
  // Account for units (yards/meters) and determine the min/max range
  //
  determine_min_max(range, units) {
    let min = +_.min(range);
    let max = +_.max(range) + 1;
    if (includeStr(units, 'meter')) {
      min = min / Stats.METERS_TO_YARDS;
      max = max / Stats.METERS_TO_YARDS;
    }
    return [min, max];
  }

  //
  // returns just the ranges
  //
  bucket_labels() {
    // %w(<25 25-50 50-75 75-100)
  }

  //
  // bucket names
  //
  bucket_names() {
    // VALID_DISTANCE_BUCKETS.map { |k,v| k unless k.to_s.starts_with?("meter") }.compact
  }

  //
  // given a distance, it'll tell you what bucket the shot belongs in.
  // currently being used for a debugging page
  //
  within_bucket?(distance) {
    console.log(distance);
    // case
    // when (0...25).cover?(distance.to_i)
    //   :bucket_one
    // when (25...50).cover?(distance.to_i)
    //   :bucket_two
    // when (50...75).cover?(distance.to_i)
    //   :bucket_three
    // when (75...100).cover?(distance.to_i)
    //   :bucket_four
    // else
    //   :none
    // end
  }
}
