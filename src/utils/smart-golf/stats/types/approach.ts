import _, { isEmpty, range } from 'lodash';
import { includeStr } from 'src/utils/utils';
import { Stats } from '../../stats';
import { SmartGolfStatsTypesBase } from './base';
import { SmartGolfStatsTypesProximity } from './proximity';

//
// Distances that are used for Proximity to the pin
//
// These are in YARDS
//
const VALID_DISTANCE_BUCKETS = {
  BUCKET_ONE: range(100, 150),
  BUCKET_TWO: range(150, 200),
  BUCKET_THREE: range(200, 250),
  BUCKET_FOUR: range(250, 1000),

  METERS_BUCKET_ONE: range(100, 150),
  METERS_BUCKET_TWO: range(150, 200),
  METERS_BUCKET_THREE: range(200, 250),
  METERS_BUCKET_FOUR: range(250, 1000),
};

export class SmartGolfStatsTypesApproach extends SmartGolfStatsTypesBase {
  //
  // Shots/Strokes that fall into a specific starting
  // distance from the pin.
  //

  // VALID_DISTANCE_BUCKETS.keys.each do |key|
  //   method_name = key
  //   ivar        = "@#{key}"

  //   define_method(key) do |units = :yards|
  //     # return [] if strokes.blank?
  //     return Proximity.new([]) if strokes.blank?

  //     return instance_variable_get(ivar) if instance_variable_defined?(ivar)
  //     instance_variable_set(ivar, Proximity.new( shots_for_range(key, units), all_strokes ))
  //   end

  //   define_method("number_of_shots_for_#{key}") do
  //     return 0 if self.send(key).blank?
  //     self.send(key).size.to_f
  //   end
  // end
  //
  // Shots for a given range
  //
  // e.g.
  //
  //   shots_for_range( :bucket_one )
  //
  calculateDistanceBucket(bucketType) {
    const shots = this.shots_for_range(bucketType);

    return new SmartGolfStatsTypesProximity(
      shots,
      this.all_strokes,
      this.holes,
      this.holePlayerService,
      this.roundService,
      this.strokePlayedService,
      this.userSkill
    );
  }
  shots_for_range(range_key) {
    // return [] if strokes.blank?
    // range = VALID_DISTANCE_BUCKETS[range_key.to_sym]
    // min, max = determine_min_max(range, units)
    // strokes.select do |stroke|
    //   distance_in_yards = ( stroke.starting_distance_to_pin * METERS_TO_YARDS )
    //   distance_in_yards >= min && distance_in_yards < max
    // end
    if (isEmpty(this.strokes)) {
      return [];
    }
    const range = VALID_DISTANCE_BUCKETS[range_key];

    return this.strokes.filter((stroke) => {
      // const min = +_.min(range);
      // const max = +_.max(range) + 1;
      // let distance_in_yards = 0;
      // if (includeStr(range_key, 'yard')) {
      //   distance_in_yards = +stroke.starting_distance_to_pin * Stats.METERS_TO_YARDS;
      // } else {
      //   distance_in_yards = +stroke.starting_distance_to_pin;
      // }

      let min = +_.min(range);
      let max = +_.max(range) + 1;
      if (includeStr(range_key, 'meter')) {
        min = min / Stats.METERS_TO_YARDS;
        max = max / Stats.METERS_TO_YARDS;
      }
      const distance_in_yards = +stroke.starting_distance_to_pin * Stats.METERS_TO_YARDS;
      const isValid = distance_in_yards >= min && distance_in_yards < max;
      return isValid;
    });
  }

  //
  // Account for units (yards/meters) and determine the min/max range
  //
  determine_min_max(range, units) {
    console.log(range, units);

    //   if units.to_s =~ /^meter/i
    //   [
    //     ( range.min / METERS_TO_YARDS ),
    //     ( (range.max + 1) / METERS_TO_YARDS )
    //   ]
    // else
    //   [ range.min, (range.max + 1) ]
    // end
  }

  //
  // returns just the ranges
  //
  bucket_labels() {
    // %w(100-150 150-200 200-250 >250)
  }

  //
  // bucket names
  //
  bucket_names() {
    // VALID_DISTANCE_BUCKETS.map { |k,v| k unless k.to_s.starts_with?("meter") }.compact
  }

  //
  // given a distance, it'll tell you what bucket the shot belongs in.
  // currently being used for a debugging page
  //
  within_bucket(distance) {
    console.log(distance);

    // case
    // when (100...150).cover?(distance.to_i)
    //   :bucket_one
    // when (150...200).cover?(distance.to_i)
    //   :bucket_two
    // when (200...250).cover?(distance.to_i)
    //   :bucket_three
    // when (250...1000).cover?(distance.to_i)
    //   :bucket_four
    // else
    //   :none
    // end
  }
}
