export class SmartGolfStatsTypesAll {
  // include SmartGolf::Stats::Calculations::Sums

  //
  // To allow chaining of methods
  //
  //   e.g.
  //     stats = StrokeStat.calculate([40128, 25034, 33528, 44801, 16889])
  //     stats.long_tee.strokes_gained
  //     stats.long_tee.fairways_in_regulation
  //
  long_tee() {
    // @long_tee ||= ::SmartGolf::Stats::Types::LongTee.new(long_tee_shots, strokes)
  }

  // alias_method :long, :long_tee
  // alias_method :driving, :long_tee

  //
  // Approach Stats
  //
  //   e.g.
  //     stats = StrokeStat.calculate([40128, 25034, 33528, 44801, 16889])
  //     stats.approach.strokes_gained
  //     stats.approach.fairways_in_regulation
  //
  approach() {
    // @approach ||= ::SmartGolf::Stats::Types::Approach.new(approach_shots, strokes)
  }

  //
  // Short Tee Stats
  //
  //   e.g.
  //     stats = StrokeStat.calculate([40128, 25034, 33528, 44801, 16889])
  //     stats.short_tee.strokes_gained
  //     stats.short_tee.greens_in_regulation
  //
  short_tee() {
    // @short_tee ||= ::SmartGolf::Stats::Types::ShortTee.new(short_tee_shots, strokes)
  }
  // alias_method :short, :short_tee

  //
  // Putting Stats
  //
  //   e.g.
  //     stats = StrokeStat.calculate([40128, 25034, 33528, 44801, 16889])
  //     stats.putting.strokes_gained
  //     stats.putting.size
  //
  putting() {
    // @putting ||= ::SmartGolf::Stats::Types::Putting.new(putting_shots, strokes)
  }

  //
  // Overall Stats
  //
  //   e.g.
  //     stats = StrokeStat.calculate([40128, 25034, 33528, 44801, 16889])
  //     stats.overall.sg
  //     stats.overall.strokes_gained_for_approach
  //
  //
  overall() {
    // @overall ||= ::SmartGolf::Stats::Types::Overall.new(strokes)
  }

  // alias_method :classic, :overall
}
