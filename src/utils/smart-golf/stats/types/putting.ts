import _ from 'lodash';
import { isEmpty, range } from 'lodash';
import { BUILD_GRAPH_TYPES } from 'src/stats/stats.const';
import { includeStr } from 'src/utils/utils';
import { Stats } from '../../stats';
import { SmartGolfStatsTypesBase } from './base';
import { SmartGolfStatsTypesProximity } from './proximity';

const VALID_DISTANCE_BUCKETS = {
  BUCKET_ONE: range(0, 3),
  BUCKET_TWO: range(3, 6),
  BUCKET_THREE: range(6, 9),
  BUCKET_FOUR: range(9, 15),
  BUCKET_FIVE: range(15, 21),
  BUCKET_SIX: range(21, 30),
  BUCKET_SEVEN: range(30, 1000),

  METERS_BUCKET_ONE: range(0, 1),
  METERS_BUCKET_TWO: range(1, 2),
  METERS_BUCKET_THREE: range(2, 3),
  METERS_BUCKET_FOUR: range(3, 5),
  METERS_BUCKET_FIVE: range(5, 7),
  METERS_BUCKET_SIX: range(7, 10),
  METERS_BUCKET_SEVEN: range(10, 1000),
};
const PRO_NUMBER_OF_PUTTS_PER_ROUND = [11.1, 4.5, 2.4, 3.4, 1.9, 2.6, 3.1];
const PRO_PERCENTAGE_HOLED = [99.2, 81.7, 51.2, 31.3, 17.6, 10.0, 4.0];
const DISTANCE_TYPE_DEFAULT = {
  BUCKET_ONE: '0...3',
  BUCKET_TWO: '3...6',
  BUCKET_THREE: '6...9',
  BUCKET_FOUR: '9...15',
  BUCKET_FIVE: '15...21',
  BUCKET_SIX: '21...30',
  BUCKET_SEVEN: '30...1000',
};
export class SmartGolfStatsTypesPutting extends SmartGolfStatsTypesBase {
  //
  // Distances that are used for Proximity to the pin
  //
  // These are in FEET
  //

  //
  // <1m, 1-2m, 2-3m, 3-5m, 5-7m, 7-10m, >10m
  //

  //
  // Shots/Strokes that fall into a specific starting
  // distance from the pin.
  //
  // example:
  //
  //     stats.putting.bucket_four
  //
  calculateDistanceBucket(bucketType) {
    const shots = this.shots_for_range(bucketType);
    return new SmartGolfStatsTypesProximity(
      shots,
      this.all_strokes,
      this.holes,
      this.holePlayerService,
      this.roundService,
      this.strokePlayedService,
      this.userSkill
    );
  }
  //
  // Shots for a given range
  //
  // e.g.
  //
  //   shots_for_range( :bucket_two )
  //
  shots_for_range(range_key) {
    if (isEmpty(this.strokes)) {
      return [];
    }
    const range = VALID_DISTANCE_BUCKETS[range_key];
    return this.strokes.filter((stroke) => {
      let distance_in_feet = 0;
      if (includeStr(range_key, 'meter')) {
        distance_in_feet = stroke.starting_distance_to_pin;
      } else {
        distance_in_feet = stroke.starting_distance_to_pin * Stats.METERS_TO_FEET;
      }
      return distance_in_feet >= _.min(range) && distance_in_feet < +_.max(range) + 1;
    });
  }

  //
  // get the green distances
  //
  get_green_distances(shot) {
    console.log(shot);
    // green_distance(shot).get_distances
  }

  last_approach_shot(shot) {
    console.log(shot);
    // shot.hole_played.last_approach_shot
  }

  green_polygon(shot) {
    console.log(shot);
    // shot.round.course.convert_to_polygon( shot.hole_name, "green" ).try(:first)
  }

  green_map(shot) {
    console.log(shot);
    // SmartGolf::Stats::Calculations::BuildGreenDirections.new(
    // last_approach_shot(shot).coords,
    // green_polygon(shot),
    // shot.hole_played.try(:flag)
    // )
  }

  green_distance(shot) {
    console.log(shot);
    // SmartGolf::Stats::Calculations::GreenDistances.new(
    //   green_map(shot).four_green_points,
    //   [ shot.latitude, shot.coords.x ]
    // )
  }

  //
  // returns just the ranges
  //
  bucket_labels() {
    // %w(<3 3-6 6-9 9-15 15-21 21-30 >30)
  }

  //
  // bucket names
  //
  bucket_names() {
    // VALID_DISTANCE_BUCKETS.map { |k,v| k unless k.to_s.starts_with?("meter") }.compact
  }

  //
  // bucket ranges
  //
  bucket_ranges() {
    // VALID_DISTANCE_BUCKETS.map { |k,v| v unless k.to_s.starts_with?("meter") }.compact
  }

  //
  // Get the bucket name by a given range
  //
  get_bucket_name_by_range(range) {
    const bucketKeys = Object.keys(VALID_DISTANCE_BUCKETS);
    const bucketValues = Object.values(VALID_DISTANCE_BUCKETS);
    const indexRange = bucketValues.indexOf(range);
    return bucketKeys[indexRange];
    // VALID_DISTANCE_BUCKETS.find { |k,v| v == range }.first
  }

  get_bucket_range_by_name(name) {
    const bucketKeys = Object.keys(VALID_DISTANCE_BUCKETS);
    const bucketValues = Object.values(VALID_DISTANCE_BUCKETS);
    const indexName = bucketKeys.indexOf(name);
    const value = bucketValues[indexName];
    return `(${value[0] - value.pop()})`;
    // range = VALID_DISTANCE_BUCKETS.find { |k,v| k.to_s == name.to_s }.last
    // "(#{range.min}-#{range.max+1})"
  }

  //
  // build graph data, without grouping by ranges
  //
  graph_in_meters_for(type) {
    console.log(type);
    // strokes.select do |stroke|
    //   distance_in_feet = stroke.starting_distance_to_pin
    //   distance_in_feet >= range.min && distance_in_feet < (range.max + 1)
    // end
    return this.strokes.filter((stroke) => {
      const distance_in_feet = stroke.starting_distance_to_pin * Stats.METERS_TO_FEET;
      const min = +_.min(VALID_DISTANCE_BUCKETS[type]);
      const max = +_.max(VALID_DISTANCE_BUCKETS[type]) + 1;
      return distance_in_feet >= min && distance_in_feet < max;
    });
  }

  //
  // Builds an array for graphing
  //
  // e.g.
  //
  //   build_graph_data_for :sg, :putts, :holed, :sg_per_shot
  //
  build_graph_data_for(type) {
    const data = [];
    const distanceTypes = DISTANCE_TYPE_DEFAULT;
    const keysDistance = Object.keys(distanceTypes);
    switch (type) {
      // # Strokes Gained
      case BUILD_GRAPH_TYPES.SG:
        for (const key of keysDistance) {
          const statsBucket = this.calculateDistanceBucket(key);
          const stats = {
            distance: distanceTypes[key],
            value: +statsBucket.strokes_gained().toFixed(1),
            total: +statsBucket.strokes_total().toFixed(1),
            units: 'feet',
          };
          data.push(stats);
        }
        break;
      // # Strokes Gained per Shot
      case BUILD_GRAPH_TYPES.SG_PER_SHOT:
        for (const key of keysDistance) {
          const statsBucket = this.calculateDistanceBucket(key);
          const stats = {
            distance: distanceTypes[key],
            value: +statsBucket.strokes_gained_per_shot().toFixed(1),
            total: +statsBucket.strokes_total().toFixed(1),
            units: 'feet',
          };
          data.push(stats);
        }
        break;

      // # Number of Putts Per Round
      case BUILD_GRAPH_TYPES.PUTTS:
        for (const key of keysDistance) {
          const index = keysDistance.indexOf(key);
          const statsBucket = this.calculateDistanceBucket(key);
          const stats = {
            distance: distanceTypes[key],
            value: +statsBucket.number_of_putts_per_round().toFixed(1),
            pro_value: PRO_NUMBER_OF_PUTTS_PER_ROUND[index],
            units: 'feet',
          };
          data.push(stats);
        }
        break;
      // # Percentage Holed
      default:
        for (const key of keysDistance) {
          const index = keysDistance.indexOf(key);
          const statsBucket = this.calculateDistanceBucket(key);
          const stats = {
            distance: distanceTypes[key],
            percentage: +statsBucket.percentage_of_shots_holed().toFixed(1),
            pro_percentage: PRO_PERCENTAGE_HOLED[index],
            units: 'feet',
          };
          data.push(stats);
        }
        break;
    }
    return data;
  }
}
