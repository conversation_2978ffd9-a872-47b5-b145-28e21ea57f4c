//
// Calculate Stroke Stats
//
// e.g.
//
// stats = StrokeStat.calculate([40128, 25034, 33528, 44801, 16889])
// stats = StrokeStat.calculate([33529, 25332, 16905, 11664])
// stats = StrokeStat.calculate([40128, 25034, 33528, 44801, 16889, 33529, 25332, 16905, 11664])
import _, { isEmpty } from 'lodash';
import { HolesPlayedService } from 'src/holes-played/holes-played.service';
import { RoundService } from 'src/rounds/rounds.service';
import { StrokesPlayedService } from 'src/strokes-played/strokes-played.service';
import { SmartGolfStatsCollections } from './collections';
import { SmartGolfStatsTypesApproach } from './types/approach';
import { SmartGolfStatsTypesBase } from './types/base';
import { SmartGolfStatsTypesDriverClub } from './types/driver_club';
import { SmartGolfStatsTypesLongTee } from './types/long_tee';
import { SmartGolfStatsTypesOverall } from './types/overall';
import { SmartGolfStatsTypesPutting } from './types/putting';
import { SmartGolfStatsTypesShortTee } from './types/short_tee';
import { SmartGolfStatsTypesThreeWoodClub } from './types/three_wood_club';
import { SmartGolfStatsTypesUser } from './types/user';

//
export class CalculateStrokeStats extends SmartGolfStatsCollections {
  private shots: any;
  all_strokes: any;
  holes: any;
  holes_played: any;
  holePlayerService: HolesPlayedService;
  roundService: RoundService;
  strokePlayedService: StrokesPlayedService;
  // extend Forwardable

  // def_delegators :@shots, :size, :map, :select

  //#
  //# Methods for selecting shot groupings
  //#
  // include ::SmartGolf::Stats::ShotGroups

  // #
  // # Array of StrokeStat Objects
  // #
  // attr_reader :shots

  //#
  //# Initialization
  //#
  //# ## Arguments:
  //#
  //#   shots:        An Array or a Single Stat Object(s)
  //#
  //# ## Example:
  //#
  //#   round_ids = [44801,40128,33528,25034,16889]
  //#   stats     = StrokeStat.calculate(round_ids)
  //#   stats.number_of_strokes # => 81
  //#   stats.long_tee.size     # => Total number of long tee shots
  //#   stats.approach.sg       # => Strokes Gained for Approach shots
  //#
  constructor(
    shots,
    all_rounds_strokes?,
    holes_played?,
    holePlayerService?: HolesPlayedService,
    roundService?: RoundService,
    strokePlayedService?: StrokesPlayedService,
    userSkill?: any
  ) {
    super(_.compact([shots].flat()), strokePlayedService, userSkill);
    this.shots = _.compact([shots].flat());
    this.all_strokes = isEmpty(all_rounds_strokes) ? shots : [all_rounds_strokes].flat();
    // this.holes        = strokes.flat_map { |stroke| stroke.hole_played }.uniq
    this.holes_played = holes_played;
    if (holePlayerService) {
      this.holePlayerService = holePlayerService;
    }
    if (roundService) {
      this.roundService = roundService;
    }
    if (strokePlayedService) {
      this.strokePlayedService = strokePlayedService;
    }
  }
  initialize(shots: any) {
    // this.shots = [shots].flatten.compact;
    this.shots = _.compact([shots].flat());
    //# required to allow this class to have access to collections like:
    //# long_tee_shots, approach_shots, short_tee_shots, putting_shots,
    //# etc.
    // self.class.strokes = [ shots ].flatten.compact
  }

  round_ids() {
    const groupByRoundId = _.groupBy(this.shots, 'round_id');
    const roundIds = Object.keys(groupByRoundId);
    return _.uniq(roundIds);
    // this.shots.group_by(&:round_id).keys.uniq
  }

  course_ids() {
    const groupByCourseId = _.groupBy(this.shots, 'course_id');
    const courseIds = Object.keys(groupByCourseId);
    return _.uniq(courseIds);
    // shots.group_by(&:course_id).keys.uniq
  }
  igolf_course_ids() {
    const groupByCourseId = _.groupBy(this.shots, 'igolf_course_id');
    const courseIds = Object.keys(groupByCourseId);
    return _.uniq(courseIds);
  }

  //#
  //# To allow chaining of methods
  //#
  //#   e.g.
  //#     stats = StrokeStat.calculate([40128, 25034, 33528, 44801, 16889])
  //#     stats.long_tee.strokes_gained
  //#     stats.long_tee.fairways_in_regulation
  //#
  long_tee() {
    // @long_tee ||= ::SmartGolf::Stats::Types::LongTee.new(long_tee_shots, shots)
    return new SmartGolfStatsTypesLongTee(
      this.long_tee_shots(this.shots),
      this.shots,
      this.holes,
      this.holePlayerService,
      this.roundService,
      this.strokePlayedService,
      this.userSkill
    );
  }

  // alias_method :long, :long_tee

  //#
  //# To allow chaining of methods
  //#
  //#   e.g.
  //#     stats = StrokeStat.calculate([40128, 25034, 33528, 44801, 16889])
  //#     stats.long_tee.strokes_gained
  //#     stats.long_tee.fairways_in_regulation
  //#
  first_long_tee() {
    // @first_long_tee ||= ::SmartGolf::Stats::Types::LongTee.new(first_long_tee_shots, shots)
    return new SmartGolfStatsTypesLongTee(
      this.first_long_tee_shots(this.shots),
      this.shots,
      this.holes,
      this.holePlayerService,
      this.roundService,
      this.strokePlayedService,
      this.userSkill
    );
  }

  //#
  //# Approach Stats
  //#
  //#   e.g.
  //#     stats = StrokeStat.calculate([40128, 25034, 33528, 44801, 16889])
  //#     stats.approach.strokes_gained
  //#     stats.approach.fairways_in_regulation
  //#
  approach() {
    // @approach ||= ::SmartGolf::Stats::Types::Approach.new(approach_shots, shots)
    return new SmartGolfStatsTypesApproach(
      this.approach_shots(),
      this.shots,
      this.holes,
      this.holePlayerService,
      this.roundService,
      this.strokePlayedService,
      this.userSkill
    );
  }

  //#
  //# Short Tee Stats
  //#
  //#   e.g.
  //#     stats = StrokeStat.calculate([40128, 25034, 33528, 44801, 16889])
  //#     stats.short_tee.strokes_gained
  //#     stats.short_tee.greens_in_regulation
  //#
  short_tee() {
    // @short_tee ||= ::SmartGolf::Stats::Types::ShortTee.new(short_tee_shots, shots)
    return new SmartGolfStatsTypesShortTee(
      this.short_tee_shots(this.shots),
      this.shots,
      this.holes,
      this.holePlayerService,
      this.roundService,
      this.strokePlayedService,
      this.userSkill
    );
  }

  // alias_method :short, :short_tee

  //#
  //# Putting Stats
  //#
  //#   e.g.
  //#     stats = StrokeStat.calculate([40128, 25034, 33528, 44801, 16889])
  //#     stats.putting.strokes_gained
  //#     stats.putting.size
  //#
  putting() {
    // @putting ||= ::SmartGolf::Stats::Types::Putting.new(putting_shots, shots)
    return new SmartGolfStatsTypesPutting(
      this.putting_shots(this.shots),
      this.shots,
      this.holes,
      this.holePlayerService,
      this.roundService,
      this.strokePlayedService,
      this.userSkill
    );
  }

  //#
  //# Putting Stats
  //#
  //#   e.g.
  //#     stats = StrokeStat.calculate([40128, 25034, 33528, 44801, 16889])
  //#     stats.putting.strokes_gained
  //#     stats.putting.size
  //#
  putting_for_club(club_id) {
    // @putting_for_club ||= ::SmartGolf::Stats::Types::Putting.new(putting_shots_for_club(club_id), shots)
    return new SmartGolfStatsTypesPutting(
      this.putting_shots_for_club(club_id),
      this.shots,
      this.holes,
      this.holePlayerService,
      this.roundService,
      this.strokePlayedService,
      this.userSkill
    );
  }

  //#
  //# Overall Stats
  //#
  //#   e.g.
  //#     stats = StrokeStat.calculate([40128, 25034, 33528, 44801, 16889])
  //#     stats.overall.sg
  //#     stats.overall.strokes_gained_for_approach
  //#
  //#
  overall() {
    // @overall ||= ::SmartGolf::Stats::Types::Overall.new(shots)
    return new SmartGolfStatsTypesOverall(
      this.shots,
      this.shots,
      this.holes,
      this.holePlayerService,
      this.roundService,
      this.strokePlayedService,
      this.userSkill
    );
  }

  // alias_method :classic, :overall

  //#
  //# Select shots for a specific user
  //#
  //# e.g.
  //#   stats = StrokeStat.calculate([40128, 25034, 33528, 44801, 16889])
  //#   stats.for_user(18)
  //#
  for_user(user_id) {
    // return [] if user_id.blank?
    // user_shots = shots.select { |shot| shot.user_id == user_id }
    // ::SmartGolf::Stats::Types::User.new(user_shots)
    if (!user_id) {
      return [];
    }
    const user_shots = this.shots.filter((shot) => shot.user_id == user_id);
    return new SmartGolfStatsTypesUser(user_shots);
  }
  // alias_method :select_by_user, :for_user

  //#
  //# All strokes that were hit with a driver club
  //#
  strokes_shot_with_driver() {
    // @strokes_shot_with_driver ||= ::SmartGolf::Stats::Types::DriverClub.new(all_driver_clubs, shots)
    return new SmartGolfStatsTypesDriverClub(this.all_driver_clubs(), this.shots);
  }

  //#
  //# All strokes that were hit with a three wood club
  //#
  strokes_shot_with_three_wood() {
    // @strokes_shot_with_three_wood ||= ::SmartGolf::Stats::Types::ThreeWoodClub.new(all_three_wood_clubs, shots)
    return new SmartGolfStatsTypesThreeWoodClub(this.all_three_wood_clubs(), this.shots);
  }

  //#
  //# All strokes that were hit with all clubs BUT a driver and 3-fw
  //#
  strokes_shot_with_other() {
    // @strokes_shot_with_other ||= ::SmartGolf::Stats::Types::OtherClubs.new(all_other_clubs, shots)
    return new SmartGolfStatsTypesBase(this.all_other_clubs(), this.shots);
  }
}
