// require 'csv'

export class SmartGolfStatsExportersCsv {
  //
  // export to a CSV file
  //
  to_csv() {
    // ::CSV.generate(headers: true) do |csv|
    // csv << csv_headers
    // strokes.sort_by{|stroke| stroke.round_id <=> stroke.round_id}.each do |stroke|
    //   csv << [
    //     stroke.stroke_played_id,
    //     stroke.round_id,
    //     stroke.round_played_on.try(:to_date),
    //     (stroke.course_name || stroke.facility.name),
    //     (stroke.hole_name || stroke.hole_played.name),
    //     stroke.holes_par,
    //     stroke.holes_yardage,
    //     stroke.stroke_ordinal,
    //     format_distances( stroke, stroke.starting_lie,  :starting_distance_to_pin ),
    //     format_distances( stroke, stroke.ending_lie,    :ending_distance_to_pin ),
    //     format_distances( stroke, stroke.starting_lie,  :shot_distance ),
    //     stroke.starting_lie,
    //     stroke.ending_lie,
    //     stroke.strokes_gained.round(2),
    //     stroke.strokes_gained_pro.round(2),
    //     stroke.strokes_gained_five.round(2),
    //     stroke.strokes_gained_ten.round(2),
    //     stroke.strokes_gained_fifteen.round(2),
    //     stroke.strokes_gained_twenty.round(2),
    //     stroke.starting_as_penalty_shot,
    //     stroke.starting_as_recovery_shot,
    //     stroke.starting_as_difficult_shot,
    //     stroke.fairway_location_from_center,
    //     stroke.get_fairway_x_coordinate,
    //     # stroke.stroke_played.tee_center_to_next_shot_bearing,
    //     # stroke.stroke_played.tee_to_shot_bearing,
    //     #
    //     # stroke.stroke_played.tee_center_to_fairway_bearing,
    //     # stroke.stroke_played.tee_center_to_fairway_bearing_normalized,
    //     #
    //     # stroke.hole_played.bearing,
    //     # stroke.hole_played.rotate2,
    //     # stroke.stroke_played.get_shot_angle_bearing,
    //     "#{stroke.green_distances["short"].to_f.round(2)} ft.",
    //     "#{stroke.green_distances["long"].to_f.round(2)} ft.",
    //     "#{stroke.green_distances["left"].to_f.round(2)} ft.",
    //     "#{stroke.green_distances["right"].to_f.round(2)} ft.",
    //   ]
  }

  //
  // csv headers
  //
  csv_headers() {
    [
      'ID',
      'Round ID',
      'Date',
      'Course',
      'Hole',
      'Par',
      'Yards',
      'Stroke Ordinal',
      'Start Distance to Pin',
      'End Distance to Pin',
      'Shot Distance',
      'Start Lie',
      'End Lie',
      'Scratch Strokes Gained',
      'PGA Strokes Gained',
      '5 Strokes Gained',
      '10 Strokes Gained',
      '15 Strokes Gained',
      '20 Strokes Gained',
      'Penalty',
      'Recovery',
      'Difficult',
      'Left/Right of Fairway Centerline',
      'Distance Left/Right of Fairway Centerline',
      'Green Short',
      'Green Long',
      'Green Left',
      'Green Right',
    ];
  }

  format_distances(stroke, lie, method) {
    console.debug({ stroke, lie, method });
    // units     = lie =~ /green/i ? 'ft' : 'yds'
    // precision = units == 'ft' ? 1 : 0
    // distance  = (stroke.send(method) || 0)
    // converted_distance = units == 'ft' ? (distance.to_f * METERS_TO_FEET) : (distance.to_f * METERS_TO_YARDS)
    // "#{converted_distance.to_f.round(precision)} #{units}"
  }
}
