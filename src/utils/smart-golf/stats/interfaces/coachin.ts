const STAT_COLUMNS = {
  approach: {
    '100-150': 'bucket_one',
    '150-200': 'bucket_two',
    '200-250': 'bucket_three',
    '250+': 'bucket_four',
  },

  short: {
    '<25': 'bucket_one',
    '25-50': 'bucket_two',
    '50-75': 'bucket_three',
    '75-100': 'bucket_four',
  },

  putting: {
    '<3': 'bucket_one',
    '3-6': 'bucket_two',
    '6-9': 'bucket_three',
    '9-15': 'bucket_four',
    '15-21': 'bucket_five',
    '21-30': 'bucket_six',
    '30+': 'bucket_seven',
  },

  classic: {
    'Avg Score': 'average_score',
    'FW %': 'fairways_in_regulation',
    'Sand Saves': 'sand_saved_percentage',
    'GIR %': 'greens_in_regulation',
    'Putts/Round': 'putts_per_round',
  },

  overall: {
    Total: 'strokes_gained',
    Approach: 'strokes_gained_for_approach',
    Short: 'strokes_gained_for_short',
    Putting: 'strokes_gained_for_putting',
  },
};
export class SmartGolfStatsInterfacesCoachin {
  //
  // Formats and executes the call to get the stats.
  //
  get_shots_for(shot_type = null, bucket_name = null, options = {}) {
    console.log(STAT_COLUMNS);
    console.log(shot_type);
    console.log(bucket_name);
    console.log(options);

    // shot_type_name = get_shot_type_name(shot_type)
    // method_to_call = get_buckets_method_name(shot_type, bucket_name)
    // call_method( shot_type_name, method_to_call, options[:user_id] )
  }

  //
  // Calls the appropriate stats method
  //
  call_method(shot_type, method_name, user_id = null) {
    console.log(shot_type);
    console.log(method_name);
    console.log(user_id);

    //   if user_id
    //   @stats.send(:for_user, user_id).send(shot_type.to_sym).send(method_name)
    // else
    //   @stats.send(shot_type.to_sym).send(method_name)
    // end
  }

  //
  // The overall page will have no name, and the summary page should just
  // use the overall type. Otherwise, just return what was given.
  //
  get_shot_type_name(shot_type = null) {
    console.log(shot_type);

    // ( shot_type.blank? || shot_type =~ /summary/i ) ? 'overall' : shot_type
  }

  //
  // Gets the method's name to call for the bucket selected.
  //
  get_buckets_method_name(shot_type, bucket_name = null) {
    console.log(shot_type, bucket_name);
    // return STAT_COLUMNS[shot_type.to_sym][0] if bucket_name.blank?
    // STAT_COLUMNS[shot_type.to_sym][bucket_name]
  }
}
