export class SmartGolfStatsCalculationsGreensInRegulation {
  //
  // Green in Regulation for Approach & Short Tee
  //
  // Green in Regulation is when a "Green" is hit in Par - 2.
  //
  // Greens in Regulation should be a % and is the number of
  // times a golfer hits the green in Par -2 or better / number
  // of holes played.
  //
  // e.g. on a Par 5 when shot 3 lands on the Green, this
  // is a green in regulation.
  //
  // ## Calculation
  //
  //   = Sum(GIR/Holes Played) * 100
  //
  //
  calculate_greens_in_regulation() {
    // return 0.0 if holes_played_total.zero? || holes_played_total.zero?
    // number_of_greens_in_regulation / holes_played_total * 100
  }

  //
  // Number of shots that are GIR
  //
  number_of_greens_in_regulation() {
    // greens_in_regulation_shots.size.to_f
  }

  //
  // Shots/Strokes that are considered Greens in Regulation.
  //
  greens_in_regulation_shots() {
    // return [] if strokes.blank?
    // strokes.group_by do |stroke|
    //   stroke.hole_played_id
    // end.select do |hole_played_id, stroke_shots|
    //   stroke_shots.find { |stroke| stroke.greens_in_regulation }
    // end
  }
}
