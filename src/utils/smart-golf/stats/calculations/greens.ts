import _ from 'lodash';
import { isEmpty } from 'lodash';
import { includeStr } from 'src/utils/utils';
import { SmartGolfStatsCalculationsRegulations } from './regulations';

export class SmartGolfStatsCalculationsGreens extends SmartGolfStatsCalculationsRegulations {
  //
  // Number of shots/strokes that missed right of the green
  //
  strokes: any;
  holes_played: any;
  user_stroke_gained_baseline: any;
  all_rounds_strokes: any;
  constructor(strokes?: any, holes_played?: any, userStrokeGainedBaseLine?: any, all_rounds_strokes?: any) {
    super(strokes, holes_played, userStrokeGainedBaseLine, all_rounds_strokes);
    this.strokes = strokes;
    this.holes_played = holes_played;
    this.user_stroke_gained_baseline = userStrokeGainedBaseLine;
  }
  missed_right_of_green_total() {
    if (isEmpty(this.strokes)) {
      return 0;
    }
    // return 0 if strokes.empty?
    // missed_right_of_green.size.to_f
    return this.missed_right_of_green().length;
  }
  missed_right_of_green() {
    if (isEmpty(this.strokes)) {
      return [];
    }
    return this.strokes.filter((stroke) => stroke.right) || [];
    // return [] if strokes.blank?
    // @missed_right_of_green ||= strokes.select { |stroke| stroke.right }
  }
  // alias_method :number_of_missed_right_of_green, :missed_right_of_green_total

  missed_right_of_green_percentage() {
    const totalMissRight = this.missed_right_of_green_total();
    const totalStrokes = this.strokes.length;
    if (totalMissRight == 0 || totalStrokes == 0) {
      return 0;
    }

    //           return 0 if missed_right_of_green_total.zero? ||
    //           strokes.size.to_i.zero?
    // missed_right_of_green_total / strokes.size.to_f * 100
    return +((totalMissRight / totalStrokes) * 100).toFixed(2);
  }

  //
  // Number of shots/strokes that missed left of the green
  //
  missed_left_of_green_total() {
    // return 0 if strokes.empty?
    // missed_left_of_green.size.to_f
    if (isEmpty(this.strokes)) {
      return 0;
    }
    const totalMissLeft = this.missed_left_of_green();
    return totalMissLeft.length;
  }
  missed_left_of_green() {
    if (isEmpty(this.strokes)) {
      return [];
    }
    const misLeft = this.strokes.filter((stroke) => stroke.left) || [];
    return misLeft;
    // return [] if strokes.blank?
    // @missed_left_of_green ||= strokes.select { |stroke| stroke.left }
  }

  // alias_method :number_of_missed_left_of_green, :missed_left_of_green_total

  missed_left_of_green_percentage() {
    // return 0 if missed_left_of_green_total.zero? ||
    // strokes.size.to_i.zero?
    // missed_left_of_green_total / strokes.size.to_f * 100
    const total = this.missed_left_of_green_total();
    const totalStrokes = this.strokes.length;
    if (total == 0 || totalStrokes == 0) {
      return 0;
    }
    return +((total / totalStrokes) * 100).toFixed(2);
  }

  //
  // Number of shots/strokes that missed short of the green
  //
  missed_short_of_green_total() {
    // return 0 if strokes.empty?
    // missed_short_of_green.size.to_f
    if (isEmpty(this.strokes)) {
      return 0;
    }
    const totalShort = this.missed_short_of_green();
    return totalShort.length;
  }
  missed_short_of_green() {
    // return [] if strokes.blank?
    // @missed_short_of_green ||= strokes.select { |stroke| stroke.short }
    if (isEmpty(this.strokes)) {
      return [];
    }
    return this.strokes.filter((stroke) => stroke.short);
  }

  // alias_method :number_of_missed_short_of_green, :missed_short_of_green_total

  missed_short_of_green_percentage() {
    //           return 0 if missed_short_of_green_total.zero? ||
    //           strokes.size.to_i.zero?
    // missed_short_of_green_total / strokes.size.to_f * 100
    const totalMissShort = this.missed_short_of_green_total();
    const totalStrokes = this.strokes.length;
    if (totalMissShort == 0 || totalStrokes == 0) {
      return 0;
    }

    return +((totalMissShort / totalStrokes) * 100).toFixed(2);
  }

  //
  // Number of shots/strokes that missed long of the green
  //
  missed_long_of_green_total() {
    // return 0 if strokes.empty?
    // missed_long_of_green.size.to_f
    if (isEmpty(this.strokes)) {
      return 0;
    }
    const totalMissLong = this.missed_long_of_green();
    return totalMissLong.length;
  }
  missed_long_of_green() {
    if (isEmpty(this.strokes)) {
      return [];
    }
    return this.strokes.filter((stroke) => stroke.long);
  }

  // alias_method :number_of_missed_long_of_green, :missed_long_of_green_total

  missed_long_of_green_percentage() {
    //           return 0 if missed_long_of_green_total.zero? ||
    //           strokes.size.to_i.zero?
    // missed_long_of_green_total / strokes.size.to_f * 100
    const totalMissLong = this.missed_long_of_green_total();
    const totalStroke = this.strokes.length;
    if (totalMissLong == 0 || totalStroke == 0) {
      return 0;
    }
    return +((totalMissLong / totalStroke) * 100).toFixed(2);
  }

  landing_on_green() {
    // strokes.select { |stroke| stroke.ending_lie =~ /green/i }
    return this.strokes.filter((stroke) => includeStr(stroke.ending_lie, 'green'));
  }
  landing_in_green() {
    return this.landing_on_green();
  }
  //
  // Number of Greens that were hit.
  //
  greens_hit_total() {
    // return 0 if landing_on_green.empty?
    // landing_on_green.size.to_f
    if (isEmpty(this.landing_on_green())) {
      return 0;
    }
    return this.landing_on_green().length;
  }

  greens_hit_percentage() {
    // return 0 if strokes_total.zero?
    // greens_hit_total / strokes_total.to_f * 100
    const strokeTotal = this.strokes_total();
    if (strokeTotal == 0) {
      return 0;
    }
    const greens_hit_total = this.greens_hit_total();
    const percent = (greens_hit_total / strokeTotal) * 100;
    return +percent.toFixed(2);
  }

  strokes_total() {
    // strokes.group_by(&:stroke_played_id).keys.uniq.size.to_f
    const group = _.groupBy(this.strokes, 'stroke_played_id');
    const strokePlayedIds = Object.keys(group);
    return _.uniq(strokePlayedIds).length;
  }

  greens_missed_total() {
    const totalNotLanding = this.not_landing_on_green();
    if (isEmpty(totalNotLanding)) {
      return 0;
    }
    return totalNotLanding.length;
  }
  not_landing_on_green() {
    return this.strokes.filter((stroke) => !stroke.ending_lie?.toLowerCase().includes('green'));
  }

  greens_missed_percentage() {
    const strokeTotal = this.strokes_total();
    if (strokeTotal == 0) {
      return 0;
    }
    // return 0 if strokes_total.zero?
    // greens_missed_total / strokes_total.to_f * 100
    const percent = (this.greens_missed_total() / strokeTotal) * 100;
    return +percent.toFixed(2);
  }
}
