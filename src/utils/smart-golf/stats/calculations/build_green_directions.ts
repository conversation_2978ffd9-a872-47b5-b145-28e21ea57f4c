//
// Console Testing Code
//
// round = Round.find(25)
// hole  = round.holes_played.where(name: "2").first
// a     = hole.strokes_played.where(lie: "Green").first
// approach_shot = a.hole_played.last_approach_shot
// green_polygon = a.round.course.convert_to_polygon( a.hole_played.name.to_i, "green" ).try(:first)
// map = SmartGolf::Stats::Calculations::BuildGreenDirections.new(approach_shot.coords, green_polygon)
// map.four_green_points
//
// green = SmartGolf::Stats::Calculations::GreenDistances.new( map.four_green_points, [a.coords.y, a.coords.x] )
//
//
//
// Pick the shot before the one being calculated
//
import { Logger } from '@nestjs/common';
import * as turf from '@turf/turf';
import { convertArrToLocation, getCenterPoint } from 'src/utils/utils';
import { GeocoderCalculations } from './geocoder';

export class SmartGolfStatsCalculationsBuildGreenDirections {
  private readonly logger = new Logger(SmartGolfStatsCalculationsBuildGreenDirections.name);
  approach_centroid: any;
  approach_shot: any;
  factory: any;
  polygon: any;
  polygon_centroid: any;
  polygon_center: any;
  pin_location: any;
  pin_location_center: any;
  initial_bearing: any;

  //
  // class method to access the 4 green points to grid the green
  //
  getGreenEdgePoints(approach_shot_centroid, polygon, pin_location = null) {
    this.initialize(approach_shot_centroid, polygon, pin_location);
    return this.fourGreenPoints();
  }

  //
  //  Last Approach Shot Coords: Array - [lat,lng]
  //
  //  Pin Location: Array - [lat,lng]
  //    - could be the pin or fairway center
  //
  //  Polygon Edges: Array of Arrays
  //
  initialize(approach_shot_centroid, polygon, pin_location = null) {
    this.approach_centroid = approach_shot_centroid;
    this.approach_shot = approach_shot_centroid; //[approach_shot_centroid[1], approach_shot_centroid[0]];
    this.polygon = polygon;
    // const polygonToPoints = buildCoordsToPointObject(polygon);
    this.polygon_centroid = getCenterPoint(polygon);
    this.pin_location = pin_location?.coordinates;
    this.initial_bearing = this.getBearing(approach_shot_centroid, this.pin_location || this.polygon_centroid);
  }

  //
  // get all 4 points on the green edge
  //
  //
  fourGreenPoints() {
    const short_point = [this.approach_shot[1], this.approach_shot[0]];
    const center = this.pin_location
      ? [this.pin_location[1], this.pin_location[0]]
      : [this.polygon_center[1], this.polygon_center[0]];
    return {
      short_point,
      left_point: this.leftPoint(),
      long_point: this.longPoint(),
      right_point: this.rightPoint(),

      short_bearing: this.initial_bearing,
      left_bearing: this.computeBearing(270),
      long_bearing: this.computeBearing(180),
      right_bearing: this.computeBearing(90),
      center,
      approach: short_point,
    };
  }

  short_point() {
    return [this.approach_centroid[1], this.approach_centroid[0]];
  }

  leftPoint() {
    return this.getEndPoint(270);
  }

  longPoint() {
    return this.getEndPoint(0);
  }

  rightPoint() {
    return this.getEndPoint(90);
  }

  getEndPoint(bearing) {
    bearing = this.computeBearing(bearing);
    const endpoint = GeocoderCalculations.endpoint(this.pin_location || this.polygon_center, bearing, 0.1);
    return [+endpoint[1], +endpoint[0]];
  }

  getIntersectPoint(startPoint: any, endpoint: any) {
    try {
      const lineBase: any = turf.lineString([this.polygon]);
      const linePoint: any = turf.lineString([[startPoint, endpoint]]);
      const intersections = turf.lineIntersect(lineBase, linePoint);
      if (intersections.features.length > 0) {
        return intersections.features[0].geometry.coordinates;
      } else {
        return [];
      }
    } catch (error) {
      return [];
    }

    // line = factory.line(startpoint, endpoint)
    // intersection = line.intersection(polygon)
    // # getting a line string... when we want a point
    // if intersection.respond_to?(:points)
    //   points        = intersection.points.select { |point| !line.points.include?(point) }
    //   intersection  = points.first
    // end
    // [ intersection.y.round(8), intersection.x.round(8) ]
  }

  //
  // normalizing the bearing so as to get the north/south, east/west points
  //
  //   * offset: Integer the degree offset to normalize
  //
  computeBearing(offset: any) {
    const final_bearing = this.initial_bearing + offset;
    if (final_bearing < 360) {
      return final_bearing;
    }
    return final_bearing - 360;
  }

  //
  //  Get bearing between 2 locations
  //
  //  start_location: Point
  //  end_location:   Point
  //
  getBearing(start_location, end_location) {
    const origin = convertArrToLocation(start_location);
    //{ lat: start_location[0], lon: start_location[1] };
    const dest = convertArrToLocation(end_location);
    //{ lat: end_location[0], lon: end_location[1] };
    return GeocoderCalculations.bearingBetween(origin, dest);
  }
}
