import { Logger } from '@nestjs/common';
import _, { isEmpty } from 'lodash';

export class SmartGolfStatsCalculationsFairwaysInRegulation {
  // include SmartGolf::Stats::Calculations::Sums
  private readonly logger = new Logger(SmartGolfStatsCalculationsFairwaysInRegulation.name);
  strokes: any;
  all_rounds_strokes: any;
  constructor(strokes, all_rounds_strokes?: any) {
    this.strokes = strokes;
    this.all_rounds_strokes = all_rounds_strokes ? all_rounds_strokes : strokes;
  }
  //
  // Calculate Fairways in Regulation
  //
  // ## Requirements
  //
  //   * Must be on a hole that is par 4 or greater
  //   * Shot/Stroke must land on either the fairway or the green
  //   * Shot/Stroke must not land and become a penalty shot
  //
  // ## Calculation
  //
  //   SUM(FIR SHOTS) / SUM(SHOTS) * 100
  //
  calculate_fairways_in_regulation() {
    // return 0.0 if strokes.empty? || long_tee_total.zero?
    const longTeeTotal = this.long_tee_total();
    if (longTeeTotal == 0) {
      return 0;
    }
    const nbFairwayInRegulation = this.number_of_fairways_in_regulation();
    const percent = (nbFairwayInRegulation / longTeeTotal) * 100;
    return percent;
  }

  calculate_fairways_in_regulation_for(club_type) {
    this.logger.log(`CALCULATE_FAIRWAYS_IN_REGULATION_FOR`);
    this.logger.log({ club_type });

    // return 0.0 if strokes.empty? || total_for(club_type).zero?
    // number_of_fairways_in_regulation_for(club_type) / total_for(club_type) * 100
    const totalClub = this.total_for(club_type);
    const isEmptyStroke = isEmpty(this.strokes);
    if (isEmptyStroke || totalClub == 0) {
      return 0;
    }
    // return 0.0 if strokes.empty? || total_for(club_type).zero?
    // number_of_fairways_in_regulation_for(club_type) / total_for(club_type) * 100
    const nbFwInRegulation = this.number_of_fairways_in_regulation_for(club_type);
    const percent = (nbFwInRegulation / totalClub) * 100;
    return percent;
  }

  //
  // Number of shots that are FIR
  //
  number_of_fairways_in_regulation() {
    // fairways_in_regulation_shots.size.to_f
    return this.fairways_in_regulation_shots().length;
  }

  //
  // Number of shots that are FIR
  //
  number_of_fairways_in_regulation_for(club_type) {
    console.log(club_type);
    // fairways_in_regulation_for(club_type).size.to_f
    return this.fairways_in_regulation_for(club_type).length;
  }

  //
  // fairways hit for certain club types
  //
  //   club_type: String. Options are:
  //
  //     * all      - default
  //     * driver
  //     * other
  //     * three_wood
  //
  fairways_in_regulation_for(club_type) {
    console.log(club_type);
    // case club_type.to_s
    // when /three_wood/i
    //   fairways_in_regulation_for_clubs(three_wood_clubs)
    // when /other/i
    //   fairways_in_regulation_for_clubs(other_clubs)
    // when /driver/i
    //   fairways_in_regulation_for_clubs(driver_clubs)
    // else
    //   fairways_in_regulation_for_clubs(long_tees)
    // end
    switch (club_type) {
      case 'three_wood':
        return this.fairways_in_regulation_for_clubs(this.three_wood_clubs());
      case 'other':
        return this.fairways_in_regulation_for_clubs(this.other_clubs());
      case 'driver':
        return this.fairways_in_regulation_for_clubs(this.driver_clubs());
      default:
        return this.fairways_in_regulation_for_clubs(this.long_tees());
    }
  }

  //
  // Shots/Strokes that are considered Fairways in Regulation.
  //
  // ## Requirements
  //
  //   * Must be on a hole that is par 4 or greater
  //   * Shot/Stroke must land on either the fairway or the green
  //   * Shot/Stroke must not land and become a penalty shot
  //
  fairways_in_regulation_shots() {
    // fairways_in_regulation_for_clubs(long_tees)
    return this.fairways_in_regulation_for_clubs(this.long_tees());
  }

  fairways_in_regulation_for_clubs(stroke_types) {
    // return [] if stroke_types.blank?
    // stroke_types.select do |stroke|
    //   landed_on_fairway?(stroke)        &&
    //     stroke.holes_par >= 4           &&
    //     !stroke.ending_as_penalty_shot
    // end
    if (isEmpty(stroke_types)) {
      return [];
    }
    const strokes = stroke_types.filter((stroke) => {
      const isValid = this.landed_on_fairway(stroke) && +stroke.holes_par >= 4 && !stroke.ending_as_penalty_shot;
      return isValid;
    });
    return strokes;
  }

  //
  // Shot/Stroke landing on the fairway?
  //
  landed_on_fairway(stroke) {
    // stroke.ending_lie =~ /green/i || stroke.ending_lie =~ /fairway/i || stroke.ending_lie =~ /tee/i
    return ['green', 'fairway', 'tee'].includes(stroke.ending_lie?.toLowerCase().trim());
  }

  //
  // Total Long Tees
  //
  long_tees() {
    // return [] if strokes.blank?
    // @long_tees ||= strokes.select { |stroke| stroke.long_tee }
    if (isEmpty(this.strokes)) {
      return [];
    }
    return this.strokes.filter((stroke) => stroke.long_tee);
  }

  //
  // Only strokes that used a 3 wood club
  //
  three_wood_clubs() {
    const longTees = this.long_tees();
    if (longTees) {
      return [];
    }
    // this.long_tees.select do |stroke|
    //   stroke.club_id && (stroke.club.club_type =~ /3-fw/i || stroke.club.club_type =~ /3fw/i)
    // end
    return longTees.filter((stroke) => {
      return stroke.club_id && ['3fw', '3-fw'].includes(stroke.club_type?.toLowerCase().trim());
    });
  }

  //
  // Only strokes that used a driver club
  //
  driver_clubs() {
    // return [] if long_tees.empty?
    // @driver_clubs ||= long_tees.select do |stroke|
    //   stroke.club_id && stroke.club.club_type =~ /driver/i
    // end
    const longTees = this.long_tees();
    if (longTees) {
      return [];
    }
    return longTees.filter((stroke) => {
      return stroke.club_id && ['driver'].includes(stroke.club_type?.toLowerCase().trim());
    });
  }

  //
  // Only strokes that used any club other than a 3 wood or
  // driver.
  //
  other_clubs() {
    // return [] if long_tees.empty?
    // @other_clubs ||= long_tees.select do |stroke|
    //   stroke.club_id &&
    //     ( stroke.club.club_type !~ /3-fw/i &&
    //       stroke.club.club_type !~ /3fw/i &&
    //       stroke.club.club_type !~ /driver/i )
    // end
    const longTees = this.long_tees();
    if (longTees) {
      return [];
    }
    return longTees.filter((stroke) => {
      return stroke.club_id && !['3fw', '3-fw', 'diver'].includes(stroke.club_type?.toLowerCase().trim());
    });
  }

  //
  // number of stroke types (club)
  //
  total_for(club_type) {
    let strokes = [];
    switch (club_type) {
      case 'three_wood_clubs':
        strokes = this.three_wood_clubs();
        break;
      case 'other':
        strokes = this.other_clubs();
        break;
      case 'driver':
        strokes = this.driver_clubs();
        break;
    }
    return strokes.length;
  }

  //
  // Total Long Tees
  //
  // Overwrites the @strokes_total for the cases of long tees
  //
  long_tee_total() {
    if (isEmpty(this.strokes)) {
      return 0;
    }
    const longTeeShot = this.long_tee_shots(this.strokes);
    return longTeeShot.length;
  }
  long_tee_shots(strokes) {
    if (isEmpty(strokes)) {
      return [];
    }
    return strokes.filter((stroke) => stroke.long_tee);
  }

  //
  // Total number of holes for long tees
  //
  long_tee_holes_total() {
    // return 0.0 if strokes.blank?
    // long_tee_holes.size.to_f
    if (isEmpty(this.strokes)) {
      return 0;
    }
    return 0;
  }

  //
  // The number of long tees that land on either the fairway
  // or the green.
  //
  long_tees_landing_on_fairway_total() {
    // long_tees_landing_on_fairway.size.to_f
  }

  // alias_method :number_of_holes_played_multiplayer, :holes_played_multiplayer_total

  //
  // Rounds Played total for a specific set of StrokeStat objects.
  //
  //   Gotchas:
  //
  //     * This is not always the total number of rounds played, but
  //       rather the total rounds played for the given set of strokes.
  //
  all_rounds_played_total() {
    const all_rounds_played = this.all_rounds_played();
    if (isEmpty(all_rounds_played)) {
      return 0;
    }
    return all_rounds_played.length;
    // return 0 if all_rounds_played.blank?
    // all_rounds_played.size.to_f
  }
  all_rounds_played() {
    if (isEmpty(this.all_rounds_strokes)) {
      return [];
    }
    return _.uniq(this.all_rounds_strokes.map((m) => m.round_id));
  }

  // alias_method :number_of_all_rounds_played, :all_rounds_played_total

  //
  // All Holes Played total
  //
  // The number of total holes played for all the rounds given.
  //
  // Unlike the subclasses of this class, this accounts for
  // all holes and not just the holes for a specific shot/stroke
  // type.
  //
  all_holes_played_total() {
    if (isEmpty(this.all_rounds_strokes)) {
      return 0;
    }
    const holes = _.uniq(this.all_rounds_strokes.map((m) => m.hole_played_id));
    if (isEmpty(holes)) {
      return 0;
    }
    return holes.length;
    // return 0 if all_holes_played.blank?
    // all_holes_played.size.to_f
  }

  //
  // Total number of shots/strokes that are holed
  //
  number_of_shots_holed() {
    // return 0 if shots_holed.empty?
    // shots_holed.size.to_f
  }

  //
  // Percentage of shots that were holed
  //
  percentage_of_shots_holed() {
    // return 0 if number_of_shots_holed.zero? || strokes_total.zero?
    // number_of_shots_holed / strokes_total * 100
  }

  //
  // Number of strokes that are on a hole with a specific par. Par
  // defaults to 3.
  //
  strokes_for_par_total(par = 3) {
    this.logger.debug(`STROKES_FOR_PAR_TOTAL`);
    this.logger.debug({ par });
    // return 0 if strokes.empty?
    // strokes_for_par(par).size.to_f
    if (isEmpty(this.strokes)) {
      return 0;
    }
    return 0;
    // return this.strokes_for_par(par);
  }
  number_of_strokes_for_par(par = 3) {
    this.logger.debug(`NUMBER_OF_STROKES_FOR_PAR`);
    return this.strokes_for_par_total(par);
  }
  // alias_method :number_of_strokes_for_par, :strokes_for_par_total

  //
  // Number of holes for a specific par. Par defaults to 3.
  //
  holes_for_par_total(par = 3) {
    if (isEmpty(this.strokes)) {
      return 0;
    }
    // return ho
    // return this.holes_for_par(par).length;
    // return 0 if strokes.empty?
    // holes_for_par(par).size.to_f
    return this.holes_for_par(par).length;
  }
  holes_for_par(par = 3) {
    this.logger.log(`HOLES_FOR_PAR`);
    console.log({ par });
    if (isEmpty(this.strokes) || !(3 <= par && par <= 5)) {
      return [];
    }
    const sForPar = this.strokes_for_par(par);
    if (!isEmpty(sForPar)) {
      return _.groupBy(sForPar, 'hole_played_id');
    }
    return [];
    // return [] if strokes.blank? || !par.between?(3,5)
    // strokes_for_par(par).group_by do |stroke|
    //   stroke.hole_played
    // end
  }

  strokes_for_par(par = 3) {
    console.log(par);
    if (!this.strokes || isEmpty(this.strokes) || !(3 <= par && par <= 5)) {
      return [];
    }
    return this.strokes.filter((stroke) => +stroke.holes_par == +par);
    // return [] if strokes.blank? || !par.between?(3,5)
    // strokes.select do |stroke|
    //   stroke.holes_par.to_i == par.to_i
    // end
  }
  number_of_holes_for_par(par = 3) {
    return this.holes_for_par_total(par);
  }
  // alias_method :number_of_holes_for_par, :holes_for_par_total

  //
  // Number of Fairways that were hit.
  //
  fairways_hit_total() {
    // return 0 if landing_on_fairway.empty?
    // landing_on_fairway.size.to_f
    const landOnFw = this.landing_on_fairway();
    if (landOnFw) {
      return 0;
    }
    return landOnFw.lenght;
  }
  landing_on_fairway() {
    // strokes.select { |stroke| stroke.ending_lie =~ /fairway/i }
    return this.strokes.filter((stroke) => stroke.ending_lie?.toLowerCase().includes('fairway'));
  }
  landing_in_fairway() {
    return this.landing_on_fairway();
  }
  // alias_method :number_of_fairways_hit, :fairways_hit_total

  //
  // Number of Bunkers that were hit.
  //
  bunkers_hit_total() {
    // return 0 if landing_on_bunker.empty?
    // landing_on_bunker.size.to_f
  }
  // alias_method :number_of_bunkers_hit, :bunkers_hit_total

  //
  // Number of Roughs that were hit.
  //
  roughs_hit_total() {
    // return 0 if landing_on_rough.empty?
    // landing_on_rough.size.to_f
  }
  // alias_method :number_of_roughs_hit, :roughs_hit_total

  //
  // Number of Putts taken
  //
  number_of_putts() {
    // return 0 if starting_on_green.empty?
    // starting_on_green.size.to_f
    // const startOnGreen = this.starting_on_green(strokes);
    // if (isEmpty(startOnGreen)) {
    //   return 0;
    // }
    // return startOnGreen.length;
  }
  calculate_greens_in_regulation() {
    // return 0.0 if holes_played_total.zero? || holes_played_total.zero?
    // number_of_greens_in_regulation / holes_played_total * 100
    const holePlayedTotal = this.holes_played_total();
    if (holePlayedTotal == 0) {
      return 0;
    }
    const nbGreenInRegulation: any = this.number_of_greens_in_regulation();
    const percent = (nbGreenInRegulation / holePlayedTotal) * 100;
    if (percent >= 100) {
      return 100;
    }
    return percent;
  }
  holes_played_total(): any {
    this.logger.log('HOLES_PLAYED_TOTAL');
    const groupHoleId = _.groupBy(this.strokes, 'hole_played_id');
    const holedIds = Object.keys(groupHoleId);
    return holedIds.length;
    // strokes.flat_map { |stroke| stroke.hole_played_id }.uniq.size.to_f
    // return _.groupBy(this.strokes, 'hole_played_id').length;
  }
  //
  // Number of shots that are GIR
  //
  number_of_greens_in_regulation() {
    // greens_in_regulation_shots.size.to_f
    return this.greens_in_regulation_shots().length;
  }

  //
  // Shots/Strokes that are considered Greens in Regulation.
  //
  greens_in_regulation_shots() {
    // return [] if strokes.blank?
    // strokes.group_by do |stroke|
    //   stroke.hole_played_id
    // end.select do |hole_played_id, stroke_shots|
    //   stroke_shots.find { |stroke| stroke.greens_in_regulation }
    // end
    if (isEmpty(this.strokes)) {
      return [];
    }
    // const groupHolePlayedId = _.groupBy(this.strokes, 'hole_played_id');
    const strokes = this.strokes.filter((stroke) => stroke.greens_in_regulation);
    const groupByStrokes = _.groupBy(strokes, 'id');
    return Object.keys(groupByStrokes);
    // strokes.group_by do |stroke|
    //   stroke.hole_played_id
    // end.select do |hole_played_id, stroke_shots|
    //   stroke_shots.find { |stroke| stroke.greens_in_regulation }
    // end
  }
}
