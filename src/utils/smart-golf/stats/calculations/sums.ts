//
// Various sum totals
//
// Expects the following methods to be available:
//
//   * strokes: an array of StrokeStat objects
//   * StrokeStat#strokes_gained
//   * StrokeStat#hole_played_id
//   * StrokeStat#round_id
//
//
// Meant to be included in different types of StrokeStats groups.
//
//   e.g.
//
//     * LongTee
//     * Approach
//     * ShortTee
//     * Putting
import { Logger } from '@nestjs/common';
import _ from 'lodash';
import { isEmpty } from 'lodash';
import { SmartGolfStatsCalculationsClassicSg } from './classic_sg';

//
export class SmartGolfStatsCalculationsSums extends SmartGolfStatsCalculationsClassicSg {
  // include SmartGolf::Stats::Collections
  // include SmartGolf::Stats::Calculations::Greens
  private readonly loggerCS = new Logger(SmartGolfStatsCalculationsSums.name);
  //
  // Strokes Gained total for a given set of StrokeStat objects
  //
  strokes: any;
  constructor(strokes) {
    super(strokes);
    this.strokes = strokes;
  }

  //
  // Total Long Tees
  //
  // Overwrites the @strokes_total for the cases of long tees
  //
  long_tee_total() {
    // return 0.0 if strokes.blank?
    // @strokes_total ||= long_tee_shots.size.to_f
  }

  //
  // Total number of holes for long tees
  //
  long_tee_holes_total() {
    // return 0.0 if strokes.blank?
    // long_tee_holes.size.to_f
    return 0;
  }

  //
  // The number of long tees that land on either the fairway
  // or the green.
  //
  long_tees_landing_on_fairway_total() {
    // long_tees_landing_on_fairway.size.to_f
  }

  // alias_method  :number_of_long_tees_landing_on_fairway,
  //               :long_tees_landing_on_fairway_total

  //
  // Total holes played for a specific set of StrokeStat objects
  //
  //   Gotchas:
  //
  //     * This is not always the total number of holes played, but
  //       rather the total holes played for the given set of strokes.
  //
  holes_played_total() {
    // strokes.flat_map { |stroke| stroke.hole_played_id }.uniq.size.to_f
  }
  // alias_method :number_of_holes_played, :holes_played_total

  holes_played_basic_total() {
    // holes_played.select { |hole_played| hole_played.score(Round::ROUND_MODE_BASIC).to_i > 0 }.size.to_f
    return 1;
  }
  // alias_method :number_of_holes_played_basic, :holes_played_basic_total

  holes_played_multiplayer_total() {
    // holes_played.select { |hole_played| hole_played.score(Round::ROUND_MODE_MULTIPLAYER).to_i > 0 }.size.to_f
    return 1;
  }

  // alias_method :number_of_holes_played_multiplayer, :holes_played_multiplayer_total

  //
  // Rounds Played total for a specific set of StrokeStat objects.
  //
  //   Gotchas:
  //
  //     * This is not always the total number of rounds played, but
  //       rather the total rounds played for the given set of strokes.
  //
  // all_rounds_played_total() {
  //   // return 0 if all_rounds_played.blank?
  //   // all_rounds_played.size.to_f
  //   return 0;
  // }

  // alias_method :number_of_all_rounds_played, :all_rounds_played_total

  //
  // All Holes Played total
  //
  // The number of total holes played for all the rounds given.
  //
  // Unlike the subclasses of this class, this accounts for
  // all holes and not just the holes for a specific shot/stroke
  // type.
  //
  all_holes_played_total() {
    // return 0 if all_holes_played.blank?
    // all_holes_played.size.to_f
    return null;
  }

  //
  // Total number of shots/strokes that are holed
  //
  number_of_shots_holed() {
    // return 0 if shots_holed.empty?
    // shots_holed.size.to_f
  }

  //
  // Percentage of shots that were holed
  //
  percentage_of_shots_holed() {
    // return 0 if number_of_shots_holed.zero? || strokes_total.zero?
    // number_of_shots_holed / strokes_total * 100
  }

  //
  // Number of strokes that are on a hole with a specific par. Par
  // defaults to 3.
  //
  strokes_for_par_total(par = 3) {
    this.loggerCS.debug(`STROKES_FOR_PAR_TOTAL`);
    this.loggerCS.debug({ par });
    // return 0 if strokes.empty?
    // strokes_for_par(par).size.to_f
    if (isEmpty(this.strokes)) {
      return 0;
    }
    return 0;
    // return this.strokes_for_par(par);
  }
  number_of_strokes_for_par(par = 3) {
    this.loggerCS.debug(`NUMBER_OF_STROKES_FOR_PAR`);
    return this.strokes_for_par_total(par);
  }
  // alias_method :number_of_strokes_for_par, :strokes_for_par_total

  //
  // Number of holes for a specific par. Par defaults to 3.
  //
  holes_for_par_total(par = 3) {
    if (isEmpty(this.strokes)) {
      return 0;
    }
    // return ho
    // return this.holes_for_par(par).length;
    // return 0 if strokes.empty?
    // holes_for_par(par).size.to_f
    return this.holes_for_par(par).length;
  }
  holes_for_par(par = 3) {
    this.loggerCS.log(`HOLES_FOR_PAR`);
    console.log({ par });
    if (isEmpty(this.strokes) || !(3 <= par && par <= 5)) {
      return [];
    }
    const sForPar = this.strokes_for_par(par);
    if (!isEmpty(sForPar)) {
      return _.groupBy(sForPar, 'hole_played_id');
    }
    return [];
    // return [] if strokes.blank? || !par.between?(3,5)
    // strokes_for_par(par).group_by do |stroke|
    //   stroke.hole_played
    // end
  }

  strokes_for_par(par = 3) {
    console.log(par);
    if (!this.strokes || isEmpty(this.strokes) || !(3 <= par && par <= 5)) {
      return [];
    }
    return this.strokes.filter((stroke) => +stroke.holes_par == +par);
    // return [] if strokes.blank? || !par.between?(3,5)
    // strokes.select do |stroke|
    //   stroke.holes_par.to_i == par.to_i
    // end
  }
  number_of_holes_for_par(par = 3) {
    return this.holes_for_par_total(par);
  }
  // alias_method :number_of_holes_for_par, :holes_for_par_total

  //
  // Number of Fairways that were hit.
  //
  fairways_hit_total() {
    // return 0 if landing_on_fairway.empty?
    // landing_on_fairway.size.to_f
  }
  // alias_method :number_of_fairways_hit, :fairways_hit_total

  //
  // Number of Bunkers that were hit.
  //
  bunkers_hit_total() {
    // return 0 if landing_on_bunker.empty?
    // landing_on_bunker.size.to_f
  }
  // alias_method :number_of_bunkers_hit, :bunkers_hit_total

  //
  // Number of Roughs that were hit.
  //
  roughs_hit_total() {
    // return 0 if landing_on_rough.empty?
    // landing_on_rough.size.to_f
  }
  // alias_method :number_of_roughs_hit, :roughs_hit_total

  //
  // Number of Putts taken
  //
  number_of_putts() {
    // return 0 if starting_on_green.empty?
    // starting_on_green.size.to_f
    // const startOnGreen = this.starting_on_green(strokes);
    // if (isEmpty(startOnGreen)) {
    //   return 0;
    // }
    // return startOnGreen.length;
  }

  // alias_method :number_of_putts_taken,  :number_of_putts
  // alias_method :number_of_putts_hit,    :number_of_putts
}
