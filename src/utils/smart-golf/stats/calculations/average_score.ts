export class SmartGolfStatsCalculationsAverageScore {
  //
  // Average Score for all shots/strokes
  //
  // calculation:
  //
  //   (Total Score / Number of Holes Played) * 18
  //
  average_score() {
    // return 0 if holes_played_total.zero?
    // total_score / holes_played_total * 18
  }

  average_score_basic() {
    // return 0 if holes_played.size.zero?
    // holes_played_basic_total = holes_played.select { |hole_played| hole_played.score(Round::ROUND_MODE_BASIC).to_i > 0 }.size.to_f
    // holes_score ||= holes_played.sum {|hole| hole.score(Round::ROUND_MODE_BASIC).to_i}
    // holes_score / holes_played_basic_total * 18
  }

  //
  // Average Score for holes that are a specific par.
  // Defaults to 3.
  //
  average_score_for_par(par = 3) {
    console.log(par);

    // return 0 if number_of_holes_for_par(par).zero?
    // number_of_strokes_for_par(par) / number_of_holes_for_par(par)
  }

  // alias_method :avg_score_for_par, :average_score_for_par

  average_score_basic_for_par(par = 3) {
    console.log(par);
    // return 0 if number_of_strokes_basic_for_par(par).zero?
    // return 0 if number_of_holes_basic_for_par(par).zero?
    // number_of_strokes_basic_for_par(par) / number_of_holes_basic_for_par(par)
  }

  //
  // Number of holes for a specific par. Par defaults to 3.
  //
  number_of_holes_for_par(par = 3) {
    console.log(par);
    // return 0 if strokes.empty?
    // holes_for_par(par).size.to_f
  }

  // alias_method :avg_holes_for_par, :number_of_holes_for_par

  number_of_holes_basic_for_par(par = 3) {
    console.log(par);
    // return 0 if holes_played.size.zero?
    // holes_basic_for_par(par).size.to_f
  }

  number_of_holes_multiplayer_for_par(par = 3) {
    console.log(par);
    // return 0 if holes_played.size.zero?
    // holes_multiplayer_for_par(par).size.to_f
  }

  //
  // Number of strokes that are on a hole with a specific par. Par
  // defaults to 3.
  //
  number_of_strokes_for_par(par = 3) {
    console.log(par);
    // return 0 if strokes.empty?
    // strokes_for_par(par).size.to_f
  }

  number_of_strokes_basic_for_par(par = 3) {
    console.log(par);
    // return 0 if holes_played.size.zero?
    // strokes_basic_for_par(par).to_f
  }

  number_of_strokes_multiplayer_for_par(par = 3) {
    console.log(par);
    // return 0 if holes_played.size.zero?
    // strokes_multiplayer_for_par(par).to_f
  }

  //
  // Shots/Strokes that are on a hole with a specific par. Default
  // par is set to 3.
  //
  // ## Returns
  //
  //   An Array of HolePlayed Objects
  //
  holes_for_par(par = 3) {
    console.log(par);
    // return [] if strokes.blank? || !par.between?(3,5)
    // strokes_for_par(par).group_by do |stroke|
    //   stroke.hole_played
    // end
  }

  holes_basic_for_par(par = 3) {
    console.log(par);
    // holes_played.select { |hole_played| hole_played.score(Round::ROUND_MODE_BASIC).to_i > 0 && hole_played.par.to_i == par.to_i }
  }

  holes_multiplayer_for_par(par = 3) {
    console.log(par);
    // holes_played.select { |hole_played| hole_played.score(Round::ROUND_MODE_MULTIPLAYER).to_i > 0 && hole_played.par.to_i == par.to_i }
  }

  //
  // Shots/Strokes that are on a hole with a specific par. Default
  // par is set to 3.
  //
  // ## Returns
  //
  //   An Array of StrokeStat Objects
  //
  strokes_for_par(par = 3) {
    console.log(par);
    // return [] if strokes.blank? || !par.between?(3,5)
    // strokes.select do |stroke|
    //   stroke.holes_par.to_i == par.to_i
    // end
  }

  strokes_basic_for_par(par = 3) {
    console.log(par);
    // holes_played.select { |hole_played| hole_played.par.to_i == par.to_i }.sum {|hole_played| hole_played.score(Round::ROUND_MODE_BASIC).to_i}
  }

  strokes_multiplayer_for_par(par = 3) {
    console.log(par);
    // holes_played.select { |hole_played| hole_played.par.to_i == par.to_i }.sum {|hole_played| hole_played.score(Round::ROUND_MODE_MULTIPLAYER).to_i}
  }
}
