//
// a.forEach(function(coords) { map.addMarker(coords[0], coords[1]) } );
//
//
//
// course = Course.find(16843)
// a = course.centerpoint_line( 17 )
// a.centerpoints
// a.get_fairway_intersect_points
//
//
//
const MILES_TO_YARDS = 1760;
export class SmartGolfStatsCalculationsFairwayCenterline {
  // attr_reader :tee_center, :fairway_polygon, :green_center, :tee_to_green_bearing,
  //             :intersect_points, :factory
  initialize(tee_center, fairway_polygon, green_center) {
    console.log(tee_center, fairway_polygon, green_center);
    console.log(MILES_TO_YARDS);

    // @tee_center             = to_point( tee_center[0], tee_center[1] )
    // @fairway_polygon        = fairway_polygon
    // @green_center           = to_point( green_center[0], green_center[1] )
    // @tee_to_green_bearing   = @tee_center.bearing_to(*@green_center.to_array)
    // @intersect_points       = []
    // @factory                = ::RGeo::Geos.factory(srid: 4326)
  }

  //
  // get an array of centerpoints
  //
  centerpoints() {
    // get_fairway_intersect_points
    // points = intersect_points.map { |point| point[:midpoint] }.compact
    // centerpoint_line = [tee_center.to_array] + points + [green_center.to_array]
    // centerpoint_line
  }

  only_centerpoints() {
    // get_fairway_intersect_points
    // intersect_points.map { |point| point[:midpoint] }.compact
  }

  dogleg_distance() {
    // distance = 0
    // centerpoint_line.points.each_with_index do |point, index|
    //   next unless centerpoint_line.points[index + 1]
    //   distance += ::Geocoder::Calculations.distance_between([point.y, point.x], [centerpoint_line.points[index + 1].y, centerpoint_line.points[index + 1].x])
    // end
    // ( distance * MILES_TO_YARDS ).round(0)
  }

  //
  // uses the centerpoints to create a line, starting with the tee center
  //
  centerpoint_line() {
    // points = centerpoints.map { |p| factory.point(p[1], p[0]) }
    // factory.line_string( points )
  }

  to_line() {
    // lines   = []
    // points  = centerpoints.map { |p| factory.point(p[1], p[0]) }
    // points.each_with_index do |ps, index|
    //   next if points[index + 1].blank?
    //   lines << factory.line_string([points[index], points[index + 1]])
    // end
    // # points.each_slice(2).map do |ps|
    // #   factory.line_string( ps )
    // # end
    // lines
  }

  //
  // get all fairway intersect points
  //
  get_fairway_intersect_points() {
    // return @intersect_points unless @intersect_points.empty?
    // [45, 40, 35, 30, 25, 20, 15, 10, 5, 0, -5, -10, -15, -20, -25, -30, -40].each do |bearing|
    //   intersect_point = get_intersect_point(bearing)
    //   next unless intersect_point
    //   @intersect_points << {
    //     intersect_point:  intersect_point,
    //     bearing:          (tee_to_green_bearing + bearing),
    //     distance:         tee_center.distance_to(*intersect_point),
    //     endpoint:         get_endpoint(intersect_point, bearing),
    //     midpoint:         get_midpoint(intersect_point, bearing),
    //   }
    // end
    // @intersect_points = @intersect_points.sort_by { |m| m[:distance] }
  }

  // #
  // # private methods
  // #
  // private

  // #
  // # get an endpoint so we can determine the intersect on the fairway
  // #
  get_endpoint(intersect_point, bearing) {
    console.log(intersect_point, bearing);

    // get_fairway_intersect_point( intersect_point[0], intersect_point[1], (tee_to_green_bearing + bearing) )
  }

  //
  // get the midpoint between two points
  //
  get_midpoint(intersect_point, bearing) {
    console.log(intersect_point, bearing);

    // endpoint  =  get_endpoint(intersect_point, bearing)
    // point     = ::Geocoder::Calculations.geographic_center( [ intersect_point, endpoint ] )
    // return nil if point[0].nan?
    // [ point[0].round(8), point[1].round(8) ]
  }

  //
  // Get's the intersect point for a given bearing
  //
  get_intersect_point(bearing) {
    console.log(bearing);

    // tee_center.get_intersect_point_using_computed_endpoint( (tee_to_green_bearing + bearing), fairway_polygon )
  }

  //
  // get the fairway's intersect point
  //
  get_fairway_intersect_point(lat, lng, bearing) {
    console.log(lat, lng, bearing);

    // to_point(lat, lng).get_intersect_point_using_computed_endpoint( (bearing + 120), fairway_polygon )
  }

  //
  // convert to a point formatter
  //
  to_point(lat, lng) {
    console.log(lat, lng);

    // ::SmartGolf::Stats::Formatters::Point.new( lat, lng )
  }
}
