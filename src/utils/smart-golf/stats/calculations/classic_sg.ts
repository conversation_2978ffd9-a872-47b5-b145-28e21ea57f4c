import { Logger } from '@nestjs/common';
import { isEmpty } from 'lodash';
import { StrokesPlayedService } from 'src/strokes-played/strokes-played.service';
import { includeStr } from 'src/utils/utils';
import { Stats } from '../../stats';
import { SmartGolfStatsCalculationsGreens } from './greens';

export class SmartGolfStatsCalculationsClassicSg extends SmartGolfStatsCalculationsGreens {
  private readonly loggerClassicSg = new Logger(SmartGolfStatsCalculationsClassicSg.name);
  strokes: any;
  userSkill: any;
  strokePlayedService: StrokesPlayedService;
  all_rounds_strokes: any;
  constructor(strokes?: any, userSkill?: any, strokePlayedService?: StrokesPlayedService, all_rounds_strokes?: any) {
    super(strokes, null, userSkill, all_rounds_strokes);
    if (strokes) {
      this.strokes = strokes;
    }
    if (userSkill) {
      this.userSkill = userSkill;
    }
    if (strokePlayedService) {
      this.strokePlayedService = strokePlayedService;
    }
  }
  //
  // Strokes Gained for all Types
  //
  strokes_gained_for_all() {
    this.loggerClassicSg.log('STROKES_GAINED_FOR_ALL');
    const sgDriving = this.strokes_gained_for_driving();
    const sgApproach = this.strokes_gained_for_approach();
    const sgShort = this.strokes_gained_for_short();
    const sgPutting = this.strokes_gained_for_putting();
    this.loggerClassicSg.debug({ sgDriving, sgApproach, sgPutting, sgShort });
    const strokes_gained_for_all = sgDriving + sgApproach + sgShort + sgPutting;
    return strokes_gained_for_all;
  }

  //
  // Strokes Gained for only long tee/driving shots/strokes
  //
  strokes_gained_for_driving() {
    try {
      const sg_total = this.get_sum_total_for_sg(this.long_tee_shots(this.strokes));
      return this.calculate_strokes_gained(sg_total);
    } catch (error) {
      this.loggerClassicSg.error(error.message);
      return 0;
    }
  }

  long_tee_shots(strokes) {
    if (isEmpty(strokes)) {
      return [];
    }
    const strokesLongTee = strokes.filter((stroke) => stroke.long_tee == true);
    return strokesLongTee;
  }

  // alias_method :driving_sg, :strokes_gained_for_driving

  //
  // Strokes Gained for only approach shots/strokes
  //
  strokes_gained_for_approach() {
    try {
      const sg_total = this.get_sum_total_for_sg(this.approach_shots());
      return this.calculate_strokes_gained(sg_total);
    } catch (error) {
      this.loggerClassicSg.error(error.message);
      return 0;
    }
  }
  approach_shots() {
    if (isEmpty(this.strokes)) {
      return [];
    }
    const approach_shots = this.strokes.filter((stroke) => {
      const isValid =
        !stroke.starting_as_penalty_shot &&
        !stroke.starting_as_recovery_shot &&
        !(stroke.starting_lie?.toLowerCase().includes('tee') && stroke.holes_par >= 4) &&
        !stroke.starting_lie?.toLowerCase().includes('green') &&
        stroke.starting_distance_to_pin > Stats.ONE_HUNDRED_YARDS;
      // stroke.starting_distance_to_pin * Stats.METERS_TO_YARDS > Stats.ONE_HUNDRED_YARDS;
      return isValid;
    });
    if (!isEmpty(approach_shots)) {
      // update approach
    }
    return approach_shots;
    // approach_shots = approach_shots?.map(|stroke| !stroke.approach ? stroke.update(approach: true) : stroke )
  }

  // alias_method :approach_sg, :strokes_gained_for_approach

  //
  // Strokes Gained for only short tee shots/strokes
  //
  strokes_gained_for_short() {
    //   @strokes_gained_for_short = begin
    //   sg_total = get_sum_total_for_sg(short_tee_shots)
    //   calculate_strokes_gained( sg_total )
    // end
    try {
      const sg_total = this.get_sum_total_for_sg(this.short_tee_shots(this.strokes));
      return this.calculate_strokes_gained(sg_total);
    } catch (error) {
      return 0;
    }
  }

  short_tee_shots(strokes) {
    if (!strokes || isEmpty(strokes)) {
      return [];
    }
    const shots = strokes.filter((stroke) => {
      return (
        !stroke.starting_as_penalty_shot &&
        !stroke.starting_as_recovery_shot &&
        !includeStr(stroke.starting_lie, 'green') &&
        stroke.starting_distance_to_pin != null &&
        stroke.starting_distance_to_pin <= Stats.ONE_HUNDRED_YARDS
        // stroke.starting_distance_to_pin * Stats.METERS_TO_YARDS <= Stats.ONE_HUNDRED_YARDS
      );
    });
    // if (shots) {
    //   shots = shots.map((shot) => {
    //     // if (!shot.short_tee) {
    //     //   this.strokePlayedService.updateStroke(shot.id, { short_tee: true });
    //     //   shot.short_tee = true;
    //     // }
    //     return shot;
    //   });
    // }
    return shots;
  }
  // alias_method :short_sg, :strokes_gained_for_short

  //
  // Strokes Gained for only putting shots/strokes
  //
  strokes_gained_for_putting() {
    try {
      const sg_total = this.get_sum_total_for_sg(this.putting_shots(this.strokes));
      return this.calculate_strokes_gained(sg_total);
    } catch (error) {
      this.loggerClassicSg.debug(error.message);
      return 0;
    }
  }

  putting_shots(strokes) {
    if (!strokes || isEmpty(strokes)) {
      return [];
    }
    const strokesPutting = strokes.filter((stroke) => includeStr(stroke.starting_lie, 'green'));
    return strokesPutting;
  }
  // alias_method :putting_sg, :strokes_gained_for_putting

  //
  // Strokes Gained for only shots/strokes that used a driver
  //
  strokes_gained_for_driver_clubs() {
    //   @strokes_gained_for_driver_clubs = begin
    //   sg_total = get_sum_total_for_sg(all_driver_clubs)
    //   calculate_strokes_gained( sg_total )
    // end
    try {
      const sg_total = this.get_sum_total_for_sg(this.all_driver_clubs());
      return this.calculate_strokes_gained(sg_total);
    } catch (error) {
      this.loggerClassicSg.error(error.message);
      return 0;
    }
  }
  all_driver_clubs() {
    // return [] if strokes.blank?
    // @all_driver_clubs ||= strokes.select do |stroke|
    //   stroke.club_id && stroke.club.club_type =~ /driver/i
    // end
    if (!this.strokes || isEmpty(this.strokes)) {
      return [];
    }
    return this.strokes.filter((stroke) => {
      return stroke.club_id && includeStr(stroke.club_type, 'driver');
    });
  }
  // alias_method :driver_clubs_sg, :strokes_gained_for_driver_clubs

  //
  // Strokes Gained for only shots/strokes that used a 3-FW
  //
  strokes_gained_for_three_wood_clubs() {
    try {
      const sg_total = this.get_sum_total_for_sg(this.all_three_wood_clubs());
      return this.calculate_strokes_gained(sg_total);
    } catch (error) {
      this.loggerClassicSg.error(error.message);
      return 0;
    }
  }
  all_three_wood_clubs() {
    if (!this.strokes || isEmpty(this.strokes)) {
      return [];
    }
    // @all_three_wood_clubs ||= strokes.select do |stroke|
    //   stroke.club_id && (stroke.club.club_type =~ /3-fw/i || stroke.club.club_type =~ /3fw/i)
    // end
    return this.strokes.filter((stroke) => {
      return stroke.club_id && ['3-fw', '3fw'].includes(stroke.club_type?.toLowerCase()?.trim());
    });
  }
  // alias_method :three_wood_clubs_sg, :strokes_gained_for_three_wood_clubs

  //
  // Strokes Gained for only shots/strokes that used a 3-FW
  //
  strokes_gained_for_other_clubs() {
    try {
      const sg_total = this.get_sum_total_for_sg(this.all_three_wood_clubs());
      return this.calculate_strokes_gained(sg_total);
    } catch (error) {
      this.loggerClassicSg.error(error.message);
      return 0;
    }
  }

  // alias_method :other_clubs_sg, :strokes_gained_for_other_clubs

  get_sum_total_for_sg(strokes_selection) {
    let totalSg = 0;
    if (isEmpty(strokes_selection)) {
      return 0;
    }
    strokes_selection.forEach((stroke) => {
      switch (this.userSkill) {
        case 'pga':
          totalSg += +stroke.strokes_gained_pro;
          break;
        case 'five':
          totalSg += +stroke.strokes_gained_five;
          break;
        case 'ten':
          totalSg += +stroke.strokes_gained_ten;
          break;
        case 'fifteen':
          totalSg += +stroke.strokes_gained_fifteen;
          break;
        case 'twenty':
          totalSg += +stroke.strokes_gained_twenty;
          break;
        default:
          totalSg += +stroke.strokes_gained;
          break;
      }
    });
    return totalSg;
  }
}
