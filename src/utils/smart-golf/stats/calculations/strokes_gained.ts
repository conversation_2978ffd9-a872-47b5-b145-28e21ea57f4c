//
// Various sum totals
//
// Expects the following methods to be available:
//
//   * strokes: an array of StrokeStat objects
//
// Meant to be included in different types of StrokeStats groups.
//
//   e.g.
//
//     * LongTee
//     * Approach
//     * ShortTee
//     * Putting
//
export class SmartGolfStatsCalculationsStrokesGained {
  //
  // Calculates the Strokes Gained for a group of strokes
  //
  // SG/Round
  //
  // calculate_strokes_gained(sg_total = strokes_gained_total) {
  calculate_strokes_gained(sg_total) {
    console.log(sg_total);

    //           return 0 if sg_total.to_f.zero?               ||
    //           all_holes_played_total.zero?      ||
    //           all_rounds_played_total.zero?
    // (sg_total.to_f / all_holes_played_total * 18).round(1)
  }

  //
  // Calculates Strokes per Round for a group of strokes
  //
  // Number of Shots / Round
  //
  calculate_strokes_per_round() {
    // return 0 if all_rounds_played_total.zero? || strokes_total.zero?
    // (strokes_total.to_f / all_rounds_played_total).round(1)
  }

  //
  // SG/Shot
  //
  calculate_strokes_gained_per_shot() {
    // if(this.strokes)
    // return 0 if strokes_total.to_f.zero?
    // return 0 if strokes_gained_total.to_f.zero?
    //
    // (strokes_gained_total.to_f / strokes_total.to_f).round(1)
  }
}
