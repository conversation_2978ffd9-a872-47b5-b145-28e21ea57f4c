import { isArray, isObject } from 'lodash';
import { convertArrToLocation } from 'src/utils/utils';

const COMPASS_POINTS = ['N', 'NE', 'E', 'SE', 'S', 'SW', 'W', 'NW'];

const KM_IN_MI = 0.621371192;

const KM_IN_NM = 0.539957;

// const DEGREES_PER_RADIAN = 57.2957795;

const EARTH_RADII = { km: 6371.0, mi: KM_IN_MI * 6371, nm: KM_IN_NM * 6371, meter: 1609.34 };

// const EARTH_RADIUS = EARTH_RADII['km']; //# TODO: deprecate this constant (use `EARTH_RADII[:km]`)
enum METHODS {
  LINEAR = 'linear',
  SPHERICAL = 'spherical',
}

export class GeocoderCalculations {
  // ##
  // # Returns true if all given arguments are valid latitude/longitude values.
  // #
  static coordinates_present(args) {
    for (const a of args) {
      if (isNaN(+a)) {
        return false;
      }
    }
    return true;
  }

  // ##
  // # Distance spanned by one degree of latitude in the given units.
  // #
  static latitude_degree_distance(units) {
    return (2 * Math.PI * this.earth_radius(units)) / 360;
  }

  // ##
  // # Distance spanned by one degree of longitude at the given latitude.
  // # This ranges from around 69 miles at the equator to zero at the poles.
  // #
  static longitude_degree_distance(latitude, units) {
    return this.latitude_degree_distance(units) * Math.cos(this.to_radians(latitude));
  }

  // ##
  // # Distance between two points on Earth (Haversine formula).
  // # Takes two points and an options hash.
  // # The points are given in the same way that points are given to all
  // # Geocoder methods that accept points as arguments. They can be:
  // #
  // # * an array of coordinates ([lat,lon])
  // # * a geocodable address (string)
  // # * a geocoded object (one which implements a +to_coordinates+ method
  // #   which returns a [lat,lon] array
  // #
  // # The options hash supports:
  // #
  // # * <tt>:units</tt> - <tt>:mi</tt> or <tt>:km</tt>
  // #   Use Geocoder.configure(:units => ...) to configure default units.
  // #
  static distanceBetween(point1, point2, options = {}) {
    //   # convert to coordinate arrays
    point1 = this.extract_coordinates(point1);
    point2 = this.extract_coordinates(point2);

    //   # convert degrees to radians
    point1 = this.to_radians(point1);
    point2 = this.to_radians(point2);

    //   # compute deltas
    const dlat = point2[0] - point1[0];
    const dlon = point2[1] - point1[1];

    const a =
      Math.pow(Math.sin(dlat / 2), 2) + Math.cos(point1[0]) * Math.pow(Math.sin(dlon / 2), 2) * Math.cos(point2[0]);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return +c * this.earth_radius(options['units']);
  }

  // ##
  // # Bearing between two points on Earth.
  // # Returns a number of degrees from due north (clockwise).
  // #
  // # See Geocoder::Calculations.distance_between for
  // # ways of specifying the points. Also accepts an options hash:
  // #
  // # * <tt>:method</tt> - <tt>:linear</tt> or <tt>:spherical</tt>;
  // #   the spherical method is "correct" in that it returns the shortest path
  // #   (one along a great circle) but the linear method is less confusing
  // #   (returns due east or west when given two points with the same latitude).
  // #   Use Geocoder.configure(:distances => ...) to configure calculation method.
  // #
  // # Based on: http://www.movable-type.co.uk/scripts/latlong.html
  // #
  static bearingBetween(point1, point2, options = {}) {
    //   # set default options
    options['method'] ||= METHODS.SPHERICAL;
    if (options['method'] != METHODS.LINEAR) {
      options['method'] = METHODS.SPHERICAL;
    }
    //   # convert to coordinate arrays
    point1 = this.extract_coordinates(point1);
    point2 = this.extract_coordinates(point2);

    //   # convert degrees to radians
    point1 = this.to_radians(point1);
    point2 = this.to_radians(point2);

    //   # compute deltas
    const dlat = point2[0] - point1[0];
    const dlon = point2[1] - point1[1];
    let y, x;
    if (options['method'] == METHODS.LINEAR) {
      y = dlon;
      x = dlat;
    }
    if (options['method'] == METHODS.SPHERICAL) {
      y = Math.sin(dlon) * Math.cos(point2[0]);
      x = Math.cos(point1[0]) * Math.sin(point2[0]) - Math.sin(point1[0]) * Math.cos(point2[0]) * Math.cos(dlon);
    }

    const bearing = Math.atan2(x, y);
    //   # Answer is in radians counterclockwise from due east.
    //   # Convert to degrees clockwise from due north:
    return (90 - this.to_degrees(bearing) + 360) % 360;
  }

  // ##
  // # Translate a bearing (float) into a compass direction (string, eg "North").
  // #
  compass_point(bearing, points = COMPASS_POINTS) {
    const seg_size = 360.0 / points.length;
    return points[((bearing + seg_size / 2) % 360) / seg_size];
  }

  // ##
  // # Compute the geographic center (aka geographic midpoint, center of
  // # gravity) for an array of geocoded objects and/or [lat,lon] arrays
  // # (can be mixed). Any objects missing coordinates are ignored. Follows
  // # the procedure documented at http://www.geomidpoint.com/calculation.html.
  // #
  static geographic_center(points) {
    //   # convert objects to [lat,lon] arrays and convert degrees to radians
    const coords = points.map((p) => this.to_radians(this.extract_coordinates(p)));

    //   # convert to Cartesian coordinates
    const x = [],
      y = [],
      z = [];
    //   coords.each do |p|
    //     x << Math.cos(p[0]) * Math.cos(p[1])
    //     y << Math.cos(p[0]) * Math.sin(p[1])
    //     z << Math.sin(p[0])
    //   end
    for (const coord of coords) {
      x.push(Math.cos(coord[0]) * Math.cos(coord[1]));
      y.push(Math.cos(coord[0]) * Math.sin(coord[1]));
      z.push(Math.sin(coord[0]));
    }

    //   # compute average coordinate values
    //   xa, ya, za = [x,y,z].map do |c|
    //     c.inject(0){ |tot,i| tot += i } / c.size.to_f
    //   end
    const xa = this.computeAverageCoords(x);
    const ya = this.computeAverageCoords(y);
    const za = this.computeAverageCoords(z);

    //   xa, ya, za = [x,y,z].map do |c|
    //     c.inject(0){ |tot,i| tot += i

    // } / c.size.to_f
    //       end

    //   # convert back to latitude/longitude
    const lon = Math.atan2(ya, xa);
    const hyp = Math.sqrt(Math.pow(xa, 2) + Math.pow(ya, 2));
    const lat = Math.atan2(za, hyp);

    //   # return answer in degrees
    return this.to_degrees([lat, lon]);
  }

  private static computeAverageCoords(x: any[]) {
    const totalX = x.reduce((total, i) => total + i, 0);
    const totalXValid = x.filter((x) => x != 0)?.length || 1;
    const xa = totalX / totalXValid;
    return xa;
  }
  static bounding_box(point, radius, options = {}) {
    const [lat, lon] = this.extract_coordinates(point);
    radius =
      radius *
      (1.0)[
        (lat - radius / this.latitude_degree_distance(options['units']),
        lon - radius / this.longitude_degree_distance(lat, options['units']),
        lat + radius / this.latitude_degree_distance(options['units']),
        lon + radius / this.longitude_degree_distance(lat, options['units']))
      ];
  }

  random_point_near(center, radius, options = {}) {
    console.log(center, radius, options);

    //   random = Random.new(options[:seed] || Random.new_seed)
    // //   # convert to coordinate arrays
    //   center = extract_coordinates(center)
    //   earth_circumference = 2 * Math::PI * earth_radius(options[:units])
    //   max_degree_delta =  360.0 * (radius / earth_circumference)
    // //   # random bearing in radians
    //   theta = 2 * Math::PI * random.rand
    // //   # random radius, use the square root to ensure a uniform
    // //   # distribution of points over the circle
    //   r = Math.sqrt(random.rand) * max_degree_delta
    //   delta_lat, delta_long = [r * Math.cos(theta), r * Math.sin(theta)]
    //  return  [center[0] + delta_lat, center[1] + delta_long]
  }

  static endpoint(start, heading, distance, options = {}) {
    const radius = this.earth_radius(options['units']);

    start = this.extract_coordinates(start);
    //   # convert degrees to radians
    start = this.to_radians(start);

    const lat = start[0];
    const lon = start[1];
    heading = this.to_radians(heading);

    const end_lat = Math.asin(
      Math.sin(lat) * Math.cos(distance / radius) + Math.cos(lat) * Math.sin(distance / radius) * Math.cos(heading)
    );

    const end_lon =
      lon +
      Math.atan2(
        Math.sin(heading) * Math.sin(distance / radius) * Math.cos(lat),
        Math.cos(distance / radius) - Math.sin(lat) * Math.sin(end_lat)
      );

    return this.to_degrees([end_lon, end_lat]);
  }

  // ##
  // # Convert degrees to radians.
  // # If an array (or multiple arguments) is passed,
  // # converts each value and returns array.
  // #
  static to_radians(args) {
    // args = args.first if args.first.is_a?(Array)
    // if args.size == 1
    //   args.first * (Math::PI / 180)
    // else
    //   args.map{ |i| to_radians(i) }
    // end
    if (isArray(args)) {
      return args.map((i) => this.to_radians(i));
    } else {
      return args * (Math.PI / 180);
    }
  }

  // ##
  // # Convert radians to degrees.
  // # If an array (or multiple arguments) is passed,
  // # converts each value and returns array.
  // #
  static to_degrees(args) {
    //   args = args.first if args.first.is_a?(Array)
    //   if args.size == 1
    //     (args.first * 180.0) / Math::PI
    //   else
    //     args.map{ |i| to_degrees(i) }
    //   end

    if (isArray(args)) {
      return args.map((i) => this.to_degrees(i));
    } else {
      return (args * 180) / Math.PI;
    }
  }

  static distance_to_radians(distance, units) {
    return distance / this.earth_radius(units);
  }

  static radians_to_distance(radians, units) {
    return radians * this.earth_radius(units);
  }

  // ##
  // # Convert miles to kilometers.
  // #
  static to_kilometers(mi) {
    return mi * this.mi_in_km();
  }

  // ##
  // # Convert kilometers to miles.
  // #
  static to_miles(km) {
    return km * KM_IN_MI;
  }

  // ##
  // # Convert kilometers to nautical miles.
  // #
  static to_nautical_miles(km) {
    return km * KM_IN_NM;
  }
  // ##
  // # Radius of the Earth in the given units (:mi or :km).
  // # Use Geocoder.configure(:units => ...) to configure default units.
  // #
  static earth_radius(units) {
    units = units ? units : 'mi';
    return EARTH_RADII[units];
  }

  // ##
  // # Conversion factor: km to mi.
  // #
  static km_in_mi() {
    return KM_IN_MI;
  }

  // ##
  // # Conversion factor: km to nm.
  // #
  static km_in_nm() {
    return KM_IN_NM;
  }

  // ##
  // # Conversion factor: mi to km.
  // #
  static mi_in_km() {
    return 1.0 / KM_IN_MI;
  }

  // ##
  // # Conversion factor: nm to km.
  // #
  static nm_in_km() {
    return 1.0 / KM_IN_NM;
  }
  static extract_coordinates(point: any) {
    if (isArray(point)) {
      const pointObject = convertArrToLocation(point);
      return [pointObject.lat, pointObject.lon];
    }
    if (isObject(point) && point['lat']) {
      return [point['lat'], point['lon']];
    }
    return [0, 0];
    //   case point
    //   when Array
    //     if point.size == 2
    //       lat, lon = point
    //       if !lat.nil? && lat.respond_to?(:to_f) and
    //         !lon.nil? && lon.respond_to?(:to_f)
    //       then
    //         return [ lat.to_f, lon.to_f ]
    //       end
    //     end
    //   when String
    //     point = Geocoder.coordinates(point) and return point
    //   else
    //     if point.respond_to?(:to_coordinates)
    //       if Array === array = point.to_coordinates
    //         return extract_coordinates(array)
    //       end
    //     end
    //   end
    //   [ NAN, NAN ]
  }
}
