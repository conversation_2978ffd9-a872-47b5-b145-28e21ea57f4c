//
// green_points = {"short_point"=>[51.38690698, -0.72085146], "left_point"=>[51.38706429, -0.72075927], "long_point"=>[51.38711556, -0.72050902], "right_point"=>[51.38697431, -0.72061854], "short_bearing"=>45.694256986548226, "left_bearing"=>315.6942569865482, "long_bearing"=>225.69425698654823, "right_bearing"=>135.69425698654823, "center"=>[51.38701248659581, -0.7206782470960525], "approach"=>[51.38625456205219, -0.7219225653085668]}
//
// shot_coords = [ 51.3870662356782, -0.72060673298986 ]
//
// green = SmartGolf::Stats::Calculations::GreenDistances.new( green_points, shot_coords )
//
//
import { Logger } from '@nestjs/common';
import * as turf from '@turf/turf';
import { Point } from 'geojson';
import { convertArrToLocation, getDistancePoints, isPointEmpty } from 'src/utils/utils';
import { Stats } from '../../stats';
import { GeocoderCalculations } from './geocoder';

export class SmartGolfStatsCalculationsGreenDistances {
  private readonly logger = new Logger(SmartGolfStatsCalculationsGreenDistances.name);
  // attr_reader :green_points, :shot_coords, :factory, :shot_bearing,
  //             :shot_location, :long_short, :right_left

  //
  // Green points: Hash - From BuildGreenDirections#four_green_points
  // Shot Coordinate: Array - Lat / Lng of the shot to get distance for
  //

  green_points: any;
  shot_coords: any;
  factory: any;
  shot_bearing: any;
  shot_location: any;
  long_short: any;
  right_left: any;
  constructor(green_points, shot_coords) {
    this.green_points = { ...green_points };
    this.shot_coords = shot_coords;
    if (this.green_points) {
      this.green_points['center']?.reverse();
      this.green_points['right_point']?.reverse();
      this.green_points['left_point']?.reverse();
      this.green_points['approach']?.reverse();
      this.shot_bearing = this.getBearing(this.shot_coords, this.green_points['center']);
      this.shot_location = this.getShotLocation(this.shot_bearing);
      if (this.shot_location) {
        this.long_short = this.shot_location.split('-')[0];
        this.right_left = this.shot_location.split('-')[1];
      }
    }
  }

  get_distances() {
    // return {} if shot_location.nil?
    // {
    //   long_short  => y_axis_distance,
    //   right_left  => x_axis_distance
    // }
    if (!this.shot_location) {
      return {};
    }
    return {
      [this.long_short]: this.yAxisDistance(),
      [this.right_left]: this.xAxisDistance(),
    };
  }

  //
  // Long/Short distance
  //
  yAxisDistance() {
    // get_distance_between_points(shot_coords, get_short_long_point)
    return this.getDistanceBetweenPoints(this.shot_coords, this.getShortLongPoint());
  }

  //
  // Right/Left distance
  //
  xAxisDistance() {
    // get_distance_between_points(shot_coords, get_left_right_point)
    return this.getDistanceBetweenPoints(this.shot_coords, this.getLeftRightPoint());
  }

  //
  // creates a new point that intersects with the y axis
  //
  getShortLongPoint() {
    const location = this.shot_location.split('-')[0];
    switch (location) {
      case 'short':
        return this.getIntersectionPoint(this.green_points['short_bearing'], this.y_axis());
      case 'long':
        return this.getIntersectionPoint(this.green_points['long_bearing'], this.y_axis());
    }
  }

  //
  // creates a new point that intersects with the x axis
  //
  getLeftRightPoint() {
    switch (this.shot_location.split('-')[1]) {
      case 'left':
        return this.getIntersectionPoint(this.green_points['right_bearing'], this.x_axis());
      case 'right':
        return this.getIntersectionPoint(this.green_points['left_bearing'], this.x_axis());
    }
  }

  //
  // TODO:
  //
  //     1) determine if the shot coords is short or long
  //     2) determine if the shot coords is right or left
  //     3) create lines for all 4 points to the center point
  //
  //     4) using green points' bearings, draw line from shot coords
  //        to the x axis and get the intersecting point.
  //     5) using green points' bearings, draw line from shot coords
  //        to the y axis and get the intersecting point.
  //     6) using each intersect point, draw a line to the centerpoint
  //        and get that line's distance.
  //
  //
  //  create the long/short line
  //
  x_axis() {
    // @x_axis ||= generate_line( green_points[:approach],
    //   green_points[:long_point] )
    return this.generateLine(this.green_points['approach'], this.green_points['long_point']);
  }

  //
  //  create the right/left line
  //
  y_axis() {
    // @y_axis ||= generate_line( green_points[:right_point],
    // green_points[:left_point] )
    return this.generateLine(this.green_points['right_point'], this.green_points['left_point']);
  }

  //
  // Calculate the distance between 2 points. Returns distance in feet.
  //
  //   * startpoint: Array - [lat,lng]
  //   * endpoint:   Array - [lat,lng]
  //
  getDistanceBetweenPoints(startPoint, endpoint) {
    // return 0 if startpoint.blank? || endpoint.blank?
    // distance_in_miles = ::Geocoder::Calculations.distance_between(startpoint, endpoint)
    // distance_in_miles * ::SmartGolf::Stats::MILES_TO_FEET
    if (isPointEmpty(startPoint) || isPointEmpty(endpoint)) {
      return 0;
    }
    const distance_in_miles = getDistancePoints(startPoint, endpoint, Stats.MILES_TO_FEET);
    // return distance_in_miles * Stats.MILES_TO_FEET;
    return distance_in_miles;
  }

  //
  // Get an intersection point
  //
  //   * Bearing: Float
  //
  getIntersectionPoint(bearing, location_line) {
    // return [] if location_line.blank?
    // point         = ::Geocoder::Calculations.endpoint(shot_coords, bearing, 0.1)
    // line          = generate_line(shot_coords, point)
    // intersection  = line.intersection(location_line)
    // # getting a line string... when we want a point
    // if intersection.respond_to?(:points)
    //   points        = intersection.points.select { |point| !line.points.include?(point) }
    //   intersection  = points.first
    // end
    // intersection = intersection.respond_to?(:first) ? intersection.first : intersection
    // if intersection.respond_to?(:y)
    //   [ intersection.y.round(8), intersection.x.round(8) ]
    // else
    //   []
    // end
    if (!location_line || isPointEmpty(location_line)) {
      return [];
    }
    const point = GeocoderCalculations.endpoint(this.shot_coords, bearing, 0.1);
    const line: any = this.generateLine(this.shot_coords, point);
    const intersection = turf.lineIntersect(location_line, line);
    if (intersection.features.length == 0) {
      return [];
    }
    return intersection.features[0].geometry.coordinates;
  }

  //
  // Get where the shot landed: left/right, short/long
  //
  getShotLocation(bearing) {
    // normalized_bearing = normalize_bearing(bearing)
    // return "short-left"   if (0..90).cover?(normalized_bearing)
    // return "long-left"    if (91..180).cover?(normalized_bearing)
    // return "long-right"   if (181..270).cover?(normalized_bearing)
    // return "short-right"  if (271..360).cover?(normalized_bearing)
    const normalized_bearing = this.normalize_bearing(bearing);
    if (0 <= normalized_bearing && normalized_bearing <= 90) {
      return 'short-left';
    }
    if (91 <= normalized_bearing && normalized_bearing <= 180) {
      return 'long-left';
    }
    if (181 <= normalized_bearing && normalized_bearing <= 270) {
      return 'long-right';
    }
    if (271 <= normalized_bearing && normalized_bearing <= 360) {
      return 'short-right';
    }
  }

  //
  // normalize a given bearing
  //
  normalize_bearing(bearing) {
    // bearing = bearing.to_f - green_points[:short_bearing].to_f
    // format_bearing(bearing)
    bearing = +bearing - +this.green_points['short_bearing'];
    return this.format_bearing(bearing);
  }

  format_bearing(bearing) {
    // return (360 + bearing) if bearing < 0
    // return (bearing - 360) if bearing > 360
    // bearing
    if (bearing < 0) {
      return 360 + bearing;
    }
    if (bearing > 360) {
      return bearing - 360;
    }
    return bearing;
  }

  //
  // generate a line
  //
  //   * startpoint: Array - [lat,lng]
  //   * endpoint:   Array - [lat,lng]
  //
  generateLine(startpoint, endpoint) {
    // return nil if startpoint.blank? || endpoint.blank?
    // start_point = generate_point(startpoint[0], startpoint[1])
    // end_point   = generate_point(endpoint[0], endpoint[1])
    // factory.line(start_point, end_point)
    if (isPointEmpty(startpoint) || isPointEmpty(endpoint)) {
      return null;
    }
    // let start_point: any = this.generate_point(startpoint[0], startpoint[1]);
    // let end_point: any = this.generate_point(endpoint[0], endpoint[1]);
    let start_point: any = convertArrToLocation(startpoint);
    let end_point: any = convertArrToLocation(endpoint);
    start_point = [+start_point.lon, +start_point.lat];
    end_point = [+end_point.lon, +end_point.lat];
    return turf.lineString([start_point, end_point]);
    // return line;
    // factory.line(start_point, end_point)
  }

  //
  // generate a point
  //
  //   * lat: Float - Latitude
  //   * lng: Float - Longitude
  //
  generate_point(lat, lng) {
    // factory.point(lng, lat)
    const pointObject: Point = {
      type: 'Point',
      coordinates: [lng, lat],
    };
    return pointObject;
  }

  //
  // Get bearing between 2 locations
  //
  // start_location: Array - [lat,lng]
  // end_location:   Array - [lat,lng]
  //
  getBearing(start_location, end_location) {
    // return if start_location.blank? || end_location.blank?
    if (isPointEmpty(start_location) || isPointEmpty(end_location)) {
      return null;
    }

    const origin = convertArrToLocation(start_location);
    //{ lat: start_location[0], lon: start_location[1] };
    const dest = convertArrToLocation(end_location);
    //{ lat: end_location[0], lon: end_location[1] };
    return GeocoderCalculations.bearingBetween(origin, dest);
    // ::Geocoder::Calculations.bearing_between(
    //   [ start_location[0], start_location[1] ],
    //   [ end_location[0], end_location[1] ]
    // )
  }
}
