import { Logger } from '@nestjs/common';
import _ from 'lodash';
import { isEmpty } from 'lodash';
import { includeStr } from 'src/utils/utils';
import { SmartGolfStatsCalculationsFairwaysInRegulation } from './fairways_in_regulation';

export class SmartGolfStatsCalculationsRegulations extends SmartGolfStatsCalculationsFairwaysInRegulation {
  // include SmartGolf::Stats::Calculations::StrokesGained
  // include SmartGolf::Stats::Calculations::FairwaysInRegulation
  // include SmartGolf::Stats::Calculations::GreensInRegulation
  private readonly loggerCR = new Logger(SmartGolfStatsCalculationsRegulations.name);
  strokes: any;
  holes_played: any;
  user_stroke_gained_baseline: any;
  all_rounds_strokes: any;

  constructor(strokes?: any, holes_played?: any, userStrokeGainedBaseLine?: any, all_rounds_strokes?: any) {
    super(strokes);
    this.strokes = strokes;
    this.holes_played = holes_played;
    this.user_stroke_gained_baseline = userStrokeGainedBaseLine;
    this.all_rounds_strokes = all_rounds_strokes;
  }
  //
  // FIR for all shot/stroke types
  //
  fairways_in_regulation() {
    //  return 0 if strokes.blank?
    //  @fairways_in_regulation ||= calculate_fairways_in_regulation
    if (isEmpty(this.strokes)) {
      return 0;
    }
    return this.calculate_fairways_in_regulation();
  }
  // alias_method :fir, :fairways_in_regulation

  //
  // GIR for all shot/stroke types
  //
  greens_in_regulation() {
    // return 0 if strokes.blank?
    // @greens_in_regulation ||= calculate_greens_in_regulation
    if (isEmpty(this.strokes)) {
      return 0;
    }
    return this.calculate_greens_in_regulation();
  }
  // alias_method :gir, :greens_in_regulation

  //
  // Calculate Strokes Gained
  //
  strokes_gained() {
    if (isEmpty(this.strokes)) {
      return 0;
    }
    const sgTotal = this.strokes_gained_total();
    return this.calculate_strokes_gained(sgTotal);
  }
  // alias_method :sg, :strokes_gained

  //
  // Calculate the number of shots per round average
  //
  // # Putts/Rounds || # Shots/Rounds
  //
  strokes_per_round() {
    return this.calculate_strokes_per_round();
  }

  //
  // Calculates the Strokes Gained for a group of strokes
  //
  // SG/Round
  //
  // calculate_strokes_gained(sg_total = strokes_gained_total) {
  calculate_strokes_gained(sg_total) {
    //           return 0 if sg_total.to_f.zero?               ||
    //           all_holes_played_total.zero?      ||
    //           all_rounds_played_total.zero?
    // (sg_total.to_f / all_holes_played_total * 18).round(1)
    if (sg_total == 0) {
      return 0;
    }
    const all_holes_played = this.all_holes_played().length || 0;
    if (all_holes_played == 0 || isNaN(all_holes_played)) {
      return 0;
    }
    return +((sg_total / all_holes_played) * 18).toFixed(1);
    //           all_holes_played_total.zero?      ||
    //           all_rounds_played_total.zero?
    // (sg_total.to_f / all_holes_played_total * 18).round(1)
  }

  all_rounds_played() {
    // return [] if all_strokes.blank?
    // @all_rounds_played ||= all_strokes.flat_map { |m| m.round }.uniq
    if (isEmpty(this.all_rounds_strokes)) {
      return [];
    }
    return _.uniq(this.all_rounds_strokes.map((m) => m.round_id));
  }
  all_holes_played() {
    // return [] if all_strokes.empty?
    // @all_holes_played ||= all_strokes.flat_map { |m| m.hole_played_id }.uniq
    if (isEmpty(this.all_rounds_strokes)) {
      return [];
    }
    return _.uniq(this.all_rounds_strokes.map((m) => m.hole_played_id));
  }
  //
  // Calculates Strokes per Round for a group of strokes
  //
  // Number of Shots / Round
  //
  calculate_strokes_per_round() {
    const allHolesPlayed = this.all_rounds_played_total();
    const strokesTotal = this.strokes_total();
    if (allHolesPlayed == 0 || strokesTotal == 0) {
      return 0;
    }
    return +(strokesTotal / allHolesPlayed).toFixed(1);
  }

  //
  // SG/Shot
  //
  calculate_strokes_gained_per_shot() {
    if (isEmpty(this.strokes)) {
      return 0;
    }
    const strokeTotal = this.strokes_total();
    const strokeGainedTotal = this.strokes_gained_total();
    // if(this.strokes)
    // return 0 if strokes_total.to_f.zero?
    // return 0 if strokes_gained_total.to_f.zero?
    //
    return +(strokeGainedTotal / strokeTotal).toFixed(1);
  }

  strokes_gained_total() {
    let total = 0;
    for (const stroke of this.strokes) {
      switch (this.user_stroke_gained_baseline) {
        case 'pga':
          total += +stroke.strokes_gained_pro;
          break;
        case 'five':
          total += +stroke.strokes_gained_five;
          break;
        case 'ten':
          total += +stroke.strokes_gained_ten;
          break;
        case 'fifteen':
          total += +stroke.strokes_gained_fifteen;
          break;
        case 'twenty':
          total += +stroke.strokes_gained_twenty;
          break;
        default:
          total += +stroke.strokes_gained;
      }
    }
    return total;
  }

  strokes_total() {
    // strokes.group_by(&:stroke_played_id).keys.uniq.size.to_f
    const group = _.groupBy(this.strokes, 'stroke_played_id');
    const strokePlayedIds = Object.keys(group);
    return _.uniq(strokePlayedIds).length;
  }

  //
  // Calculate the strokes_gained per shot average
  //
  // SG/Shot
  //
  strokes_gained_per_shot() {
    if (isEmpty(this.strokes)) {
      return 0;
    }
    // return 0 if strokes.blank?
    // @strokes_gained_per_shot = calculate_strokes_gained_per_shot
    return this.calculate_strokes_gained_per_shot();
  }

  // alias_method :sg_shot, :strokes_gained_per_shot

  //
  // Number of total putts taken
  //
  number_of_putts_taken() {
    // @number_of_putts_taken ||= putting_shots.size
    let shots = [];
    if (!isEmpty(this.strokes)) {
      shots = this.strokes.filter((stroke) => includeStr(stroke.starting_lie, 'green'));
    }
    return shots.length;
  }

  //
  // Calculate the total number of putts per round
  //
  // Putts/Rounds
  //
  number_of_putts_per_round() {
    // return 0 if all_holes_played_total.to_f.zero?
    // (number_of_putts_taken.to_f / all_holes_played_total * 18).round(2)
    let allHolePlayed = 0;
    if (!isEmpty(this.strokes)) {
      allHolePlayed = _.uniq(this.all_rounds_strokes.map((m) => m.hole_played_id)).length;
    }
    if (allHolePlayed == 0) {
      return 0;
    }
    const nbPutts = this.number_of_putts_taken();
    const pPR = (nbPutts / allHolePlayed) * 18;
    if (isNaN(pPR)) {
      return 0;
    }
    return +pPR.toFixed(2);
  }

  //
  // Total number of long tee holes
  //
  number_of_holes() {
    // return 0 if strokes.blank?
    // holes_played_total
    if (isEmpty(this.strokes)) {
      return 0;
    }
    return this.total_hole_played(this.holes_played);
  }

  //
  // The number of shots/strokes where the ball starts in a bunker
  // and is equal to 30 yards or less away from the pin/flag.
  //
  number_of_sand_saved_opportunities() {
    return this.sand_saved_opportunities();
  }

  //
  // This is the number of actual sand saves the golfer had for
  // the given set of strokes.
  //
  number_of_sand_saved() {
    return this.sand_saves().length;
  }

  //
  // Calculate the sand saved percentage
  //
  sand_saved_percentage() {
    const nbSSOp = this.sand_saved_opportunities().length;
    const nbSS = this.sand_saves().length;
    this.loggerCR.log({ nbSS, nbSSOp });
    if (nbSSOp == 0 && nbSS == 0) {
      return null;
    }
    if (nbSSOp == 0 || nbSS == 0) {
      return 0;
    }
    //           return 0 if number_of_sand_saved_opportunities.zero? ||
    //           number_of_sand_saved.zero?
    // number_of_sand_saved / number_of_sand_saved_opportunities * 100

    //           return 0 if number_of_sand_saved_opportunities.zero? ||
    //           number_of_sand_saved.zero?
    // number_of_sand_saved / number_of_sand_saved_opportunities * 100
    return (nbSS / nbSSOp) * 100;
  }

  //
  // The number of shots/strokes where the ball starts in a bunker
  // and is equal to 30 yards or less away from the pin/flag.
  //
  // ## Returns
  //
  //   An Array of HolePlayed Objects
  //
  sand_saved_opportunities() {
    // return [] if strokes.blank?
    // @sand_saved_opportunities ||= strokes.select do |stroke|
    //   stroke.sand_saved_opportunity
    // end
    if (isEmpty(this.strokes)) {
      return [];
    }
    const strokes = this.strokes.filter((stroke) => stroke.sand_saved_opportunity) || [];
    return strokes;
  }

  //
  // This is the number of actual sand saves the golfer had for
  // the given set of strokes.
  //
  // ## Returns
  //
  //   An Array of HolePlayed Objects
  //
  sand_saves() {
    if (isEmpty(this.strokes)) {
      return [];
    }
    const strokes = this.strokes.filter((stroke) => stroke.sand_saved) || [];
    return strokes;
    // return [] if strokes.blank?
    // @sand_saves ||= strokes.select { |stroke| stroke.sand_saved }
  }

  //
  // get shot distance percentile. defaults to 75th percentile
  //
  calculate_percentile(quadrant = 75) {
    if (isEmpty(this.strokes)) {
      return 0;
    }
    const lsShotDistance = this.strokes
      .map((stroke) => stroke.shot_distance)
      .flat()
      .sort((a, b) => +a - +b);

    const distances: any = this.percentile(quadrant, lsShotDistance);

    return distances;
  }

  percentile(p, values) {
    if (!values || (values && values.length == 0)) {
      return null;
    }

    values = values.sort((a, b) => a - b);
    if (p == 100) {
      return values[values.length - 1];
    }
    const rank = (p / 100) * (values.length - 1);
    const rankPart = rank.toString().split('.');
    const intPart = +rankPart[0];
    const fractionalPart = rank - intPart;
    if (fractionalPart != 0) {
      const lower = values[intPart];
      const upper = values[intPart + 1];
      return fractionalPart * (upper - lower) + lower;
    }
    return values[intPart];
  }
  // # # # # # # # # # #
  // Classic round
  // # # # # # # # # # #
  //
  //
  // ## Returns
  //
  //   Percent total_hole_have fw_hit / total_hole_played par >= 4
  //
  // par all 3 fw nil
  fairways_in_regulation_classic() {
    // percent = classic_round_fairway_stats_percent('fw_hit')
    // Rails.logger.info "fairways_in_regulation_classic percent: #{percent}"
    // return percent.nil? ? 0 : percent
    const percent = this.classic_round_fairway_stats_percent('fw_hit');
    this.loggerCR.log(`FAIRWAYS_IN_REGULATION_CLASSIC PERCENT: ${percent}`);
    return percent == null ? 0 : percent;
  }

  //
  // A Green in Regulation is when you hit the green in two strokes less than Par for that hole.
  //
  // ## Returns
  //
  //   Percent hole par - score >= 2 / total_hole_played
  //
  greens_in_regulation_classic() {
    // gr_missed_left = classic_round_green_stats_percent('gr_missed_left')
    // gr_missed_left = gr_missed_left.nil? ? 0 : gr_missed_left
    // gr_missed_right = classic_round_green_stats_percent('gr_missed_right')
    // gr_missed_right = gr_missed_right.nil? ? 0 : gr_missed_right
    // gr_missed_long = classic_round_green_stats_percent('gr_missed_long')
    // gr_missed_long = gr_missed_long.nil? ? 0 : gr_missed_long
    // gr_missed_short = classic_round_green_stats_percent('gr_missed_short')
    // gr_missed_short = gr_missed_short.nil? ? 0 : gr_missed_short
    // gr_hit = classic_round_green_stats_percent('gr_hit')
    // gr_hit = gr_hit.nil? ? 0 : gr_hit
    // Rails.logger.info "gr_hit percent: #{gr_hit}"
    // return nil if gr_missed_left == 0 && gr_missed_right == 0 && gr_hit == 0  && gr_missed_long == 0  && gr_missed_short == 0
    // return 100.to_f - gr_missed_left - gr_missed_right - gr_missed_long - gr_missed_short
    let gr_missed_left = this.classic_round_green_stats_percent('gr_missed_left');
    gr_missed_left = gr_missed_left == null ? 0 : gr_missed_left;
    let gr_missed_right = this.classic_round_green_stats_percent('gr_missed_right');
    gr_missed_right = gr_missed_right == null ? 0 : gr_missed_right;

    let gr_missed_long = this.classic_round_green_stats_percent('gr_missed_long');
    gr_missed_long = gr_missed_long == null ? 0 : gr_missed_long;
    let gr_missed_short = this.classic_round_green_stats_percent('gr_missed_short');
    gr_missed_short = gr_missed_short == null ? 0 : gr_missed_short;

    let gr_hit = this.classic_round_green_stats_percent('gr_hit');
    gr_hit = gr_hit == null ? 0 : gr_hit;

    if (gr_missed_left == 0 && gr_missed_right == 0 && gr_hit == 0 && gr_missed_long == 0 && gr_missed_short == 0) {
      return null;
    }

    return 100 - gr_missed_left - gr_missed_right - gr_missed_long - gr_missed_short;
  }

  //
  // Calculate the total number of putts per round classic
  //
  // Putts/Rounds
  //
  number_of_putts_per_round_classic() {
    // return 0 if holes_played.blank?
    // putts_number = holes_played.to_a.sum{|hole| hole.putts_number}
    // return 0 if total_hole_played(holes_played) == 0
    // putts_number.to_f / total_hole_played(holes_played).to_f * 18 rescue 0
    if (isEmpty(this.holes_played)) {
      return 0;
    }
    const totalPutts = this.holes_played.reduce((total, hole) => total + hole.putts_number, 0);
    // const putts_number = holes_played.to_a.sum{|hole| hole.putts_number}
    // return 0 if total_hole_played(holes_played) == 0
    const totalHolePlayed = this.total_hole_played(this.holes_played);
    try {
      return (totalPutts / totalHolePlayed) * 18;
    } catch (error) {
      return 0;
    }
  }

  //
  // How many holes had a single putt round classic
  // returns a percentage
  //
  putting_one_putt_classic() {
    // return 0 if holes_played.blank?
    // total_putting_one = holes_played.to_a.count{|hole| hole.putts_number.to_i == 1}
    // return 0 if total_hole_played(holes_played) == 0
    // total_putting_one.to_f / total_hole_played(holes_played).to_f * 100
    this.loggerCR.log(`PUTTING_ONE_PUTT_CLASSIC`);
    if (isEmpty(this.holes_played)) {
      return 0;
    }
    const total_putting_one = this.holes_played.filter((hole) => +hole.putts_number == 1).length;
    const totalHolePlayed = this.total_hole_played(this.holes_played);
    if (totalHolePlayed == 0) {
      return 0;
    }
    const percent = (total_putting_one / totalHolePlayed) * 100;
    if (isNaN(percent)) {
      return 0;
    }
    return +percent.toFixed(2);
  }

  sand_saved_percentage_classic() {
    this.loggerCR.log(`SAND_SAVED_PERCENTAGE_CLASSIC`);
    // return 0 if holes_played.blank?
    // total_bunker_hit = holes_played.count {|hole| hole.bunker_hit == true}
    // total_sand_save = holes_played.count {|hole| hole.bunker_hit == true && hole.score.to_i <= hole.par.to_i && hole.score.to_i > 0}
    // Rails.logger.info "total_bunker_hit: #{total_bunker_hit} total_sand_save: #{total_sand_save}"
    // return nil if total_bunker_hit == 0
    // return ((total_sand_save.to_f / total_bunker_hit.to_f) * 100).round(2) rescue 0
    // return 0 if holes_played.blank?
    this.loggerCR.debug(`HOLES_PLAYED`);
    // this.loggerCR.debug(this.holes_played);
    if (isEmpty(this.holes_played)) {
      return 0;
    }
    const total_bunker_hit = this.holes_played.filter((hole) => hole.bunker_hit == true).length;
    const total_sand_save = this.holes_played.filter(
      (hole) => hole.bunker_hit == true && hole.score <= hole.par && hole.score > 0
    ).length;
    this.loggerCR.debug(`TOTAL_BUNKER_HIT: ${total_bunker_hit} TOTAL_SAND_SAVE: ${total_sand_save}`);
    if (total_bunker_hit == 0) {
      return null;
    }
    try {
      const percent = (total_sand_save / total_bunker_hit) * 100;
      if (isNaN(percent)) {
        return 0;
      }
      return +percent.toFixed(2);
    } catch (error) {
      return 0;
    }
  }

  getSandAndBunkerHit() {
    if (isEmpty(this.holes_played)) {
      return {};
    }
    const total_bunker_hit = this.holes_played.filter((hole) => hole.bunker_hit == true).length;
    const total_sand_save = this.holes_played.filter(
      (hole) => hole.bunker_hit == true && hole.score <= hole.par && hole.score > 0
    ).length;
    return {
      sand: total_sand_save || 0,
      bunkerHit: total_bunker_hit || 0,
    };
  }

  // calculate percent fairway stats: fw_missed_left, fw_missed_right, fw_hit
  classic_round_fairway_stats_percent(type) {
    this.loggerCR.log(`CLASSIC_ROUND_FAIRWAY_STATS_PERCENT`);
    this.loggerCR.log(type);

    if (isEmpty(this.holes_played)) {
      return 0;
    }
    const total_hole_par_3 = this.holes_played.filter((hole) => +hole.par == 3 && +hole.score > 0).length;
    const total_hole_valid = this.total_hole_played(this.holes_played) - total_hole_par_3;
    const total_fw_stats_type = this.holes_played.filter((hole) => hole.fw_stats == type).length;
    if (total_hole_valid == 0) {
      return null;
    }
    try {
      const percent = (total_fw_stats_type / total_hole_valid) * 100;
      this.loggerCR.debug`${type}: ${total_fw_stats_type} TOTAL_HOLE_VALID: ${total_hole_valid}`;
      if (percent == 0) {
        return 0;
      }
      return percent == 0 || isNaN(percent) ? null : +percent.toFixed(2);
    } catch (error) {
      this.loggerCR.error(`CLASSIC_ROUND_FAIRWAY_STATS_PERCENT ERROR`);
      this.loggerCR.error(error.message);
      return null;
    }
  }
  classic_round_fairway_stats_total() {
    if (isEmpty(this.holes_played)) {
      return 0;
    }
    const holesFW = this.holes_played.filter((hole) => hole.fw_stats != null && hole.fw_stats != '');
    if (holesFW.length > 0) {
      const total_fairway_hit =
        holesFW.filter((hole) => +hole.par >= 4 && +hole.score > 0 && hole.fw_stats == 'fw_hit')?.length || null;
      const total_fairway_hit_opportunities = holesFW.length;

      return {
        classic_driving: +((total_fairway_hit / total_fairway_hit_opportunities) * 100).toFixed(0),
        total_fairway_hit,
        total_fairway_hit_opportunities,
      };
    }
    return {
      classic_driving: null,
      total_fairway_hit: null,
      total_fairway_hit_opportunities: null,
    };
  }

  is_fairway_nil() {
    // return true if holes_played.blank?
    // total_hole_par_3 = holes_played.count{|hole| hole.par.to_i == 3 && hole.score.to_i > 0}
    // total_hole_valid = total_hole_played(holes_played) - total_hole_par_3
    // return true if total_hole_valid == 0
    // return false

    this.loggerCR.log(`IS_FAIRWAY_NIL`);
    if (isEmpty(this.holes_played)) {
      return true;
    }
    const total_hole_par_3 = this.holes_played.filter((hole) => +hole.par == 3 && +hole.score > 0).length;
    const total_hole_valid = this.total_hole_played(this.holes_played) - total_hole_par_3;
    return total_hole_valid == 0 ? true : false;
  }

  // # calculate percent green stats: gr_missed_long, gr_missed_short, gr_missed_left, gr_missed_right, gr_hit
  classic_round_green_stats_percent(type) {
    // return 0 if holes_played.blank?
    // total_green_stats_type = holes_played.count {|hole| hole.gr_stats == type}
    // percent = ((total_green_stats_type.to_f / total_hole_played(holes_played).to_f) * 100) rescue 0
    // Rails.logger.info "#{type}: #{total_green_stats_type} total_hole_played: #{total_hole_played(holes_played)}"
    // return percent == 0 || percent.nan? ? nil : percent.round(2)
    this.loggerCR.log(`CLASSIC_ROUND_GREEN_STATS_PERCENT`);
    this.loggerCR.log({ type });
    if (isEmpty(this.holes_played)) {
      return 0;
    }
    const total_green_stats_type = this.holes_played.filter((hole) => hole.gr_stats == type).length;
    let percent = 0;
    const totalHolesPlayed = this.total_hole_played(this.holes_played);
    try {
      percent = (total_green_stats_type / totalHolesPlayed) * 100;
    } catch (error) {
      this.loggerCR.error(error.message);
      percent = 0;
    }
    if (percent == 0 || isNaN(percent)) {
      return null;
    }
    this.loggerCR.log(
      `CLASSIC_ROUND_GREEN_STATS_PERCENT ${type}: ${total_green_stats_type} TOTAL_HOLE_PLAYED: ${totalHolesPlayed}`
    );
    return +percent.toFixed(2);

    // return percent == 0 || percent.nan? ? nil : percent.round(2)
  }

  // # get total hole score > 0
  total_hole_played(holes_played) {
    this.loggerCR.log(`TOTAL_HOLE_PLAYED`);
    const holes = holes_played.filter((hole) => hole.score > 0);
    return holes.length;
    // return holes_played.count {|hole| hole.score.to_i > 0}
  }
  holes_played_basic_total() {
    this.loggerCR.log(`HOLES_PLAYED_BASIC_TOTAL`);
    console.log(this.holes_played);
    const holes = this.holes_played.filter((hole_played) => hole_played.score > 0) || [];
    return holes.length;
  }
  // alias_method :number_of_holes_played_basic, :holes_played_basic_total

  holes_played_multiplayer_total() {
    this.loggerCR.log(`HOLES_PLAYED_MULTIPLAYER_TOTAL`);
    console.log(this.holes_played);
    // holes_played.select { |hole_played| hole_played.score(Round::ROUND_MODE_MULTIPLAYER).to_i > 0 }.size.to_f
    const holes = this.holes_played.filter((hole_played) => hole_played.score > 0) || [];
    return holes.length;
  }
}
