//
//   * determine the bearing between 2 points
//   * normalize a bearing
//   * format a bearing
//   *
//
// SmartGolf::Stats::Calculations::Bearing.get_bearing()
//
import { convertArrToLocation } from 'src/utils/utils';
import { GeocoderCalculations } from './geocoder';

export class SmartGolfStatsCalculationsBearing {
  //  #
  //  # Get bearing between 2 locations
  //  #
  //  # start_location: Array - [lat,lng]
  //  # end_location:   Array - [lat,lng]
  //  #
  static get_bearing(start_location: any, end_location: any) {
    const origin = convertArrToLocation(start_location);
    // { lat: start_location[0], lon: start_location[1] };
    const dest = convertArrToLocation(end_location);
    // { lat: end_location[0], lon: end_location[1] };
    return GeocoderCalculations.bearingBetween(origin, dest);
    // return geolib.getRhumbLineBearing(start_location, end_location);
    // ::Geocoder::Calculations.bearing_between(
    //   [start_location[0], start_location[1]], [end_location[0], end_location[1]]
    // )
  }

  // #
  // # normalize a given bearing
  // #
  normalize_bearing(initial_bearing: any, offset_bearing: any) {
    const bearing = parseFloat(offset_bearing) - parseFloat(initial_bearing);
    return this.format_bearing(bearing);
  }

  // #
  // # format the bearing if >360 or <0
  // #
  format_bearing(bearing: any) {
    if (bearing < 0) {
      return 360 + +bearing;
    }
    if (bearing > 360) {
      return +bearing - 360;
    }
    return bearing;
  }

  rotate_west_to_east(initial_bearing: any) {
    const computation = 180 - +initial_bearing + 270;
    return (computation * Math.PI) / 180;
  }
}
