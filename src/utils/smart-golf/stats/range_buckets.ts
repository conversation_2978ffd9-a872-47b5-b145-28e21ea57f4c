export class SmartGolfStatsRangeBuckets {
  //
  // Handle including Class Methods defined in
  // the ClassMethods Module.
  //

  // def self.included(klass)
  //   klass.send :include, InstanceMethods
  // end

  //
  // Instance Methods
  //
  // module InstanceMethods
  //
  // Shots/Strokes that fall into a specific starting
  // distance from the pin.
  //

  // VALID_DISTANCE_BUCKETS.keys.each do |key|
  //   method_name = "shot_bucket_#{key}"
  //   ivar        = "@#{method_name}"

  //   define_method(method_name) do
  //     return [] if strokes.blank?
  //     return instance_variable_get(ivar) if instance_variable_defined?(ivar)
  //     instance_variable_set(ivar, Proximity.new( shots_for_range(key) ))
  //   end

  //   define_method("number_of_shots_for_bucket_#{key}") do
  //     return 0 if self.send(method_name).blank?
  //     self.send(method_name).size.to_f
  //   end
  // end

  //
  // Shots for a given range
  //
  // e.g.
  //
  //   shots_for_range( (100...150) )
  //
  shots_for_range(range_key) {
    console.log(range_key);

    // return [] if strokes.blank?
    // range = VALID_DISTANCE_BUCKETS[range_key.to_sym]
    // strokes.select do |stroke|
    //   distance_in_yards = stroke.starting_distance_to_pin * METERS_TO_YARDS
    //   distance_in_yards >= range.min && distance_in_yards <= range.max
    // end
  }
}
