//
// Populates new array's of specific types of strokes
import { Logger } from '@nestjs/common';
import _, { isEmpty } from 'lodash';
import { StrokesPlayedService } from 'src/strokes-played/strokes-played.service';
import { includeStr } from 'src/utils/utils';
import { LIE_CONDITIONS, Stats } from '../stats';
import { SmartGolfStatsCalculationsClassicSg } from './calculations/classic_sg';

//
export class SmartGolfStatsCollections extends SmartGolfStatsCalculationsClassicSg {
  private readonly loggerCollection = new Logger(SmartGolfStatsCollections.name);
  //
  // Rounds
  //
  // Collection of all the unique rounds played.
  //
  // ## Returns
  //
  //   An array of Round objects.
  //
  strokes: any;
  strokePlayedService: StrokesPlayedService;
  all_rounds_strokes: any;
  constructor(strokes, strokePlayedService?: StrokesPlayedService, userSkill?: any, all_rounds_strokes?: any) {
    super(strokes, userSkill, strokePlayedService, all_rounds_strokes);
    this.strokes = strokes;
    if (strokePlayedService) {
      this.strokePlayedService = strokePlayedService;
    }
    this.all_rounds_strokes = all_rounds_strokes ? all_rounds_strokes : strokes;
  }
  // all_rounds_played(all_strokes) {
  //   // return [] if all_strokes.blank?
  //   // @all_rounds_played ||= all_strokes.flat_map { |m| m.round }.uniq
  //   if (isEmpty(all_strokes)) {
  //     return [];
  //   }
  //   return _.uniq(all_strokes.map((m) => m.round_id));
  // }

  //
  // Holes Played
  //
  // Collection of all the holes played, for all the rounds played.
  //
  // Unlike the subclasses of this class, this accounts for
  // all holes and not just the holes for a specific shot/stroke
  // type.
  //
  // ## Returns
  //
  //   An array of HolePlayed IDs
  //
  // all_holes_played(all_strokes) {
  //   // return [] if all_strokes.empty?
  //   // @all_holes_played ||= all_strokes.flat_map { |m| m.hole_played_id }.uniq
  //   if (isEmpty(all_strokes)) {
  //     return [];
  //   }
  //   return _.uniq(all_strokes.map((m) => m.hole_played_id));
  // }

  //
  // LongTee
  //
  // ## About
  //
  // Tee Shots on Holes that are either Par 4 or Par 5.
  // Tee Shots are the very first shots only.
  //
  // ## Returns
  //
  //   An Array of StrokeStat Objects
  //
  long_tee_shots(strokes) {
    // return [] if strokes.blank?
    // @long_tee_shots ||= strokes.select do |stroke|
    //   stroke.long_tee
    // end
    if (isEmpty(strokes)) {
      return [];
    }
    return strokes.filter((stroke) => stroke.long_tee == true);
  }

  first_long_tee_shots(strokes) {
    // return [] if strokes.blank?
    // @first_long_tee_shots ||= strokes.select do |stroke|
    //   stroke.long_tee && !stroke.starting_as_penalty_shot && stroke.stroke_ordinal == 1
    // end
    if (isEmpty(strokes)) {
      return [];
    }
    return strokes.filter((stroke) => {
      console.log(stroke.id);
      const isValid = stroke.long_tee && !stroke.starting_as_penalty_shot && stroke.stroke_ordinal == 1;
      console.log(isValid);
      return isValid;
    });
  }

  //
  // LongTee Holes
  //
  // ## Returns
  //
  //   An array if HolePlayed ID's
  //
  long_tee_holes(strokes) {
    // return [] if strokes.blank?
    // @long_tee_holes ||= strokes.select do |stroke|
    //   stroke.holes_par >= 4
    // end.uniq { |stroke| stroke.hole_played_id }
    if (isEmpty(strokes)) {
      return [];
    }
    return _.uniq(strokes.filter((stroke) => stroke.holes_par >= 4)?.map((stroke) => stroke.hole_played_id));
  }

  //
  // Long Tee Shots for a given range
  //
  // e.g.
  //
  //   long_tee_shots_for_range( (250...300) )
  //
  long_tee_shots_for_range(range, strokes) {
    // return [] if long_tee_shots.blank?
    // long_tee_shots.select do |stroke|
    //   range.cover?( (stroke.starting_distance_to_pin * METERS_TO_YARDS) )
    // end
    const shots = this.long_tee_shots(strokes);
    if (isEmpty(shots)) {
      return [];
    }
    const longTeeShots = shots.filter((shot) =>
      range.includes(+(shot.starting_distance_to_pin * Stats.METERS_TO_YARDS).toFixed(0))
    );
    return longTeeShots;
  }

  //
  // Short
  //
  // ## About
  //
  //   * Can start from any lie condition except for on the Green
  //   * Starting distance to pin must be < 100 yds
  //   * Ending distance to pin must be <= 30 yds
  //   * Cannot be a Recovery shot
  //   * Cannot be a Penalty shot
  //   * Strokes that fall within the above specific bucket range. (e.g. 25-50)
  //
  // ## Returns
  //
  //   An Array of HolePlayed Objects
  //
  // short_tee_shots(strokes) {
  //   //     return [] if strokes.blank?
  //   //     @short_tee_shots ||= strokes.select do |stroke|
  //   // !stroke.starting_as_penalty_shot    &&
  //   //       !stroke.starting_as_recovery_shot   &&
  //   //       stroke.starting_lie !~ /green/i     &&
  //   // !stroke.starting_distance_to_pin.nil? &&
  //   //       stroke.starting_distance_to_pin <= ONE_HUNDRED_YARDS
  //   //     end
  //   //     @short_tee_shots.map { |stroke| !stroke.short_tee ? stroke.update(short_tee: true) : stroke }
  //   //     @short_tee_shots
  //   if (!strokes) {
  //     return [];
  //   }
  //   let shots = strokes.filter((stroke) => {
  //     return (
  //       !stroke.starting_as_penalty_shot &&
  //       !stroke.starting_as_recovery_shot &&
  //       stroke.starting_lie?.toLowerCase() == 'green' &&
  //       stroke.starting_distance_to_pin != null &&
  //       stroke.starting_distance_to_pin * Stats.METERS_TO_YARDS <= Stats.ONE_HUNDRED_YARDS
  //     );
  //   });
  //   if (shots) {
  //     shots = shots.map((shot) => {
  //       if (!shot.short_tee) {
  //         this.strokePlayedService.updateStroke(shot.id, { short_tee: true });
  //         shot.short_tee = true;
  //       }
  //       return shot;
  //     });
  //     // @short_tee_shots.map { |stroke| !stroke.short_tee ? stroke.update(short_tee: true) : stroke }
  //   }
  //   return shots;
  // }

  //
  // Short Tee Shots for a given range
  //
  // e.g.
  //
  //   short_tee_shots_for_range( (0...25) )
  //
  short_tee_shots_for_range(range) {
    console.log(range);

    // return [] if short_tee_shots.blank?
    // short_tee_shots.select do |stroke|
    //   range.cover?( (stroke.starting_distance_to_pin * METERS_TO_YARDS) )
    // end
  }

  //
  // Putting
  //
  // Shots/Stroke starting on the green.
  //
  // ## Returns
  //
  //   An Array of StrokeStats Objects
  //
  putting_shots_for_club(club_id) {
    if (isEmpty(this.strokes)) {
      return [];
    }
    // return [] if strokes.blank?
    // @putting_shots_for_club ||= strokes.select do |stroke|
    //   stroke.starting_lie =~ /green/i && stroke.club_id == club_id
    // end
    return this.strokes.filter((stroke) => includeStr(stroke.starting_lie, 'green') && stroke.club_id == club_id);
  }

  //
  // Putting Shots for a given range
  //
  // e.g.
  //
  //   putting_shots_for_range( (0...3) )
  //
  putting_shots_for_range(range) {
    console.log(range);

    // return [] if putting_shots.blank?
    // putting_shots.select do |stroke|
    //   range.cover?( (stroke.starting_distance_to_pin * METERS_TO_YARDS) )
    // end
  }

  //
  // An array of StrokeStat objects that end in the hole
  //
  shots_holed() {
    // return [] if strokes.blank?
    // strokes.select { |stroke| stroke.ends_in_hole }
    if (isEmpty(this.strokes)) {
      return [];
    }
    return this.strokes.filter((stroke) => stroke.ends_in_hole);
  }

  //
  // Short Tee & Approach Shots
  //
  // Used primarily for Coaching Admin Phase 2 work and Classic Stats.
  //
  // ## Returns
  //
  //   An Array of HolePlayed Objects
  //
  short_tee_and_approach_shots() {
    // return [] if strokes.blank?
    // @short_tee_and_approach_shots ||= strokes.select do |stroke|
    //   stroke.approach || stroke.short_tee
    // end
  }

  //
  // Shots that land on either the Green or Fairway
  //
  long_tees_landing_on_fairway() {
    // return [] if long_tee_shots.empty?
    // long_tee_shots.select do |stroke|
    //   stroke.ending_lie =~ /green/i || stroke.ending_lie =~ /fairway/i
    // end
  }

  //
  // Shots/Strokes that are on a hole with a specific par. Default
  // par is set to 3.
  //
  // ## Returns
  //
  //   An Array of HolePlayed Objects
  //
  holes_for_par(par = 3) {
    this.loggerCollection.log(`HOLES_FOR_PAR`);
    if (isEmpty(this.strokes) || !(3 <= par && par <= 5)) {
      return [];
    }
    const sForPar = this.strokes_for_par(par);
    if (!isEmpty(sForPar)) {
      const group = _.groupBy(sForPar, 'hole_played_id');
      return Object.keys(group);
    }
    return [];
    // return [] if strokes.blank? || !par.between?(3,5)
    // strokes_for_par(par).group_by do |stroke|
    //   stroke.hole_played
    // end
  }
  holes_on_par(par) {
    return this.holes_for_par(par);
  }
  // alias_method :holes_on_par, :holes_for_par

  //
  // Shots/Strokes that are on a hole with a specific par. Default
  // par is set to 3.
  //
  // ## Returns
  //
  //   An Array of StrokeStat Objects
  //
  strokes_for_par(par = 3) {
    if (!this.strokes || isEmpty(this.strokes) || !(3 <= par && par <= 5)) {
      return [];
    }
    return this.strokes.filter((stroke) => +stroke.holes_par == +par);
    // return [] if strokes.blank? || !par.between?(3,5)
    // strokes.select do |stroke|
    //   stroke.holes_par.to_i == par.to_i
    // end
  }

  // alias_method :strokes_on_par, :strokes_for_par

  //
  // Shots/Strokes that landed on the fairway
  //
  // ## Returns
  //
  //   Array of StrokeStat Objects
  //
  landing_on_fairway() {
    // strokes.select { |stroke| stroke.ending_lie =~ /fairway/i }
    return this.strokes.filter((stroke) => stroke.ending_lie?.toLowerCase().includes('fairway'));
  }
  landing_in_fairway() {
    return this.landing_on_fairway();
  }
  // alias_method :landing_in_fairway, :landing_on_fairway

  //
  // Shots/Strokes that landed on the green
  //
  // ## Returns
  //
  //   Array of StrokeStat Objects
  //
  landing_on_green() {
    // strokes.select { |stroke| stroke.ending_lie =~ /green/i }
    return this.strokes.filter((stroke) => stroke.ending_lie?.toLowerCase().includes('green'));
  }
  landing_in_green() {
    return this.landing_on_green();
  }
  // alias_method :landing_in_green, :landing_on_green

  //
  // Shots/Strokes that did NOT land on the green
  //
  // ## Returns
  //
  //   Array of StrokeStat Objects
  //
  not_landing_on_green() {
    // strokes.select { |stroke| stroke.ending_lie !~ /green/i }
    return this.strokes.filter((stroke) => !stroke.ending_lie?.toLowerCase().includes('green'));
  }
  not_landing_in_green() {
    this.not_landing_on_green();
  }
  // alias_method :not_landing_in_green, :not_landing_on_green

  //
  // Shots/Strokes that landed in the bunker
  //
  // ## Returns
  //
  //   Array of StrokeStat Objects
  //
  landing_on_bunker() {
    // strokes.select { |stroke| stroke.ending_lie =~ /bunker/i }
    return this.strokes.filter((stroke) => !stroke.ending_lie?.toLowerCase().includes('bunker'));
  }
  landing_in_bunker() {
    return this.landing_in_bunker();
  }
  // alias_method :landing_in_bunker, :landing_on_bunker

  //
  // Shots/Strokes that landed in the rough
  //
  // ## Returns
  //
  //   Array of StrokeStat Objects
  //
  landing_on_rough() {
    // strokes.select { |stroke| stroke.ending_lie =~ /hole boundary/i }
    return this.strokes.filter((stroke) => !stroke.ending_lie?.toLowerCase().includes('hole boundary'));
  }
  landing_in_rough() {
    return this.landing_in_rough();
  }
  // alias_method :landing_in_rough, :landing_on_rough

  //
  // Shots/Strokes starting on the green
  //
  // ## Returns
  //
  //   Array of StrokeStat Objects
  //
  starting_on_green() {
    // strokes.select { |stroke| stroke.starting_lie =~ /green/i }
    return this.strokes.filter((stroke) => stroke.starting_lie?.toLowerCase().includes('green'));
  }
  starting_in_green() {
    return this.starting_on_green();
  }
  // alias_method :starting_in_green, :starting_on_green

  //
  // Check to see if the stroke/shot landed on a specific
  // lie condition.
  //
  // ## Returns
  //
  //   Array of StrokeStat Objects
  //
  landing_in(lie_condition) {
    // return [] if strokes.blank? || lie_condition.blank?
    // strokes.select do |stroke|
    //   stroke.ending_lie =~ /#{format_lie_condition(lie_condition)}/i
    // end
    if (!this.strokes || !lie_condition) {
      return [];
    }
    return this.strokes.filter((stroke) => {
      const condition = this.format_lie_condition(lie_condition);
      return stroke.ending_lie?.toLowerCase().includes(condition);
    });
    // strokes.select do |stroke|
    //   stroke.ending_lie =~ /#{format_lie_condition(lie_condition)}/i
    // end
  }
  // alias_method :landed_on, :landing_in

  //
  // Check to see if the stroke/shot is starting on a specific
  // lie condition.
  //
  // ## Returns
  //
  //   Array of StrokeStat Objects
  //
  starting_in(lie_condition) {
    if (isEmpty(this.strokes) || !lie_condition) {
      return [];
    }
    return this.strokes.filter((stroke) => {
      const condition = this.format_lie_condition(lie_condition);
      return stroke.starting_lie?.toLowerCase().includes(condition);
    });
    // return [] if strokes.blank? || lie_condition.blank?
    // strokes.select do |stroke|
    //   stroke.starting_lie =~ /#{format_lie_condition(lie_condition)}/i
    // end
  }
  started_on(lie_condition) {
    return this.starting_in(lie_condition);
  }
  // alias_method :started_on, :starting_in

  //
  // Format lie Condition
  //
  // Rough is stored as Hole Boundary, so allow the interface to
  // use rough or hole boundary.
  //
  // ## Returns
  //
  //   String
  //
  format_lie_condition(lie_condition) {
    if (!LIE_CONDITIONS.map((c) => c.toLowerCase()).includes(lie_condition)) {
      return 'hole boundary';
    }
    if (lie_condition.toLowerCase().includes('rough')) {
      return 'hole boundary';
    }
    return lie_condition;
    // return "hole boundary" unless LIE_CONDITIONS.include?(lie_condition)
    // return "hole boundary" if lie_condition.to_s =~ /rough/i
    // lie_condition
  }

  //
  // All strokes that used a driver
  //
  all_driver_clubs() {
    // return [] if strokes.blank?
    // @all_driver_clubs ||= strokes.select do |stroke|
    //   stroke.club_id && stroke.club.club_type =~ /driver/i
    // end
    if (isEmpty(this.strokes)) {
      return [];
    }
    return this.strokes.filter((stroke) => {
      return stroke.club_id && stroke.club_type?.toLowerCase() == 'driver';
    });
  }

  //
  // All strokes that used a 3-fw club
  //
  all_three_wood_clubs() {
    if (isEmpty(this.strokes)) {
      return [];
    }
    // @all_three_wood_clubs ||= strokes.select do |stroke|
    //   stroke.club_id && (stroke.club.club_type =~ /3-fw/i || stroke.club.club_type =~ /3fw/i)
    // end
    return this.strokes.filter((stroke) => {
      return stroke.club_id && ['3-fw', '3fw'].includes(stroke.club_type.toLowerCase());
    });
  }

  //
  // All strokes that used any club other than a driver and 3-fw club
  //
  all_other_clubs() {
    if (isEmpty(this.strokes)) {
      return [];
    }
    // return [] if strokes.blank?
    // @all_other_clubs ||= strokes.select do |stroke|
    //   stroke.club_id &&
    //     ( stroke.club.club_type !~ /3-fw/i && stroke.club.club_type !~ /3fw/i  && stroke.club.club_type !~ /driver/i )
    // end
    const strokes = this.strokes.filter((stroke) => {
      return stroke.club_id && !['3-fw', '3fw', 'driver'].includes(stroke.club_type.toLowerCase());
    });

    return strokes;
  }

  // alias_method :number_of_all_rounds_played, :all_rounds_played_total

  //
  // Total number of shots/strokes that are holed
  //
  number_of_shots_holed() {
    // return 0 if shots_holed.empty?
    // shots_holed.size.to_f
    const shotsHoled = this.shots_holed();
    return shotsHoled.length;
  }

  //
  // Percentage of shots that were holed
  //
  percentage_of_shots_holed() {
    // return 0 if number_of_shots_holed.zero? || strokes_total.zero?
    // number_of_shots_holed / strokes_total * 100
    const nbOfShot = this.number_of_shots_holed();
    const strokesTotal = this.strokes_total();
    if (nbOfShot == 0 || strokesTotal == 0) {
      return 0;
    }
    return (nbOfShot / strokesTotal) * 100;
  }

  //
  // Number of strokes that are on a hole with a specific par. Par
  // defaults to 3.
  //
  strokes_for_par_total(par = 3) {
    this.loggerCollection.debug(`STROKES_FOR_PAR_TOTAL`);
    this.loggerCollection.debug({ par });
    // return 0 if strokes.empty?
    // strokes_for_par(par).size.to_f
    if (isEmpty(this.strokes)) {
      return 0;
    }
    return this.strokes_for_par(par).length;
  }
  number_of_strokes_for_par(par = 3) {
    this.loggerCollection.debug(`NUMBER_OF_STROKES_FOR_PAR`);
    return this.strokes_for_par_total(par);
  }
  // alias_method :number_of_strokes_for_par, :strokes_for_par_total

  //
  // Number of holes for a specific par. Par defaults to 3.
  //
  holes_for_par_total(par = 3) {
    if (isEmpty(this.strokes)) {
      return 0;
    }
    const holes = this.holes_for_par(par);
    return holes.length;
    // return 0 if strokes.empty?
    // holes_for_par(par).size.to_f
  }
  number_of_holes_for_par(par = 3) {
    return this.holes_for_par_total(par);
  }
  // alias_method :number_of_holes_for_par, :holes_for_par_total

  //
  // Number of Fairways that were hit.
  //
  fairways_hit_total() {
    return 0;
    // return 0 if landing_on_fairway.empty?
    // landing_on_fairway.size.to_f
  }
  // alias_method :number_of_fairways_hit, :fairways_hit_total

  //
  // Number of Bunkers that were hit.
  //
  bunkers_hit_total() {
    return 0;
    // return 0 if landing_on_bunker.empty?
    // landing_on_bunker.size.to_f
  }
  // alias_method :number_of_bunkers_hit, :bunkers_hit_total

  //
  // Number of Roughs that were hit.
  //
  roughs_hit_total() {
    return 0;
    // return 0 if landing_on_rough.empty?
    // landing_on_rough.size.to_f
  }
  // alias_method :number_of_roughs_hit, :roughs_hit_total

  //
  // Number of Putts taken
  //
  number_of_putts() {
    return 0;
    // return 0 if starting_on_green.empty?
    // starting_on_green.size.to_f
    // const startOnGreen = this.starting_on_green(strokes);
    // if (isEmpty(startOnGreen)) {
    //   return 0;
    // }
    // return startOnGreen.length;
  }

  // alias_method :number_of_putts_taken,  :number_of_putts
  // alias_method :number_of_putts_hit,    :number_of_putts

  //
  // Average Score for all shots/strokes
  //
  // calculation:
  //
  //   (Total Score / Number of Holes Played) * 18
  //
  average_score() {
    return 0;
    // return 0 if holes_played_total.zero?
    // total_score / holes_played_total * 18
  }

  average_score_basic() {
    return 0;
    // return 0 if holes_played.size.zero?
    // holes_played_basic_total = holes_played.select { |hole_played| hole_played.score(Round::ROUND_MODE_BASIC).to_i > 0 }.size.to_f
    // holes_score ||= holes_played.sum {|hole| hole.score(Round::ROUND_MODE_BASIC).to_i}
    // holes_score / holes_played_basic_total * 18
  }

  //
  // Average Score for holes that are a specific par.
  // Defaults to 3.
  //
  average_score_for_par(par = 3) {
    console.log(par);
    return 0;
    // return 0 if number_of_holes_for_par(par).zero?
    // number_of_strokes_for_par(par) / number_of_holes_for_par(par)
  }

  // alias_method :avg_score_for_par, :average_score_for_par

  average_score_basic_for_par(par = 3) {
    console.log(par);
    return 0;
    // return 0 if number_of_strokes_basic_for_par(par).zero?
    // return 0 if number_of_holes_basic_for_par(par).zero?
    // number_of_strokes_basic_for_par(par) / number_of_holes_basic_for_par(par)
  }

  // alias_method :avg_holes_for_par, :number_of_holes_for_par

  number_of_holes_basic_for_par(par = 3) {
    if (isEmpty(this.holes_played)) {
      return 0;
    }
    return this.holes_basic_for_par(par).length;
  }

  number_of_holes_multiplayer_for_par(par = 3) {
    if (isEmpty(this.holes_played)) {
      return 0;
    }
    // return 0 if holes_played.size.zero?
    // holes_multiplayer_for_par(par).size.to_f
    return this.holes_multiplayer_for_par(par).length;
  }

  //
  // Number of strokes that are on a hole with a specific par. Par
  // defaults to 3.
  //

  number_of_strokes_basic_for_par(par = 3) {
    if (isEmpty(this.holes_played)) {
      return 0;
    }
    // return 0 if holes_played.size.zero?
    return this.strokes_basic_for_par(par);
  }

  number_of_strokes_multiplayer_for_par(par = 3) {
    if (isEmpty(this.holes_played)) {
      return 0;
    }
    return this.strokes_multiplayer_for_par(par);
  }

  //
  // Shots/Strokes that are on a hole with a specific par. Default
  // par is set to 3.
  //
  // ## Returns
  //
  //   An Array of HolePlayed Objects
  //

  holes_basic_for_par(par = 3) {
    return this.holes_played.filter((hole_played) => +hole_played.score > 0 && +hole_played.par == +par);
    // holes_played.select { |hole_played| hole_played.score(Round::ROUND_MODE_BASIC).to_i > 0 && hole_played.par.to_i == par.to_i }
  }

  holes_multiplayer_for_par(par = 3) {
    return this.holes_played.filter((hole_played) => +hole_played.score > 0 && +hole_played.par == +par);
  }

  strokes_basic_for_par(par = 3) {
    // holes_played.select { |hole_played| hole_played.par.to_i == par.to_i }.sum {|hole_played| hole_played.score(Round::ROUND_MODE_BASIC).to_i}
    return this.holes_played
      .filter((hole_played) => +hole_played.par == +par)
      ?.reduce((total, hole) => total + +hole.score, 0);
  }

  strokes_multiplayer_for_par(par = 3) {
    // holes_played.select { |hole_played| hole_played.par.to_i == par.to_i }.sum {|hole_played| hole_played.score(Round::ROUND_MODE_MULTIPLAYER).to_i}
    return this.holes_played
      .filter((hole_played) => +hole_played.par == +par)
      ?.reduce((total, hole) => total + +hole.score, 0);
  }
}
