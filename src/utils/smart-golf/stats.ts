import { HolesPlayedService } from 'src/holes-played/holes-played.service';
import { RoundService } from 'src/rounds/rounds.service';
import { StrokesPlayedService } from 'src/strokes-played/strokes-played.service';
import { CalculateStrokeStats } from './stats/calculate_stroke_stats';

// require 'descriptive_statistics'
export enum Stats {
  YARDS_TO_METERS = 0.9144,
  YARDS_TO_FEET = 3,
  FEET_TO_METERS = 0.3048,
  FEET_TO_YARDS = 0.333333,
  METERS_TO_YARDS = 1.09361,
  METERS_TO_FEET = 3.28084,
  MILES_TO_FEET = 5280,
  MILES_TO_YARDS = 1760,
  MILES_TO_METERS = 1609.34,
  ONE_HUNDRED_YARDS = 91.44,
  THIRTY_YARDS = 27.432,
}
export const LIE_CONDITIONS = ['Hole Boundary', 'Tee', 'Green', 'Bunker', 'Hazard', 'Fairway'];

// require_relative 'stats/exporters/csv'

// require_relative 'stats/stroke_state'
// require_relative 'stats/shot_type'
// require_relative 'stats/sand_saves'
// require_relative 'stats/collections'
// require_relative 'stats/shot_groups'

// require_relative 'stats/formatters/point'

// require_relative 'stats/calculations/bearing'
// require_relative 'stats/calculations/fairway_centerline'
// require_relative 'stats/calculations/average_score'
// require_relative 'stats/calculations/fairways_in_regulation'
// require_relative 'stats/calculations/greens_in_regulation'
// require_relative 'stats/calculations/greens'
// require_relative 'stats/calculations/strokes_gained'
// require_relative 'stats/calculations/sums'
// require_relative 'stats/calculations/regulations'
// require_relative 'stats/calculations/classic_sg'
// require_relative 'stats/calculations/build_green_directions'

// require_relative 'stats/types/all'
// require_relative 'stats/types/base'
// require_relative 'stats/types/proximity'
// require_relative 'stats/types/overall'
// require_relative 'stats/types/long_tee'
// require_relative 'stats/types/approach'
// require_relative 'stats/types/short_tee'
// require_relative 'stats/types/putting'
// require_relative 'stats/types/driver_club'
// require_relative 'stats/types/other_clubs'
// require_relative 'stats/types/three_wood_club'
// require_relative 'stats/types/user'

// require_relative 'stats/calculate_stroke_stats'

export class SmartGolfStats {
  calculate(
    shots: any,
    all_rounds_strokes?: any,
    holes_played?: any,
    holePlayerService?: HolesPlayedService,
    roundService?: RoundService,
    strokePlayedService?: StrokesPlayedService,
    userSkill?: any
  ): CalculateStrokeStats {
    const strokeStats = new CalculateStrokeStats(
      shots,
      all_rounds_strokes,
      holes_played,
      holePlayerService,
      roundService,
      strokePlayedService,
      userSkill
    );
    // strokeStats.initialize(shots);
    return strokeStats;
  }
}
