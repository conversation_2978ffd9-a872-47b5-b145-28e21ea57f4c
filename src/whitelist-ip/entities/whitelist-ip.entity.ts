import { Column, CreateDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

@Entity('whitelist_ips')
@Index(['ip_address'])
@Index(['partner_name'])
@Index(['ip_address', 'partner_name'])
export class WhitelistIp {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 255 })
  partner_name: string;

  @Column({ type: 'varchar', length: 45 })
  ip_address: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
