import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreateWhitelistIpDto } from './dto/create-whitelist-ip.dto';
import { WhitelistIp } from './entities/whitelist-ip.entity';

@Injectable()
export class WhitelistIpService {
  private readonly logger = new Logger(WhitelistIpService.name);

  constructor(
    @InjectRepository(WhitelistIp)
    private readonly whitelistIpRepository: Repository<WhitelistIp>
  ) {}

  async checkIpAndPartner(ipAddress: string, partnerName: string): Promise<boolean> {
    try {
      if (!ipAddress || !partnerName) {
        return false;
      }
      const whitelistEntry = await this.whitelistIpRepository.findOne({
        where: {
          ip_address: ipAddress,
          partner_name: partnerName,
        },
      });

      return !!whitelistEntry;
    } catch (error) {
      this.logger.error(`Error checking IP whitelist: ${error.message}`);
      return false;
    }
  }

  async create(createWhitelistIpDto: CreateWhitelistIpDto): Promise<WhitelistIp> {
    const existingEntry = await this.whitelistIpRepository.findOne({
      where: {
        partner_name: createWhitelistIpDto.partner_name,
        ip_address: createWhitelistIpDto.ip_address,
      },
    });

    if (existingEntry) {
      return existingEntry;
    }
    const whitelistIp = this.whitelistIpRepository.create(createWhitelistIpDto);
    return await this.whitelistIpRepository.save(whitelistIp);
  }

  async findAll(): Promise<WhitelistIp[]> {
    return await this.whitelistIpRepository.find({
      order: { created_at: 'DESC' },
    });
  }

  async findByPartner(partnerName: string): Promise<WhitelistIp[]> {
    return await this.whitelistIpRepository.find({
      where: { partner_name: partnerName },
      order: { created_at: 'DESC' },
    });
  }

  async remove(id: number): Promise<void> {
    await this.whitelistIpRepository.delete(id);
  }
}
