import { Body, Controller, Delete, Get, HttpCode, HttpStatus, Param, Post, Req, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuthGuard } from '../guards/auth.guard';
import { CheckWhitelistIpDto } from './dto/check-whitelist-ip.dto';
import { CreateWhitelistIpDto } from './dto/create-whitelist-ip.dto';
import { WhitelistIpService } from './whitelist-ip.service';

@ApiTags('Whitelist IP')
@Controller('whitelist-ip')
export class WhitelistIpController {
  constructor(private readonly whitelistIpService: WhitelistIpService) {}

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Post('check')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Check if <PERSON> and partner are whitelisted' })
  @ApiResponse({ status: 200, description: 'IP check result' })
  async checkIp(@Body() checkDto: CheckWhitelistIpDto, @Req() req: any) {
    const ipAddress = req.headers['X-Forwarded-For']?.split(',')[0]?.trim() || null;
    const isWhitelisted = await this.whitelistIpService.checkIpAndPartner(ipAddress, checkDto.partner_name);

    return {
      ip_address: ipAddress,
      partner_name: checkDto.partner_name,
      is_whitelisted: isWhitelisted,
    };
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Post()
  @ApiOperation({ summary: 'Add IP to whitelist' })
  async create(@Body() createWhitelistIpDto: CreateWhitelistIpDto) {
    return await this.whitelistIpService.create(createWhitelistIpDto);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get()
  @ApiOperation({ summary: 'Get all whitelisted IPs' })
  async findAll() {
    return await this.whitelistIpService.findAll();
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('partner/:partnerName')
  @ApiOperation({ summary: 'Get whitelisted IPs by partner' })
  async findByPartner(@Param('partnerName') partnerName: string) {
    return await this.whitelistIpService.findByPartner(partnerName);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Delete(':id')
  @ApiOperation({ summary: 'Remove IP from whitelist' })
  async remove(@Param('id') id: number) {
    await this.whitelistIpService.remove(id);
    return { message: 'IP removed from whitelist successfully' };
  }
}
