import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SharedModule } from '../shared/shared.module';
import { WhitelistIp } from './entities/whitelist-ip.entity';
import { WhitelistIpController } from './whitelist-ip.controller';
import { WhitelistIpService } from './whitelist-ip.service';

@Module({
  imports: [SharedModule, TypeOrmModule.forFeature([WhitelistIp])],
  controllers: [WhitelistIpController],
  providers: [WhitelistIpService],
  exports: [WhitelistIpService],
})
export class WhitelistIpModule {}
