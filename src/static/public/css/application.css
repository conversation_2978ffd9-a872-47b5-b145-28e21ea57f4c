@font-face {
  font-family: 'Clubhaus';
  src: url(/assets/clubhaus.eot);
  src: url(/assets/clubhaus.eot?#iefix) format("embedded-opentype"), url(/assets/fonts/clubhaus.woff2) format("woff2"), url(/assets/fonts/clubhaus.woff) format("woff"), url(/assets/fonts/clubhaus.ttf) format("truetype"), url(/assets/fonts/clubhaus.svg#clubhaus) format("svg");
  font-weight: normal;
  font-style: normal
}

@font-face {
  font-family: 'Clubhaus Oblique';
  src: url(/assets/fonts/clubhaus-oblique.eot);
  src: url(/assets/fonts/clubhaus-oblique.eot?#iefix) format("embedded-opentype"), url(/assets/fonts/clubhaus-oblique.woff2) format("woff2"), url(/assets/fonts/clubhaus-oblique.woff) format("woff"), url(/clubhaus-oblique.ttf) format("truetype"), url(/assets/fonts/clubhaus-oblique.svg#clubhaus) format("svg");
  font-weight: normal;
  font-style: normal
}

/*!
  Ionicons, v2.0.1
  Created by <PERSON> for the Ionic Framework, http://ionicons.com/
  https://twitter.com/benjsperry  https://twitter.com/ionicframework
  MIT License: https://github.com/driftyco/ionicons

  Android-style icons originally built by Google’s
  Material Design Icons: https://github.com/google/material-design-icons
  used under CC BY http://creativecommons.org/licenses/by/4.0/
  Modified icons to fit ionicon’s grid from original.
*/
@font-face {
  font-family: "Ionicons";
  src: url(/assets/fonts/ionicons.eot?v=2.0.1);
  src: url(/assets/fonts/ionicons.eot?v=2.0.1#iefix) format("embedded-opentype"), url(/assets/fonts/ionicons.ttf?v=2.0.1) format("truetype"), url(/assets/fonts/ionicons.woff?v=2.0.1) format("woff"), url(/assets/ionicons.svg?v=2.0.1#Ionicons) format("svg");
  font-weight: normal;
  font-style: normal
}

.ion,
.ionicons,
.ion-alert:before,
.ion-alert-circled:before,
.ion-android-add:before,
.ion-android-add-circle:before,
.ion-android-alarm-clock:before,
.ion-android-alert:before,
.ion-android-apps:before,
.ion-android-archive:before,
.ion-android-arrow-back:before,
.ion-android-arrow-down:before,
.ion-android-arrow-dropdown:before,
.ion-android-arrow-dropdown-circle:before,
.ion-android-arrow-dropleft:before,
.ion-android-arrow-dropleft-circle:before,
.ion-android-arrow-dropright:before,
.ion-android-arrow-dropright-circle:before,
.ion-android-arrow-dropup:before,
.ion-android-arrow-dropup-circle:before,
.ion-android-arrow-forward:before,
.ion-android-arrow-up:before,
.ion-android-attach:before,
.ion-android-bar:before,
.ion-android-bicycle:before,
.ion-android-boat:before,
.ion-android-bookmark:before,
.ion-android-bulb:before,
.ion-android-bus:before,
.ion-android-calendar:before,
.ion-android-call:before,
.ion-android-camera:before,
.ion-android-cancel:before,
.ion-android-car:before,
.ion-android-cart:before,
.ion-android-chat:before,
.ion-android-checkbox:before,
.ion-android-checkbox-blank:before,
.ion-android-checkbox-outline:before,
.ion-android-checkbox-outline-blank:before,
.ion-android-checkmark-circle:before,
.ion-android-clipboard:before,
.ion-android-close:before,
.ion-android-cloud:before,
.ion-android-cloud-circle:before,
.ion-android-cloud-done:before,
.ion-android-cloud-outline:before,
.ion-android-color-palette:before,
.ion-android-compass:before,
.ion-android-contact:before,
.ion-android-contacts:before,
.ion-android-contract:before,
.ion-android-create:before,
.ion-android-delete:before,
.ion-android-desktop:before,
.ion-android-document:before,
.ion-android-done:before,
.ion-android-done-all:before,
.ion-android-download:before,
.ion-android-drafts:before,
.ion-android-exit:before,
.ion-android-expand:before,
.ion-android-favorite:before,
.ion-android-favorite-outline:before,
.ion-android-film:before,
.ion-android-folder:before,
.ion-android-folder-open:before,
.ion-android-funnel:before,
.ion-android-globe:before,
.ion-android-hand:before,
.ion-android-hangout:before,
.ion-android-happy:before,
.ion-android-home:before,
.ion-android-image:before,
.ion-android-laptop:before,
.ion-android-list:before,
.ion-android-locate:before,
.ion-android-lock:before,
.ion-android-mail:before,
.ion-android-map:before,
.ion-android-menu:before,
.ion-android-microphone:before,
.ion-android-microphone-off:before,
.ion-android-more-horizontal:before,
.ion-android-more-vertical:before,
.ion-android-navigate:before,
.ion-android-notifications:before,
.ion-android-notifications-none:before,
.ion-android-notifications-off:before,
.ion-android-open:before,
.ion-android-options:before,
.ion-android-people:before,
.ion-android-person:before,
.ion-android-person-add:before,
.ion-android-phone-landscape:before,
.ion-android-phone-portrait:before,
.ion-android-pin:before,
.ion-android-plane:before,
.ion-android-playstore:before,
.ion-android-print:before,
.ion-android-radio-button-off:before,
.ion-android-radio-button-on:before,
.ion-android-refresh:before,
.ion-android-remove:before,
.ion-android-remove-circle:before,
.ion-android-restaurant:before,
.ion-android-sad:before,
.ion-android-search:before,
.ion-android-send:before,
.ion-android-settings:before,
.ion-android-share:before,
.ion-android-share-alt:before,
.ion-android-star:before,
.ion-android-star-half:before,
.ion-android-star-outline:before,
.ion-android-stopwatch:before,
.ion-android-subway:before,
.ion-android-sunny:before,
.ion-android-sync:before,
.ion-android-textsms:before,
.ion-android-time:before,
.ion-android-train:before,
.ion-android-unlock:before,
.ion-android-upload:before,
.ion-android-volume-down:before,
.ion-android-volume-mute:before,
.ion-android-volume-off:before,
.ion-android-volume-up:before,
.ion-android-walk:before,
.ion-android-warning:before,
.ion-android-watch:before,
.ion-android-wifi:before,
.ion-aperture:before,
.ion-archive:before,
.ion-arrow-down-a:before,
.ion-arrow-down-b:before,
.ion-arrow-down-c:before,
.ion-arrow-expand:before,
.ion-arrow-graph-down-left:before,
.ion-arrow-graph-down-right:before,
.ion-arrow-graph-up-left:before,
.ion-arrow-graph-up-right:before,
.ion-arrow-left-a:before,
.ion-arrow-left-b:before,
.ion-arrow-left-c:before,
.ion-arrow-move:before,
.ion-arrow-resize:before,
.ion-arrow-return-left:before,
.ion-arrow-return-right:before,
.ion-arrow-right-a:before,
.ion-arrow-right-b:before,
.ion-arrow-right-c:before,
.ion-arrow-shrink:before,
.ion-arrow-swap:before,
.ion-arrow-up-a:before,
.ion-arrow-up-b:before,
.ion-arrow-up-c:before,
.ion-asterisk:before,
.ion-at:before,
.ion-backspace:before,
.ion-backspace-outline:before,
.ion-bag:before,
.ion-battery-charging:before,
.ion-battery-empty:before,
.ion-battery-full:before,
.ion-battery-half:before,
.ion-battery-low:before,
.ion-beaker:before,
.ion-beer:before,
.ion-bluetooth:before,
.ion-bonfire:before,
.ion-bookmark:before,
.ion-bowtie:before,
.ion-briefcase:before,
.ion-bug:before,
.ion-calculator:before,
.ion-calendar:before,
.ion-camera:before,
.ion-card:before,
.ion-cash:before,
.ion-chatbox:before,
.ion-chatbox-working:before,
.ion-chatboxes:before,
.ion-chatbubble:before,
.ion-chatbubble-working:before,
.ion-chatbubbles:before,
.ion-checkmark:before,
.ion-checkmark-circled:before,
.ion-checkmark-round:before,
.ion-chevron-down:before,
.ion-chevron-left:before,
.ion-chevron-right:before,
.ion-chevron-up:before,
.ion-clipboard:before,
.ion-clock:before,
.ion-close:before,
.ion-close-circled:before,
.ion-close-round:before,
.ion-closed-captioning:before,
.ion-cloud:before,
.ion-code:before,
.ion-code-download:before,
.ion-code-working:before,
.ion-coffee:before,
.ion-compass:before,
.ion-compose:before,
.ion-connection-bars:before,
.ion-contrast:before,
.ion-crop:before,
.ion-cube:before,
.ion-disc:before,
.ion-document:before,
.ion-document-text:before,
.ion-drag:before,
.ion-earth:before,
.ion-easel:before,
.ion-edit:before,
.ion-egg:before,
.ion-eject:before,
.ion-email:before,
.ion-email-unread:before,
.ion-erlenmeyer-flask:before,
.ion-erlenmeyer-flask-bubbles:before,
.ion-eye:before,
.ion-eye-disabled:before,
.ion-female:before,
.ion-filing:before,
.ion-film-marker:before,
.ion-fireball:before,
.ion-flag:before,
.ion-flame:before,
.ion-flash:before,
.ion-flash-off:before,
.ion-folder:before,
.ion-fork:before,
.ion-fork-repo:before,
.ion-forward:before,
.ion-funnel:before,
.ion-gear-a:before,
.ion-gear-b:before,
.ion-grid:before,
.ion-hammer:before,
.ion-happy:before,
.ion-happy-outline:before,
.ion-headphone:before,
.ion-heart:before,
.ion-heart-broken:before,
.ion-help:before,
.ion-help-buoy:before,
.ion-help-circled:before,
.ion-home:before,
.ion-icecream:before,
.ion-image:before,
.ion-images:before,
.ion-information:before,
.ion-information-circled:before,
.ion-ionic:before,
.ion-ios-alarm:before,
.ion-ios-alarm-outline:before,
.ion-ios-albums:before,
.ion-ios-albums-outline:before,
.ion-ios-americanfootball:before,
.ion-ios-americanfootball-outline:before,
.ion-ios-analytics:before,
.ion-ios-analytics-outline:before,
.ion-ios-arrow-back:before,
.ion-ios-arrow-down:before,
.ion-ios-arrow-forward:before,
.ion-ios-arrow-left:before,
.ion-ios-arrow-right:before,
.ion-ios-arrow-thin-down:before,
.ion-ios-arrow-thin-left:before,
.ion-ios-arrow-thin-right:before,
.ion-ios-arrow-thin-up:before,
.ion-ios-arrow-up:before,
.ion-ios-at:before,
.ion-ios-at-outline:before,
.ion-ios-barcode:before,
.ion-ios-barcode-outline:before,
.ion-ios-baseball:before,
.ion-ios-baseball-outline:before,
.ion-ios-basketball:before,
.ion-ios-basketball-outline:before,
.ion-ios-bell:before,
.ion-ios-bell-outline:before,
.ion-ios-body:before,
.ion-ios-body-outline:before,
.ion-ios-bolt:before,
.ion-ios-bolt-outline:before,
.ion-ios-book:before,
.ion-ios-book-outline:before,
.ion-ios-bookmarks:before,
.ion-ios-bookmarks-outline:before,
.ion-ios-box:before,
.ion-ios-box-outline:before,
.ion-ios-briefcase:before,
.ion-ios-briefcase-outline:before,
.ion-ios-browsers:before,
.ion-ios-browsers-outline:before,
.ion-ios-calculator:before,
.ion-ios-calculator-outline:before,
.ion-ios-calendar:before,
.ion-ios-calendar-outline:before,
.ion-ios-camera:before,
.ion-ios-camera-outline:before,
.ion-ios-cart:before,
.ion-ios-cart-outline:before,
.ion-ios-chatboxes:before,
.ion-ios-chatboxes-outline:before,
.ion-ios-chatbubble:before,
.ion-ios-chatbubble-outline:before,
.ion-ios-checkmark:before,
.ion-ios-checkmark-empty:before,
.ion-ios-checkmark-outline:before,
.ion-ios-circle-filled:before,
.ion-ios-circle-outline:before,
.ion-ios-clock:before,
.ion-ios-clock-outline:before,
.ion-ios-close:before,
.ion-ios-close-empty:before,
.ion-ios-close-outline:before,
.ion-ios-cloud:before,
.ion-ios-cloud-download:before,
.ion-ios-cloud-download-outline:before,
.ion-ios-cloud-outline:before,
.ion-ios-cloud-upload:before,
.ion-ios-cloud-upload-outline:before,
.ion-ios-cloudy:before,
.ion-ios-cloudy-night:before,
.ion-ios-cloudy-night-outline:before,
.ion-ios-cloudy-outline:before,
.ion-ios-cog:before,
.ion-ios-cog-outline:before,
.ion-ios-color-filter:before,
.ion-ios-color-filter-outline:before,
.ion-ios-color-wand:before,
.ion-ios-color-wand-outline:before,
.ion-ios-compose:before,
.ion-ios-compose-outline:before,
.ion-ios-contact:before,
.ion-ios-contact-outline:before,
.ion-ios-copy:before,
.ion-ios-copy-outline:before,
.ion-ios-crop:before,
.ion-ios-crop-strong:before,
.ion-ios-download:before,
.ion-ios-download-outline:before,
.ion-ios-drag:before,
.ion-ios-email:before,
.ion-ios-email-outline:before,
.ion-ios-eye:before,
.ion-ios-eye-outline:before,
.ion-ios-fastforward:before,
.ion-ios-fastforward-outline:before,
.ion-ios-filing:before,
.ion-ios-filing-outline:before,
.ion-ios-film:before,
.ion-ios-film-outline:before,
.ion-ios-flag:before,
.ion-ios-flag-outline:before,
.ion-ios-flame:before,
.ion-ios-flame-outline:before,
.ion-ios-flask:before,
.ion-ios-flask-outline:before,
.ion-ios-flower:before,
.ion-ios-flower-outline:before,
.ion-ios-folder:before,
.ion-ios-folder-outline:before,
.ion-ios-football:before,
.ion-ios-football-outline:before,
.ion-ios-game-controller-a:before,
.ion-ios-game-controller-a-outline:before,
.ion-ios-game-controller-b:before,
.ion-ios-game-controller-b-outline:before,
.ion-ios-gear:before,
.ion-ios-gear-outline:before,
.ion-ios-glasses:before,
.ion-ios-glasses-outline:before,
.ion-ios-grid-view:before,
.ion-ios-grid-view-outline:before,
.ion-ios-heart:before,
.ion-ios-heart-outline:before,
.ion-ios-help:before,
.ion-ios-help-empty:before,
.ion-ios-help-outline:before,
.ion-ios-home:before,
.ion-ios-home-outline:before,
.ion-ios-infinite:before,
.ion-ios-infinite-outline:before,
.ion-ios-information:before,
.ion-ios-information-empty:before,
.ion-ios-information-outline:before,
.ion-ios-ionic-outline:before,
.ion-ios-keypad:before,
.ion-ios-keypad-outline:before,
.ion-ios-lightbulb:before,
.ion-ios-lightbulb-outline:before,
.ion-ios-list:before,
.ion-ios-list-outline:before,
.ion-ios-location:before,
.ion-ios-location-outline:before,
.ion-ios-locked:before,
.ion-ios-locked-outline:before,
.ion-ios-loop:before,
.ion-ios-loop-strong:before,
.ion-ios-medical:before,
.ion-ios-medical-outline:before,
.ion-ios-medkit:before,
.ion-ios-medkit-outline:before,
.ion-ios-mic:before,
.ion-ios-mic-off:before,
.ion-ios-mic-outline:before,
.ion-ios-minus:before,
.ion-ios-minus-empty:before,
.ion-ios-minus-outline:before,
.ion-ios-monitor:before,
.ion-ios-monitor-outline:before,
.ion-ios-moon:before,
.ion-ios-moon-outline:before,
.ion-ios-more:before,
.ion-ios-more-outline:before,
.ion-ios-musical-note:before,
.ion-ios-musical-notes:before,
.ion-ios-navigate:before,
.ion-ios-navigate-outline:before,
.ion-ios-nutrition:before,
.ion-ios-nutrition-outline:before,
.ion-ios-paper:before,
.ion-ios-paper-outline:before,
.ion-ios-paperplane:before,
.ion-ios-paperplane-outline:before,
.ion-ios-partlysunny:before,
.ion-ios-partlysunny-outline:before,
.ion-ios-pause:before,
.ion-ios-pause-outline:before,
.ion-ios-paw:before,
.ion-ios-paw-outline:before,
.ion-ios-people:before,
.ion-ios-people-outline:before,
.ion-ios-person:before,
.ion-ios-person-outline:before,
.ion-ios-personadd:before,
.ion-ios-personadd-outline:before,
.ion-ios-photos:before,
.ion-ios-photos-outline:before,
.ion-ios-pie:before,
.ion-ios-pie-outline:before,
.ion-ios-pint:before,
.ion-ios-pint-outline:before,
.ion-ios-play:before,
.ion-ios-play-outline:before,
.ion-ios-plus:before,
.ion-ios-plus-empty:before,
.ion-ios-plus-outline:before,
.ion-ios-pricetag:before,
.ion-ios-pricetag-outline:before,
.ion-ios-pricetags:before,
.ion-ios-pricetags-outline:before,
.ion-ios-printer:before,
.ion-ios-printer-outline:before,
.ion-ios-pulse:before,
.ion-ios-pulse-strong:before,
.ion-ios-rainy:before,
.ion-ios-rainy-outline:before,
.ion-ios-recording:before,
.ion-ios-recording-outline:before,
.ion-ios-redo:before,
.ion-ios-redo-outline:before,
.ion-ios-refresh:before,
.ion-ios-refresh-empty:before,
.ion-ios-refresh-outline:before,
.ion-ios-reload:before,
.ion-ios-reverse-camera:before,
.ion-ios-reverse-camera-outline:before,
.ion-ios-rewind:before,
.ion-ios-rewind-outline:before,
.ion-ios-rose:before,
.ion-ios-rose-outline:before,
.ion-ios-search:before,
.ion-ios-search-strong:before,
.ion-ios-settings:before,
.ion-ios-settings-strong:before,
.ion-ios-shuffle:before,
.ion-ios-shuffle-strong:before,
.ion-ios-skipbackward:before,
.ion-ios-skipbackward-outline:before,
.ion-ios-skipforward:before,
.ion-ios-skipforward-outline:before,
.ion-ios-snowy:before,
.ion-ios-speedometer:before,
.ion-ios-speedometer-outline:before,
.ion-ios-star:before,
.ion-ios-star-half:before,
.ion-ios-star-outline:before,
.ion-ios-stopwatch:before,
.ion-ios-stopwatch-outline:before,
.ion-ios-sunny:before,
.ion-ios-sunny-outline:before,
.ion-ios-telephone:before,
.ion-ios-telephone-outline:before,
.ion-ios-tennisball:before,
.ion-ios-tennisball-outline:before,
.ion-ios-thunderstorm:before,
.ion-ios-thunderstorm-outline:before,
.ion-ios-time:before,
.ion-ios-time-outline:before,
.ion-ios-timer:before,
.ion-ios-timer-outline:before,
.ion-ios-toggle:before,
.ion-ios-toggle-outline:before,
.ion-ios-trash:before,
.ion-ios-trash-outline:before,
.ion-ios-undo:before,
.ion-ios-undo-outline:before,
.ion-ios-unlocked:before,
.ion-ios-unlocked-outline:before,
.ion-ios-upload:before,
.ion-ios-upload-outline:before,
.ion-ios-videocam:before,
.ion-ios-videocam-outline:before,
.ion-ios-volume-high:before,
.ion-ios-volume-low:before,
.ion-ios-wineglass:before,
.ion-ios-wineglass-outline:before,
.ion-ios-world:before,
.ion-ios-world-outline:before,
.ion-ipad:before,
.ion-iphone:before,
.ion-ipod:before,
.ion-jet:before,
.ion-key:before,
.ion-knife:before,
.ion-laptop:before,
.ion-leaf:before,
.ion-levels:before,
.ion-lightbulb:before,
.ion-link:before,
.ion-load-a:before,
.ion-load-b:before,
.ion-load-c:before,
.ion-load-d:before,
.ion-location:before,
.ion-lock-combination:before,
.ion-locked:before,
.ion-log-in:before,
.ion-log-out:before,
.ion-loop:before,
.ion-magnet:before,
.ion-male:before,
.ion-man:before,
.ion-map:before,
.ion-medkit:before,
.ion-merge:before,
.ion-mic-a:before,
.ion-mic-b:before,
.ion-mic-c:before,
.ion-minus:before,
.ion-minus-circled:before,
.ion-minus-round:before,
.ion-model-s:before,
.ion-monitor:before,
.ion-more:before,
.ion-mouse:before,
.ion-music-note:before,
.ion-navicon:before,
.ion-navicon-round:before,
.ion-navigate:before,
.ion-network:before,
.ion-no-smoking:before,
.ion-nuclear:before,
.ion-outlet:before,
.ion-paintbrush:before,
.ion-paintbucket:before,
.ion-paper-airplane:before,
.ion-paperclip:before,
.ion-pause:before,
.ion-person:before,
.ion-person-add:before,
.ion-person-stalker:before,
.ion-pie-graph:before,
.ion-pin:before,
.ion-pinpoint:before,
.ion-pizza:before,
.ion-plane:before,
.ion-planet:before,
.ion-play:before,
.ion-playstation:before,
.ion-plus:before,
.ion-plus-circled:before,
.ion-plus-round:before,
.ion-podium:before,
.ion-pound:before,
.ion-power:before,
.ion-pricetag:before,
.ion-pricetags:before,
.ion-printer:before,
.ion-pull-request:before,
.ion-qr-scanner:before,
.ion-quote:before,
.ion-radio-waves:before,
.ion-record:before,
.ion-refresh:before,
.ion-reply:before,
.ion-reply-all:before,
.ion-ribbon-a:before,
.ion-ribbon-b:before,
.ion-sad:before,
.ion-sad-outline:before,
.ion-scissors:before,
.ion-search:before,
.ion-settings:before,
.ion-share:before,
.ion-shuffle:before,
.ion-skip-backward:before,
.ion-skip-forward:before,
.ion-social-android:before,
.ion-social-android-outline:before,
.ion-social-angular:before,
.ion-social-angular-outline:before,
.ion-social-apple:before,
.ion-social-apple-outline:before,
.ion-social-bitcoin:before,
.ion-social-bitcoin-outline:before,
.ion-social-buffer:before,
.ion-social-buffer-outline:before,
.ion-social-chrome:before,
.ion-social-chrome-outline:before,
.ion-social-codepen:before,
.ion-social-codepen-outline:before,
.ion-social-css3:before,
.ion-social-css3-outline:before,
.ion-social-designernews:before,
.ion-social-designernews-outline:before,
.ion-social-dribbble:before,
.ion-social-dribbble-outline:before,
.ion-social-dropbox:before,
.ion-social-dropbox-outline:before,
.ion-social-euro:before,
.ion-social-euro-outline:before,
.ion-social-facebook:before,
.ion-social-facebook-outline:before,
.ion-social-foursquare:before,
.ion-social-foursquare-outline:before,
.ion-social-freebsd-devil:before,
.ion-social-github:before,
.ion-social-github-outline:before,
.ion-social-google:before,
.ion-social-google-outline:before,
.ion-social-googleplus:before,
.ion-social-googleplus-outline:before,
.ion-social-hackernews:before,
.ion-social-hackernews-outline:before,
.ion-social-html5:before,
.ion-social-html5-outline:before,
.ion-social-instagram:before,
.ion-social-instagram-outline:before,
.ion-social-javascript:before,
.ion-social-javascript-outline:before,
.ion-social-linkedin:before,
.ion-social-linkedin-outline:before,
.ion-social-markdown:before,
.ion-social-nodejs:before,
.ion-social-octocat:before,
.ion-social-pinterest:before,
.ion-social-pinterest-outline:before,
.ion-social-python:before,
.ion-social-reddit:before,
.ion-social-reddit-outline:before,
.ion-social-rss:before,
.ion-social-rss-outline:before,
.ion-social-sass:before,
.ion-social-skype:before,
.ion-social-skype-outline:before,
.ion-social-snapchat:before,
.ion-social-snapchat-outline:before,
.ion-social-tumblr:before,
.ion-social-tumblr-outline:before,
.ion-social-tux:before,
.ion-social-twitch:before,
.ion-social-twitch-outline:before,
.ion-social-twitter:before,
.ion-social-twitter-outline:before,
.ion-social-usd:before,
.ion-social-usd-outline:before,
.ion-social-vimeo:before,
.ion-social-vimeo-outline:before,
.ion-social-whatsapp:before,
.ion-social-whatsapp-outline:before,
.ion-social-windows:before,
.ion-social-windows-outline:before,
.ion-social-wordpress:before,
.ion-social-wordpress-outline:before,
.ion-social-yahoo:before,
.ion-social-yahoo-outline:before,
.ion-social-yen:before,
.ion-social-yen-outline:before,
.ion-social-youtube:before,
.ion-social-youtube-outline:before,
.ion-soup-can:before,
.ion-soup-can-outline:before,
.ion-speakerphone:before,
.ion-speedometer:before,
.ion-spoon:before,
.ion-star:before,
.ion-stats-bars:before,
.ion-steam:before,
.ion-stop:before,
.ion-thermometer:before,
.ion-thumbsdown:before,
.ion-thumbsup:before,
.ion-toggle:before,
.ion-toggle-filled:before,
.ion-transgender:before,
.ion-trash-a:before,
.ion-trash-b:before,
.ion-trophy:before,
.ion-tshirt:before,
.ion-tshirt-outline:before,
.ion-umbrella:before,
.ion-university:before,
.ion-unlocked:before,
.ion-upload:before,
.ion-usb:before,
.ion-videocamera:before,
.ion-volume-high:before,
.ion-volume-low:before,
.ion-volume-medium:before,
.ion-volume-mute:before,
.ion-wand:before,
.ion-waterdrop:before,
.ion-wifi:before,
.ion-wineglass:before,
.ion-woman:before,
.ion-wrench:before,
.ion-xbox:before {
  display: inline-block;
  font-family: "Ionicons";
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  text-rendering: auto;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale
}

.ion-alert:before {
  content: ""
}

.ion-alert-circled:before {
  content: ""
}

.ion-android-add:before {
  content: ""
}

.ion-android-add-circle:before {
  content: ""
}

.ion-android-alarm-clock:before {
  content: ""
}

.ion-android-alert:before {
  content: ""
}

.ion-android-apps:before {
  content: ""
}

.ion-android-archive:before {
  content: ""
}

.ion-android-arrow-back:before {
  content: ""
}

.ion-android-arrow-down:before {
  content: ""
}

.ion-android-arrow-dropdown:before {
  content: ""
}

.ion-android-arrow-dropdown-circle:before {
  content: ""
}

.ion-android-arrow-dropleft:before {
  content: ""
}

.ion-android-arrow-dropleft-circle:before {
  content: ""
}

.ion-android-arrow-dropright:before {
  content: ""
}

.ion-android-arrow-dropright-circle:before {
  content: ""
}

.ion-android-arrow-dropup:before {
  content: ""
}

.ion-android-arrow-dropup-circle:before {
  content: ""
}

.ion-android-arrow-forward:before {
  content: ""
}

.ion-android-arrow-up:before {
  content: ""
}

.ion-android-attach:before {
  content: ""
}

.ion-android-bar:before {
  content: ""
}

.ion-android-bicycle:before {
  content: ""
}

.ion-android-boat:before {
  content: ""
}

.ion-android-bookmark:before {
  content: ""
}

.ion-android-bulb:before {
  content: ""
}

.ion-android-bus:before {
  content: ""
}

.ion-android-calendar:before {
  content: ""
}

.ion-android-call:before {
  content: ""
}

.ion-android-camera:before {
  content: ""
}

.ion-android-cancel:before {
  content: ""
}

.ion-android-car:before {
  content: ""
}

.ion-android-cart:before {
  content: ""
}

.ion-android-chat:before {
  content: ""
}

.ion-android-checkbox:before {
  content: ""
}

.ion-android-checkbox-blank:before {
  content: ""
}

.ion-android-checkbox-outline:before {
  content: ""
}

.ion-android-checkbox-outline-blank:before {
  content: ""
}

.ion-android-checkmark-circle:before {
  content: ""
}

.ion-android-clipboard:before {
  content: ""
}

.ion-android-close:before {
  content: ""
}

.ion-android-cloud:before {
  content: ""
}

.ion-android-cloud-circle:before {
  content: ""
}

.ion-android-cloud-done:before {
  content: ""
}

.ion-android-cloud-outline:before {
  content: ""
}

.ion-android-color-palette:before {
  content: ""
}

.ion-android-compass:before {
  content: ""
}

.ion-android-contact:before {
  content: ""
}

.ion-android-contacts:before {
  content: ""
}

.ion-android-contract:before {
  content: ""
}

.ion-android-create:before {
  content: ""
}

.ion-android-delete:before {
  content: ""
}

.ion-android-desktop:before {
  content: ""
}

.ion-android-document:before {
  content: ""
}

.ion-android-done:before {
  content: ""
}

.ion-android-done-all:before {
  content: ""
}

.ion-android-download:before {
  content: ""
}

.ion-android-drafts:before {
  content: ""
}

.ion-android-exit:before {
  content: ""
}

.ion-android-expand:before {
  content: ""
}

.ion-android-favorite:before {
  content: ""
}

.ion-android-favorite-outline:before {
  content: ""
}

.ion-android-film:before {
  content: ""
}

.ion-android-folder:before {
  content: ""
}

.ion-android-folder-open:before {
  content: ""
}

.ion-android-funnel:before {
  content: ""
}

.ion-android-globe:before {
  content: ""
}

.ion-android-hand:before {
  content: ""
}

.ion-android-hangout:before {
  content: ""
}

.ion-android-happy:before {
  content: ""
}

.ion-android-home:before {
  content: ""
}

.ion-android-image:before {
  content: ""
}

.ion-android-laptop:before {
  content: ""
}

.ion-android-list:before {
  content: ""
}

.ion-android-locate:before {
  content: ""
}

.ion-android-lock:before {
  content: ""
}

.ion-android-mail:before {
  content: ""
}

.ion-android-map:before {
  content: ""
}

.ion-android-menu:before {
  content: ""
}

.ion-android-microphone:before {
  content: ""
}

.ion-android-microphone-off:before {
  content: ""
}

.ion-android-more-horizontal:before {
  content: ""
}

.ion-android-more-vertical:before {
  content: ""
}

.ion-android-navigate:before {
  content: ""
}

.ion-android-notifications:before {
  content: ""
}

.ion-android-notifications-none:before {
  content: ""
}

.ion-android-notifications-off:before {
  content: ""
}

.ion-android-open:before {
  content: ""
}

.ion-android-options:before {
  content: ""
}

.ion-android-people:before {
  content: ""
}

.ion-android-person:before {
  content: ""
}

.ion-android-person-add:before {
  content: ""
}

.ion-android-phone-landscape:before {
  content: ""
}

.ion-android-phone-portrait:before {
  content: ""
}

.ion-android-pin:before {
  content: ""
}

.ion-android-plane:before {
  content: ""
}

.ion-android-playstore:before {
  content: ""
}

.ion-android-print:before {
  content: ""
}

.ion-android-radio-button-off:before {
  content: ""
}

.ion-android-radio-button-on:before {
  content: ""
}

.ion-android-refresh:before {
  content: ""
}

.ion-android-remove:before {
  content: ""
}

.ion-android-remove-circle:before {
  content: ""
}

.ion-android-restaurant:before {
  content: ""
}

.ion-android-sad:before {
  content: ""
}

.ion-android-search:before {
  content: ""
}

.ion-android-send:before {
  content: ""
}

.ion-android-settings:before {
  content: ""
}

.ion-android-share:before {
  content: ""
}

.ion-android-share-alt:before {
  content: ""
}

.ion-android-star:before {
  content: ""
}

.ion-android-star-half:before {
  content: ""
}

.ion-android-star-outline:before {
  content: ""
}

.ion-android-stopwatch:before {
  content: ""
}

.ion-android-subway:before {
  content: ""
}

.ion-android-sunny:before {
  content: ""
}

.ion-android-sync:before {
  content: ""
}

.ion-android-textsms:before {
  content: ""
}

.ion-android-time:before {
  content: ""
}

.ion-android-train:before {
  content: ""
}

.ion-android-unlock:before {
  content: ""
}

.ion-android-upload:before {
  content: ""
}

.ion-android-volume-down:before {
  content: ""
}

.ion-android-volume-mute:before {
  content: ""
}

.ion-android-volume-off:before {
  content: ""
}

.ion-android-volume-up:before {
  content: ""
}

.ion-android-walk:before {
  content: ""
}

.ion-android-warning:before {
  content: ""
}

.ion-android-watch:before {
  content: ""
}

.ion-android-wifi:before {
  content: ""
}

.ion-aperture:before {
  content: ""
}

.ion-archive:before {
  content: ""
}

.ion-arrow-down-a:before {
  content: ""
}

.ion-arrow-down-b:before {
  content: ""
}

.ion-arrow-down-c:before {
  content: ""
}

.ion-arrow-expand:before {
  content: ""
}

.ion-arrow-graph-down-left:before {
  content: ""
}

.ion-arrow-graph-down-right:before {
  content: ""
}

.ion-arrow-graph-up-left:before {
  content: ""
}

.ion-arrow-graph-up-right:before {
  content: ""
}

.ion-arrow-left-a:before {
  content: ""
}

.ion-arrow-left-b:before {
  content: ""
}

.ion-arrow-left-c:before {
  content: ""
}

.ion-arrow-move:before {
  content: ""
}

.ion-arrow-resize:before {
  content: ""
}

.ion-arrow-return-left:before {
  content: ""
}

.ion-arrow-return-right:before {
  content: ""
}

.ion-arrow-right-a:before {
  content: ""
}

.ion-arrow-right-b:before {
  content: ""
}

.ion-arrow-right-c:before {
  content: ""
}

.ion-arrow-shrink:before {
  content: ""
}

.ion-arrow-swap:before {
  content: ""
}

.ion-arrow-up-a:before {
  content: ""
}

.ion-arrow-up-b:before {
  content: ""
}

.ion-arrow-up-c:before {
  content: ""
}

.ion-asterisk:before {
  content: ""
}

.ion-at:before {
  content: ""
}

.ion-backspace:before {
  content: ""
}

.ion-backspace-outline:before {
  content: ""
}

.ion-bag:before {
  content: ""
}

.ion-battery-charging:before {
  content: ""
}

.ion-battery-empty:before {
  content: ""
}

.ion-battery-full:before {
  content: ""
}

.ion-battery-half:before {
  content: ""
}

.ion-battery-low:before {
  content: ""
}

.ion-beaker:before {
  content: ""
}

.ion-beer:before {
  content: ""
}

.ion-bluetooth:before {
  content: ""
}

.ion-bonfire:before {
  content: ""
}

.ion-bookmark:before {
  content: ""
}

.ion-bowtie:before {
  content: ""
}

.ion-briefcase:before {
  content: ""
}

.ion-bug:before {
  content: ""
}

.ion-calculator:before {
  content: ""
}

.ion-calendar:before {
  content: ""
}

.ion-camera:before {
  content: ""
}

.ion-card:before {
  content: ""
}

.ion-cash:before {
  content: ""
}

.ion-chatbox:before {
  content: ""
}

.ion-chatbox-working:before {
  content: ""
}

.ion-chatboxes:before {
  content: ""
}

.ion-chatbubble:before {
  content: ""
}

.ion-chatbubble-working:before {
  content: ""
}

.ion-chatbubbles:before {
  content: ""
}

.ion-checkmark:before {
  content: ""
}

.ion-checkmark-circled:before {
  content: ""
}

.ion-checkmark-round:before {
  content: ""
}

.ion-chevron-down:before {
  content: ""
}

.ion-chevron-left:before {
  content: ""
}

.ion-chevron-right:before {
  content: ""
}

.ion-chevron-up:before {
  content: ""
}

.ion-clipboard:before {
  content: ""
}

.ion-clock:before {
  content: ""
}

.ion-close:before {
  content: ""
}

.ion-close-circled:before {
  content: ""
}

.ion-close-round:before {
  content: ""
}

.ion-closed-captioning:before {
  content: ""
}

.ion-cloud:before {
  content: ""
}

.ion-code:before {
  content: ""
}

.ion-code-download:before {
  content: ""
}

.ion-code-working:before {
  content: ""
}

.ion-coffee:before {
  content: ""
}

.ion-compass:before {
  content: ""
}

.ion-compose:before {
  content: ""
}

.ion-connection-bars:before {
  content: ""
}

.ion-contrast:before {
  content: ""
}

.ion-crop:before {
  content: ""
}

.ion-cube:before {
  content: ""
}

.ion-disc:before {
  content: ""
}

.ion-document:before {
  content: ""
}

.ion-document-text:before {
  content: ""
}

.ion-drag:before {
  content: ""
}

.ion-earth:before {
  content: ""
}

.ion-easel:before {
  content: ""
}

.ion-edit:before {
  content: ""
}

.ion-egg:before {
  content: ""
}

.ion-eject:before {
  content: ""
}

.ion-email:before {
  content: ""
}

.ion-email-unread:before {
  content: ""
}

.ion-erlenmeyer-flask:before {
  content: ""
}

.ion-erlenmeyer-flask-bubbles:before {
  content: ""
}

.ion-eye:before {
  content: ""
}

.ion-eye-disabled:before {
  content: ""
}

.ion-female:before {
  content: ""
}

.ion-filing:before {
  content: ""
}

.ion-film-marker:before {
  content: ""
}

.ion-fireball:before {
  content: ""
}

.ion-flag:before {
  content: ""
}

.ion-flame:before {
  content: ""
}

.ion-flash:before {
  content: ""
}

.ion-flash-off:before {
  content: ""
}

.ion-folder:before {
  content: ""
}

.ion-fork:before {
  content: ""
}

.ion-fork-repo:before {
  content: ""
}

.ion-forward:before {
  content: ""
}

.ion-funnel:before {
  content: ""
}

.ion-gear-a:before {
  content: ""
}

.ion-gear-b:before {
  content: ""
}

.ion-grid:before {
  content: ""
}

.ion-hammer:before {
  content: ""
}

.ion-happy:before {
  content: ""
}

.ion-happy-outline:before {
  content: ""
}

.ion-headphone:before {
  content: ""
}

.ion-heart:before {
  content: ""
}

.ion-heart-broken:before {
  content: ""
}

.ion-help:before {
  content: ""
}

.ion-help-buoy:before {
  content: ""
}

.ion-help-circled:before {
  content: ""
}

.ion-home:before {
  content: ""
}

.ion-icecream:before {
  content: ""
}

.ion-image:before {
  content: ""
}

.ion-images:before {
  content: ""
}

.ion-information:before {
  content: ""
}

.ion-information-circled:before {
  content: ""
}

.ion-ionic:before {
  content: ""
}

.ion-ios-alarm:before {
  content: ""
}

.ion-ios-alarm-outline:before {
  content: ""
}

.ion-ios-albums:before {
  content: ""
}

.ion-ios-albums-outline:before {
  content: ""
}

.ion-ios-americanfootball:before {
  content: ""
}

.ion-ios-americanfootball-outline:before {
  content: ""
}

.ion-ios-analytics:before {
  content: ""
}

.ion-ios-analytics-outline:before {
  content: ""
}

.ion-ios-arrow-back:before {
  content: ""
}

.ion-ios-arrow-down:before {
  content: ""
}

.ion-ios-arrow-forward:before {
  content: ""
}

.ion-ios-arrow-left:before {
  content: ""
}

.ion-ios-arrow-right:before {
  content: ""
}

.ion-ios-arrow-thin-down:before {
  content: ""
}

.ion-ios-arrow-thin-left:before {
  content: ""
}

.ion-ios-arrow-thin-right:before {
  content: ""
}

.ion-ios-arrow-thin-up:before {
  content: ""
}

.ion-ios-arrow-up:before {
  content: ""
}

.ion-ios-at:before {
  content: ""
}

.ion-ios-at-outline:before {
  content: ""
}

.ion-ios-barcode:before {
  content: ""
}

.ion-ios-barcode-outline:before {
  content: ""
}

.ion-ios-baseball:before {
  content: ""
}

.ion-ios-baseball-outline:before {
  content: ""
}

.ion-ios-basketball:before {
  content: ""
}

.ion-ios-basketball-outline:before {
  content: ""
}

.ion-ios-bell:before {
  content: ""
}

.ion-ios-bell-outline:before {
  content: ""
}

.ion-ios-body:before {
  content: ""
}

.ion-ios-body-outline:before {
  content: ""
}

.ion-ios-bolt:before {
  content: ""
}

.ion-ios-bolt-outline:before {
  content: ""
}

.ion-ios-book:before {
  content: ""
}

.ion-ios-book-outline:before {
  content: ""
}

.ion-ios-bookmarks:before {
  content: ""
}

.ion-ios-bookmarks-outline:before {
  content: ""
}

.ion-ios-box:before {
  content: ""
}

.ion-ios-box-outline:before {
  content: ""
}

.ion-ios-briefcase:before {
  content: ""
}

.ion-ios-briefcase-outline:before {
  content: ""
}

.ion-ios-browsers:before {
  content: ""
}

.ion-ios-browsers-outline:before {
  content: ""
}

.ion-ios-calculator:before {
  content: ""
}

.ion-ios-calculator-outline:before {
  content: ""
}

.ion-ios-calendar:before {
  content: ""
}

.ion-ios-calendar-outline:before {
  content: ""
}

.ion-ios-camera:before {
  content: ""
}

.ion-ios-camera-outline:before {
  content: ""
}

.ion-ios-cart:before {
  content: ""
}

.ion-ios-cart-outline:before {
  content: ""
}

.ion-ios-chatboxes:before {
  content: ""
}

.ion-ios-chatboxes-outline:before {
  content: ""
}

.ion-ios-chatbubble:before {
  content: ""
}

.ion-ios-chatbubble-outline:before {
  content: ""
}

.ion-ios-checkmark:before {
  content: ""
}

.ion-ios-checkmark-empty:before {
  content: ""
}

.ion-ios-checkmark-outline:before {
  content: ""
}

.ion-ios-circle-filled:before {
  content: ""
}

.ion-ios-circle-outline:before {
  content: ""
}

.ion-ios-clock:before {
  content: ""
}

.ion-ios-clock-outline:before {
  content: ""
}

.ion-ios-close:before {
  content: ""
}

.ion-ios-close-empty:before {
  content: ""
}

.ion-ios-close-outline:before {
  content: ""
}

.ion-ios-cloud:before {
  content: ""
}

.ion-ios-cloud-download:before {
  content: ""
}

.ion-ios-cloud-download-outline:before {
  content: ""
}

.ion-ios-cloud-outline:before {
  content: ""
}

.ion-ios-cloud-upload:before {
  content: ""
}

.ion-ios-cloud-upload-outline:before {
  content: ""
}

.ion-ios-cloudy:before {
  content: ""
}

.ion-ios-cloudy-night:before {
  content: ""
}

.ion-ios-cloudy-night-outline:before {
  content: ""
}

.ion-ios-cloudy-outline:before {
  content: ""
}

.ion-ios-cog:before {
  content: ""
}

.ion-ios-cog-outline:before {
  content: ""
}

.ion-ios-color-filter:before {
  content: ""
}

.ion-ios-color-filter-outline:before {
  content: ""
}

.ion-ios-color-wand:before {
  content: ""
}

.ion-ios-color-wand-outline:before {
  content: ""
}

.ion-ios-compose:before {
  content: ""
}

.ion-ios-compose-outline:before {
  content: ""
}

.ion-ios-contact:before {
  content: ""
}

.ion-ios-contact-outline:before {
  content: ""
}

.ion-ios-copy:before {
  content: ""
}

.ion-ios-copy-outline:before {
  content: ""
}

.ion-ios-crop:before {
  content: ""
}

.ion-ios-crop-strong:before {
  content: ""
}

.ion-ios-download:before {
  content: ""
}

.ion-ios-download-outline:before {
  content: ""
}

.ion-ios-drag:before {
  content: ""
}

.ion-ios-email:before {
  content: ""
}

.ion-ios-email-outline:before {
  content: ""
}

.ion-ios-eye:before {
  content: ""
}

.ion-ios-eye-outline:before {
  content: ""
}

.ion-ios-fastforward:before {
  content: ""
}

.ion-ios-fastforward-outline:before {
  content: ""
}

.ion-ios-filing:before {
  content: ""
}

.ion-ios-filing-outline:before {
  content: ""
}

.ion-ios-film:before {
  content: ""
}

.ion-ios-film-outline:before {
  content: ""
}

.ion-ios-flag:before {
  content: ""
}

.ion-ios-flag-outline:before {
  content: ""
}

.ion-ios-flame:before {
  content: ""
}

.ion-ios-flame-outline:before {
  content: ""
}

.ion-ios-flask:before {
  content: ""
}

.ion-ios-flask-outline:before {
  content: ""
}

.ion-ios-flower:before {
  content: ""
}

.ion-ios-flower-outline:before {
  content: ""
}

.ion-ios-folder:before {
  content: ""
}

.ion-ios-folder-outline:before {
  content: ""
}

.ion-ios-football:before {
  content: ""
}

.ion-ios-football-outline:before {
  content: ""
}

.ion-ios-game-controller-a:before {
  content: ""
}

.ion-ios-game-controller-a-outline:before {
  content: ""
}

.ion-ios-game-controller-b:before {
  content: ""
}

.ion-ios-game-controller-b-outline:before {
  content: ""
}

.ion-ios-gear:before {
  content: ""
}

.ion-ios-gear-outline:before {
  content: ""
}

.ion-ios-glasses:before {
  content: ""
}

.ion-ios-glasses-outline:before {
  content: ""
}

.ion-ios-grid-view:before {
  content: ""
}

.ion-ios-grid-view-outline:before {
  content: ""
}

.ion-ios-heart:before {
  content: ""
}

.ion-ios-heart-outline:before {
  content: ""
}

.ion-ios-help:before {
  content: ""
}

.ion-ios-help-empty:before {
  content: ""
}

.ion-ios-help-outline:before {
  content: ""
}

.ion-ios-home:before {
  content: ""
}

.ion-ios-home-outline:before {
  content: ""
}

.ion-ios-infinite:before {
  content: ""
}

.ion-ios-infinite-outline:before {
  content: ""
}

.ion-ios-information:before {
  content: ""
}

.ion-ios-information-empty:before {
  content: ""
}

.ion-ios-information-outline:before {
  content: ""
}

.ion-ios-ionic-outline:before {
  content: ""
}

.ion-ios-keypad:before {
  content: ""
}

.ion-ios-keypad-outline:before {
  content: ""
}

.ion-ios-lightbulb:before {
  content: ""
}

.ion-ios-lightbulb-outline:before {
  content: ""
}

.ion-ios-list:before {
  content: ""
}

.ion-ios-list-outline:before {
  content: ""
}

.ion-ios-location:before {
  content: ""
}

.ion-ios-location-outline:before {
  content: ""
}

.ion-ios-locked:before {
  content: ""
}

.ion-ios-locked-outline:before {
  content: ""
}

.ion-ios-loop:before {
  content: ""
}

.ion-ios-loop-strong:before {
  content: ""
}

.ion-ios-medical:before {
  content: ""
}

.ion-ios-medical-outline:before {
  content: ""
}

.ion-ios-medkit:before {
  content: ""
}

.ion-ios-medkit-outline:before {
  content: ""
}

.ion-ios-mic:before {
  content: ""
}

.ion-ios-mic-off:before {
  content: ""
}

.ion-ios-mic-outline:before {
  content: ""
}

.ion-ios-minus:before {
  content: ""
}

.ion-ios-minus-empty:before {
  content: ""
}

.ion-ios-minus-outline:before {
  content: ""
}

.ion-ios-monitor:before {
  content: ""
}

.ion-ios-monitor-outline:before {
  content: ""
}

.ion-ios-moon:before {
  content: ""
}

.ion-ios-moon-outline:before {
  content: ""
}

.ion-ios-more:before {
  content: ""
}

.ion-ios-more-outline:before {
  content: ""
}

.ion-ios-musical-note:before {
  content: ""
}

.ion-ios-musical-notes:before {
  content: ""
}

.ion-ios-navigate:before {
  content: ""
}

.ion-ios-navigate-outline:before {
  content: ""
}

.ion-ios-nutrition:before {
  content: ""
}

.ion-ios-nutrition-outline:before {
  content: ""
}

.ion-ios-paper:before {
  content: ""
}

.ion-ios-paper-outline:before {
  content: ""
}

.ion-ios-paperplane:before {
  content: ""
}

.ion-ios-paperplane-outline:before {
  content: ""
}

.ion-ios-partlysunny:before {
  content: ""
}

.ion-ios-partlysunny-outline:before {
  content: ""
}

.ion-ios-pause:before {
  content: ""
}

.ion-ios-pause-outline:before {
  content: ""
}

.ion-ios-paw:before {
  content: ""
}

.ion-ios-paw-outline:before {
  content: ""
}

.ion-ios-people:before {
  content: ""
}

.ion-ios-people-outline:before {
  content: ""
}

.ion-ios-person:before {
  content: ""
}

.ion-ios-person-outline:before {
  content: ""
}

.ion-ios-personadd:before {
  content: ""
}

.ion-ios-personadd-outline:before {
  content: ""
}

.ion-ios-photos:before {
  content: ""
}

.ion-ios-photos-outline:before {
  content: ""
}

.ion-ios-pie:before {
  content: ""
}

.ion-ios-pie-outline:before {
  content: ""
}

.ion-ios-pint:before {
  content: ""
}

.ion-ios-pint-outline:before {
  content: ""
}

.ion-ios-play:before {
  content: ""
}

.ion-ios-play-outline:before {
  content: ""
}

.ion-ios-plus:before {
  content: ""
}

.ion-ios-plus-empty:before {
  content: ""
}

.ion-ios-plus-outline:before {
  content: ""
}

.ion-ios-pricetag:before {
  content: ""
}

.ion-ios-pricetag-outline:before {
  content: ""
}

.ion-ios-pricetags:before {
  content: ""
}

.ion-ios-pricetags-outline:before {
  content: ""
}

.ion-ios-printer:before {
  content: ""
}

.ion-ios-printer-outline:before {
  content: ""
}

.ion-ios-pulse:before {
  content: ""
}

.ion-ios-pulse-strong:before {
  content: ""
}

.ion-ios-rainy:before {
  content: ""
}

.ion-ios-rainy-outline:before {
  content: ""
}

.ion-ios-recording:before {
  content: ""
}

.ion-ios-recording-outline:before {
  content: ""
}

.ion-ios-redo:before {
  content: ""
}

.ion-ios-redo-outline:before {
  content: ""
}

.ion-ios-refresh:before {
  content: ""
}

.ion-ios-refresh-empty:before {
  content: ""
}

.ion-ios-refresh-outline:before {
  content: ""
}

.ion-ios-reload:before {
  content: ""
}

.ion-ios-reverse-camera:before {
  content: ""
}

.ion-ios-reverse-camera-outline:before {
  content: ""
}

.ion-ios-rewind:before {
  content: ""
}

.ion-ios-rewind-outline:before {
  content: ""
}

.ion-ios-rose:before {
  content: ""
}

.ion-ios-rose-outline:before {
  content: ""
}

.ion-ios-search:before {
  content: ""
}

.ion-ios-search-strong:before {
  content: ""
}

.ion-ios-settings:before {
  content: ""
}

.ion-ios-settings-strong:before {
  content: ""
}

.ion-ios-shuffle:before {
  content: ""
}

.ion-ios-shuffle-strong:before {
  content: ""
}

.ion-ios-skipbackward:before {
  content: ""
}

.ion-ios-skipbackward-outline:before {
  content: ""
}

.ion-ios-skipforward:before {
  content: ""
}

.ion-ios-skipforward-outline:before {
  content: ""
}

.ion-ios-snowy:before {
  content: ""
}

.ion-ios-speedometer:before {
  content: ""
}

.ion-ios-speedometer-outline:before {
  content: ""
}

.ion-ios-star:before {
  content: ""
}

.ion-ios-star-half:before {
  content: ""
}

.ion-ios-star-outline:before {
  content: ""
}

.ion-ios-stopwatch:before {
  content: ""
}

.ion-ios-stopwatch-outline:before {
  content: ""
}

.ion-ios-sunny:before {
  content: ""
}

.ion-ios-sunny-outline:before {
  content: ""
}

.ion-ios-telephone:before {
  content: ""
}

.ion-ios-telephone-outline:before {
  content: ""
}

.ion-ios-tennisball:before {
  content: ""
}

.ion-ios-tennisball-outline:before {
  content: ""
}

.ion-ios-thunderstorm:before {
  content: ""
}

.ion-ios-thunderstorm-outline:before {
  content: ""
}

.ion-ios-time:before {
  content: ""
}

.ion-ios-time-outline:before {
  content: ""
}

.ion-ios-timer:before {
  content: ""
}

.ion-ios-timer-outline:before {
  content: ""
}

.ion-ios-toggle:before {
  content: ""
}

.ion-ios-toggle-outline:before {
  content: ""
}

.ion-ios-trash:before {
  content: ""
}

.ion-ios-trash-outline:before {
  content: ""
}

.ion-ios-undo:before {
  content: ""
}

.ion-ios-undo-outline:before {
  content: ""
}

.ion-ios-unlocked:before {
  content: ""
}

.ion-ios-unlocked-outline:before {
  content: ""
}

.ion-ios-upload:before {
  content: ""
}

.ion-ios-upload-outline:before {
  content: ""
}

.ion-ios-videocam:before {
  content: ""
}

.ion-ios-videocam-outline:before {
  content: ""
}

.ion-ios-volume-high:before {
  content: ""
}

.ion-ios-volume-low:before {
  content: ""
}

.ion-ios-wineglass:before {
  content: ""
}

.ion-ios-wineglass-outline:before {
  content: ""
}

.ion-ios-world:before {
  content: ""
}

.ion-ios-world-outline:before {
  content: ""
}

.ion-ipad:before {
  content: ""
}

.ion-iphone:before {
  content: ""
}

.ion-ipod:before {
  content: ""
}

.ion-jet:before {
  content: ""
}

.ion-key:before {
  content: ""
}

.ion-knife:before {
  content: ""
}

.ion-laptop:before {
  content: ""
}

.ion-leaf:before {
  content: ""
}

.ion-levels:before {
  content: ""
}

.ion-lightbulb:before {
  content: ""
}

.ion-link:before {
  content: ""
}

.ion-load-a:before {
  content: ""
}

.ion-load-b:before {
  content: ""
}

.ion-load-c:before {
  content: ""
}

.ion-load-d:before {
  content: ""
}

.ion-location:before {
  content: ""
}

.ion-lock-combination:before {
  content: ""
}

.ion-locked:before {
  content: ""
}

.ion-log-in:before {
  content: ""
}

.ion-log-out:before {
  content: ""
}

.ion-loop:before {
  content: ""
}

.ion-magnet:before {
  content: ""
}

.ion-male:before {
  content: ""
}

.ion-man:before {
  content: ""
}

.ion-map:before {
  content: ""
}

.ion-medkit:before {
  content: ""
}

.ion-merge:before {
  content: ""
}

.ion-mic-a:before {
  content: ""
}

.ion-mic-b:before {
  content: ""
}

.ion-mic-c:before {
  content: ""
}

.ion-minus:before {
  content: ""
}

.ion-minus-circled:before {
  content: ""
}

.ion-minus-round:before {
  content: ""
}

.ion-model-s:before {
  content: ""
}

.ion-monitor:before {
  content: ""
}

.ion-more:before {
  content: ""
}

.ion-mouse:before {
  content: ""
}

.ion-music-note:before {
  content: ""
}

.ion-navicon:before {
  content: ""
}

.ion-navicon-round:before {
  content: ""
}

.ion-navigate:before {
  content: ""
}

.ion-network:before {
  content: ""
}

.ion-no-smoking:before {
  content: ""
}

.ion-nuclear:before {
  content: ""
}

.ion-outlet:before {
  content: ""
}

.ion-paintbrush:before {
  content: ""
}

.ion-paintbucket:before {
  content: ""
}

.ion-paper-airplane:before {
  content: ""
}

.ion-paperclip:before {
  content: ""
}

.ion-pause:before {
  content: ""
}

.ion-person:before {
  content: ""
}

.ion-person-add:before {
  content: ""
}

.ion-person-stalker:before {
  content: ""
}

.ion-pie-graph:before {
  content: ""
}

.ion-pin:before {
  content: ""
}

.ion-pinpoint:before {
  content: ""
}

.ion-pizza:before {
  content: ""
}

.ion-plane:before {
  content: ""
}

.ion-planet:before {
  content: ""
}

.ion-play:before {
  content: ""
}

.ion-playstation:before {
  content: ""
}

.ion-plus:before {
  content: ""
}

.ion-plus-circled:before {
  content: ""
}

.ion-plus-round:before {
  content: ""
}

.ion-podium:before {
  content: ""
}

.ion-pound:before {
  content: ""
}

.ion-power:before {
  content: ""
}

.ion-pricetag:before {
  content: ""
}

.ion-pricetags:before {
  content: ""
}

.ion-printer:before {
  content: ""
}

.ion-pull-request:before {
  content: ""
}

.ion-qr-scanner:before {
  content: ""
}

.ion-quote:before {
  content: ""
}

.ion-radio-waves:before {
  content: ""
}

.ion-record:before {
  content: ""
}

.ion-refresh:before {
  content: ""
}

.ion-reply:before {
  content: ""
}

.ion-reply-all:before {
  content: ""
}

.ion-ribbon-a:before {
  content: ""
}

.ion-ribbon-b:before {
  content: ""
}

.ion-sad:before {
  content: ""
}

.ion-sad-outline:before {
  content: ""
}

.ion-scissors:before {
  content: ""
}

.ion-search:before {
  content: ""
}

.ion-settings:before {
  content: ""
}

.ion-share:before {
  content: ""
}

.ion-shuffle:before {
  content: ""
}

.ion-skip-backward:before {
  content: ""
}

.ion-skip-forward:before {
  content: ""
}

.ion-social-android:before {
  content: ""
}

.ion-social-android-outline:before {
  content: ""
}

.ion-social-angular:before {
  content: ""
}

.ion-social-angular-outline:before {
  content: ""
}

.ion-social-apple:before {
  content: ""
}

.ion-social-apple-outline:before {
  content: ""
}

.ion-social-bitcoin:before {
  content: ""
}

.ion-social-bitcoin-outline:before {
  content: ""
}

.ion-social-buffer:before {
  content: ""
}

.ion-social-buffer-outline:before {
  content: ""
}

.ion-social-chrome:before {
  content: ""
}

.ion-social-chrome-outline:before {
  content: ""
}

.ion-social-codepen:before {
  content: ""
}

.ion-social-codepen-outline:before {
  content: ""
}

.ion-social-css3:before {
  content: ""
}

.ion-social-css3-outline:before {
  content: ""
}

.ion-social-designernews:before {
  content: ""
}

.ion-social-designernews-outline:before {
  content: ""
}

.ion-social-dribbble:before {
  content: ""
}

.ion-social-dribbble-outline:before {
  content: ""
}

.ion-social-dropbox:before {
  content: ""
}

.ion-social-dropbox-outline:before {
  content: ""
}

.ion-social-euro:before {
  content: ""
}

.ion-social-euro-outline:before {
  content: ""
}

.ion-social-facebook:before {
  content: ""
}

.ion-social-facebook-outline:before {
  content: ""
}

.ion-social-foursquare:before {
  content: ""
}

.ion-social-foursquare-outline:before {
  content: ""
}

.ion-social-freebsd-devil:before {
  content: ""
}

.ion-social-github:before {
  content: ""
}

.ion-social-github-outline:before {
  content: ""
}

.ion-social-google:before {
  content: ""
}

.ion-social-google-outline:before {
  content: ""
}

.ion-social-googleplus:before {
  content: ""
}

.ion-social-googleplus-outline:before {
  content: ""
}

.ion-social-hackernews:before {
  content: ""
}

.ion-social-hackernews-outline:before {
  content: ""
}

.ion-social-html5:before {
  content: ""
}

.ion-social-html5-outline:before {
  content: ""
}

.ion-social-instagram:before {
  content: ""
}

.ion-social-instagram-outline:before {
  content: ""
}

.ion-social-javascript:before {
  content: ""
}

.ion-social-javascript-outline:before {
  content: ""
}

.ion-social-linkedin:before {
  content: ""
}

.ion-social-linkedin-outline:before {
  content: ""
}

.ion-social-markdown:before {
  content: ""
}

.ion-social-nodejs:before {
  content: ""
}

.ion-social-octocat:before {
  content: ""
}

.ion-social-pinterest:before {
  content: ""
}

.ion-social-pinterest-outline:before {
  content: ""
}

.ion-social-python:before {
  content: ""
}

.ion-social-reddit:before {
  content: ""
}

.ion-social-reddit-outline:before {
  content: ""
}

.ion-social-rss:before {
  content: ""
}

.ion-social-rss-outline:before {
  content: ""
}

.ion-social-sass:before {
  content: ""
}

.ion-social-skype:before {
  content: ""
}

.ion-social-skype-outline:before {
  content: ""
}

.ion-social-snapchat:before {
  content: ""
}

.ion-social-snapchat-outline:before {
  content: ""
}

.ion-social-tumblr:before {
  content: ""
}

.ion-social-tumblr-outline:before {
  content: ""
}

.ion-social-tux:before {
  content: ""
}

.ion-social-twitch:before {
  content: ""
}

.ion-social-twitch-outline:before {
  content: ""
}

.ion-social-twitter:before {
  content: ""
}

.ion-social-twitter-outline:before {
  content: ""
}

.ion-social-usd:before {
  content: ""
}

.ion-social-usd-outline:before {
  content: ""
}

.ion-social-vimeo:before {
  content: ""
}

.ion-social-vimeo-outline:before {
  content: ""
}

.ion-social-whatsapp:before {
  content: ""
}

.ion-social-whatsapp-outline:before {
  content: ""
}

.ion-social-windows:before {
  content: ""
}

.ion-social-windows-outline:before {
  content: ""
}

.ion-social-wordpress:before {
  content: ""
}

.ion-social-wordpress-outline:before {
  content: ""
}

.ion-social-yahoo:before {
  content: ""
}

.ion-social-yahoo-outline:before {
  content: ""
}

.ion-social-yen:before {
  content: ""
}

.ion-social-yen-outline:before {
  content: ""
}

.ion-social-youtube:before {
  content: ""
}

.ion-social-youtube-outline:before {
  content: ""
}

.ion-soup-can:before {
  content: ""
}

.ion-soup-can-outline:before {
  content: ""
}

.ion-speakerphone:before {
  content: ""
}

.ion-speedometer:before {
  content: ""
}

.ion-spoon:before {
  content: ""
}

.ion-star:before {
  content: ""
}

.ion-stats-bars:before {
  content: ""
}

.ion-steam:before {
  content: ""
}

.ion-stop:before {
  content: ""
}

.ion-thermometer:before {
  content: ""
}

.ion-thumbsdown:before {
  content: ""
}

.ion-thumbsup:before {
  content: ""
}

.ion-toggle:before {
  content: ""
}

.ion-toggle-filled:before {
  content: ""
}

.ion-transgender:before {
  content: ""
}

.ion-trash-a:before {
  content: ""
}

.ion-trash-b:before {
  content: ""
}

.ion-trophy:before {
  content: ""
}

.ion-tshirt:before {
  content: ""
}

.ion-tshirt-outline:before {
  content: ""
}

.ion-umbrella:before {
  content: ""
}

.ion-university:before {
  content: ""
}

.ion-unlocked:before {
  content: ""
}

.ion-upload:before {
  content: ""
}

.ion-usb:before {
  content: ""
}

.ion-videocamera:before {
  content: ""
}

.ion-volume-high:before {
  content: ""
}

.ion-volume-low:before {
  content: ""
}

.ion-volume-medium:before {
  content: ""
}

.ion-volume-mute:before {
  content: ""
}

.ion-wand:before {
  content: ""
}

.ion-waterdrop:before {
  content: ""
}

.ion-wifi:before {
  content: ""
}

.ion-wineglass:before {
  content: ""
}

.ion-woman:before {
  content: ""
}

.ion-wrench:before {
  content: ""
}

.ion-xbox:before {
  content: ""
}

/*!
 *  Font Awesome 4.4.0 by @davegandy - http://fontawesome.io - @fontawesome
 *  License - http://fontawesome.io/license (Font: SIL OFL 1.1, CSS: MIT License)
 */
@font-face {
  font-family: 'FontAwesome';
  src: url("/assets/fontawesome-webfont-d4f5a99224154f2a808e42a441ddc9248ffe78b7a4083684ce159270b30b912a.eot?v=4.4.0");
  src: url("/assets/fontawesome-webfont-d4f5a99224154f2a808e42a441ddc9248ffe78b7a4083684ce159270b30b912a.eot?#iefix&v=4.4.0") format("embedded-opentype"), url("/assets/fontawesome-webfont-3c4a1bb7ce3234407184f0d80cc4dec075e4ad616b44dcc5778e1cfb1bc24019.woff2?v=4.4.0") format("woff2"), url("/assets/fontawesome-webfont-a7c7e4930090e038a280fd61d88f0dc03dad4aeaedbd8c9be3dd9aa4c3b6f8d1.woff?v=4.4.0") format("woff"), url("/assets/fontawesome-webfont-1b7f3de49d68b01f415574ebb82e6110a1d09cda2071ad8451bdb5124131a292.ttf?v=4.4.0") format("truetype"), url("/assets/fontawesome-webfont-7414288c272f6cc10304aa18e89bf24fb30f40afd644623f425c2c3d71fbe06a.svg?v=4.4.0#fontawesomeregular") format("svg");
  font-weight: normal;
  font-style: normal
}

.fa {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale
}

.fa-lg {
  font-size: 1.33333333em;
  line-height: 0.75em;
  vertical-align: -15%
}

.fa-2x {
  font-size: 2em
}

.fa-3x {
  font-size: 3em
}

.fa-4x {
  font-size: 4em
}

.fa-5x {
  font-size: 5em
}

.fa-fw {
  width: 1.28571429em;
  text-align: center
}

.fa-ul {
  padding-left: 0;
  margin-left: 2.14285714em;
  list-style-type: none
}

.fa-ul>li {
  position: relative
}

.fa-li {
  position: absolute;
  left: -2.14285714em;
  width: 2.14285714em;
  top: 0.14285714em;
  text-align: center
}

.fa-li.fa-lg {
  left: -1.85714286em
}

.fa-border {
  padding: .2em .25em .15em;
  border: solid 0.08em #eeeeee;
  border-radius: .1em
}

.fa-pull-left {
  float: left
}

.fa-pull-right {
  float: right
}

.fa.fa-pull-left {
  margin-right: .3em
}

.fa.fa-pull-right {
  margin-left: .3em
}

.pull-right {
  float: right
}

.pull-left {
  float: left
}

.fa.pull-left {
  margin-right: .3em
}

.fa.pull-right {
  margin-left: .3em
}

.fa-spin {
  -webkit-animation: fa-spin 2s infinite linear;
  animation: fa-spin 2s infinite linear
}

.fa-pulse {
  -webkit-animation: fa-spin 1s infinite steps(8);
  animation: fa-spin 1s infinite steps(8)
}

@-webkit-keyframes fa-spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg)
  }

  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg)
  }
}

@keyframes fa-spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg)
  }

  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg)
  }
}

.fa-rotate-90 {
  filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=1);
  -webkit-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  transform: rotate(90deg)
}

.fa-rotate-180 {
  filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=2);
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg)
}

.fa-rotate-270 {
  filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=3);
  -webkit-transform: rotate(270deg);
  -ms-transform: rotate(270deg);
  transform: rotate(270deg)
}

.fa-flip-horizontal {
  filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1);
  -webkit-transform: scale(-1, 1);
  -ms-transform: scale(-1, 1);
  transform: scale(-1, 1)
}

.fa-flip-vertical {
  filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1);
  -webkit-transform: scale(1, -1);
  -ms-transform: scale(1, -1);
  transform: scale(1, -1)
}

:root .fa-rotate-90,
:root .fa-rotate-180,
:root .fa-rotate-270,
:root .fa-flip-horizontal,
:root .fa-flip-vertical {
  -webkit-filter: none;
  filter: none
}

.fa-stack {
  position: relative;
  display: inline-block;
  width: 2em;
  height: 2em;
  line-height: 2em;
  vertical-align: middle
}

.fa-stack-1x,
.fa-stack-2x {
  position: absolute;
  left: 0;
  width: 100%;
  text-align: center
}

.fa-stack-1x {
  line-height: inherit
}

.fa-stack-2x {
  font-size: 2em
}

.fa-inverse {
  color: #ffffff
}

.fa-glass:before {
  content: "\f000"
}

.fa-music:before {
  content: "\f001"
}

.fa-search:before {
  content: "\f002"
}

.fa-envelope-o:before {
  content: "\f003"
}

.fa-heart:before {
  content: "\f004"
}

.fa-star:before {
  content: "\f005"
}

.fa-star-o:before {
  content: "\f006"
}

.fa-user:before {
  content: "\f007"
}

.fa-film:before {
  content: "\f008"
}

.fa-th-large:before {
  content: "\f009"
}

.fa-th:before {
  content: "\f00a"
}

.fa-th-list:before {
  content: "\f00b"
}

.fa-check:before {
  content: "\f00c"
}

.fa-remove:before,
.fa-close:before,
.fa-times:before {
  content: "\f00d"
}

.fa-search-plus:before {
  content: "\f00e"
}

.fa-search-minus:before {
  content: "\f010"
}

.fa-power-off:before {
  content: "\f011"
}

.fa-signal:before {
  content: "\f012"
}

.fa-gear:before,
.fa-cog:before {
  content: "\f013"
}

.fa-trash-o:before {
  content: "\f014"
}

.fa-home:before {
  content: "\f015"
}

.fa-file-o:before {
  content: "\f016"
}

.fa-clock-o:before {
  content: "\f017"
}

.fa-road:before {
  content: "\f018"
}

.fa-download:before {
  content: "\f019"
}

.fa-arrow-circle-o-down:before {
  content: "\f01a"
}

.fa-arrow-circle-o-up:before {
  content: "\f01b"
}

.fa-inbox:before {
  content: "\f01c"
}

.fa-play-circle-o:before {
  content: "\f01d"
}

.fa-rotate-right:before,
.fa-repeat:before {
  content: "\f01e"
}

.fa-refresh:before {
  content: "\f021"
}

.fa-list-alt:before {
  content: "\f022"
}

.fa-lock:before {
  content: "\f023"
}

.fa-flag:before {
  content: "\f024"
}

.fa-headphones:before {
  content: "\f025"
}

.fa-volume-off:before {
  content: "\f026"
}

.fa-volume-down:before {
  content: "\f027"
}

.fa-volume-up:before {
  content: "\f028"
}

.fa-qrcode:before {
  content: "\f029"
}

.fa-barcode:before {
  content: "\f02a"
}

.fa-tag:before {
  content: "\f02b"
}

.fa-tags:before {
  content: "\f02c"
}

.fa-book:before {
  content: "\f02d"
}

.fa-bookmark:before {
  content: "\f02e"
}

.fa-print:before {
  content: "\f02f"
}

.fa-camera:before {
  content: "\f030"
}

.fa-font:before {
  content: "\f031"
}

.fa-bold:before {
  content: "\f032"
}

.fa-italic:before {
  content: "\f033"
}

.fa-text-height:before {
  content: "\f034"
}

.fa-text-width:before {
  content: "\f035"
}

.fa-align-left:before {
  content: "\f036"
}

.fa-align-center:before {
  content: "\f037"
}

.fa-align-right:before {
  content: "\f038"
}

.fa-align-justify:before {
  content: "\f039"
}

.fa-list:before {
  content: "\f03a"
}

.fa-dedent:before,
.fa-outdent:before {
  content: "\f03b"
}

.fa-indent:before {
  content: "\f03c"
}

.fa-video-camera:before {
  content: "\f03d"
}

.fa-photo:before,
.fa-image:before,
.fa-picture-o:before {
  content: "\f03e"
}

.fa-pencil:before {
  content: "\f040"
}

.fa-map-marker:before {
  content: "\f041"
}

.fa-adjust:before {
  content: "\f042"
}

.fa-tint:before {
  content: "\f043"
}

.fa-edit:before,
.fa-pencil-square-o:before {
  content: "\f044"
}

.fa-share-square-o:before {
  content: "\f045"
}

.fa-check-square-o:before {
  content: "\f046"
}

.fa-arrows:before {
  content: "\f047"
}

.fa-step-backward:before {
  content: "\f048"
}

.fa-fast-backward:before {
  content: "\f049"
}

.fa-backward:before {
  content: "\f04a"
}

.fa-play:before {
  content: "\f04b"
}

.fa-pause:before {
  content: "\f04c"
}

.fa-stop:before {
  content: "\f04d"
}

.fa-forward:before {
  content: "\f04e"
}

.fa-fast-forward:before {
  content: "\f050"
}

.fa-step-forward:before {
  content: "\f051"
}

.fa-eject:before {
  content: "\f052"
}

.fa-chevron-left:before {
  content: "\f053"
}

.fa-chevron-right:before {
  content: "\f054"
}

.fa-plus-circle:before {
  content: "\f055"
}

.fa-minus-circle:before {
  content: "\f056"
}

.fa-times-circle:before {
  content: "\f057"
}

.fa-check-circle:before {
  content: "\f058"
}

.fa-question-circle:before {
  content: "\f059"
}

.fa-info-circle:before {
  content: "\f05a"
}

.fa-crosshairs:before {
  content: "\f05b"
}

.fa-times-circle-o:before {
  content: "\f05c"
}

.fa-check-circle-o:before {
  content: "\f05d"
}

.fa-ban:before {
  content: "\f05e"
}

.fa-arrow-left:before {
  content: "\f060"
}

.fa-arrow-right:before {
  content: "\f061"
}

.fa-arrow-up:before {
  content: "\f062"
}

.fa-arrow-down:before {
  content: "\f063"
}

.fa-mail-forward:before,
.fa-share:before {
  content: "\f064"
}

.fa-expand:before {
  content: "\f065"
}

.fa-compress:before {
  content: "\f066"
}

.fa-plus:before {
  content: "\f067"
}

.fa-minus:before {
  content: "\f068"
}

.fa-asterisk:before {
  content: "\f069"
}

.fa-exclamation-circle:before {
  content: "\f06a"
}

.fa-gift:before {
  content: "\f06b"
}

.fa-leaf:before {
  content: "\f06c"
}

.fa-fire:before {
  content: "\f06d"
}

.fa-eye:before {
  content: "\f06e"
}

.fa-eye-slash:before {
  content: "\f070"
}

.fa-warning:before,
.fa-exclamation-triangle:before {
  content: "\f071"
}

.fa-plane:before {
  content: "\f072"
}

.fa-calendar:before {
  content: "\f073"
}

.fa-random:before {
  content: "\f074"
}

.fa-comment:before {
  content: "\f075"
}

.fa-magnet:before {
  content: "\f076"
}

.fa-chevron-up:before {
  content: "\f077"
}

.fa-chevron-down:before {
  content: "\f078"
}

.fa-retweet:before {
  content: "\f079"
}

.fa-shopping-cart:before {
  content: "\f07a"
}

.fa-folder:before {
  content: "\f07b"
}

.fa-folder-open:before {
  content: "\f07c"
}

.fa-arrows-v:before {
  content: "\f07d"
}

.fa-arrows-h:before {
  content: "\f07e"
}

.fa-bar-chart-o:before,
.fa-bar-chart:before {
  content: "\f080"
}

.fa-twitter-square:before {
  content: "\f081"
}

.fa-facebook-square:before {
  content: "\f082"
}

.fa-camera-retro:before {
  content: "\f083"
}

.fa-key:before {
  content: "\f084"
}

.fa-gears:before,
.fa-cogs:before {
  content: "\f085"
}

.fa-comments:before {
  content: "\f086"
}

.fa-thumbs-o-up:before {
  content: "\f087"
}

.fa-thumbs-o-down:before {
  content: "\f088"
}

.fa-star-half:before {
  content: "\f089"
}

.fa-heart-o:before {
  content: "\f08a"
}

.fa-sign-out:before {
  content: "\f08b"
}

.fa-linkedin-square:before {
  content: "\f08c"
}

.fa-thumb-tack:before {
  content: "\f08d"
}

.fa-external-link:before {
  content: "\f08e"
}

.fa-sign-in:before {
  content: "\f090"
}

.fa-trophy:before {
  content: "\f091"
}

.fa-github-square:before {
  content: "\f092"
}

.fa-upload:before {
  content: "\f093"
}

.fa-lemon-o:before {
  content: "\f094"
}

.fa-phone:before {
  content: "\f095"
}

.fa-square-o:before {
  content: "\f096"
}

.fa-bookmark-o:before {
  content: "\f097"
}

.fa-phone-square:before {
  content: "\f098"
}

.fa-twitter:before {
  content: "\f099"
}

.fa-facebook-f:before,
.fa-facebook:before {
  content: "\f09a"
}

.fa-github:before {
  content: "\f09b"
}

.fa-unlock:before {
  content: "\f09c"
}

.fa-credit-card:before {
  content: "\f09d"
}

.fa-feed:before,
.fa-rss:before {
  content: "\f09e"
}

.fa-hdd-o:before {
  content: "\f0a0"
}

.fa-bullhorn:before {
  content: "\f0a1"
}

.fa-bell:before {
  content: "\f0f3"
}

.fa-certificate:before {
  content: "\f0a3"
}

.fa-hand-o-right:before {
  content: "\f0a4"
}

.fa-hand-o-left:before {
  content: "\f0a5"
}

.fa-hand-o-up:before {
  content: "\f0a6"
}

.fa-hand-o-down:before {
  content: "\f0a7"
}

.fa-arrow-circle-left:before {
  content: "\f0a8"
}

.fa-arrow-circle-right:before {
  content: "\f0a9"
}

.fa-arrow-circle-up:before {
  content: "\f0aa"
}

.fa-arrow-circle-down:before {
  content: "\f0ab"
}

.fa-globe:before {
  content: "\f0ac"
}

.fa-wrench:before {
  content: "\f0ad"
}

.fa-tasks:before {
  content: "\f0ae"
}

.fa-filter:before {
  content: "\f0b0"
}

.fa-briefcase:before {
  content: "\f0b1"
}

.fa-arrows-alt:before {
  content: "\f0b2"
}

.fa-group:before,
.fa-users:before {
  content: "\f0c0"
}

.fa-chain:before,
.fa-link:before {
  content: "\f0c1"
}

.fa-cloud:before {
  content: "\f0c2"
}

.fa-flask:before {
  content: "\f0c3"
}

.fa-cut:before,
.fa-scissors:before {
  content: "\f0c4"
}

.fa-copy:before,
.fa-files-o:before {
  content: "\f0c5"
}

.fa-paperclip:before {
  content: "\f0c6"
}

.fa-save:before,
.fa-floppy-o:before {
  content: "\f0c7"
}

.fa-square:before {
  content: "\f0c8"
}

.fa-navicon:before,
.fa-reorder:before,
.fa-bars:before {
  content: "\f0c9"
}

.fa-list-ul:before {
  content: "\f0ca"
}

.fa-list-ol:before {
  content: "\f0cb"
}

.fa-strikethrough:before {
  content: "\f0cc"
}

.fa-underline:before {
  content: "\f0cd"
}

.fa-table:before {
  content: "\f0ce"
}

.fa-magic:before {
  content: "\f0d0"
}

.fa-truck:before {
  content: "\f0d1"
}

.fa-pinterest:before {
  content: "\f0d2"
}

.fa-pinterest-square:before {
  content: "\f0d3"
}

.fa-google-plus-square:before {
  content: "\f0d4"
}

.fa-google-plus:before {
  content: "\f0d5"
}

.fa-money:before {
  content: "\f0d6"
}

.fa-caret-down:before {
  content: "\f0d7"
}

.fa-caret-up:before {
  content: "\f0d8"
}

.fa-caret-left:before {
  content: "\f0d9"
}

.fa-caret-right:before {
  content: "\f0da"
}

.fa-columns:before {
  content: "\f0db"
}

.fa-unsorted:before,
.fa-sort:before {
  content: "\f0dc"
}

.fa-sort-down:before,
.fa-sort-desc:before {
  content: "\f0dd"
}

.fa-sort-up:before,
.fa-sort-asc:before {
  content: "\f0de"
}

.fa-envelope:before {
  content: "\f0e0"
}

.fa-linkedin:before {
  content: "\f0e1"
}

.fa-rotate-left:before,
.fa-undo:before {
  content: "\f0e2"
}

.fa-legal:before,
.fa-gavel:before {
  content: "\f0e3"
}

.fa-dashboard:before,
.fa-tachometer:before {
  content: "\f0e4"
}

.fa-comment-o:before {
  content: "\f0e5"
}

.fa-comments-o:before {
  content: "\f0e6"
}

.fa-flash:before,
.fa-bolt:before {
  content: "\f0e7"
}

.fa-sitemap:before {
  content: "\f0e8"
}

.fa-umbrella:before {
  content: "\f0e9"
}

.fa-paste:before,
.fa-clipboard:before {
  content: "\f0ea"
}

.fa-lightbulb-o:before {
  content: "\f0eb"
}

.fa-exchange:before {
  content: "\f0ec"
}

.fa-cloud-download:before {
  content: "\f0ed"
}

.fa-cloud-upload:before {
  content: "\f0ee"
}

.fa-user-md:before {
  content: "\f0f0"
}

.fa-stethoscope:before {
  content: "\f0f1"
}

.fa-suitcase:before {
  content: "\f0f2"
}

.fa-bell-o:before {
  content: "\f0a2"
}

.fa-coffee:before {
  content: "\f0f4"
}

.fa-cutlery:before {
  content: "\f0f5"
}

.fa-file-text-o:before {
  content: "\f0f6"
}

.fa-building-o:before {
  content: "\f0f7"
}

.fa-hospital-o:before {
  content: "\f0f8"
}

.fa-ambulance:before {
  content: "\f0f9"
}

.fa-medkit:before {
  content: "\f0fa"
}

.fa-fighter-jet:before {
  content: "\f0fb"
}

.fa-beer:before {
  content: "\f0fc"
}

.fa-h-square:before {
  content: "\f0fd"
}

.fa-plus-square:before {
  content: "\f0fe"
}

.fa-angle-double-left:before {
  content: "\f100"
}

.fa-angle-double-right:before {
  content: "\f101"
}

.fa-angle-double-up:before {
  content: "\f102"
}

.fa-angle-double-down:before {
  content: "\f103"
}

.fa-angle-left:before {
  content: "\f104"
}

.fa-angle-right:before {
  content: "\f105"
}

.fa-angle-up:before {
  content: "\f106"
}

.fa-angle-down:before {
  content: "\f107"
}

.fa-desktop:before {
  content: "\f108"
}

.fa-laptop:before {
  content: "\f109"
}

.fa-tablet:before {
  content: "\f10a"
}

.fa-mobile-phone:before,
.fa-mobile:before {
  content: "\f10b"
}

.fa-circle-o:before {
  content: "\f10c"
}

.fa-quote-left:before {
  content: "\f10d"
}

.fa-quote-right:before {
  content: "\f10e"
}

.fa-spinner:before {
  content: "\f110"
}

.fa-circle:before {
  content: "\f111"
}

.fa-mail-reply:before,
.fa-reply:before {
  content: "\f112"
}

.fa-github-alt:before {
  content: "\f113"
}

.fa-folder-o:before {
  content: "\f114"
}

.fa-folder-open-o:before {
  content: "\f115"
}

.fa-smile-o:before {
  content: "\f118"
}

.fa-frown-o:before {
  content: "\f119"
}

.fa-meh-o:before {
  content: "\f11a"
}

.fa-gamepad:before {
  content: "\f11b"
}

.fa-keyboard-o:before {
  content: "\f11c"
}

.fa-flag-o:before {
  content: "\f11d"
}

.fa-flag-checkered:before {
  content: "\f11e"
}

.fa-terminal:before {
  content: "\f120"
}

.fa-code:before {
  content: "\f121"
}

.fa-mail-reply-all:before,
.fa-reply-all:before {
  content: "\f122"
}

.fa-star-half-empty:before,
.fa-star-half-full:before,
.fa-star-half-o:before {
  content: "\f123"
}

.fa-location-arrow:before {
  content: "\f124"
}

.fa-crop:before {
  content: "\f125"
}

.fa-code-fork:before {
  content: "\f126"
}

.fa-unlink:before,
.fa-chain-broken:before {
  content: "\f127"
}

.fa-question:before {
  content: "\f128"
}

.fa-info:before {
  content: "\f129"
}

.fa-exclamation:before {
  content: "\f12a"
}

.fa-superscript:before {
  content: "\f12b"
}

.fa-subscript:before {
  content: "\f12c"
}

.fa-eraser:before {
  content: "\f12d"
}

.fa-puzzle-piece:before {
  content: "\f12e"
}

.fa-microphone:before {
  content: "\f130"
}

.fa-microphone-slash:before {
  content: "\f131"
}

.fa-shield:before {
  content: "\f132"
}

.fa-calendar-o:before {
  content: "\f133"
}

.fa-fire-extinguisher:before {
  content: "\f134"
}

.fa-rocket:before {
  content: "\f135"
}

.fa-maxcdn:before {
  content: "\f136"
}

.fa-chevron-circle-left:before {
  content: "\f137"
}

.fa-chevron-circle-right:before {
  content: "\f138"
}

.fa-chevron-circle-up:before {
  content: "\f139"
}

.fa-chevron-circle-down:before {
  content: "\f13a"
}

.fa-html5:before {
  content: "\f13b"
}

.fa-css3:before {
  content: "\f13c"
}

.fa-anchor:before {
  content: "\f13d"
}

.fa-unlock-alt:before {
  content: "\f13e"
}

.fa-bullseye:before {
  content: "\f140"
}

.fa-ellipsis-h:before {
  content: "\f141"
}

.fa-ellipsis-v:before {
  content: "\f142"
}

.fa-rss-square:before {
  content: "\f143"
}

.fa-play-circle:before {
  content: "\f144"
}

.fa-ticket:before {
  content: "\f145"
}

.fa-minus-square:before {
  content: "\f146"
}

.fa-minus-square-o:before {
  content: "\f147"
}

.fa-level-up:before {
  content: "\f148"
}

.fa-level-down:before {
  content: "\f149"
}

.fa-check-square:before {
  content: "\f14a"
}

.fa-pencil-square:before {
  content: "\f14b"
}

.fa-external-link-square:before {
  content: "\f14c"
}

.fa-share-square:before {
  content: "\f14d"
}

.fa-compass:before {
  content: "\f14e"
}

.fa-toggle-down:before,
.fa-caret-square-o-down:before {
  content: "\f150"
}

.fa-toggle-up:before,
.fa-caret-square-o-up:before {
  content: "\f151"
}

.fa-toggle-right:before,
.fa-caret-square-o-right:before {
  content: "\f152"
}

.fa-euro:before,
.fa-eur:before {
  content: "\f153"
}

.fa-gbp:before {
  content: "\f154"
}

.fa-dollar:before,
.fa-usd:before {
  content: "\f155"
}

.fa-rupee:before,
.fa-inr:before {
  content: "\f156"
}

.fa-cny:before,
.fa-rmb:before,
.fa-yen:before,
.fa-jpy:before {
  content: "\f157"
}

.fa-ruble:before,
.fa-rouble:before,
.fa-rub:before {
  content: "\f158"
}

.fa-won:before,
.fa-krw:before {
  content: "\f159"
}

.fa-bitcoin:before,
.fa-btc:before {
  content: "\f15a"
}

.fa-file:before {
  content: "\f15b"
}

.fa-file-text:before {
  content: "\f15c"
}

.fa-sort-alpha-asc:before {
  content: "\f15d"
}

.fa-sort-alpha-desc:before {
  content: "\f15e"
}

.fa-sort-amount-asc:before {
  content: "\f160"
}

.fa-sort-amount-desc:before {
  content: "\f161"
}

.fa-sort-numeric-asc:before {
  content: "\f162"
}

.fa-sort-numeric-desc:before {
  content: "\f163"
}

.fa-thumbs-up:before {
  content: "\f164"
}

.fa-thumbs-down:before {
  content: "\f165"
}

.fa-youtube-square:before {
  content: "\f166"
}

.fa-youtube:before {
  content: "\f167"
}

.fa-xing:before {
  content: "\f168"
}

.fa-xing-square:before {
  content: "\f169"
}

.fa-youtube-play:before {
  content: "\f16a"
}

.fa-dropbox:before {
  content: "\f16b"
}

.fa-stack-overflow:before {
  content: "\f16c"
}

.fa-instagram:before {
  content: "\f16d"
}

.fa-flickr:before {
  content: "\f16e"
}

.fa-adn:before {
  content: "\f170"
}

.fa-bitbucket:before {
  content: "\f171"
}

.fa-bitbucket-square:before {
  content: "\f172"
}

.fa-tumblr:before {
  content: "\f173"
}

.fa-tumblr-square:before {
  content: "\f174"
}

.fa-long-arrow-down:before {
  content: "\f175"
}

.fa-long-arrow-up:before {
  content: "\f176"
}

.fa-long-arrow-left:before {
  content: "\f177"
}

.fa-long-arrow-right:before {
  content: "\f178"
}

.fa-apple:before {
  content: "\f179"
}

.fa-windows:before {
  content: "\f17a"
}

.fa-android:before {
  content: "\f17b"
}

.fa-linux:before {
  content: "\f17c"
}

.fa-dribbble:before {
  content: "\f17d"
}

.fa-skype:before {
  content: "\f17e"
}

.fa-foursquare:before {
  content: "\f180"
}

.fa-trello:before {
  content: "\f181"
}

.fa-female:before {
  content: "\f182"
}

.fa-male:before {
  content: "\f183"
}

.fa-gittip:before,
.fa-gratipay:before {
  content: "\f184"
}

.fa-sun-o:before {
  content: "\f185"
}

.fa-moon-o:before {
  content: "\f186"
}

.fa-archive:before {
  content: "\f187"
}

.fa-bug:before {
  content: "\f188"
}

.fa-vk:before {
  content: "\f189"
}

.fa-weibo:before {
  content: "\f18a"
}

.fa-renren:before {
  content: "\f18b"
}

.fa-pagelines:before {
  content: "\f18c"
}

.fa-stack-exchange:before {
  content: "\f18d"
}

.fa-arrow-circle-o-right:before {
  content: "\f18e"
}

.fa-arrow-circle-o-left:before {
  content: "\f190"
}

.fa-toggle-left:before,
.fa-caret-square-o-left:before {
  content: "\f191"
}

.fa-dot-circle-o:before {
  content: "\f192"
}

.fa-wheelchair:before {
  content: "\f193"
}

.fa-vimeo-square:before {
  content: "\f194"
}

.fa-turkish-lira:before,
.fa-try:before {
  content: "\f195"
}

.fa-plus-square-o:before {
  content: "\f196"
}

.fa-space-shuttle:before {
  content: "\f197"
}

.fa-slack:before {
  content: "\f198"
}

.fa-envelope-square:before {
  content: "\f199"
}

.fa-wordpress:before {
  content: "\f19a"
}

.fa-openid:before {
  content: "\f19b"
}

.fa-institution:before,
.fa-bank:before,
.fa-university:before {
  content: "\f19c"
}

.fa-mortar-board:before,
.fa-graduation-cap:before {
  content: "\f19d"
}

.fa-yahoo:before {
  content: "\f19e"
}

.fa-google:before {
  content: "\f1a0"
}

.fa-reddit:before {
  content: "\f1a1"
}

.fa-reddit-square:before {
  content: "\f1a2"
}

.fa-stumbleupon-circle:before {
  content: "\f1a3"
}

.fa-stumbleupon:before {
  content: "\f1a4"
}

.fa-delicious:before {
  content: "\f1a5"
}

.fa-digg:before {
  content: "\f1a6"
}

.fa-pied-piper:before {
  content: "\f1a7"
}

.fa-pied-piper-alt:before {
  content: "\f1a8"
}

.fa-drupal:before {
  content: "\f1a9"
}

.fa-joomla:before {
  content: "\f1aa"
}

.fa-language:before {
  content: "\f1ab"
}

.fa-fax:before {
  content: "\f1ac"
}

.fa-building:before {
  content: "\f1ad"
}

.fa-child:before {
  content: "\f1ae"
}

.fa-paw:before {
  content: "\f1b0"
}

.fa-spoon:before {
  content: "\f1b1"
}

.fa-cube:before {
  content: "\f1b2"
}

.fa-cubes:before {
  content: "\f1b3"
}

.fa-behance:before {
  content: "\f1b4"
}

.fa-behance-square:before {
  content: "\f1b5"
}

.fa-steam:before {
  content: "\f1b6"
}

.fa-steam-square:before {
  content: "\f1b7"
}

.fa-recycle:before {
  content: "\f1b8"
}

.fa-automobile:before,
.fa-car:before {
  content: "\f1b9"
}

.fa-cab:before,
.fa-taxi:before {
  content: "\f1ba"
}

.fa-tree:before {
  content: "\f1bb"
}

.fa-spotify:before {
  content: "\f1bc"
}

.fa-deviantart:before {
  content: "\f1bd"
}

.fa-soundcloud:before {
  content: "\f1be"
}

.fa-database:before {
  content: "\f1c0"
}

.fa-file-pdf-o:before {
  content: "\f1c1"
}

.fa-file-word-o:before {
  content: "\f1c2"
}

.fa-file-excel-o:before {
  content: "\f1c3"
}

.fa-file-powerpoint-o:before {
  content: "\f1c4"
}

.fa-file-photo-o:before,
.fa-file-picture-o:before,
.fa-file-image-o:before {
  content: "\f1c5"
}

.fa-file-zip-o:before,
.fa-file-archive-o:before {
  content: "\f1c6"
}

.fa-file-sound-o:before,
.fa-file-audio-o:before {
  content: "\f1c7"
}

.fa-file-movie-o:before,
.fa-file-video-o:before {
  content: "\f1c8"
}

.fa-file-code-o:before {
  content: "\f1c9"
}

.fa-vine:before {
  content: "\f1ca"
}

.fa-codepen:before {
  content: "\f1cb"
}

.fa-jsfiddle:before {
  content: "\f1cc"
}

.fa-life-bouy:before,
.fa-life-buoy:before,
.fa-life-saver:before,
.fa-support:before,
.fa-life-ring:before {
  content: "\f1cd"
}

.fa-circle-o-notch:before {
  content: "\f1ce"
}

.fa-ra:before,
.fa-rebel:before {
  content: "\f1d0"
}

.fa-ge:before,
.fa-empire:before {
  content: "\f1d1"
}

.fa-git-square:before {
  content: "\f1d2"
}

.fa-git:before {
  content: "\f1d3"
}

.fa-y-combinator-square:before,
.fa-yc-square:before,
.fa-hacker-news:before {
  content: "\f1d4"
}

.fa-tencent-weibo:before {
  content: "\f1d5"
}

.fa-qq:before {
  content: "\f1d6"
}

.fa-wechat:before,
.fa-weixin:before {
  content: "\f1d7"
}

.fa-send:before,
.fa-paper-plane:before {
  content: "\f1d8"
}

.fa-send-o:before,
.fa-paper-plane-o:before {
  content: "\f1d9"
}

.fa-history:before {
  content: "\f1da"
}

.fa-circle-thin:before {
  content: "\f1db"
}

.fa-header:before {
  content: "\f1dc"
}

.fa-paragraph:before {
  content: "\f1dd"
}

.fa-sliders:before {
  content: "\f1de"
}

.fa-share-alt:before {
  content: "\f1e0"
}

.fa-share-alt-square:before {
  content: "\f1e1"
}

.fa-bomb:before {
  content: "\f1e2"
}

.fa-soccer-ball-o:before,
.fa-futbol-o:before {
  content: "\f1e3"
}

.fa-tty:before {
  content: "\f1e4"
}

.fa-binoculars:before {
  content: "\f1e5"
}

.fa-plug:before {
  content: "\f1e6"
}

.fa-slideshare:before {
  content: "\f1e7"
}

.fa-twitch:before {
  content: "\f1e8"
}

.fa-yelp:before {
  content: "\f1e9"
}

.fa-newspaper-o:before {
  content: "\f1ea"
}

.fa-wifi:before {
  content: "\f1eb"
}

.fa-calculator:before {
  content: "\f1ec"
}

.fa-paypal:before {
  content: "\f1ed"
}

.fa-google-wallet:before {
  content: "\f1ee"
}

.fa-cc-visa:before {
  content: "\f1f0"
}

.fa-cc-mastercard:before {
  content: "\f1f1"
}

.fa-cc-discover:before {
  content: "\f1f2"
}

.fa-cc-amex:before {
  content: "\f1f3"
}

.fa-cc-paypal:before {
  content: "\f1f4"
}

.fa-cc-stripe:before {
  content: "\f1f5"
}

.fa-bell-slash:before {
  content: "\f1f6"
}

.fa-bell-slash-o:before {
  content: "\f1f7"
}

.fa-trash:before {
  content: "\f1f8"
}

.fa-copyright:before {
  content: "\f1f9"
}

.fa-at:before {
  content: "\f1fa"
}

.fa-eyedropper:before {
  content: "\f1fb"
}

.fa-paint-brush:before {
  content: "\f1fc"
}

.fa-birthday-cake:before {
  content: "\f1fd"
}

.fa-area-chart:before {
  content: "\f1fe"
}

.fa-pie-chart:before {
  content: "\f200"
}

.fa-line-chart:before {
  content: "\f201"
}

.fa-lastfm:before {
  content: "\f202"
}

.fa-lastfm-square:before {
  content: "\f203"
}

.fa-toggle-off:before {
  content: "\f204"
}

.fa-toggle-on:before {
  content: "\f205"
}

.fa-bicycle:before {
  content: "\f206"
}

.fa-bus:before {
  content: "\f207"
}

.fa-ioxhost:before {
  content: "\f208"
}

.fa-angellist:before {
  content: "\f209"
}

.fa-cc:before {
  content: "\f20a"
}

.fa-shekel:before,
.fa-sheqel:before,
.fa-ils:before {
  content: "\f20b"
}

.fa-meanpath:before {
  content: "\f20c"
}

.fa-buysellads:before {
  content: "\f20d"
}

.fa-connectdevelop:before {
  content: "\f20e"
}

.fa-dashcube:before {
  content: "\f210"
}

.fa-forumbee:before {
  content: "\f211"
}

.fa-leanpub:before {
  content: "\f212"
}

.fa-sellsy:before {
  content: "\f213"
}

.fa-shirtsinbulk:before {
  content: "\f214"
}

.fa-simplybuilt:before {
  content: "\f215"
}

.fa-skyatlas:before {
  content: "\f216"
}

.fa-cart-plus:before {
  content: "\f217"
}

.fa-cart-arrow-down:before {
  content: "\f218"
}

.fa-diamond:before {
  content: "\f219"
}

.fa-ship:before {
  content: "\f21a"
}

.fa-user-secret:before {
  content: "\f21b"
}

.fa-motorcycle:before {
  content: "\f21c"
}

.fa-street-view:before {
  content: "\f21d"
}

.fa-heartbeat:before {
  content: "\f21e"
}

.fa-venus:before {
  content: "\f221"
}

.fa-mars:before {
  content: "\f222"
}

.fa-mercury:before {
  content: "\f223"
}

.fa-intersex:before,
.fa-transgender:before {
  content: "\f224"
}

.fa-transgender-alt:before {
  content: "\f225"
}

.fa-venus-double:before {
  content: "\f226"
}

.fa-mars-double:before {
  content: "\f227"
}

.fa-venus-mars:before {
  content: "\f228"
}

.fa-mars-stroke:before {
  content: "\f229"
}

.fa-mars-stroke-v:before {
  content: "\f22a"
}

.fa-mars-stroke-h:before {
  content: "\f22b"
}

.fa-neuter:before {
  content: "\f22c"
}

.fa-genderless:before {
  content: "\f22d"
}

.fa-facebook-official:before {
  content: "\f230"
}

.fa-pinterest-p:before {
  content: "\f231"
}

.fa-whatsapp:before {
  content: "\f232"
}

.fa-server:before {
  content: "\f233"
}

.fa-user-plus:before {
  content: "\f234"
}

.fa-user-times:before {
  content: "\f235"
}

.fa-hotel:before,
.fa-bed:before {
  content: "\f236"
}

.fa-viacoin:before {
  content: "\f237"
}

.fa-train:before {
  content: "\f238"
}

.fa-subway:before {
  content: "\f239"
}

.fa-medium:before {
  content: "\f23a"
}

.fa-yc:before,
.fa-y-combinator:before {
  content: "\f23b"
}

.fa-optin-monster:before {
  content: "\f23c"
}

.fa-opencart:before {
  content: "\f23d"
}

.fa-expeditedssl:before {
  content: "\f23e"
}

.fa-battery-4:before,
.fa-battery-full:before {
  content: "\f240"
}

.fa-battery-3:before,
.fa-battery-three-quarters:before {
  content: "\f241"
}

.fa-battery-2:before,
.fa-battery-half:before {
  content: "\f242"
}

.fa-battery-1:before,
.fa-battery-quarter:before {
  content: "\f243"
}

.fa-battery-0:before,
.fa-battery-empty:before {
  content: "\f244"
}

.fa-mouse-pointer:before {
  content: "\f245"
}

.fa-i-cursor:before {
  content: "\f246"
}

.fa-object-group:before {
  content: "\f247"
}

.fa-object-ungroup:before {
  content: "\f248"
}

.fa-sticky-note:before {
  content: "\f249"
}

.fa-sticky-note-o:before {
  content: "\f24a"
}

.fa-cc-jcb:before {
  content: "\f24b"
}

.fa-cc-diners-club:before {
  content: "\f24c"
}

.fa-clone:before {
  content: "\f24d"
}

.fa-balance-scale:before {
  content: "\f24e"
}

.fa-hourglass-o:before {
  content: "\f250"
}

.fa-hourglass-1:before,
.fa-hourglass-start:before {
  content: "\f251"
}

.fa-hourglass-2:before,
.fa-hourglass-half:before {
  content: "\f252"
}

.fa-hourglass-3:before,
.fa-hourglass-end:before {
  content: "\f253"
}

.fa-hourglass:before {
  content: "\f254"
}

.fa-hand-grab-o:before,
.fa-hand-rock-o:before {
  content: "\f255"
}

.fa-hand-stop-o:before,
.fa-hand-paper-o:before {
  content: "\f256"
}

.fa-hand-scissors-o:before {
  content: "\f257"
}

.fa-hand-lizard-o:before {
  content: "\f258"
}

.fa-hand-spock-o:before {
  content: "\f259"
}

.fa-hand-pointer-o:before {
  content: "\f25a"
}

.fa-hand-peace-o:before {
  content: "\f25b"
}

.fa-trademark:before {
  content: "\f25c"
}

.fa-registered:before {
  content: "\f25d"
}

.fa-creative-commons:before {
  content: "\f25e"
}

.fa-gg:before {
  content: "\f260"
}

.fa-gg-circle:before {
  content: "\f261"
}

.fa-tripadvisor:before {
  content: "\f262"
}

.fa-odnoklassniki:before {
  content: "\f263"
}

.fa-odnoklassniki-square:before {
  content: "\f264"
}

.fa-get-pocket:before {
  content: "\f265"
}

.fa-wikipedia-w:before {
  content: "\f266"
}

.fa-safari:before {
  content: "\f267"
}

.fa-chrome:before {
  content: "\f268"
}

.fa-firefox:before {
  content: "\f269"
}

.fa-opera:before {
  content: "\f26a"
}

.fa-internet-explorer:before {
  content: "\f26b"
}

.fa-tv:before,
.fa-television:before {
  content: "\f26c"
}

.fa-contao:before {
  content: "\f26d"
}

.fa-500px:before {
  content: "\f26e"
}

.fa-amazon:before {
  content: "\f270"
}

.fa-calendar-plus-o:before {
  content: "\f271"
}

.fa-calendar-minus-o:before {
  content: "\f272"
}

.fa-calendar-times-o:before {
  content: "\f273"
}

.fa-calendar-check-o:before {
  content: "\f274"
}

.fa-industry:before {
  content: "\f275"
}

.fa-map-pin:before {
  content: "\f276"
}

.fa-map-signs:before {
  content: "\f277"
}

.fa-map-o:before {
  content: "\f278"
}

.fa-map:before {
  content: "\f279"
}

.fa-commenting:before {
  content: "\f27a"
}

.fa-commenting-o:before {
  content: "\f27b"
}

.fa-houzz:before {
  content: "\f27c"
}

.fa-vimeo:before {
  content: "\f27d"
}

.fa-black-tie:before {
  content: "\f27e"
}

.fa-fonticons:before {
  content: "\f280"
}

@font-face {
  font-family: 'taylor-made-icons';
  src: url(/assets/taylor-made-icons-b7d12c554c6a3d46a43155dfac450c9d95770d423ef01ca91f2081e26b0d0952.eot?94243770);
  src: url(/assets/taylor-made-icons-b7d12c554c6a3d46a43155dfac450c9d95770d423ef01ca91f2081e26b0d0952.eot?94243770#iefix) format("embedded-opentype"), url(/assets/taylor-made-icons-109e569af0316bb6ab0c97460858a83fdafa7dd656bec35a25599456f1d28793.woff?94243770) format("woff"), url(/assets/taylor-made-icons-97abd97dd7d5c7bdd13a6cb245e9a62662ce69a511451a1895e2888ef514fc43.ttf?94243770) format("truetype"), url(/assets/taylor-made-icons-7f492907c91d6ec48380abbaec5633e64dbc9a56ef7c97e95ed8b777ab5a9bac.svg?94243770#taylor-made-icons) format("svg");
  font-weight: normal;
  font-style: normal
}

[class^="tm-icon-"]:before,
[class*=" tm-icon-"]:before {
  font-family: "taylor-made-icons";
  font-style: normal;
  font-weight: normal;
  speak: none;
  display: inline-block;
  text-decoration: inherit;
  text-align: center;
  font-variant: normal;
  text-transform: none;
  line-height: 1em;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale
}

.tm-icon-club:before {
  content: '\e800'
}

/*!
 * Bootstrap v3.3.5 (http://getbootstrap.com)
 * Copyright 2011-2015 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)
 */
/*! normalize.css v3.0.3 | MIT License | github.com/necolas/normalize.css */
html {
  font-family: sans-serif;
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%
}

body {
  margin: 0
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
menu,
nav,
section,
summary {
  display: block
}

audio,
canvas,
progress,
video {
  display: inline-block;
  vertical-align: baseline
}

audio:not([controls]) {
  display: none;
  height: 0
}

[hidden],
template {
  display: none
}

a {
  background-color: transparent
}

a:active,
a:hover {
  outline: 0
}

abbr[title] {
  border-bottom: 1px dotted
}

b,
strong {
  font-weight: bold
}

dfn {
  font-style: italic
}

h1 {
  font-size: 2em;
  margin: 0.67em 0
}

mark {
  background: #ff0;
  color: #000
}

small {
  font-size: 80%
}

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline
}

sup {
  top: -0.5em
}

sub {
  bottom: -0.25em
}

img {
  border: 0
}

svg:not(:root) {
  overflow: hidden
}

figure {
  margin: 1em 40px
}

hr {
  box-sizing: content-box;
  height: 0
}

pre {
  overflow: auto
}

code,
kbd,
pre,
samp {
  font-family: monospace, monospace;
  font-size: 1em
}

button,
input,
optgroup,
select,
textarea {
  color: inherit;
  font: inherit;
  margin: 0
}

button {
  overflow: visible
}

button,
select {
  text-transform: none
}

button,
html input[type="button"],
input[type="reset"],
input[type="submit"] {
  -webkit-appearance: button;
  cursor: pointer
}

button[disabled],
html input[disabled] {
  cursor: default
}

button::-moz-focus-inner,
input::-moz-focus-inner {
  border: 0;
  padding: 0
}

input {
  line-height: normal
}

input[type="checkbox"],
input[type="radio"] {
  box-sizing: border-box;
  padding: 0
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  height: auto
}

input[type="search"] {
  -webkit-appearance: textfield;
  box-sizing: content-box
}

input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none
}

fieldset {
  border: 1px solid #c0c0c0;
  margin: 0 2px;
  padding: 0.35em 0.625em 0.75em
}

legend {
  border: 0;
  padding: 0
}

textarea {
  overflow: auto
}

optgroup {
  font-weight: bold
}

table {
  border-collapse: collapse;
  border-spacing: 0
}

td,
th {
  padding: 0
}

/*! Source: https://github.com/h5bp/html5-boilerplate/blob/master/src/css/main.css */
@media print {

  *,
  *:before,
  *:after {
    background: transparent !important;
    color: #000 !important;
    box-shadow: none !important;
    text-shadow: none !important
  }

  a,
  a:visited {
    text-decoration: underline
  }

  a[href]:after {
    content: " (" attr(href) ")"
  }

  abbr[title]:after {
    content: " (" attr(title) ")"
  }

  a[href^="#"]:after,
  a[href^="javascript:"]:after {
    content: ""
  }

  pre,
  blockquote {
    border: 1px solid #999;
    page-break-inside: avoid
  }

  thead {
    display: table-header-group
  }

  tr,
  img {
    page-break-inside: avoid
  }

  img {
    max-width: 100% !important
  }

  p,
  h2,
  h3 {
    orphans: 3;
    widows: 3
  }

  h2,
  h3 {
    page-break-after: avoid
  }

  .navbar {
    display: none
  }

  .btn>.caret,
  .dropup>.btn>.caret {
    border-top-color: #000 !important
  }

  .label {
    border: 1px solid #000
  }

  .table {
    border-collapse: collapse !important
  }

  .table td,
  .table th {
    background-color: #fff !important
  }

  .table-bordered th,
  .table-bordered td {
    border: 1px solid #ddd !important
  }
}

@font-face {
  font-family: 'Glyphicons Halflings';
  src: url("../fonts/bootstrap/glyphicons-halflings-regular.eot");
  src: url("../fonts/bootstrap/glyphicons-halflings-regular.eot?#iefix") format("embedded-opentype"), url("../fonts/bootstrap/glyphicons-halflings-regular.woff2") format("woff2"), url("../fonts/bootstrap/glyphicons-halflings-regular.woff") format("woff"), url("../fonts/bootstrap/glyphicons-halflings-regular.ttf") format("truetype"), url("../fonts/bootstrap/glyphicons-halflings-regular.svg#glyphicons_halflingsregular") format("svg")
}

.glyphicon {
  position: relative;
  top: 1px;
  display: inline-block;
  font-family: 'Glyphicons Halflings';
  font-style: normal;
  font-weight: normal;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale
}

.glyphicon-asterisk:before {
  content: "\2a"
}

.glyphicon-plus:before {
  content: "\2b"
}

.glyphicon-euro:before,
.glyphicon-eur:before {
  content: "\20ac"
}

.glyphicon-minus:before {
  content: "\2212"
}

.glyphicon-cloud:before {
  content: "\2601"
}

.glyphicon-envelope:before {
  content: "\2709"
}

.glyphicon-pencil:before {
  content: "\270f"
}

.glyphicon-glass:before {
  content: "\e001"
}

.glyphicon-music:before {
  content: "\e002"
}

.glyphicon-search:before {
  content: "\e003"
}

.glyphicon-heart:before {
  content: "\e005"
}

.glyphicon-star:before {
  content: "\e006"
}

.glyphicon-star-empty:before {
  content: "\e007"
}

.glyphicon-user:before {
  content: "\e008"
}

.glyphicon-film:before {
  content: "\e009"
}

.glyphicon-th-large:before {
  content: "\e010"
}

.glyphicon-th:before {
  content: "\e011"
}

.glyphicon-th-list:before {
  content: "\e012"
}

.glyphicon-ok:before {
  content: "\e013"
}

.glyphicon-remove:before {
  content: "\e014"
}

.glyphicon-zoom-in:before {
  content: "\e015"
}

.glyphicon-zoom-out:before {
  content: "\e016"
}

.glyphicon-off:before {
  content: "\e017"
}

.glyphicon-signal:before {
  content: "\e018"
}

.glyphicon-cog:before {
  content: "\e019"
}

.glyphicon-trash:before {
  content: "\e020"
}

.glyphicon-home:before {
  content: "\e021"
}

.glyphicon-file:before {
  content: "\e022"
}

.glyphicon-time:before {
  content: "\e023"
}

.glyphicon-road:before {
  content: "\e024"
}

.glyphicon-download-alt:before {
  content: "\e025"
}

.glyphicon-download:before {
  content: "\e026"
}

.glyphicon-upload:before {
  content: "\e027"
}

.glyphicon-inbox:before {
  content: "\e028"
}

.glyphicon-play-circle:before {
  content: "\e029"
}

.glyphicon-repeat:before {
  content: "\e030"
}

.glyphicon-refresh:before {
  content: "\e031"
}

.glyphicon-list-alt:before {
  content: "\e032"
}

.glyphicon-lock:before {
  content: "\e033"
}

.glyphicon-flag:before {
  content: "\e034"
}

.glyphicon-headphones:before {
  content: "\e035"
}

.glyphicon-volume-off:before {
  content: "\e036"
}

.glyphicon-volume-down:before {
  content: "\e037"
}

.glyphicon-volume-up:before {
  content: "\e038"
}

.glyphicon-qrcode:before {
  content: "\e039"
}

.glyphicon-barcode:before {
  content: "\e040"
}

.glyphicon-tag:before {
  content: "\e041"
}

.glyphicon-tags:before {
  content: "\e042"
}

.glyphicon-book:before {
  content: "\e043"
}

.glyphicon-bookmark:before {
  content: "\e044"
}

.glyphicon-print:before {
  content: "\e045"
}

.glyphicon-camera:before {
  content: "\e046"
}

.glyphicon-font:before {
  content: "\e047"
}

.glyphicon-bold:before {
  content: "\e048"
}

.glyphicon-italic:before {
  content: "\e049"
}

.glyphicon-text-height:before {
  content: "\e050"
}

.glyphicon-text-width:before {
  content: "\e051"
}

.glyphicon-align-left:before {
  content: "\e052"
}

.glyphicon-align-center:before {
  content: "\e053"
}

.glyphicon-align-right:before {
  content: "\e054"
}

.glyphicon-align-justify:before {
  content: "\e055"
}

.glyphicon-list:before {
  content: "\e056"
}

.glyphicon-indent-left:before {
  content: "\e057"
}

.glyphicon-indent-right:before {
  content: "\e058"
}

.glyphicon-facetime-video:before {
  content: "\e059"
}

.glyphicon-picture:before {
  content: "\e060"
}

.glyphicon-map-marker:before {
  content: "\e062"
}

.glyphicon-adjust:before {
  content: "\e063"
}

.glyphicon-tint:before {
  content: "\e064"
}

.glyphicon-edit:before {
  content: "\e065"
}

.glyphicon-share:before {
  content: "\e066"
}

.glyphicon-check:before {
  content: "\e067"
}

.glyphicon-move:before {
  content: "\e068"
}

.glyphicon-step-backward:before {
  content: "\e069"
}

.glyphicon-fast-backward:before {
  content: "\e070"
}

.glyphicon-backward:before {
  content: "\e071"
}

.glyphicon-play:before {
  content: "\e072"
}

.glyphicon-pause:before {
  content: "\e073"
}

.glyphicon-stop:before {
  content: "\e074"
}

.glyphicon-forward:before {
  content: "\e075"
}

.glyphicon-fast-forward:before {
  content: "\e076"
}

.glyphicon-step-forward:before {
  content: "\e077"
}

.glyphicon-eject:before {
  content: "\e078"
}

.glyphicon-chevron-left:before {
  content: "\e079"
}

.glyphicon-chevron-right:before {
  content: "\e080"
}

.glyphicon-plus-sign:before {
  content: "\e081"
}

.glyphicon-minus-sign:before {
  content: "\e082"
}

.glyphicon-remove-sign:before {
  content: "\e083"
}

.glyphicon-ok-sign:before {
  content: "\e084"
}

.glyphicon-question-sign:before {
  content: "\e085"
}

.glyphicon-info-sign:before {
  content: "\e086"
}

.glyphicon-screenshot:before {
  content: "\e087"
}

.glyphicon-remove-circle:before {
  content: "\e088"
}

.glyphicon-ok-circle:before {
  content: "\e089"
}

.glyphicon-ban-circle:before {
  content: "\e090"
}

.glyphicon-arrow-left:before {
  content: "\e091"
}

.glyphicon-arrow-right:before {
  content: "\e092"
}

.glyphicon-arrow-up:before {
  content: "\e093"
}

.glyphicon-arrow-down:before {
  content: "\e094"
}

.glyphicon-share-alt:before {
  content: "\e095"
}

.glyphicon-resize-full:before {
  content: "\e096"
}

.glyphicon-resize-small:before {
  content: "\e097"
}

.glyphicon-exclamation-sign:before {
  content: "\e101"
}

.glyphicon-gift:before {
  content: "\e102"
}

.glyphicon-leaf:before {
  content: "\e103"
}

.glyphicon-fire:before {
  content: "\e104"
}

.glyphicon-eye-open:before {
  content: "\e105"
}

.glyphicon-eye-close:before {
  content: "\e106"
}

.glyphicon-warning-sign:before {
  content: "\e107"
}

.glyphicon-plane:before {
  content: "\e108"
}

.glyphicon-calendar:before {
  content: "\e109"
}

.glyphicon-random:before {
  content: "\e110"
}

.glyphicon-comment:before {
  content: "\e111"
}

.glyphicon-magnet:before {
  content: "\e112"
}

.glyphicon-chevron-up:before {
  content: "\e113"
}

.glyphicon-chevron-down:before {
  content: "\e114"
}

.glyphicon-retweet:before {
  content: "\e115"
}

.glyphicon-shopping-cart:before {
  content: "\e116"
}

.glyphicon-folder-close:before {
  content: "\e117"
}

.glyphicon-folder-open:before {
  content: "\e118"
}

.glyphicon-resize-vertical:before {
  content: "\e119"
}

.glyphicon-resize-horizontal:before {
  content: "\e120"
}

.glyphicon-hdd:before {
  content: "\e121"
}

.glyphicon-bullhorn:before {
  content: "\e122"
}

.glyphicon-bell:before {
  content: "\e123"
}

.glyphicon-certificate:before {
  content: "\e124"
}

.glyphicon-thumbs-up:before {
  content: "\e125"
}

.glyphicon-thumbs-down:before {
  content: "\e126"
}

.glyphicon-hand-right:before {
  content: "\e127"
}

.glyphicon-hand-left:before {
  content: "\e128"
}

.glyphicon-hand-up:before {
  content: "\e129"
}

.glyphicon-hand-down:before {
  content: "\e130"
}

.glyphicon-circle-arrow-right:before {
  content: "\e131"
}

.glyphicon-circle-arrow-left:before {
  content: "\e132"
}

.glyphicon-circle-arrow-up:before {
  content: "\e133"
}

.glyphicon-circle-arrow-down:before {
  content: "\e134"
}

.glyphicon-globe:before {
  content: "\e135"
}

.glyphicon-wrench:before {
  content: "\e136"
}

.glyphicon-tasks:before {
  content: "\e137"
}

.glyphicon-filter:before {
  content: "\e138"
}

.glyphicon-briefcase:before {
  content: "\e139"
}

.glyphicon-fullscreen:before {
  content: "\e140"
}

.glyphicon-dashboard:before {
  content: "\e141"
}

.glyphicon-paperclip:before {
  content: "\e142"
}

.glyphicon-heart-empty:before {
  content: "\e143"
}

.glyphicon-link:before {
  content: "\e144"
}

.glyphicon-phone:before {
  content: "\e145"
}

.glyphicon-pushpin:before {
  content: "\e146"
}

.glyphicon-usd:before {
  content: "\e148"
}

.glyphicon-gbp:before {
  content: "\e149"
}

.glyphicon-sort:before {
  content: "\e150"
}

.glyphicon-sort-by-alphabet:before {
  content: "\e151"
}

.glyphicon-sort-by-alphabet-alt:before {
  content: "\e152"
}

.glyphicon-sort-by-order:before {
  content: "\e153"
}

.glyphicon-sort-by-order-alt:before {
  content: "\e154"
}

.glyphicon-sort-by-attributes:before {
  content: "\e155"
}

.glyphicon-sort-by-attributes-alt:before {
  content: "\e156"
}

.glyphicon-unchecked:before {
  content: "\e157"
}

.glyphicon-expand:before {
  content: "\e158"
}

.glyphicon-collapse-down:before {
  content: "\e159"
}

.glyphicon-collapse-up:before {
  content: "\e160"
}

.glyphicon-log-in:before {
  content: "\e161"
}

.glyphicon-flash:before {
  content: "\e162"
}

.glyphicon-log-out:before {
  content: "\e163"
}

.glyphicon-new-window:before {
  content: "\e164"
}

.glyphicon-record:before {
  content: "\e165"
}

.glyphicon-save:before {
  content: "\e166"
}

.glyphicon-open:before {
  content: "\e167"
}

.glyphicon-saved:before {
  content: "\e168"
}

.glyphicon-import:before {
  content: "\e169"
}

.glyphicon-export:before {
  content: "\e170"
}

.glyphicon-send:before {
  content: "\e171"
}

.glyphicon-floppy-disk:before {
  content: "\e172"
}

.glyphicon-floppy-saved:before {
  content: "\e173"
}

.glyphicon-floppy-remove:before {
  content: "\e174"
}

.glyphicon-floppy-save:before {
  content: "\e175"
}

.glyphicon-floppy-open:before {
  content: "\e176"
}

.glyphicon-credit-card:before {
  content: "\e177"
}

.glyphicon-transfer:before {
  content: "\e178"
}

.glyphicon-cutlery:before {
  content: "\e179"
}

.glyphicon-header:before {
  content: "\e180"
}

.glyphicon-compressed:before {
  content: "\e181"
}

.glyphicon-earphone:before {
  content: "\e182"
}

.glyphicon-phone-alt:before {
  content: "\e183"
}

.glyphicon-tower:before {
  content: "\e184"
}

.glyphicon-stats:before {
  content: "\e185"
}

.glyphicon-sd-video:before {
  content: "\e186"
}

.glyphicon-hd-video:before {
  content: "\e187"
}

.glyphicon-subtitles:before {
  content: "\e188"
}

.glyphicon-sound-stereo:before {
  content: "\e189"
}

.glyphicon-sound-dolby:before {
  content: "\e190"
}

.glyphicon-sound-5-1:before {
  content: "\e191"
}

.glyphicon-sound-6-1:before {
  content: "\e192"
}

.glyphicon-sound-7-1:before {
  content: "\e193"
}

.glyphicon-copyright-mark:before {
  content: "\e194"
}

.glyphicon-registration-mark:before {
  content: "\e195"
}

.glyphicon-cloud-download:before {
  content: "\e197"
}

.glyphicon-cloud-upload:before {
  content: "\e198"
}

.glyphicon-tree-conifer:before {
  content: "\e199"
}

.glyphicon-tree-deciduous:before {
  content: "\e200"
}

.glyphicon-cd:before {
  content: "\e201"
}

.glyphicon-save-file:before {
  content: "\e202"
}

.glyphicon-open-file:before {
  content: "\e203"
}

.glyphicon-level-up:before {
  content: "\e204"
}

.glyphicon-copy:before {
  content: "\e205"
}

.glyphicon-paste:before {
  content: "\e206"
}

.glyphicon-alert:before {
  content: "\e209"
}

.glyphicon-equalizer:before {
  content: "\e210"
}

.glyphicon-king:before {
  content: "\e211"
}

.glyphicon-queen:before {
  content: "\e212"
}

.glyphicon-pawn:before {
  content: "\e213"
}

.glyphicon-bishop:before {
  content: "\e214"
}

.glyphicon-knight:before {
  content: "\e215"
}

.glyphicon-baby-formula:before {
  content: "\e216"
}

.glyphicon-tent:before {
  content: "\26fa"
}

.glyphicon-blackboard:before {
  content: "\e218"
}

.glyphicon-bed:before {
  content: "\e219"
}

.glyphicon-apple:before {
  content: "\f8ff"
}

.glyphicon-erase:before {
  content: "\e221"
}

.glyphicon-hourglass:before {
  content: "\231b"
}

.glyphicon-lamp:before {
  content: "\e223"
}

.glyphicon-duplicate:before {
  content: "\e224"
}

.glyphicon-piggy-bank:before {
  content: "\e225"
}

.glyphicon-scissors:before {
  content: "\e226"
}

.glyphicon-bitcoin:before {
  content: "\e227"
}

.glyphicon-btc:before {
  content: "\e227"
}

.glyphicon-xbt:before {
  content: "\e227"
}

.glyphicon-yen:before {
  content: "\00a5"
}

.glyphicon-jpy:before {
  content: "\00a5"
}

.glyphicon-ruble:before {
  content: "\20bd"
}

.glyphicon-rub:before {
  content: "\20bd"
}

.glyphicon-scale:before {
  content: "\e230"
}

.glyphicon-ice-lolly:before {
  content: "\e231"
}

.glyphicon-ice-lolly-tasted:before {
  content: "\e232"
}

.glyphicon-education:before {
  content: "\e233"
}

.glyphicon-option-horizontal:before {
  content: "\e234"
}

.glyphicon-option-vertical:before {
  content: "\e235"
}

.glyphicon-menu-hamburger:before {
  content: "\e236"
}

.glyphicon-modal-window:before {
  content: "\e237"
}

.glyphicon-oil:before {
  content: "\e238"
}

.glyphicon-grain:before {
  content: "\e239"
}

.glyphicon-sunglasses:before {
  content: "\e240"
}

.glyphicon-text-size:before {
  content: "\e241"
}

.glyphicon-text-color:before {
  content: "\e242"
}

.glyphicon-text-background:before {
  content: "\e243"
}

.glyphicon-object-align-top:before {
  content: "\e244"
}

.glyphicon-object-align-bottom:before {
  content: "\e245"
}

.glyphicon-object-align-horizontal:before {
  content: "\e246"
}

.glyphicon-object-align-left:before {
  content: "\e247"
}

.glyphicon-object-align-vertical:before {
  content: "\e248"
}

.glyphicon-object-align-right:before {
  content: "\e249"
}

.glyphicon-triangle-right:before {
  content: "\e250"
}

.glyphicon-triangle-left:before {
  content: "\e251"
}

.glyphicon-triangle-bottom:before {
  content: "\e252"
}

.glyphicon-triangle-top:before {
  content: "\e253"
}

.glyphicon-console:before {
  content: "\e254"
}

.glyphicon-superscript:before {
  content: "\e255"
}

.glyphicon-subscript:before {
  content: "\e256"
}

.glyphicon-menu-left:before {
  content: "\e257"
}

.glyphicon-menu-right:before {
  content: "\e258"
}

.glyphicon-menu-down:before {
  content: "\e259"
}

.glyphicon-menu-up:before {
  content: "\e260"
}

* {
  box-sizing: border-box
}

*:before,
*:after {
  box-sizing: border-box
}

html {
  font-size: 10px;
  -webkit-tap-highlight-color: transparent
}

body {
  font-family: "Roboto", Helvetica, Arial, sans-serif;
  font-size: 14px;
  line-height: 1.42857143;
  color: #333333;
  background-color: #F9F9F9
}

input,
button,
select,
textarea {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit
}

a {
  color: #CC0000;
  text-decoration: none
}

a:hover,
a:focus {
  color: maroon;
  text-decoration: none
}

a:focus {
  outline: thin dotted;
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px
}

figure {
  margin: 0
}

img {
  vertical-align: middle
}

.img-responsive {
  display: block;
  max-width: 100%;
  height: auto
}

.img-rounded {
  border-radius: 2px
}

.img-thumbnail {
  padding: 4px;
  line-height: 1.42857143;
  background-color: #F9F9F9;
  border: 1px solid #ddd;
  border-radius: 2px;
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  display: inline-block;
  max-width: 100%;
  height: auto
}

.img-circle {
  border-radius: 50%
}

hr {
  margin-top: 20px;
  margin-bottom: 20px;
  border: 0;
  border-top: 1px solid #F9F9F9
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0
}

.sr-only-focusable:active,
.sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  margin: 0;
  overflow: visible;
  clip: auto
}

[role="button"] {
  cursor: pointer
}

h1,
h2,
h3,
h4,
h5,
h6,
.h1,
.h2,
.h3,
.h4,
.h5,
.h6 {
  font-family: "Clubhaus", Helvetica, Arial, sans-serif;
  font-weight: 500;
  line-height: 1.1;
  color: inherit
}

h1 small,
h1 .small,
h2 small,
h2 .small,
h3 small,
h3 .small,
h4 small,
h4 .small,
h5 small,
h5 .small,
h6 small,
h6 .small,
.h1 small,
.h1 .small,
.h2 small,
.h2 .small,
.h3 small,
.h3 .small,
.h4 small,
.h4 .small,
.h5 small,
.h5 .small,
.h6 small,
.h6 .small {
  font-weight: normal;
  line-height: 1;
  color: #CCCCCC
}

h1,
.h1,
h2,
.h2,
h3,
.h3 {
  margin-top: 20px;
  margin-bottom: 10px
}

h1 small,
h1 .small,
.h1 small,
.h1 .small,
h2 small,
h2 .small,
.h2 small,
.h2 .small,
h3 small,
h3 .small,
.h3 small,
.h3 .small {
  font-size: 65%
}

h4,
.h4,
h5,
.h5,
h6,
.h6 {
  margin-top: 10px;
  margin-bottom: 10px
}

h4 small,
h4 .small,
.h4 small,
.h4 .small,
h5 small,
h5 .small,
.h5 small,
.h5 .small,
h6 small,
h6 .small,
.h6 small,
.h6 .small {
  font-size: 75%
}

h1,
.h1 {
  font-size: 36px
}

h2,
.h2 {
  font-size: 30px
}

h3,
.h3 {
  font-size: 24px
}

h4,
.h4 {
  font-size: 18px
}

h5,
.h5 {
  font-size: 14px
}

h6,
.h6 {
  font-size: 12px
}

p {
  margin: 0 0 10px
}

.lead {
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 300;
  line-height: 1.4
}

@media (min-width: 768px) {
  .lead {
    font-size: 21px
  }
}

small,
.small {
  font-size: 85%
}

mark,
.mark {
  background-color: #fcf8e3;
  padding: .2em
}

.text-left {
  text-align: left
}

.text-right {
  text-align: right
}

.text-center {
  text-align: center
}

.text-justify {
  text-align: justify
}

.text-nowrap {
  white-space: nowrap
}

.text-lowercase {
  text-transform: lowercase
}

.text-uppercase,
.initialism {
  text-transform: uppercase
}

.text-capitalize {
  text-transform: capitalize
}

.text-muted {
  color: #CCCCCC
}

.text-primary {
  color: #CC0000
}

a.text-primary:hover,
a.text-primary:focus {
  color: #990000
}

.text-success {
  color: #fff
}

a.text-success:hover,
a.text-success:focus {
  color: #e6e6e6
}

.text-info {
  color: #31708f
}

a.text-info:hover,
a.text-info:focus {
  color: #245269
}

.text-warning {
  color: #8a6d3b
}

a.text-warning:hover,
a.text-warning:focus {
  color: #66512c
}

.text-danger {
  color: #a94442
}

a.text-danger:hover,
a.text-danger:focus {
  color: #843534
}

.bg-primary {
  color: #fff
}

.bg-primary {
  background-color: #CC0000
}

a.bg-primary:hover,
a.bg-primary:focus {
  background-color: #990000
}

.bg-success {
  background-color: #06851F
}

a.bg-success:hover,
a.bg-success:focus {
  background-color: #045414
}

.bg-info {
  background-color: #d9edf7
}

a.bg-info:hover,
a.bg-info:focus {
  background-color: #afd9ee
}

.bg-warning {
  background-color: #fcf8e3
}

a.bg-warning:hover,
a.bg-warning:focus {
  background-color: #f7ecb5
}

.bg-danger {
  background-color: #f2dede
}

a.bg-danger:hover,
a.bg-danger:focus {
  background-color: #e4b9b9
}

.page-header {
  padding-bottom: 9px;
  margin: 40px 0 20px;
  border-bottom: 1px solid #F9F9F9
}

ul,
ol {
  margin-top: 0;
  margin-bottom: 10px
}

ul ul,
ul ol,
ol ul,
ol ol {
  margin-bottom: 0
}

.list-unstyled {
  padding-left: 0;
  list-style: none
}

.list-inline {
  padding-left: 0;
  list-style: none;
  margin-left: -5px
}

.list-inline>li {
  display: inline-block;
  padding-left: 5px;
  padding-right: 5px
}

dl {
  margin-top: 0;
  margin-bottom: 20px
}

dt,
dd {
  line-height: 1.42857143
}

dt {
  font-weight: bold
}

dd {
  margin-left: 0
}

.dl-horizontal dd:before,
.dl-horizontal dd:after {
  content: " ";
  display: table
}

.dl-horizontal dd:after {
  clear: both
}

@media (min-width: 768px) {
  .dl-horizontal dt {
    float: left;
    width: 160px;
    clear: left;
    text-align: right;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
  }

  .dl-horizontal dd {
    margin-left: 180px
  }
}

abbr[title],
abbr[data-original-title] {
  cursor: help;
  border-bottom: 1px dotted #E2E2E2
}

.initialism {
  font-size: 90%
}

blockquote {
  padding: 10px 20px;
  margin: 0 0 20px;
  font-size: 17.5px;
  border-left: 5px solid #F9F9F9
}

blockquote p:last-child,
blockquote ul:last-child,
blockquote ol:last-child {
  margin-bottom: 0
}

blockquote footer,
blockquote small,
blockquote .small {
  display: block;
  font-size: 80%;
  line-height: 1.42857143;
  color: #CCCCCC
}

blockquote footer:before,
blockquote small:before,
blockquote .small:before {
  content: '\2014 \00A0'
}

.blockquote-reverse,
blockquote.pull-right {
  padding-right: 15px;
  padding-left: 0;
  border-right: 5px solid #F9F9F9;
  border-left: 0;
  text-align: right
}

.blockquote-reverse footer:before,
.blockquote-reverse small:before,
.blockquote-reverse .small:before,
blockquote.pull-right footer:before,
blockquote.pull-right small:before,
blockquote.pull-right .small:before {
  content: ''
}

.blockquote-reverse footer:after,
.blockquote-reverse small:after,
.blockquote-reverse .small:after,
blockquote.pull-right footer:after,
blockquote.pull-right small:after,
blockquote.pull-right .small:after {
  content: '\00A0 \2014'
}

address {
  margin-bottom: 20px;
  font-style: normal;
  line-height: 1.42857143
}

code,
kbd,
pre,
samp {
  font-family: Menlo, Monaco, Consolas, "Courier New", monospace
}

code {
  padding: 2px 4px;
  font-size: 90%;
  color: #c7254e;
  background-color: #f9f2f4;
  border-radius: 2px
}

kbd {
  padding: 2px 4px;
  font-size: 90%;
  color: #fff;
  background-color: #333;
  border-radius: 2px;
  box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.25)
}

kbd kbd {
  padding: 0;
  font-size: 100%;
  font-weight: bold;
  box-shadow: none
}

pre {
  display: block;
  padding: 9.5px;
  margin: 0 0 10px;
  font-size: 13px;
  line-height: 1.42857143;
  word-break: break-all;
  word-wrap: break-word;
  color: #979797;
  background-color: #f5f5f5;
  border: 1px solid #ccc;
  border-radius: 2px
}

pre code {
  padding: 0;
  font-size: inherit;
  color: inherit;
  white-space: pre-wrap;
  background-color: transparent;
  border-radius: 0
}

.pre-scrollable {
  max-height: 340px;
  overflow-y: scroll
}

.container {
  margin-right: auto;
  margin-left: auto;
  padding-left: 15px;
  padding-right: 15px
}

.container:before,
.container:after {
  content: " ";
  display: table
}

.container:after {
  clear: both
}

@media (min-width: 768px) {
  .container {
    width: 750px
  }
}

@media (min-width: 992px) {
  .container {
    width: 970px
  }
}

@media (min-width: 1200px) {
  .container {
    width: 1170px
  }
}

.container-fluid {
  margin-right: auto;
  margin-left: auto;
  padding-left: 15px;
  padding-right: 15px
}

.container-fluid:before,
.container-fluid:after {
  content: " ";
  display: table
}

.container-fluid:after {
  clear: both
}

.row {
  margin-left: -15px;
  margin-right: -15px
}

.row:before,
.row:after {
  content: " ";
  display: table
}

.row:after {
  clear: both
}

.col-xs-1,
.col-sm-1,
.col-md-1,
.col-lg-1,
.col-xs-2,
.col-sm-2,
.col-md-2,
.col-lg-2,
.col-xs-3,
.col-sm-3,
.col-md-3,
.col-lg-3,
.col-xs-4,
.col-sm-4,
.col-md-4,
.col-lg-4,
.col-xs-5,
.col-sm-5,
.col-md-5,
.col-lg-5,
.col-xs-6,
.col-sm-6,
.col-md-6,
.col-lg-6,
.col-xs-7,
.col-sm-7,
.col-md-7,
.col-lg-7,
.col-xs-8,
.col-sm-8,
.col-md-8,
.col-lg-8,
.col-xs-9,
.col-sm-9,
.col-md-9,
.col-lg-9,
.col-xs-10,
.col-sm-10,
.col-md-10,
.col-lg-10,
.col-xs-11,
.col-sm-11,
.col-md-11,
.col-lg-11,
.col-xs-12,
.col-sm-12,
.col-md-12,
.col-lg-12 {
  position: relative;
  min-height: 1px;
  padding-left: 15px;
  padding-right: 15px
}

.col-xs-1,
.col-xs-2,
.col-xs-3,
.col-xs-4,
.col-xs-5,
.col-xs-6,
.col-xs-7,
.col-xs-8,
.col-xs-9,
.col-xs-10,
.col-xs-11,
.col-xs-12 {
  float: left
}

.col-xs-1 {
  width: 8.33333333%
}

.col-xs-2 {
  width: 16.66666667%
}

.col-xs-3 {
  width: 25%
}

.col-xs-4 {
  width: 33.33333333%
}

.col-xs-5 {
  width: 41.66666667%
}

.col-xs-6 {
  width: 50%
}

.col-xs-7 {
  width: 58.33333333%
}

.col-xs-8 {
  width: 66.66666667%
}

.col-xs-9 {
  width: 75%
}

.col-xs-10 {
  width: 83.33333333%
}

.col-xs-11 {
  width: 91.66666667%
}

.col-xs-12 {
  width: 100%
}

.col-xs-pull-0 {
  right: auto
}

.col-xs-pull-1 {
  right: 8.33333333%
}

.col-xs-pull-2 {
  right: 16.66666667%
}

.col-xs-pull-3 {
  right: 25%
}

.col-xs-pull-4 {
  right: 33.33333333%
}

.col-xs-pull-5 {
  right: 41.66666667%
}

.col-xs-pull-6 {
  right: 50%
}

.col-xs-pull-7 {
  right: 58.33333333%
}

.col-xs-pull-8 {
  right: 66.66666667%
}

.col-xs-pull-9 {
  right: 75%
}

.col-xs-pull-10 {
  right: 83.33333333%
}

.col-xs-pull-11 {
  right: 91.66666667%
}

.col-xs-pull-12 {
  right: 100%
}

.col-xs-push-0 {
  left: auto
}

.col-xs-push-1 {
  left: 8.33333333%
}

.col-xs-push-2 {
  left: 16.66666667%
}

.col-xs-push-3 {
  left: 25%
}

.col-xs-push-4 {
  left: 33.33333333%
}

.col-xs-push-5 {
  left: 41.66666667%
}

.col-xs-push-6 {
  left: 50%
}

.col-xs-push-7 {
  left: 58.33333333%
}

.col-xs-push-8 {
  left: 66.66666667%
}

.col-xs-push-9 {
  left: 75%
}

.col-xs-push-10 {
  left: 83.33333333%
}

.col-xs-push-11 {
  left: 91.66666667%
}

.col-xs-push-12 {
  left: 100%
}

.col-xs-offset-0 {
  margin-left: 0%
}

.col-xs-offset-1 {
  margin-left: 8.33333333%
}

.col-xs-offset-2 {
  margin-left: 16.66666667%
}

.col-xs-offset-3 {
  margin-left: 25%
}

.col-xs-offset-4 {
  margin-left: 33.33333333%
}

.col-xs-offset-5 {
  margin-left: 41.66666667%
}

.col-xs-offset-6 {
  margin-left: 50%
}

.col-xs-offset-7 {
  margin-left: 58.33333333%
}

.col-xs-offset-8 {
  margin-left: 66.66666667%
}

.col-xs-offset-9 {
  margin-left: 75%
}

.col-xs-offset-10 {
  margin-left: 83.33333333%
}

.col-xs-offset-11 {
  margin-left: 91.66666667%
}

.col-xs-offset-12 {
  margin-left: 100%
}

@media (min-width: 768px) {

  .col-sm-1,
  .col-sm-2,
  .col-sm-3,
  .col-sm-4,
  .col-sm-5,
  .col-sm-6,
  .col-sm-7,
  .col-sm-8,
  .col-sm-9,
  .col-sm-10,
  .col-sm-11,
  .col-sm-12 {
    float: left
  }

  .col-sm-1 {
    width: 8.33333333%
  }

  .col-sm-2 {
    width: 16.66666667%
  }

  .col-sm-3 {
    width: 25%
  }

  .col-sm-4 {
    width: 33.33333333%
  }

  .col-sm-5 {
    width: 41.66666667%
  }

  .col-sm-6 {
    width: 50%
  }

  .col-sm-7 {
    width: 58.33333333%
  }

  .col-sm-8 {
    width: 66.66666667%
  }

  .col-sm-9 {
    width: 75%
  }

  .col-sm-10 {
    width: 83.33333333%
  }

  .col-sm-11 {
    width: 91.66666667%
  }

  .col-sm-12 {
    width: 100%
  }

  .col-sm-pull-0 {
    right: auto
  }

  .col-sm-pull-1 {
    right: 8.33333333%
  }

  .col-sm-pull-2 {
    right: 16.66666667%
  }

  .col-sm-pull-3 {
    right: 25%
  }

  .col-sm-pull-4 {
    right: 33.33333333%
  }

  .col-sm-pull-5 {
    right: 41.66666667%
  }

  .col-sm-pull-6 {
    right: 50%
  }

  .col-sm-pull-7 {
    right: 58.33333333%
  }

  .col-sm-pull-8 {
    right: 66.66666667%
  }

  .col-sm-pull-9 {
    right: 75%
  }

  .col-sm-pull-10 {
    right: 83.33333333%
  }

  .col-sm-pull-11 {
    right: 91.66666667%
  }

  .col-sm-pull-12 {
    right: 100%
  }

  .col-sm-push-0 {
    left: auto
  }

  .col-sm-push-1 {
    left: 8.33333333%
  }

  .col-sm-push-2 {
    left: 16.66666667%
  }

  .col-sm-push-3 {
    left: 25%
  }

  .col-sm-push-4 {
    left: 33.33333333%
  }

  .col-sm-push-5 {
    left: 41.66666667%
  }

  .col-sm-push-6 {
    left: 50%
  }

  .col-sm-push-7 {
    left: 58.33333333%
  }

  .col-sm-push-8 {
    left: 66.66666667%
  }

  .col-sm-push-9 {
    left: 75%
  }

  .col-sm-push-10 {
    left: 83.33333333%
  }

  .col-sm-push-11 {
    left: 91.66666667%
  }

  .col-sm-push-12 {
    left: 100%
  }

  .col-sm-offset-0 {
    margin-left: 0%
  }

  .col-sm-offset-1 {
    margin-left: 8.33333333%
  }

  .col-sm-offset-2 {
    margin-left: 16.66666667%
  }

  .col-sm-offset-3 {
    margin-left: 25%
  }

  .col-sm-offset-4 {
    margin-left: 33.33333333%
  }

  .col-sm-offset-5 {
    margin-left: 41.66666667%
  }

  .col-sm-offset-6 {
    margin-left: 50%
  }

  .col-sm-offset-7 {
    margin-left: 58.33333333%
  }

  .col-sm-offset-8 {
    margin-left: 66.66666667%
  }

  .col-sm-offset-9 {
    margin-left: 75%
  }

  .col-sm-offset-10 {
    margin-left: 83.33333333%
  }

  .col-sm-offset-11 {
    margin-left: 91.66666667%
  }

  .col-sm-offset-12 {
    margin-left: 100%
  }
}

@media (min-width: 992px) {

  .col-md-1,
  .col-md-2,
  .col-md-3,
  .col-md-4,
  .col-md-5,
  .col-md-6,
  .col-md-7,
  .col-md-8,
  .col-md-9,
  .col-md-10,
  .col-md-11,
  .col-md-12 {
    float: left
  }

  .col-md-1 {
    width: 8.33333333%
  }

  .col-md-2 {
    width: 16.66666667%
  }

  .col-md-3 {
    width: 25%
  }

  .col-md-4 {
    width: 33.33333333%
  }

  .col-md-5 {
    width: 41.66666667%
  }

  .col-md-6 {
    width: 50%
  }

  .col-md-7 {
    width: 58.33333333%
  }

  .col-md-8 {
    width: 66.66666667%
  }

  .col-md-9 {
    width: 75%
  }

  .col-md-10 {
    width: 83.33333333%
  }

  .col-md-11 {
    width: 91.66666667%
  }

  .col-md-12 {
    width: 100%
  }

  .col-md-pull-0 {
    right: auto
  }

  .col-md-pull-1 {
    right: 8.33333333%
  }

  .col-md-pull-2 {
    right: 16.66666667%
  }

  .col-md-pull-3 {
    right: 25%
  }

  .col-md-pull-4 {
    right: 33.33333333%
  }

  .col-md-pull-5 {
    right: 41.66666667%
  }

  .col-md-pull-6 {
    right: 50%
  }

  .col-md-pull-7 {
    right: 58.33333333%
  }

  .col-md-pull-8 {
    right: 66.66666667%
  }

  .col-md-pull-9 {
    right: 75%
  }

  .col-md-pull-10 {
    right: 83.33333333%
  }

  .col-md-pull-11 {
    right: 91.66666667%
  }

  .col-md-pull-12 {
    right: 100%
  }

  .col-md-push-0 {
    left: auto
  }

  .col-md-push-1 {
    left: 8.33333333%
  }

  .col-md-push-2 {
    left: 16.66666667%
  }

  .col-md-push-3 {
    left: 25%
  }

  .col-md-push-4 {
    left: 33.33333333%
  }

  .col-md-push-5 {
    left: 41.66666667%
  }

  .col-md-push-6 {
    left: 50%
  }

  .col-md-push-7 {
    left: 58.33333333%
  }

  .col-md-push-8 {
    left: 66.66666667%
  }

  .col-md-push-9 {
    left: 75%
  }

  .col-md-push-10 {
    left: 83.33333333%
  }

  .col-md-push-11 {
    left: 91.66666667%
  }

  .col-md-push-12 {
    left: 100%
  }

  .col-md-offset-0 {
    margin-left: 0%
  }

  .col-md-offset-1 {
    margin-left: 8.33333333%
  }

  .col-md-offset-2 {
    margin-left: 16.66666667%
  }

  .col-md-offset-3 {
    margin-left: 25%
  }

  .col-md-offset-4 {
    margin-left: 33.33333333%
  }

  .col-md-offset-5 {
    margin-left: 41.66666667%
  }

  .col-md-offset-6 {
    margin-left: 50%
  }

  .col-md-offset-7 {
    margin-left: 58.33333333%
  }

  .col-md-offset-8 {
    margin-left: 66.66666667%
  }

  .col-md-offset-9 {
    margin-left: 75%
  }

  .col-md-offset-10 {
    margin-left: 83.33333333%
  }

  .col-md-offset-11 {
    margin-left: 91.66666667%
  }

  .col-md-offset-12 {
    margin-left: 100%
  }
}

@media (min-width: 1200px) {

  .col-lg-1,
  .col-lg-2,
  .col-lg-3,
  .col-lg-4,
  .col-lg-5,
  .col-lg-6,
  .col-lg-7,
  .col-lg-8,
  .col-lg-9,
  .col-lg-10,
  .col-lg-11,
  .col-lg-12 {
    float: left
  }

  .col-lg-1 {
    width: 8.33333333%
  }

  .col-lg-2 {
    width: 16.66666667%
  }

  .col-lg-3 {
    width: 25%
  }

  .col-lg-4 {
    width: 33.33333333%
  }

  .col-lg-5 {
    width: 41.66666667%
  }

  .col-lg-6 {
    width: 50%
  }

  .col-lg-7 {
    width: 58.33333333%
  }

  .col-lg-8 {
    width: 66.66666667%
  }

  .col-lg-9 {
    width: 75%
  }

  .col-lg-10 {
    width: 83.33333333%
  }

  .col-lg-11 {
    width: 91.66666667%
  }

  .col-lg-12 {
    width: 100%
  }

  .col-lg-pull-0 {
    right: auto
  }

  .col-lg-pull-1 {
    right: 8.33333333%
  }

  .col-lg-pull-2 {
    right: 16.66666667%
  }

  .col-lg-pull-3 {
    right: 25%
  }

  .col-lg-pull-4 {
    right: 33.33333333%
  }

  .col-lg-pull-5 {
    right: 41.66666667%
  }

  .col-lg-pull-6 {
    right: 50%
  }

  .col-lg-pull-7 {
    right: 58.33333333%
  }

  .col-lg-pull-8 {
    right: 66.66666667%
  }

  .col-lg-pull-9 {
    right: 75%
  }

  .col-lg-pull-10 {
    right: 83.33333333%
  }

  .col-lg-pull-11 {
    right: 91.66666667%
  }

  .col-lg-pull-12 {
    right: 100%
  }

  .col-lg-push-0 {
    left: auto
  }

  .col-lg-push-1 {
    left: 8.33333333%
  }

  .col-lg-push-2 {
    left: 16.66666667%
  }

  .col-lg-push-3 {
    left: 25%
  }

  .col-lg-push-4 {
    left: 33.33333333%
  }

  .col-lg-push-5 {
    left: 41.66666667%
  }

  .col-lg-push-6 {
    left: 50%
  }

  .col-lg-push-7 {
    left: 58.33333333%
  }

  .col-lg-push-8 {
    left: 66.66666667%
  }

  .col-lg-push-9 {
    left: 75%
  }

  .col-lg-push-10 {
    left: 83.33333333%
  }

  .col-lg-push-11 {
    left: 91.66666667%
  }

  .col-lg-push-12 {
    left: 100%
  }

  .col-lg-offset-0 {
    margin-left: 0%
  }

  .col-lg-offset-1 {
    margin-left: 8.33333333%
  }

  .col-lg-offset-2 {
    margin-left: 16.66666667%
  }

  .col-lg-offset-3 {
    margin-left: 25%
  }

  .col-lg-offset-4 {
    margin-left: 33.33333333%
  }

  .col-lg-offset-5 {
    margin-left: 41.66666667%
  }

  .col-lg-offset-6 {
    margin-left: 50%
  }

  .col-lg-offset-7 {
    margin-left: 58.33333333%
  }

  .col-lg-offset-8 {
    margin-left: 66.66666667%
  }

  .col-lg-offset-9 {
    margin-left: 75%
  }

  .col-lg-offset-10 {
    margin-left: 83.33333333%
  }

  .col-lg-offset-11 {
    margin-left: 91.66666667%
  }

  .col-lg-offset-12 {
    margin-left: 100%
  }
}

table {
  background-color: #fff
}

caption {
  padding-top: 8px;
  padding-bottom: 8px;
  color: #CCCCCC;
  text-align: left
}

th {
  text-align: left
}

.table {
  width: 100%;
  max-width: 100%;
  margin-bottom: 20px
}

.table>thead>tr>th,
.table>thead>tr>td,
.table>tbody>tr>th,
.table>tbody>tr>td,
.table>tfoot>tr>th,
.table>tfoot>tr>td {
  padding: 8px;
  line-height: 1.42857143;
  vertical-align: top;
  border-top: 1px solid #CCCCCC
}

.table>thead>tr>th {
  vertical-align: bottom;
  border-bottom: 2px solid #CCCCCC
}

.table>caption+thead>tr:first-child>th,
.table>caption+thead>tr:first-child>td,
.table>colgroup+thead>tr:first-child>th,
.table>colgroup+thead>tr:first-child>td,
.table>thead:first-child>tr:first-child>th,
.table>thead:first-child>tr:first-child>td {
  border-top: 0
}

.table>tbody+tbody {
  border-top: 2px solid #CCCCCC
}

.table .table {
  background-color: #F9F9F9
}

.table-condensed>thead>tr>th,
.table-condensed>thead>tr>td,
.table-condensed>tbody>tr>th,
.table-condensed>tbody>tr>td,
.table-condensed>tfoot>tr>th,
.table-condensed>tfoot>tr>td {
  padding: 5px
}

.table-bordered {
  border: 1px solid #CCCCCC
}

.table-bordered>thead>tr>th,
.table-bordered>thead>tr>td,
.table-bordered>tbody>tr>th,
.table-bordered>tbody>tr>td,
.table-bordered>tfoot>tr>th,
.table-bordered>tfoot>tr>td {
  border: 1px solid #CCCCCC
}

.table-bordered>thead>tr>th,
.table-bordered>thead>tr>td {
  border-bottom-width: 2px
}

.table-striped>tbody>tr:nth-of-type(odd) {
  background-color: #f9f9f9
}

.table-hover>tbody>tr:hover {
  background-color: #f5f5f5
}

table col[class*="col-"] {
  position: static;
  float: none;
  display: table-column
}

table td[class*="col-"],
table th[class*="col-"] {
  position: static;
  float: none;
  display: table-cell
}

.table>thead>tr>td.active,
.table>thead>tr>th.active,
.table>thead>tr.active>td,
.table>thead>tr.active>th,
.table>tbody>tr>td.active,
.table>tbody>tr>th.active,
.table>tbody>tr.active>td,
.table>tbody>tr.active>th,
.table>tfoot>tr>td.active,
.table>tfoot>tr>th.active,
.table>tfoot>tr.active>td,
.table>tfoot>tr.active>th {
  background-color: #f5f5f5
}

.table-hover>tbody>tr>td.active:hover,
.table-hover>tbody>tr>th.active:hover,
.table-hover>tbody>tr.active:hover>td,
.table-hover>tbody>tr:hover>.active,
.table-hover>tbody>tr.active:hover>th {
  background-color: #e8e8e8
}

.table>thead>tr>td.success,
.table>thead>tr>th.success,
.table>thead>tr.success>td,
.table>thead>tr.success>th,
.table>tbody>tr>td.success,
.table>tbody>tr>th.success,
.table>tbody>tr.success>td,
.table>tbody>tr.success>th,
.table>tfoot>tr>td.success,
.table>tfoot>tr>th.success,
.table>tfoot>tr.success>td,
.table>tfoot>tr.success>th {
  background-color: #06851F
}

.table-hover>tbody>tr>td.success:hover,
.table-hover>tbody>tr>th.success:hover,
.table-hover>tbody>tr.success:hover>td,
.table-hover>tbody>tr:hover>.success,
.table-hover>tbody>tr.success:hover>th {
  background-color: #056d19
}

.table>thead>tr>td.info,
.table>thead>tr>th.info,
.table>thead>tr.info>td,
.table>thead>tr.info>th,
.table>tbody>tr>td.info,
.table>tbody>tr>th.info,
.table>tbody>tr.info>td,
.table>tbody>tr.info>th,
.table>tfoot>tr>td.info,
.table>tfoot>tr>th.info,
.table>tfoot>tr.info>td,
.table>tfoot>tr.info>th {
  background-color: #d9edf7
}

.table-hover>tbody>tr>td.info:hover,
.table-hover>tbody>tr>th.info:hover,
.table-hover>tbody>tr.info:hover>td,
.table-hover>tbody>tr:hover>.info,
.table-hover>tbody>tr.info:hover>th {
  background-color: #c4e3f3
}

.table>thead>tr>td.warning,
.table>thead>tr>th.warning,
.table>thead>tr.warning>td,
.table>thead>tr.warning>th,
.table>tbody>tr>td.warning,
.table>tbody>tr>th.warning,
.table>tbody>tr.warning>td,
.table>tbody>tr.warning>th,
.table>tfoot>tr>td.warning,
.table>tfoot>tr>th.warning,
.table>tfoot>tr.warning>td,
.table>tfoot>tr.warning>th {
  background-color: #fcf8e3
}

.table-hover>tbody>tr>td.warning:hover,
.table-hover>tbody>tr>th.warning:hover,
.table-hover>tbody>tr.warning:hover>td,
.table-hover>tbody>tr:hover>.warning,
.table-hover>tbody>tr.warning:hover>th {
  background-color: #faf2cc
}

.table>thead>tr>td.danger,
.table>thead>tr>th.danger,
.table>thead>tr.danger>td,
.table>thead>tr.danger>th,
.table>tbody>tr>td.danger,
.table>tbody>tr>th.danger,
.table>tbody>tr.danger>td,
.table>tbody>tr.danger>th,
.table>tfoot>tr>td.danger,
.table>tfoot>tr>th.danger,
.table>tfoot>tr.danger>td,
.table>tfoot>tr.danger>th {
  background-color: #f2dede
}

.table-hover>tbody>tr>td.danger:hover,
.table-hover>tbody>tr>th.danger:hover,
.table-hover>tbody>tr.danger:hover>td,
.table-hover>tbody>tr:hover>.danger,
.table-hover>tbody>tr.danger:hover>th {
  background-color: #ebcccc
}

.table-responsive {
  overflow-x: auto;
  min-height: 0.01%
}

@media screen and (max-width: 767px) {
  .table-responsive {
    width: 100%;
    margin-bottom: 15px;
    overflow-y: hidden;
    -ms-overflow-style: -ms-autohiding-scrollbar;
    border: 1px solid #CCCCCC
  }

  .table-responsive>.table {
    margin-bottom: 0
  }

  .table-responsive>.table>thead>tr>th,
  .table-responsive>.table>thead>tr>td,
  .table-responsive>.table>tbody>tr>th,
  .table-responsive>.table>tbody>tr>td,
  .table-responsive>.table>tfoot>tr>th,
  .table-responsive>.table>tfoot>tr>td {
    white-space: nowrap
  }

  .table-responsive>.table-bordered {
    border: 0
  }

  .table-responsive>.table-bordered>thead>tr>th:first-child,
  .table-responsive>.table-bordered>thead>tr>td:first-child,
  .table-responsive>.table-bordered>tbody>tr>th:first-child,
  .table-responsive>.table-bordered>tbody>tr>td:first-child,
  .table-responsive>.table-bordered>tfoot>tr>th:first-child,
  .table-responsive>.table-bordered>tfoot>tr>td:first-child {
    border-left: 0
  }

  .table-responsive>.table-bordered>thead>tr>th:last-child,
  .table-responsive>.table-bordered>thead>tr>td:last-child,
  .table-responsive>.table-bordered>tbody>tr>th:last-child,
  .table-responsive>.table-bordered>tbody>tr>td:last-child,
  .table-responsive>.table-bordered>tfoot>tr>th:last-child,
  .table-responsive>.table-bordered>tfoot>tr>td:last-child {
    border-right: 0
  }

  .table-responsive>.table-bordered>tbody>tr:last-child>th,
  .table-responsive>.table-bordered>tbody>tr:last-child>td,
  .table-responsive>.table-bordered>tfoot>tr:last-child>th,
  .table-responsive>.table-bordered>tfoot>tr:last-child>td {
    border-bottom: 0
  }
}

fieldset {
  padding: 0;
  margin: 0;
  border: 0;
  min-width: 0
}

legend {
  display: block;
  width: 100%;
  padding: 0;
  margin-bottom: 20px;
  font-size: 21px;
  line-height: inherit;
  color: #979797;
  border: 0;
  border-bottom: 1px solid #e5e5e5
}

label {
  display: inline-block;
  max-width: 100%;
  margin-bottom: 5px;
  font-weight: bold
}

input[type="search"] {
  box-sizing: border-box
}

input[type="radio"],
input[type="checkbox"] {
  margin: 4px 0 0;
  margin-top: 1px \9;
  line-height: normal
}

input[type="file"] {
  display: block
}

input[type="range"] {
  display: block;
  width: 100%
}

select[multiple],
select[size] {
  height: auto
}

input[type="file"]:focus,
input[type="radio"]:focus,
input[type="checkbox"]:focus {
  outline: thin dotted;
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px
}

output {
  display: block;
  padding-top: 7px;
  font-size: 14px;
  line-height: 1.42857143;
  color: #CCCCCC
}

.form-control {
  display: block;
  width: 100%;
  height: 34px;
  padding: 6px 12px;
  font-size: 14px;
  line-height: 1.42857143;
  color: #CCCCCC;
  background-color: #fff;
  background-image: none;
  border: 1px solid #ccc;
  border-radius: 0;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  -webkit-transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
  transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s
}

.form-control:focus {
  border-color: #CC0000;
  outline: 0;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(204, 0, 0, 0.6)
}

.form-control::-moz-placeholder {
  color: #CCCCCC;
  opacity: 1
}

.form-control:-ms-input-placeholder {
  color: #CCCCCC
}

.form-control::-webkit-input-placeholder {
  color: #CCCCCC
}

.form-control[disabled],
.form-control[readonly],
fieldset[disabled] .form-control {
  background-color: #fff;
  opacity: 1
}

.form-control[disabled],
fieldset[disabled] .form-control {
  cursor: auto
}

textarea.form-control {
  height: auto
}

input[type="search"] {
  -webkit-appearance: none
}

@media screen and (-webkit-min-device-pixel-ratio: 0) {

  input[type="date"].form-control,
  input[type="time"].form-control,
  input[type="datetime-local"].form-control,
  input[type="month"].form-control {
    line-height: 34px
  }

  input[type="date"].input-sm,
  .input-group-sm>input[type="date"].form-control,
  .input-group-sm>input[type="date"].input-group-addon,
  .input-group-sm>.input-group-btn>input[type="date"].btn,
  .input-group-sm input[type="date"],
  input[type="time"].input-sm,
  .input-group-sm>input[type="time"].form-control,
  .input-group-sm>input[type="time"].input-group-addon,
  .input-group-sm>.input-group-btn>input[type="time"].btn,
  .input-group-sm input[type="time"],
  input[type="datetime-local"].input-sm,
  .input-group-sm>input[type="datetime-local"].form-control,
  .input-group-sm>input[type="datetime-local"].input-group-addon,
  .input-group-sm>.input-group-btn>input[type="datetime-local"].btn,
  .input-group-sm input[type="datetime-local"],
  input[type="month"].input-sm,
  .input-group-sm>input[type="month"].form-control,
  .input-group-sm>input[type="month"].input-group-addon,
  .input-group-sm>.input-group-btn>input[type="month"].btn,
  .input-group-sm input[type="month"] {
    line-height: 30px
  }

  input[type="date"].input-lg,
  .input-group-lg>input[type="date"].form-control,
  .input-group-lg>input[type="date"].input-group-addon,
  .input-group-lg>.input-group-btn>input[type="date"].btn,
  .input-group-lg input[type="date"],
  input[type="time"].input-lg,
  .input-group-lg>input[type="time"].form-control,
  .input-group-lg>input[type="time"].input-group-addon,
  .input-group-lg>.input-group-btn>input[type="time"].btn,
  .input-group-lg input[type="time"],
  input[type="datetime-local"].input-lg,
  .input-group-lg>input[type="datetime-local"].form-control,
  .input-group-lg>input[type="datetime-local"].input-group-addon,
  .input-group-lg>.input-group-btn>input[type="datetime-local"].btn,
  .input-group-lg input[type="datetime-local"],
  input[type="month"].input-lg,
  .input-group-lg>input[type="month"].form-control,
  .input-group-lg>input[type="month"].input-group-addon,
  .input-group-lg>.input-group-btn>input[type="month"].btn,
  .input-group-lg input[type="month"] {
    line-height: 57px
  }
}

.form-group {
  margin-bottom: 15px
}

.radio,
.checkbox {
  position: relative;
  display: block;
  margin-top: 10px;
  margin-bottom: 10px
}

.radio label,
.checkbox label {
  min-height: 20px;
  padding-left: 20px;
  margin-bottom: 0;
  font-weight: normal;
  cursor: pointer
}

.radio input[type="radio"],
.radio-inline input[type="radio"],
.checkbox input[type="checkbox"],
.checkbox-inline input[type="checkbox"] {
  position: absolute;
  margin-left: -20px;
  margin-top: 4px \9
}

.radio+.radio,
.checkbox+.checkbox {
  margin-top: -5px
}

.radio-inline,
.checkbox-inline {
  position: relative;
  display: inline-block;
  padding-left: 20px;
  margin-bottom: 0;
  vertical-align: middle;
  font-weight: normal;
  cursor: pointer
}

.radio-inline+.radio-inline,
.checkbox-inline+.checkbox-inline {
  margin-top: 0;
  margin-left: 10px
}

input[type="radio"][disabled],
input[type="radio"].disabled,
fieldset[disabled] input[type="radio"],
input[type="checkbox"][disabled],
input[type="checkbox"].disabled,
fieldset[disabled] input[type="checkbox"] {
  cursor: auto
}

.radio-inline.disabled,
fieldset[disabled] .radio-inline,
.checkbox-inline.disabled,
fieldset[disabled] .checkbox-inline {
  cursor: auto
}

.radio.disabled label,
fieldset[disabled] .radio label,
.checkbox.disabled label,
fieldset[disabled] .checkbox label {
  cursor: auto
}

.form-control-static {
  padding-top: 7px;
  padding-bottom: 7px;
  margin-bottom: 0;
  min-height: 34px
}

.form-control-static.input-lg,
.input-group-lg>.form-control-static.form-control,
.input-group-lg>.form-control-static.input-group-addon,
.input-group-lg>.input-group-btn>.form-control-static.btn,
.form-control-static.input-sm,
.input-group-sm>.form-control-static.form-control,
.input-group-sm>.form-control-static.input-group-addon,
.input-group-sm>.input-group-btn>.form-control-static.btn {
  padding-left: 0;
  padding-right: 0
}

.input-sm,
.input-group-sm>.form-control,
.input-group-sm>.input-group-addon,
.input-group-sm>.input-group-btn>.btn {
  height: 30px;
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 0
}

select.input-sm,
.input-group-sm>select.form-control,
.input-group-sm>select.input-group-addon,
.input-group-sm>.input-group-btn>select.btn {
  height: 30px;
  line-height: 30px
}

textarea.input-sm,
.input-group-sm>textarea.form-control,
.input-group-sm>textarea.input-group-addon,
.input-group-sm>.input-group-btn>textarea.btn,
select[multiple].input-sm,
.input-group-sm>select[multiple].form-control,
.input-group-sm>select[multiple].input-group-addon,
.input-group-sm>.input-group-btn>select[multiple].btn {
  height: auto
}

.form-group-sm .form-control {
  height: 30px;
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 0
}

.form-group-sm select.form-control {
  height: 30px;
  line-height: 30px
}

.form-group-sm textarea.form-control,
.form-group-sm select[multiple].form-control {
  height: auto
}

.form-group-sm .form-control-static {
  height: 30px;
  min-height: 32px;
  padding: 6px 10px;
  font-size: 12px;
  line-height: 1.5
}

.input-lg,
.input-group-lg>.form-control,
.input-group-lg>.input-group-addon,
.input-group-lg>.input-group-btn>.btn {
  height: 57px;
  padding: 14px 32px;
  font-size: 20px;
  line-height: 1.3333333;
  border-radius: 0
}

select.input-lg,
.input-group-lg>select.form-control,
.input-group-lg>select.input-group-addon,
.input-group-lg>.input-group-btn>select.btn {
  height: 57px;
  line-height: 57px
}

textarea.input-lg,
.input-group-lg>textarea.form-control,
.input-group-lg>textarea.input-group-addon,
.input-group-lg>.input-group-btn>textarea.btn,
select[multiple].input-lg,
.input-group-lg>select[multiple].form-control,
.input-group-lg>select[multiple].input-group-addon,
.input-group-lg>.input-group-btn>select[multiple].btn {
  height: auto
}

.form-group-lg .form-control {
  height: 57px;
  padding: 14px 32px;
  font-size: 20px;
  line-height: 1.3333333;
  border-radius: 0
}

.form-group-lg select.form-control {
  height: 57px;
  line-height: 57px
}

.form-group-lg textarea.form-control,
.form-group-lg select[multiple].form-control {
  height: auto
}

.form-group-lg .form-control-static {
  height: 57px;
  min-height: 40px;
  padding: 15px 32px;
  font-size: 20px;
  line-height: 1.3333333
}

.has-feedback {
  position: relative
}

.has-feedback .form-control {
  padding-right: 42.5px
}

.form-control-feedback {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 2;
  display: block;
  width: 34px;
  height: 34px;
  line-height: 34px;
  text-align: center;
  pointer-events: none
}

.input-lg+.form-control-feedback,
.input-group-lg>.form-control+.form-control-feedback,
.input-group-lg>.input-group-addon+.form-control-feedback,
.input-group-lg>.input-group-btn>.btn+.form-control-feedback,
.input-group-lg+.form-control-feedback,
.form-group-lg .form-control+.form-control-feedback {
  width: 57px;
  height: 57px;
  line-height: 57px
}

.input-sm+.form-control-feedback,
.input-group-sm>.form-control+.form-control-feedback,
.input-group-sm>.input-group-addon+.form-control-feedback,
.input-group-sm>.input-group-btn>.btn+.form-control-feedback,
.input-group-sm+.form-control-feedback,
.form-group-sm .form-control+.form-control-feedback {
  width: 30px;
  height: 30px;
  line-height: 30px
}

.has-success .help-block,
.has-success .control-label,
.has-success .radio,
.has-success .checkbox,
.has-success .radio-inline,
.has-success .checkbox-inline,
.has-success.radio label,
.has-success.checkbox label,
.has-success.radio-inline label,
.has-success.checkbox-inline label {
  color: #fff
}

.has-success .form-control {
  border-color: #fff;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075)
}

.has-success .form-control:focus {
  border-color: #e6e6e6;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #fff
}

.has-success .input-group-addon {
  color: #fff;
  border-color: #fff;
  background-color: #06851F
}

.has-success .form-control-feedback {
  color: #fff
}

.has-warning .help-block,
.has-warning .control-label,
.has-warning .radio,
.has-warning .checkbox,
.has-warning .radio-inline,
.has-warning .checkbox-inline,
.has-warning.radio label,
.has-warning.checkbox label,
.has-warning.radio-inline label,
.has-warning.checkbox-inline label {
  color: #8a6d3b
}

.has-warning .form-control {
  border-color: #8a6d3b;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075)
}

.has-warning .form-control:focus {
  border-color: #66512c;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #c0a16b
}

.has-warning .input-group-addon {
  color: #8a6d3b;
  border-color: #8a6d3b;
  background-color: #fcf8e3
}

.has-warning .form-control-feedback {
  color: #8a6d3b
}

.has-error .help-block,
.has-error .control-label,
.has-error .radio,
.has-error .checkbox,
.has-error .radio-inline,
.has-error .checkbox-inline,
.has-error.radio label,
.has-error.checkbox label,
.has-error.radio-inline label,
.has-error.checkbox-inline label {
  color: #a94442
}

.has-error .form-control {
  border-color: #a94442;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075)
}

.has-error .form-control:focus {
  border-color: #843534;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #ce8483
}

.has-error .input-group-addon {
  color: #a94442;
  border-color: #a94442;
  background-color: #f2dede
}

.has-error .form-control-feedback {
  color: #a94442
}

.has-feedback label~.form-control-feedback {
  top: 25px
}

.has-feedback label.sr-only~.form-control-feedback {
  top: 0
}

.help-block {
  display: block;
  margin-top: 5px;
  margin-bottom: 10px;
  color: #737373
}

@media (min-width: 768px) {
  .form-inline .form-group {
    display: inline-block;
    margin-bottom: 0;
    vertical-align: middle
  }

  .form-inline .form-control {
    display: inline-block;
    width: auto;
    vertical-align: middle
  }

  .form-inline .form-control-static {
    display: inline-block
  }

  .form-inline .input-group {
    display: inline-table;
    vertical-align: middle
  }

  .form-inline .input-group .input-group-addon,
  .form-inline .input-group .input-group-btn,
  .form-inline .input-group .form-control {
    width: auto
  }

  .form-inline .input-group>.form-control {
    width: 100%
  }

  .form-inline .control-label {
    margin-bottom: 0;
    vertical-align: middle
  }

  .form-inline .radio,
  .form-inline .checkbox {
    display: inline-block;
    margin-top: 0;
    margin-bottom: 0;
    vertical-align: middle
  }

  .form-inline .radio label,
  .form-inline .checkbox label {
    padding-left: 0
  }

  .form-inline .radio input[type="radio"],
  .form-inline .checkbox input[type="checkbox"] {
    position: relative;
    margin-left: 0
  }

  .form-inline .has-feedback .form-control-feedback {
    top: 0
  }
}

.form-horizontal .radio,
.form-horizontal .checkbox,
.form-horizontal .radio-inline,
.form-horizontal .checkbox-inline {
  margin-top: 0;
  margin-bottom: 0;
  padding-top: 7px
}

.form-horizontal .radio,
.form-horizontal .checkbox {
  min-height: 27px
}

.form-horizontal .form-group {
  margin-left: -15px;
  margin-right: -15px
}

.form-horizontal .form-group:before,
.form-horizontal .form-group:after {
  content: " ";
  display: table
}

.form-horizontal .form-group:after {
  clear: both
}

@media (min-width: 768px) {
  .form-horizontal .control-label {
    text-align: right;
    margin-bottom: 0;
    padding-top: 7px
  }
}

.form-horizontal .has-feedback .form-control-feedback {
  right: 15px
}

@media (min-width: 768px) {
  .form-horizontal .form-group-lg .control-label {
    padding-top: 19.6666662px;
    font-size: 20px
  }
}

@media (min-width: 768px) {
  .form-horizontal .form-group-sm .control-label {
    padding-top: 6px;
    font-size: 12px
  }
}

.btn {
  display: inline-block;
  margin-bottom: 0;
  font-weight: 500;
  text-align: center;
  vertical-align: middle;
  -ms-touch-action: manipulation;
  touch-action: manipulation;
  cursor: pointer;
  background-image: none;
  border: 1px solid transparent;
  white-space: nowrap;
  padding: 6px 12px;
  font-size: 14px;
  line-height: 1.42857143;
  border-radius: 2px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none
}

.btn:focus,
.btn.focus,
.btn:active:focus,
.btn:active.focus,
.btn.active:focus,
.btn.active.focus {
  outline: thin dotted;
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px
}

.btn:hover,
.btn:focus,
.btn.focus {
  color: #333;
  text-decoration: none
}

.btn:active,
.btn.active {
  outline: 0;
  background-image: none;
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125)
}

.btn.disabled,
.btn[disabled],
fieldset[disabled] .btn {
  cursor: auto;
  opacity: 0.65;
  filter: alpha(opacity=65);
  box-shadow: none
}

a.btn.disabled,
fieldset[disabled] a.btn {
  pointer-events: none
}

.btn-default {
  color: #333;
  background-color: #fff;
  border-color: #ccc
}

.btn-default:focus,
.btn-default.focus {
  color: #333;
  background-color: #e6e6e6;
  border-color: #8c8c8c
}

.btn-default:hover {
  color: #333;
  background-color: #e6e6e6;
  border-color: #adadad
}

.btn-default:active,
.btn-default.active,
.open>.btn-default.dropdown-toggle {
  color: #333;
  background-color: #e6e6e6;
  border-color: #adadad
}

.btn-default:active:hover,
.btn-default:active:focus,
.btn-default:active.focus,
.btn-default.active:hover,
.btn-default.active:focus,
.btn-default.active.focus,
.open>.btn-default.dropdown-toggle:hover,
.open>.btn-default.dropdown-toggle:focus,
.open>.btn-default.dropdown-toggle.focus {
  color: #333;
  background-color: #d4d4d4;
  border-color: #8c8c8c
}

.btn-default:active,
.btn-default.active,
.open>.btn-default.dropdown-toggle {
  background-image: none
}

.btn-default.disabled,
.btn-default.disabled:hover,
.btn-default.disabled:focus,
.btn-default.disabled.focus,
.btn-default.disabled:active,
.btn-default.disabled.active,
.btn-default[disabled],
.btn-default[disabled]:hover,
.btn-default[disabled]:focus,
.btn-default[disabled].focus,
.btn-default[disabled]:active,
.btn-default[disabled].active,
fieldset[disabled] .btn-default,
fieldset[disabled] .btn-default:hover,
fieldset[disabled] .btn-default:focus,
fieldset[disabled] .btn-default.focus,
fieldset[disabled] .btn-default:active,
fieldset[disabled] .btn-default.active {
  background-color: #fff;
  border-color: #ccc
}

.btn-default .badge {
  color: #fff;
  background-color: #333
}

.btn-primary {
  color: #fff;
  background-color: #CC0000;
  border-color: #CC0000
}

.btn-primary:focus,
.btn-primary.focus {
  color: #fff;
  background-color: #990000;
  border-color: #4d0000
}

.btn-primary:hover {
  color: #fff;
  background-color: #990000;
  border-color: #8f0000
}

.btn-primary:active,
.btn-primary.active,
.open>.btn-primary.dropdown-toggle {
  color: #fff;
  background-color: #990000;
  border-color: #8f0000
}

.btn-primary:active:hover,
.btn-primary:active:focus,
.btn-primary:active.focus,
.btn-primary.active:hover,
.btn-primary.active:focus,
.btn-primary.active.focus,
.open>.btn-primary.dropdown-toggle:hover,
.open>.btn-primary.dropdown-toggle:focus,
.open>.btn-primary.dropdown-toggle.focus {
  color: #fff;
  background-color: #750000;
  border-color: #4d0000
}

.btn-primary:active,
.btn-primary.active,
.open>.btn-primary.dropdown-toggle {
  background-image: none
}

.btn-primary.disabled,
.btn-primary.disabled:hover,
.btn-primary.disabled:focus,
.btn-primary.disabled.focus,
.btn-primary.disabled:active,
.btn-primary.disabled.active,
.btn-primary[disabled],
.btn-primary[disabled]:hover,
.btn-primary[disabled]:focus,
.btn-primary[disabled].focus,
.btn-primary[disabled]:active,
.btn-primary[disabled].active,
fieldset[disabled] .btn-primary,
fieldset[disabled] .btn-primary:hover,
fieldset[disabled] .btn-primary:focus,
fieldset[disabled] .btn-primary.focus,
fieldset[disabled] .btn-primary:active,
fieldset[disabled] .btn-primary.active {
  background-color: #CC0000;
  border-color: #CC0000
}

.btn-primary .badge {
  color: #CC0000;
  background-color: #fff
}

.btn-success {
  color: #fff;
  background-color: #06851F;
  border-color: #06851F
}

.btn-success:focus,
.btn-success.focus {
  color: #fff;
  background-color: #045414;
  border-color: #000b03
}

.btn-success:hover {
  color: #fff;
  background-color: #045414;
  border-color: #034a11
}

.btn-success:active,
.btn-success.active,
.open>.btn-success.dropdown-toggle {
  color: #fff;
  background-color: #045414;
  border-color: #034a11
}

.btn-success:active:hover,
.btn-success:active:focus,
.btn-success:active.focus,
.btn-success.active:hover,
.btn-success.active:focus,
.btn-success.active.focus,
.open>.btn-success.dropdown-toggle:hover,
.open>.btn-success.dropdown-toggle:focus,
.open>.btn-success.dropdown-toggle.focus {
  color: #fff;
  background-color: #02320c;
  border-color: #000b03
}

.btn-success:active,
.btn-success.active,
.open>.btn-success.dropdown-toggle {
  background-image: none
}

.btn-success.disabled,
.btn-success.disabled:hover,
.btn-success.disabled:focus,
.btn-success.disabled.focus,
.btn-success.disabled:active,
.btn-success.disabled.active,
.btn-success[disabled],
.btn-success[disabled]:hover,
.btn-success[disabled]:focus,
.btn-success[disabled].focus,
.btn-success[disabled]:active,
.btn-success[disabled].active,
fieldset[disabled] .btn-success,
fieldset[disabled] .btn-success:hover,
fieldset[disabled] .btn-success:focus,
fieldset[disabled] .btn-success.focus,
fieldset[disabled] .btn-success:active,
fieldset[disabled] .btn-success.active {
  background-color: #06851F;
  border-color: #06851F
}

.btn-success .badge {
  color: #06851F;
  background-color: #fff
}

.btn-info {
  color: #fff;
  background-color: #5bc0de;
  border-color: #5bc0de
}

.btn-info:focus,
.btn-info.focus {
  color: #fff;
  background-color: #31b0d5;
  border-color: #1f7e9a
}

.btn-info:hover {
  color: #fff;
  background-color: #31b0d5;
  border-color: #2aabd2
}

.btn-info:active,
.btn-info.active,
.open>.btn-info.dropdown-toggle {
  color: #fff;
  background-color: #31b0d5;
  border-color: #2aabd2
}

.btn-info:active:hover,
.btn-info:active:focus,
.btn-info:active.focus,
.btn-info.active:hover,
.btn-info.active:focus,
.btn-info.active.focus,
.open>.btn-info.dropdown-toggle:hover,
.open>.btn-info.dropdown-toggle:focus,
.open>.btn-info.dropdown-toggle.focus {
  color: #fff;
  background-color: #269abc;
  border-color: #1f7e9a
}

.btn-info:active,
.btn-info.active,
.open>.btn-info.dropdown-toggle {
  background-image: none
}

.btn-info.disabled,
.btn-info.disabled:hover,
.btn-info.disabled:focus,
.btn-info.disabled.focus,
.btn-info.disabled:active,
.btn-info.disabled.active,
.btn-info[disabled],
.btn-info[disabled]:hover,
.btn-info[disabled]:focus,
.btn-info[disabled].focus,
.btn-info[disabled]:active,
.btn-info[disabled].active,
fieldset[disabled] .btn-info,
fieldset[disabled] .btn-info:hover,
fieldset[disabled] .btn-info:focus,
fieldset[disabled] .btn-info.focus,
fieldset[disabled] .btn-info:active,
fieldset[disabled] .btn-info.active {
  background-color: #5bc0de;
  border-color: #5bc0de
}

.btn-info .badge {
  color: #5bc0de;
  background-color: #fff
}

.btn-warning {
  color: #fff;
  background-color: #f0ad4e;
  border-color: #f0ad4e
}

.btn-warning:focus,
.btn-warning.focus {
  color: #fff;
  background-color: #ec971f;
  border-color: #b06d0f
}

.btn-warning:hover {
  color: #fff;
  background-color: #ec971f;
  border-color: #eb9316
}

.btn-warning:active,
.btn-warning.active,
.open>.btn-warning.dropdown-toggle {
  color: #fff;
  background-color: #ec971f;
  border-color: #eb9316
}

.btn-warning:active:hover,
.btn-warning:active:focus,
.btn-warning:active.focus,
.btn-warning.active:hover,
.btn-warning.active:focus,
.btn-warning.active.focus,
.open>.btn-warning.dropdown-toggle:hover,
.open>.btn-warning.dropdown-toggle:focus,
.open>.btn-warning.dropdown-toggle.focus {
  color: #fff;
  background-color: #d58512;
  border-color: #b06d0f
}

.btn-warning:active,
.btn-warning.active,
.open>.btn-warning.dropdown-toggle {
  background-image: none
}

.btn-warning.disabled,
.btn-warning.disabled:hover,
.btn-warning.disabled:focus,
.btn-warning.disabled.focus,
.btn-warning.disabled:active,
.btn-warning.disabled.active,
.btn-warning[disabled],
.btn-warning[disabled]:hover,
.btn-warning[disabled]:focus,
.btn-warning[disabled].focus,
.btn-warning[disabled]:active,
.btn-warning[disabled].active,
fieldset[disabled] .btn-warning,
fieldset[disabled] .btn-warning:hover,
fieldset[disabled] .btn-warning:focus,
fieldset[disabled] .btn-warning.focus,
fieldset[disabled] .btn-warning:active,
fieldset[disabled] .btn-warning.active {
  background-color: #f0ad4e;
  border-color: #f0ad4e
}

.btn-warning .badge {
  color: #f0ad4e;
  background-color: #fff
}

.btn-danger {
  color: #fff;
  background-color: #CC0000;
  border-color: #CC0000
}

.btn-danger:focus,
.btn-danger.focus {
  color: #fff;
  background-color: #990000;
  border-color: #4d0000
}

.btn-danger:hover {
  color: #fff;
  background-color: #990000;
  border-color: #8f0000
}

.btn-danger:active,
.btn-danger.active,
.open>.btn-danger.dropdown-toggle {
  color: #fff;
  background-color: #990000;
  border-color: #8f0000
}

.btn-danger:active:hover,
.btn-danger:active:focus,
.btn-danger:active.focus,
.btn-danger.active:hover,
.btn-danger.active:focus,
.btn-danger.active.focus,
.open>.btn-danger.dropdown-toggle:hover,
.open>.btn-danger.dropdown-toggle:focus,
.open>.btn-danger.dropdown-toggle.focus {
  color: #fff;
  background-color: #750000;
  border-color: #4d0000
}

.btn-danger:active,
.btn-danger.active,
.open>.btn-danger.dropdown-toggle {
  background-image: none
}

.btn-danger.disabled,
.btn-danger.disabled:hover,
.btn-danger.disabled:focus,
.btn-danger.disabled.focus,
.btn-danger.disabled:active,
.btn-danger.disabled.active,
.btn-danger[disabled],
.btn-danger[disabled]:hover,
.btn-danger[disabled]:focus,
.btn-danger[disabled].focus,
.btn-danger[disabled]:active,
.btn-danger[disabled].active,
fieldset[disabled] .btn-danger,
fieldset[disabled] .btn-danger:hover,
fieldset[disabled] .btn-danger:focus,
fieldset[disabled] .btn-danger.focus,
fieldset[disabled] .btn-danger:active,
fieldset[disabled] .btn-danger.active {
  background-color: #CC0000;
  border-color: #CC0000
}

.btn-danger .badge {
  color: #CC0000;
  background-color: #fff
}

.btn-link {
  color: #CC0000;
  font-weight: normal;
  border-radius: 0
}

.btn-link,
.btn-link:active,
.btn-link.active,
.btn-link[disabled],
fieldset[disabled] .btn-link {
  background-color: transparent;
  box-shadow: none
}

.btn-link,
.btn-link:hover,
.btn-link:focus,
.btn-link:active {
  border-color: transparent
}

.btn-link:hover,
.btn-link:focus {
  color: maroon;
  text-decoration: none;
  background-color: transparent
}

.btn-link[disabled]:hover,
.btn-link[disabled]:focus,
fieldset[disabled] .btn-link:hover,
fieldset[disabled] .btn-link:focus {
  color: #E2E2E2;
  text-decoration: none
}

.btn-lg,
.btn-group-lg>.btn {
  padding: 14px 32px;
  font-size: 20px;
  line-height: 1.3333333;
  border-radius: 2px
}

.btn-sm,
.btn-group-sm>.btn {
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 2px
}

.btn-xs,
.btn-group-xs>.btn {
  padding: 1px 5px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 2px
}

.btn-block {
  display: block;
  width: 100%
}

.btn-block+.btn-block {
  margin-top: 5px
}

input[type="submit"].btn-block,
input[type="reset"].btn-block,
input[type="button"].btn-block {
  width: 100%
}

.fade {
  opacity: 0;
  -webkit-transition: opacity 0.15s linear;
  transition: opacity 0.15s linear
}

.fade.in {
  opacity: 1
}

.collapse {
  display: none
}

.collapse.in {
  display: block
}

tr.collapse.in {
  display: table-row
}

tbody.collapse.in {
  display: table-row-group
}

.collapsing {
  position: relative;
  height: 0;
  overflow: hidden;
  -webkit-transition-property: height, visibility;
  transition-property: height, visibility;
  -webkit-transition-duration: 0.35s;
  transition-duration: 0.35s;
  -webkit-transition-timing-function: ease;
  transition-timing-function: ease
}

.caret {
  display: inline-block;
  width: 0;
  height: 0;
  margin-left: 2px;
  vertical-align: middle;
  border-top: 4px dashed;
  border-top: 4px solid \9;
  border-right: 4px solid transparent;
  border-left: 4px solid transparent
}

.dropup,
.dropdown {
  position: relative
}

.dropdown-toggle:focus {
  outline: 0
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  display: none;
  float: left;
  min-width: 160px;
  padding: 5px 0;
  margin: 2px 0 0;
  list-style: none;
  font-size: 14px;
  text-align: left;
  background-color: #fff;
  border: 1px solid #ccc;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 2px;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  background-clip: padding-box
}

.dropdown-menu.pull-right {
  right: 0;
  left: auto
}

.dropdown-menu .divider {
  height: 1px;
  margin: 9px 0;
  overflow: hidden;
  background-color: #e5e5e5
}

.dropdown-menu>li>a {
  display: block;
  padding: 3px 20px;
  clear: both;
  font-weight: normal;
  line-height: 1.42857143;
  color: #979797;
  white-space: nowrap
}

.dropdown-menu>li>a:hover,
.dropdown-menu>li>a:focus {
  text-decoration: none;
  color: #8a8a8a;
  background-color: #f5f5f5
}

.dropdown-menu>.active>a,
.dropdown-menu>.active>a:hover,
.dropdown-menu>.active>a:focus {
  color: #fff;
  text-decoration: none;
  outline: 0;
  background-color: #CC0000
}

.dropdown-menu>.disabled>a,
.dropdown-menu>.disabled>a:hover,
.dropdown-menu>.disabled>a:focus {
  color: #E2E2E2
}

.dropdown-menu>.disabled>a:hover,
.dropdown-menu>.disabled>a:focus {
  text-decoration: none;
  background-color: transparent;
  background-image: none;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
  cursor: auto
}

.open>.dropdown-menu {
  display: block
}

.open>a {
  outline: 0
}

.dropdown-menu-right {
  left: auto;
  right: 0
}

.dropdown-menu-left {
  left: 0;
  right: auto
}

.dropdown-header {
  display: block;
  padding: 3px 20px;
  font-size: 12px;
  line-height: 1.42857143;
  color: #CCCCCC;
  white-space: nowrap
}

.dropdown-backdrop {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  z-index: 990
}

.pull-right>.dropdown-menu {
  right: 0;
  left: auto
}

.dropup .caret,
.navbar-fixed-bottom .dropdown .caret {
  border-top: 0;
  border-bottom: 4px dashed;
  border-bottom: 4px solid \9;
  content: ""
}

.dropup .dropdown-menu,
.navbar-fixed-bottom .dropdown .dropdown-menu {
  top: auto;
  bottom: 100%;
  margin-bottom: 2px
}

@media (min-width: 768px) {
  .navbar-right .dropdown-menu {
    right: 0;
    left: auto
  }

  .navbar-right .dropdown-menu-left {
    left: 0;
    right: auto
  }
}

.btn-group,
.btn-group-vertical {
  position: relative;
  display: inline-block;
  vertical-align: middle
}

.btn-group>.btn,
.btn-group-vertical>.btn {
  position: relative;
  float: left
}

.btn-group>.btn:hover,
.btn-group>.btn:focus,
.btn-group>.btn:active,
.btn-group>.btn.active,
.btn-group-vertical>.btn:hover,
.btn-group-vertical>.btn:focus,
.btn-group-vertical>.btn:active,
.btn-group-vertical>.btn.active {
  z-index: 2
}

.btn-group .btn+.btn,
.btn-group .btn+.btn-group,
.btn-group .btn-group+.btn,
.btn-group .btn-group+.btn-group {
  margin-left: -1px
}

.btn-toolbar {
  margin-left: -5px
}

.btn-toolbar:before,
.btn-toolbar:after {
  content: " ";
  display: table
}

.btn-toolbar:after {
  clear: both
}

.btn-toolbar .btn,
.btn-toolbar .btn-group,
.btn-toolbar .input-group {
  float: left
}

.btn-toolbar>.btn,
.btn-toolbar>.btn-group,
.btn-toolbar>.input-group {
  margin-left: 5px
}

.btn-group>.btn:not(:first-child):not(:last-child):not(.dropdown-toggle) {
  border-radius: 0
}

.btn-group>.btn:first-child {
  margin-left: 0
}

.btn-group>.btn:first-child:not(:last-child):not(.dropdown-toggle) {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0
}

.btn-group>.btn:last-child:not(:first-child),
.btn-group>.dropdown-toggle:not(:first-child) {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0
}

.btn-group>.btn-group {
  float: left
}

.btn-group>.btn-group:not(:first-child):not(:last-child)>.btn {
  border-radius: 0
}

.btn-group>.btn-group:first-child:not(:last-child)>.btn:last-child,
.btn-group>.btn-group:first-child:not(:last-child)>.dropdown-toggle {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0
}

.btn-group>.btn-group:last-child:not(:first-child)>.btn:first-child {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0
}

.btn-group .dropdown-toggle:active,
.btn-group.open .dropdown-toggle {
  outline: 0
}

.btn-group>.btn+.dropdown-toggle {
  padding-left: 8px;
  padding-right: 8px
}

.btn-group>.btn-lg+.dropdown-toggle,
.btn-group-lg.btn-group>.btn+.dropdown-toggle {
  padding-left: 12px;
  padding-right: 12px
}

.btn-group.open .dropdown-toggle {
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125)
}

.btn-group.open .dropdown-toggle.btn-link {
  box-shadow: none
}

.btn .caret {
  margin-left: 0
}

.btn-lg .caret,
.btn-group-lg>.btn .caret {
  border-width: 5px 5px 0;
  border-bottom-width: 0
}

.dropup .btn-lg .caret,
.dropup .btn-group-lg>.btn .caret {
  border-width: 0 5px 5px
}

.btn-group-vertical>.btn,
.btn-group-vertical>.btn-group,
.btn-group-vertical>.btn-group>.btn {
  display: block;
  float: none;
  width: 100%;
  max-width: 100%
}

.btn-group-vertical>.btn-group:before,
.btn-group-vertical>.btn-group:after {
  content: " ";
  display: table
}

.btn-group-vertical>.btn-group:after {
  clear: both
}

.btn-group-vertical>.btn-group>.btn {
  float: none
}

.btn-group-vertical>.btn+.btn,
.btn-group-vertical>.btn+.btn-group,
.btn-group-vertical>.btn-group+.btn,
.btn-group-vertical>.btn-group+.btn-group {
  margin-top: -1px;
  margin-left: 0
}

.btn-group-vertical>.btn:not(:first-child):not(:last-child) {
  border-radius: 0
}

.btn-group-vertical>.btn:first-child:not(:last-child) {
  border-top-right-radius: 2px;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0
}

.btn-group-vertical>.btn:last-child:not(:first-child) {
  border-bottom-left-radius: 2px;
  border-top-right-radius: 0;
  border-top-left-radius: 0
}

.btn-group-vertical>.btn-group:not(:first-child):not(:last-child)>.btn {
  border-radius: 0
}

.btn-group-vertical>.btn-group:first-child:not(:last-child)>.btn:last-child,
.btn-group-vertical>.btn-group:first-child:not(:last-child)>.dropdown-toggle {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0
}

.btn-group-vertical>.btn-group:last-child:not(:first-child)>.btn:first-child {
  border-top-right-radius: 0;
  border-top-left-radius: 0
}

.btn-group-justified {
  display: table;
  width: 100%;
  table-layout: fixed;
  border-collapse: separate
}

.btn-group-justified>.btn,
.btn-group-justified>.btn-group {
  float: none;
  display: table-cell;
  width: 1%
}

.btn-group-justified>.btn-group .btn {
  width: 100%
}

.btn-group-justified>.btn-group .dropdown-menu {
  left: auto
}

[data-toggle="buttons"]>.btn input[type="radio"],
[data-toggle="buttons"]>.btn input[type="checkbox"],
[data-toggle="buttons"]>.btn-group>.btn input[type="radio"],
[data-toggle="buttons"]>.btn-group>.btn input[type="checkbox"] {
  position: absolute;
  clip: rect(0, 0, 0, 0);
  pointer-events: none
}

.input-group {
  position: relative;
  display: table;
  border-collapse: separate
}

.input-group[class*="col-"] {
  float: none;
  padding-left: 0;
  padding-right: 0
}

.input-group .form-control {
  position: relative;
  z-index: 2;
  float: left;
  width: 100%;
  margin-bottom: 0
}

.input-group-addon,
.input-group-btn,
.input-group .form-control {
  display: table-cell
}

.input-group-addon:not(:first-child):not(:last-child),
.input-group-btn:not(:first-child):not(:last-child),
.input-group .form-control:not(:first-child):not(:last-child) {
  border-radius: 0
}

.input-group-addon,
.input-group-btn {
  width: 1%;
  white-space: nowrap;
  vertical-align: middle
}

.input-group-addon {
  padding: 6px 12px;
  font-size: 14px;
  font-weight: normal;
  line-height: 1;
  color: #CCCCCC;
  text-align: center;
  background-color: #F9F9F9;
  border: 1px solid #ccc;
  border-radius: 2px
}

.input-group-addon.input-sm,
.input-group-sm>.input-group-addon,
.input-group-sm>.input-group-btn>.input-group-addon.btn {
  padding: 5px 10px;
  font-size: 12px;
  border-radius: 2px
}

.input-group-addon.input-lg,
.input-group-lg>.input-group-addon,
.input-group-lg>.input-group-btn>.input-group-addon.btn {
  padding: 14px 32px;
  font-size: 20px;
  border-radius: 2px
}

.input-group-addon input[type="radio"],
.input-group-addon input[type="checkbox"] {
  margin-top: 0
}

.input-group .form-control:first-child,
.input-group-addon:first-child,
.input-group-btn:first-child>.btn,
.input-group-btn:first-child>.btn-group>.btn,
.input-group-btn:first-child>.dropdown-toggle,
.input-group-btn:last-child>.btn:not(:last-child):not(.dropdown-toggle),
.input-group-btn:last-child>.btn-group:not(:last-child)>.btn {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0
}

.input-group-addon:first-child {
  border-right: 0
}

.input-group .form-control:last-child,
.input-group-addon:last-child,
.input-group-btn:last-child>.btn,
.input-group-btn:last-child>.btn-group>.btn,
.input-group-btn:last-child>.dropdown-toggle,
.input-group-btn:first-child>.btn:not(:first-child),
.input-group-btn:first-child>.btn-group:not(:first-child)>.btn {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0
}

.input-group-addon:last-child {
  border-left: 0
}

.input-group-btn {
  position: relative;
  font-size: 0;
  white-space: nowrap
}

.input-group-btn>.btn {
  position: relative
}

.input-group-btn>.btn+.btn {
  margin-left: -1px
}

.input-group-btn>.btn:hover,
.input-group-btn>.btn:focus,
.input-group-btn>.btn:active {
  z-index: 2
}

.input-group-btn:first-child>.btn,
.input-group-btn:first-child>.btn-group {
  margin-right: -1px
}

.input-group-btn:last-child>.btn,
.input-group-btn:last-child>.btn-group {
  z-index: 2;
  margin-left: -1px
}

.nav {
  margin-bottom: 0;
  padding-left: 0;
  list-style: none
}

.nav:before,
.nav:after {
  content: " ";
  display: table
}

.nav:after {
  clear: both
}

.nav>li {
  position: relative;
  display: block
}

.nav>li>a {
  position: relative;
  display: block;
  padding: 10px 15px
}

.nav>li>a:hover,
.nav>li>a:focus {
  text-decoration: none;
  background-color: #F9F9F9
}

.nav>li.disabled>a {
  color: #E2E2E2
}

.nav>li.disabled>a:hover,
.nav>li.disabled>a:focus {
  color: #E2E2E2;
  text-decoration: none;
  background-color: transparent;
  cursor: auto
}

.nav .open>a,
.nav .open>a:hover,
.nav .open>a:focus {
  background-color: #F9F9F9;
  border-color: #CC0000
}

.nav .nav-divider {
  height: 1px;
  margin: 9px 0;
  overflow: hidden;
  background-color: #e5e5e5
}

.nav>li>a>img {
  max-width: none
}

.nav-tabs {
  border-bottom: 1px solid #ddd
}

.nav-tabs>li {
  float: left;
  margin-bottom: -1px
}

.nav-tabs>li>a {
  margin-right: 2px;
  line-height: 1.42857143;
  border: 1px solid transparent;
  border-radius: 2px 2px 0 0
}

.nav-tabs>li>a:hover {
  border-color: #F9F9F9 #F9F9F9 #ddd
}

.nav-tabs>li.active>a,
.nav-tabs>li.active>a:hover,
.nav-tabs>li.active>a:focus {
  color: #CCCCCC;
  background-color: #F9F9F9;
  border: 1px solid #ddd;
  border-bottom-color: transparent;
  cursor: default
}

.nav-pills>li {
  float: left
}

.nav-pills>li>a {
  border-radius: 2px
}

.nav-pills>li+li {
  margin-left: 2px
}

.nav-pills>li.active>a,
.nav-pills>li.active>a:hover,
.nav-pills>li.active>a:focus {
  color: #fff;
  background-color: #CC0000
}

.nav-stacked>li {
  float: none
}

.nav-stacked>li+li {
  margin-top: 2px;
  margin-left: 0
}

.nav-justified,
.nav-tabs.nav-justified {
  width: 100%
}

.nav-justified>li,
.nav-tabs.nav-justified>li {
  float: none
}

.nav-justified>li>a,
.nav-tabs.nav-justified>li>a {
  text-align: center;
  margin-bottom: 5px
}

.nav-justified>.dropdown .dropdown-menu {
  top: auto;
  left: auto
}

@media (min-width: 768px) {

  .nav-justified>li,
  .nav-tabs.nav-justified>li {
    display: table-cell;
    width: 1%
  }

  .nav-justified>li>a,
  .nav-tabs.nav-justified>li>a {
    margin-bottom: 0
  }
}

.nav-tabs-justified,
.nav-tabs.nav-justified {
  border-bottom: 0
}

.nav-tabs-justified>li>a,
.nav-tabs.nav-justified>li>a {
  margin-right: 0;
  border-radius: 2px
}

.nav-tabs-justified>.active>a,
.nav-tabs.nav-justified>.active>a,
.nav-tabs-justified>.active>a:hover,
.nav-tabs.nav-justified>.active>a:hover,
.nav-tabs-justified>.active>a:focus,
.nav-tabs.nav-justified>.active>a:focus {
  border: 1px solid #ddd
}

@media (min-width: 768px) {

  .nav-tabs-justified>li>a,
  .nav-tabs.nav-justified>li>a {
    border-bottom: 1px solid #ddd;
    border-radius: 2px 2px 0 0
  }

  .nav-tabs-justified>.active>a,
  .nav-tabs.nav-justified>.active>a,
  .nav-tabs-justified>.active>a:hover,
  .nav-tabs.nav-justified>.active>a:hover,
  .nav-tabs-justified>.active>a:focus,
  .nav-tabs.nav-justified>.active>a:focus {
    border-bottom-color: #F9F9F9
  }
}

.tab-content>.tab-pane {
  display: none
}

.tab-content>.active {
  display: block
}

.nav-tabs .dropdown-menu {
  margin-top: -1px;
  border-top-right-radius: 0;
  border-top-left-radius: 0
}

.navbar {
  position: relative;
  min-height: 45px;
  margin-bottom: 0;
  border: 1px solid transparent
}

.navbar:before,
.navbar:after {
  content: " ";
  display: table
}

.navbar:after {
  clear: both
}

@media (min-width: 768px) {
  .navbar {
    border-radius: 2px
  }
}

.navbar-header:before,
.navbar-header:after {
  content: " ";
  display: table
}

.navbar-header:after {
  clear: both
}

@media (min-width: 768px) {
  .navbar-header {
    float: left
  }
}

.navbar-collapse {
  overflow-x: visible;
  padding-right: 15px;
  padding-left: 15px;
  border-top: 1px solid transparent;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
  -webkit-overflow-scrolling: touch
}

.navbar-collapse:before,
.navbar-collapse:after {
  content: " ";
  display: table
}

.navbar-collapse:after {
  clear: both
}

.navbar-collapse.in {
  overflow-y: auto
}

@media (min-width: 768px) {
  .navbar-collapse {
    width: auto;
    border-top: 0;
    box-shadow: none
  }

  .navbar-collapse.collapse {
    display: block !important;
    height: auto !important;
    padding-bottom: 0;
    overflow: visible !important
  }

  .navbar-collapse.in {
    overflow-y: visible
  }

  .navbar-fixed-top .navbar-collapse,
  .navbar-static-top .navbar-collapse,
  .navbar-fixed-bottom .navbar-collapse {
    padding-left: 0;
    padding-right: 0
  }
}

.navbar-fixed-top .navbar-collapse,
.navbar-fixed-bottom .navbar-collapse {
  max-height: 340px
}

@media (max-device-width: 480px) and (orientation: landscape) {

  .navbar-fixed-top .navbar-collapse,
  .navbar-fixed-bottom .navbar-collapse {
    max-height: 200px
  }
}

.container>.navbar-header,
.container>.navbar-collapse,
.container-fluid>.navbar-header,
.container-fluid>.navbar-collapse {
  margin-right: -15px;
  margin-left: -15px
}

@media (min-width: 768px) {

  .container>.navbar-header,
  .container>.navbar-collapse,
  .container-fluid>.navbar-header,
  .container-fluid>.navbar-collapse {
    margin-right: 0;
    margin-left: 0
  }
}

.navbar-static-top {
  z-index: 1000;
  border-width: 0 0 1px
}

@media (min-width: 768px) {
  .navbar-static-top {
    border-radius: 0
  }
}

.navbar-fixed-top,
.navbar-fixed-bottom {
  position: fixed;
  right: 0;
  left: 0;
  z-index: 1030
}

@media (min-width: 768px) {

  .navbar-fixed-top,
  .navbar-fixed-bottom {
    border-radius: 0
  }
}

.navbar-fixed-top {
  top: 0;
  border-width: 0 0 1px
}

.navbar-fixed-bottom {
  bottom: 0;
  margin-bottom: 0;
  border-width: 1px 0 0
}

.navbar-brand {
  float: left;
  padding: 12.5px 15px;
  font-size: 20px;
  line-height: 20px;
  height: 45px
}

.navbar-brand:hover,
.navbar-brand:focus {
  text-decoration: none
}

.navbar-brand>img {
  display: block
}

@media (min-width: 768px) {

  .navbar>.container .navbar-brand,
  .navbar>.container-fluid .navbar-brand {
    margin-left: -15px
  }
}

.navbar-toggle {
  position: relative;
  float: right;
  margin-right: 15px;
  padding: 9px 10px;
  margin-top: 5.5px;
  margin-bottom: 5.5px;
  background-color: transparent;
  background-image: none;
  border: 1px solid transparent;
  border-radius: 2px
}

.navbar-toggle:focus {
  outline: 0
}

.navbar-toggle .icon-bar {
  display: block;
  width: 22px;
  height: 2px;
  border-radius: 1px
}

.navbar-toggle .icon-bar+.icon-bar {
  margin-top: 4px
}

@media (min-width: 768px) {
  .navbar-toggle {
    display: none
  }
}

.navbar-nav {
  margin: 6.25px -15px
}

.navbar-nav>li>a {
  padding-top: 10px;
  padding-bottom: 10px;
  line-height: 20px
}

@media (max-width: 767px) {
  .navbar-nav .open .dropdown-menu {
    position: static;
    float: none;
    width: auto;
    margin-top: 0;
    background-color: transparent;
    border: 0;
    box-shadow: none
  }

  .navbar-nav .open .dropdown-menu>li>a,
  .navbar-nav .open .dropdown-menu .dropdown-header {
    padding: 5px 15px 5px 25px
  }

  .navbar-nav .open .dropdown-menu>li>a {
    line-height: 20px
  }

  .navbar-nav .open .dropdown-menu>li>a:hover,
  .navbar-nav .open .dropdown-menu>li>a:focus {
    background-image: none
  }
}

@media (min-width: 768px) {
  .navbar-nav {
    float: left;
    margin: 0
  }

  .navbar-nav>li {
    float: left
  }

  .navbar-nav>li>a {
    padding-top: 12.5px;
    padding-bottom: 12.5px
  }
}

.navbar-form {
  margin-left: -15px;
  margin-right: -15px;
  padding: 10px 15px;
  border-top: 1px solid transparent;
  border-bottom: 1px solid transparent;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1), 0 1px 0 rgba(255, 255, 255, 0.1);
  margin-top: 5.5px;
  margin-bottom: 5.5px
}

@media (min-width: 768px) {
  .navbar-form .form-group {
    display: inline-block;
    margin-bottom: 0;
    vertical-align: middle
  }

  .navbar-form .form-control {
    display: inline-block;
    width: auto;
    vertical-align: middle
  }

  .navbar-form .form-control-static {
    display: inline-block
  }

  .navbar-form .input-group {
    display: inline-table;
    vertical-align: middle
  }

  .navbar-form .input-group .input-group-addon,
  .navbar-form .input-group .input-group-btn,
  .navbar-form .input-group .form-control {
    width: auto
  }

  .navbar-form .input-group>.form-control {
    width: 100%
  }

  .navbar-form .control-label {
    margin-bottom: 0;
    vertical-align: middle
  }

  .navbar-form .radio,
  .navbar-form .checkbox {
    display: inline-block;
    margin-top: 0;
    margin-bottom: 0;
    vertical-align: middle
  }

  .navbar-form .radio label,
  .navbar-form .checkbox label {
    padding-left: 0
  }

  .navbar-form .radio input[type="radio"],
  .navbar-form .checkbox input[type="checkbox"] {
    position: relative;
    margin-left: 0
  }

  .navbar-form .has-feedback .form-control-feedback {
    top: 0
  }
}

@media (max-width: 767px) {
  .navbar-form .form-group {
    margin-bottom: 5px
  }

  .navbar-form .form-group:last-child {
    margin-bottom: 0
  }
}

@media (min-width: 768px) {
  .navbar-form {
    width: auto;
    border: 0;
    margin-left: 0;
    margin-right: 0;
    padding-top: 0;
    padding-bottom: 0;
    box-shadow: none
  }
}

.navbar-nav>li>.dropdown-menu {
  margin-top: 0;
  border-top-right-radius: 0;
  border-top-left-radius: 0
}

.navbar-fixed-bottom .navbar-nav>li>.dropdown-menu {
  margin-bottom: 0;
  border-top-right-radius: 2px;
  border-top-left-radius: 2px;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0
}

.navbar-btn {
  margin-top: 5.5px;
  margin-bottom: 5.5px
}

.navbar-btn.btn-sm,
.btn-group-sm>.navbar-btn.btn {
  margin-top: 7.5px;
  margin-bottom: 7.5px
}

.navbar-btn.btn-xs,
.btn-group-xs>.navbar-btn.btn {
  margin-top: 11.5px;
  margin-bottom: 11.5px
}

.navbar-text {
  margin-top: 12.5px;
  margin-bottom: 12.5px
}

@media (min-width: 768px) {
  .navbar-text {
    float: left;
    margin-left: 15px;
    margin-right: 15px
  }
}

@media (min-width: 768px) {
  .navbar-left {
    float: left !important
  }

  .navbar-right {
    float: right !important;
    margin-right: -15px
  }

  .navbar-right~.navbar-right {
    margin-right: 0
  }
}

.navbar-default {
  background-color: #f8f8f8;
  border-color: #e7e7e7
}

.navbar-default .navbar-brand {
  color: #777
}

.navbar-default .navbar-brand:hover,
.navbar-default .navbar-brand:focus {
  color: #5e5e5e;
  background-color: transparent
}

.navbar-default .navbar-text {
  color: #777
}

.navbar-default .navbar-nav>li>a {
  color: #777
}

.navbar-default .navbar-nav>li>a:hover,
.navbar-default .navbar-nav>li>a:focus {
  color: #333;
  background-color: transparent
}

.navbar-default .navbar-nav>.active>a,
.navbar-default .navbar-nav>.active>a:hover,
.navbar-default .navbar-nav>.active>a:focus {
  color: #555;
  background-color: #e7e7e7
}

.navbar-default .navbar-nav>.disabled>a,
.navbar-default .navbar-nav>.disabled>a:hover,
.navbar-default .navbar-nav>.disabled>a:focus {
  color: #ccc;
  background-color: transparent
}

.navbar-default .navbar-toggle {
  border-color: #ddd
}

.navbar-default .navbar-toggle:hover,
.navbar-default .navbar-toggle:focus {
  background-color: #ddd
}

.navbar-default .navbar-toggle .icon-bar {
  background-color: #888
}

.navbar-default .navbar-collapse,
.navbar-default .navbar-form {
  border-color: #e7e7e7
}

.navbar-default .navbar-nav>.open>a,
.navbar-default .navbar-nav>.open>a:hover,
.navbar-default .navbar-nav>.open>a:focus {
  background-color: #e7e7e7;
  color: #555
}

@media (max-width: 767px) {
  .navbar-default .navbar-nav .open .dropdown-menu>li>a {
    color: #777
  }

  .navbar-default .navbar-nav .open .dropdown-menu>li>a:hover,
  .navbar-default .navbar-nav .open .dropdown-menu>li>a:focus {
    color: #333;
    background-color: transparent
  }

  .navbar-default .navbar-nav .open .dropdown-menu>.active>a,
  .navbar-default .navbar-nav .open .dropdown-menu>.active>a:hover,
  .navbar-default .navbar-nav .open .dropdown-menu>.active>a:focus {
    color: #555;
    background-color: #e7e7e7
  }

  .navbar-default .navbar-nav .open .dropdown-menu>.disabled>a,
  .navbar-default .navbar-nav .open .dropdown-menu>.disabled>a:hover,
  .navbar-default .navbar-nav .open .dropdown-menu>.disabled>a:focus {
    color: #ccc;
    background-color: transparent
  }
}

.navbar-default .navbar-link {
  color: #777
}

.navbar-default .navbar-link:hover {
  color: #333
}

.navbar-default .btn-link {
  color: #777
}

.navbar-default .btn-link:hover,
.navbar-default .btn-link:focus {
  color: #333
}

.navbar-default .btn-link[disabled]:hover,
.navbar-default .btn-link[disabled]:focus,
fieldset[disabled] .navbar-default .btn-link:hover,
fieldset[disabled] .navbar-default .btn-link:focus {
  color: #ccc
}

.navbar-inverse {
  background-color: #333333;
  border-color: #333333
}

.navbar-inverse .navbar-brand {
  color: #fff
}

.navbar-inverse .navbar-brand:hover,
.navbar-inverse .navbar-brand:focus {
  color: #fff;
  background-color: transparent
}

.navbar-inverse .navbar-text {
  color: #fff
}

.navbar-inverse .navbar-nav>li>a {
  color: #fff
}

.navbar-inverse .navbar-nav>li>a:hover,
.navbar-inverse .navbar-nav>li>a:focus {
  color: white;
  background-color: inherit
}

.navbar-inverse .navbar-nav>.active>a,
.navbar-inverse .navbar-nav>.active>a:hover,
.navbar-inverse .navbar-nav>.active>a:focus {
  color: white;
  background-color: #1a1a1a
}

.navbar-inverse .navbar-nav>.disabled>a,
.navbar-inverse .navbar-nav>.disabled>a:hover,
.navbar-inverse .navbar-nav>.disabled>a:focus {
  color: #444;
  background-color: inherit
}

.navbar-inverse .navbar-toggle {
  border-color: #333
}

.navbar-inverse .navbar-toggle:hover,
.navbar-inverse .navbar-toggle:focus {
  background-color: #333
}

.navbar-inverse .navbar-toggle .icon-bar {
  background-color: #fff
}

.navbar-inverse .navbar-collapse,
.navbar-inverse .navbar-form {
  border-color: #212121
}

.navbar-inverse .navbar-nav>.open>a,
.navbar-inverse .navbar-nav>.open>a:hover,
.navbar-inverse .navbar-nav>.open>a:focus {
  background-color: #1a1a1a;
  color: white
}

@media (max-width: 767px) {
  .navbar-inverse .navbar-nav .open .dropdown-menu>.dropdown-header {
    border-color: #333333
  }

  .navbar-inverse .navbar-nav .open .dropdown-menu .divider {
    background-color: #333333
  }

  .navbar-inverse .navbar-nav .open .dropdown-menu>li>a {
    color: #fff
  }

  .navbar-inverse .navbar-nav .open .dropdown-menu>li>a:hover,
  .navbar-inverse .navbar-nav .open .dropdown-menu>li>a:focus {
    color: white;
    background-color: inherit
  }

  .navbar-inverse .navbar-nav .open .dropdown-menu>.active>a,
  .navbar-inverse .navbar-nav .open .dropdown-menu>.active>a:hover,
  .navbar-inverse .navbar-nav .open .dropdown-menu>.active>a:focus {
    color: white;
    background-color: #1a1a1a
  }

  .navbar-inverse .navbar-nav .open .dropdown-menu>.disabled>a,
  .navbar-inverse .navbar-nav .open .dropdown-menu>.disabled>a:hover,
  .navbar-inverse .navbar-nav .open .dropdown-menu>.disabled>a:focus {
    color: #444;
    background-color: inherit
  }
}

.navbar-inverse .navbar-link {
  color: #fff
}

.navbar-inverse .navbar-link:hover {
  color: white
}

.navbar-inverse .btn-link {
  color: #fff
}

.navbar-inverse .btn-link:hover,
.navbar-inverse .btn-link:focus {
  color: white
}

.navbar-inverse .btn-link[disabled]:hover,
.navbar-inverse .btn-link[disabled]:focus,
fieldset[disabled] .navbar-inverse .btn-link:hover,
fieldset[disabled] .navbar-inverse .btn-link:focus {
  color: #444
}

.breadcrumb {
  padding: 8px 15px;
  margin-bottom: 20px;
  list-style: none;
  background-color: #f5f5f5;
  border-radius: 2px
}

.breadcrumb>li {
  display: inline-block
}

.breadcrumb>li+li:before {
  content: "/ ";
  padding: 0 5px;
  color: #ccc
}

.breadcrumb>.active {
  color: #CCCCCC
}

.pagination {
  display: inline-block;
  padding-left: 0;
  margin: 20px 0;
  border-radius: 2px
}

.pagination>li {
  display: inline
}

.pagination>li>a,
.pagination>li>span {
  position: relative;
  float: left;
  padding: 6px 12px;
  line-height: 1.42857143;
  text-decoration: none;
  color: #CC0000;
  background-color: #fff;
  border: 1px solid #ddd;
  margin-left: -1px
}

.pagination>li:first-child>a,
.pagination>li:first-child>span {
  margin-left: 0;
  border-bottom-left-radius: 2px;
  border-top-left-radius: 2px
}

.pagination>li:last-child>a,
.pagination>li:last-child>span {
  border-bottom-right-radius: 2px;
  border-top-right-radius: 2px
}

.pagination>li>a:hover,
.pagination>li>a:focus,
.pagination>li>span:hover,
.pagination>li>span:focus {
  z-index: 3;
  color: maroon;
  background-color: #F9F9F9;
  border-color: #ddd
}

.pagination>.active>a,
.pagination>.active>a:hover,
.pagination>.active>a:focus,
.pagination>.active>span,
.pagination>.active>span:hover,
.pagination>.active>span:focus {
  z-index: 2;
  color: #fff;
  background-color: #CC0000;
  border-color: #CC0000;
  cursor: default
}

.pagination>.disabled>span,
.pagination>.disabled>span:hover,
.pagination>.disabled>span:focus,
.pagination>.disabled>a,
.pagination>.disabled>a:hover,
.pagination>.disabled>a:focus {
  color: #E2E2E2;
  background-color: #fff;
  border-color: #ddd;
  cursor: auto
}

.pagination-lg>li>a,
.pagination-lg>li>span {
  padding: 14px 32px;
  font-size: 20px;
  line-height: 1.3333333
}

.pagination-lg>li:first-child>a,
.pagination-lg>li:first-child>span {
  border-bottom-left-radius: 2px;
  border-top-left-radius: 2px
}

.pagination-lg>li:last-child>a,
.pagination-lg>li:last-child>span {
  border-bottom-right-radius: 2px;
  border-top-right-radius: 2px
}

.pagination-sm>li>a,
.pagination-sm>li>span {
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.5
}

.pagination-sm>li:first-child>a,
.pagination-sm>li:first-child>span {
  border-bottom-left-radius: 2px;
  border-top-left-radius: 2px
}

.pagination-sm>li:last-child>a,
.pagination-sm>li:last-child>span {
  border-bottom-right-radius: 2px;
  border-top-right-radius: 2px
}

.pager {
  padding-left: 0;
  margin: 20px 0;
  list-style: none;
  text-align: center
}

.pager:before,
.pager:after {
  content: " ";
  display: table
}

.pager:after {
  clear: both
}

.pager li {
  display: inline
}

.pager li>a,
.pager li>span {
  display: inline-block;
  padding: 5px 14px;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 15px
}

.pager li>a:hover,
.pager li>a:focus {
  text-decoration: none;
  background-color: #F9F9F9
}

.pager .next>a,
.pager .next>span {
  float: right
}

.pager .previous>a,
.pager .previous>span {
  float: left
}

.pager .disabled>a,
.pager .disabled>a:hover,
.pager .disabled>a:focus,
.pager .disabled>span {
  color: #E2E2E2;
  background-color: #fff;
  cursor: auto
}

.label {
  display: inline;
  padding: .2em .6em .3em;
  font-size: 75%;
  font-weight: bold;
  line-height: 1;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: .25em
}

.label:empty {
  display: none
}

.btn .label {
  position: relative;
  top: -1px
}

a.label:hover,
a.label:focus {
  color: #fff;
  text-decoration: none;
  cursor: pointer
}

.label-default {
  background-color: #CCCCCC
}

.label-default[href]:hover,
.label-default[href]:focus {
  background-color: #b3b3b3
}

.label-primary {
  background-color: #CC0000
}

.label-primary[href]:hover,
.label-primary[href]:focus {
  background-color: #990000
}

.label-success {
  background-color: #06851F
}

.label-success[href]:hover,
.label-success[href]:focus {
  background-color: #045414
}

.label-info {
  background-color: #5bc0de
}

.label-info[href]:hover,
.label-info[href]:focus {
  background-color: #31b0d5
}

.label-warning {
  background-color: #f0ad4e
}

.label-warning[href]:hover,
.label-warning[href]:focus {
  background-color: #ec971f
}

.label-danger {
  background-color: #CC0000
}

.label-danger[href]:hover,
.label-danger[href]:focus {
  background-color: #990000
}

.badge {
  display: inline-block;
  min-width: 10px;
  padding: 3px 7px;
  font-size: 12px;
  font-weight: bold;
  color: #fff;
  line-height: 1;
  vertical-align: middle;
  white-space: nowrap;
  text-align: center;
  background-color: #CCCCCC;
  border-radius: 10px
}

.badge:empty {
  display: none
}

.btn .badge {
  position: relative;
  top: -1px
}

.btn-xs .badge,
.btn-group-xs>.btn .badge,
.btn-group-xs>.btn .badge {
  top: 0;
  padding: 1px 5px
}

.list-group-item.active>.badge,
.nav-pills>.active>a>.badge {
  color: #CC0000;
  background-color: #fff
}

.list-group-item>.badge {
  float: right
}

.list-group-item>.badge+.badge {
  margin-right: 5px
}

.nav-pills>li>a>.badge {
  margin-left: 3px
}

a.badge:hover,
a.badge:focus {
  color: #fff;
  text-decoration: none;
  cursor: pointer
}

.jumbotron {
  padding-top: 30px;
  padding-bottom: 30px;
  margin-bottom: 30px;
  color: inherit;
  background-color: #F9F9F9
}

.jumbotron h1,
.jumbotron .h1 {
  color: inherit
}

.jumbotron p {
  margin-bottom: 15px;
  font-size: 21px;
  font-weight: 200
}

.jumbotron>hr {
  border-top-color: #e0e0e0
}

.container .jumbotron,
.container-fluid .jumbotron {
  border-radius: 2px
}

.jumbotron .container {
  max-width: 100%
}

@media screen and (min-width: 768px) {
  .jumbotron {
    padding-top: 48px;
    padding-bottom: 48px
  }

  .container .jumbotron,
  .container-fluid .jumbotron {
    padding-left: 60px;
    padding-right: 60px
  }

  .jumbotron h1,
  .jumbotron .h1 {
    font-size: 63px
  }
}

.thumbnail {
  display: block;
  padding: 4px;
  margin-bottom: 20px;
  line-height: 1.42857143;
  background-color: #F9F9F9;
  border: 1px solid #ddd;
  border-radius: 2px;
  -webkit-transition: border 0.2s ease-in-out;
  transition: border 0.2s ease-in-out
}

.thumbnail>img,
.thumbnail a>img {
  display: block;
  max-width: 100%;
  height: auto;
  margin-left: auto;
  margin-right: auto
}

.thumbnail .caption {
  padding: 9px;
  color: #333333
}

a.thumbnail:hover,
a.thumbnail:focus,
a.thumbnail.active {
  border-color: #CC0000
}

.alert {
  padding: 15px;
  margin-bottom: 20px;
  border: 1px solid transparent;
  border-radius: 2px
}

.alert h4 {
  margin-top: 0;
  color: inherit
}

.alert .alert-link {
  font-weight: bold
}

.alert>p,
.alert>ul {
  margin-bottom: 0
}

.alert>p+p {
  margin-top: 5px
}

.alert-dismissable,
.alert-dismissible {
  padding-right: 35px
}

.alert-dismissable .close,
.alert-dismissible .close {
  position: relative;
  top: -2px;
  right: -21px;
  color: inherit
}

.alert-success {
  background-color: #06851F;
  border-color: #06851F;
  color: #fff
}

.alert-success hr {
  border-top-color: #056d19
}

.alert-success .alert-link {
  color: #e6e6e6
}

.alert-info {
  background-color: #d9edf7;
  border-color: #bce8f1;
  color: #31708f
}

.alert-info hr {
  border-top-color: #a6e1ec
}

.alert-info .alert-link {
  color: #245269
}

.alert-warning {
  background-color: #fcf8e3;
  border-color: #faebcc;
  color: #8a6d3b
}

.alert-warning hr {
  border-top-color: #f7e1b5
}

.alert-warning .alert-link {
  color: #66512c
}

.alert-danger {
  background-color: #CC0000;
  border-color: #CC0000;
  color: #fff
}

.alert-danger hr {
  border-top-color: #b30000
}

.alert-danger .alert-link {
  color: #e6e6e6
}

@-webkit-keyframes progress-bar-stripes {
  from {
    background-position: 40px 0
  }

  to {
    background-position: 0 0
  }
}

@keyframes progress-bar-stripes {
  from {
    background-position: 40px 0
  }

  to {
    background-position: 0 0
  }
}

.progress {
  overflow: hidden;
  height: 20px;
  margin-bottom: 20px;
  background-color: #f5f5f5;
  border-radius: 2px;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1)
}

.progress-bar {
  float: left;
  width: 0%;
  height: 100%;
  font-size: 12px;
  line-height: 20px;
  color: #fff;
  text-align: center;
  background-color: #333333;
  box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.15);
  -webkit-transition: width 0.6s ease;
  transition: width 0.6s ease
}

.progress-striped .progress-bar,
.progress-bar-striped {
  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px
}

.progress.active .progress-bar,
.progress-bar.active {
  -webkit-animation: progress-bar-stripes 2s linear infinite;
  animation: progress-bar-stripes 2s linear infinite
}

.progress-bar-success {
  background-color: #06851F
}

.progress-striped .progress-bar-success {
  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent)
}

.progress-bar-info {
  background-color: #5bc0de
}

.progress-striped .progress-bar-info {
  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent)
}

.progress-bar-warning {
  background-color: #f0ad4e
}

.progress-striped .progress-bar-warning {
  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent)
}

.progress-bar-danger {
  background-color: #CC0000
}

.progress-striped .progress-bar-danger {
  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent)
}

.media {
  margin-top: 15px
}

.media:first-child {
  margin-top: 0
}

.media,
.media-body {
  zoom: 1;
  overflow: hidden
}

.media-body {
  width: 10000px
}

.media-object {
  display: block
}

.media-object.img-thumbnail {
  max-width: none
}

.media-right,
.media>.pull-right {
  padding-left: 10px
}

.media-left,
.media>.pull-left {
  padding-right: 10px
}

.media-left,
.media-right,
.media-body {
  display: table-cell;
  vertical-align: top
}

.media-middle {
  vertical-align: middle
}

.media-bottom {
  vertical-align: bottom
}

.media-heading {
  margin-top: 0;
  margin-bottom: 5px
}

.media-list {
  padding-left: 0;
  list-style: none
}

.list-group {
  margin-bottom: 20px;
  padding-left: 0
}

.list-group-item {
  position: relative;
  display: block;
  padding: 10px 15px;
  margin-bottom: -1px;
  background-color: #fff;
  border: 1px solid #ddd
}

.list-group-item:first-child {
  border-top-right-radius: 2px;
  border-top-left-radius: 2px
}

.list-group-item:last-child {
  margin-bottom: 0;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 2px
}

a.list-group-item,
button.list-group-item {
  color: #555
}

a.list-group-item .list-group-item-heading,
button.list-group-item .list-group-item-heading {
  color: #333
}

a.list-group-item:hover,
a.list-group-item:focus,
button.list-group-item:hover,
button.list-group-item:focus {
  text-decoration: none;
  color: #555;
  background-color: #f5f5f5
}

button.list-group-item {
  width: 100%;
  text-align: left
}

.list-group-item.disabled,
.list-group-item.disabled:hover,
.list-group-item.disabled:focus {
  background-color: #F9F9F9;
  color: #E2E2E2;
  cursor: auto
}

.list-group-item.disabled .list-group-item-heading,
.list-group-item.disabled:hover .list-group-item-heading,
.list-group-item.disabled:focus .list-group-item-heading {
  color: inherit
}

.list-group-item.disabled .list-group-item-text,
.list-group-item.disabled:hover .list-group-item-text,
.list-group-item.disabled:focus .list-group-item-text {
  color: #E2E2E2
}

.list-group-item.active,
.list-group-item.active:hover,
.list-group-item.active:focus {
  z-index: 2;
  color: #fff;
  background-color: #CC0000;
  border-color: #CC0000
}

.list-group-item.active .list-group-item-heading,
.list-group-item.active .list-group-item-heading>small,
.list-group-item.active .list-group-item-heading>.small,
.list-group-item.active:hover .list-group-item-heading,
.list-group-item.active:hover .list-group-item-heading>small,
.list-group-item.active:hover .list-group-item-heading>.small,
.list-group-item.active:focus .list-group-item-heading,
.list-group-item.active:focus .list-group-item-heading>small,
.list-group-item.active:focus .list-group-item-heading>.small {
  color: inherit
}

.list-group-item.active .list-group-item-text,
.list-group-item.active:hover .list-group-item-text,
.list-group-item.active:focus .list-group-item-text {
  color: #ff9999
}

.list-group-item-success {
  color: #fff;
  background-color: #06851F
}

a.list-group-item-success,
button.list-group-item-success {
  color: #fff
}

a.list-group-item-success .list-group-item-heading,
button.list-group-item-success .list-group-item-heading {
  color: inherit
}

a.list-group-item-success:hover,
a.list-group-item-success:focus,
button.list-group-item-success:hover,
button.list-group-item-success:focus {
  color: #fff;
  background-color: #056d19
}

a.list-group-item-success.active,
a.list-group-item-success.active:hover,
a.list-group-item-success.active:focus,
button.list-group-item-success.active,
button.list-group-item-success.active:hover,
button.list-group-item-success.active:focus {
  color: #fff;
  background-color: #fff;
  border-color: #fff
}

.list-group-item-info {
  color: #31708f;
  background-color: #d9edf7
}

a.list-group-item-info,
button.list-group-item-info {
  color: #31708f
}

a.list-group-item-info .list-group-item-heading,
button.list-group-item-info .list-group-item-heading {
  color: inherit
}

a.list-group-item-info:hover,
a.list-group-item-info:focus,
button.list-group-item-info:hover,
button.list-group-item-info:focus {
  color: #31708f;
  background-color: #c4e3f3
}

a.list-group-item-info.active,
a.list-group-item-info.active:hover,
a.list-group-item-info.active:focus,
button.list-group-item-info.active,
button.list-group-item-info.active:hover,
button.list-group-item-info.active:focus {
  color: #fff;
  background-color: #31708f;
  border-color: #31708f
}

.list-group-item-warning {
  color: #8a6d3b;
  background-color: #fcf8e3
}

a.list-group-item-warning,
button.list-group-item-warning {
  color: #8a6d3b
}

a.list-group-item-warning .list-group-item-heading,
button.list-group-item-warning .list-group-item-heading {
  color: inherit
}

a.list-group-item-warning:hover,
a.list-group-item-warning:focus,
button.list-group-item-warning:hover,
button.list-group-item-warning:focus {
  color: #8a6d3b;
  background-color: #faf2cc
}

a.list-group-item-warning.active,
a.list-group-item-warning.active:hover,
a.list-group-item-warning.active:focus,
button.list-group-item-warning.active,
button.list-group-item-warning.active:hover,
button.list-group-item-warning.active:focus {
  color: #fff;
  background-color: #8a6d3b;
  border-color: #8a6d3b
}

.list-group-item-danger {
  color: #a94442;
  background-color: #f2dede
}

a.list-group-item-danger,
button.list-group-item-danger {
  color: #a94442
}

a.list-group-item-danger .list-group-item-heading,
button.list-group-item-danger .list-group-item-heading {
  color: inherit
}

a.list-group-item-danger:hover,
a.list-group-item-danger:focus,
button.list-group-item-danger:hover,
button.list-group-item-danger:focus {
  color: #a94442;
  background-color: #ebcccc
}

a.list-group-item-danger.active,
a.list-group-item-danger.active:hover,
a.list-group-item-danger.active:focus,
button.list-group-item-danger.active,
button.list-group-item-danger.active:hover,
button.list-group-item-danger.active:focus {
  color: #fff;
  background-color: #a94442;
  border-color: #a94442
}

.list-group-item-heading {
  margin-top: 0;
  margin-bottom: 5px
}

.list-group-item-text {
  margin-bottom: 0;
  line-height: 1.3
}

.panel {
  margin-bottom: 20px;
  background-color: #fff;
  border: 1px solid transparent;
  border-radius: 2px;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05)
}

.panel-body {
  padding: 15px
}

.panel-body:before,
.panel-body:after {
  content: " ";
  display: table
}

.panel-body:after {
  clear: both
}

.panel-heading {
  padding: 10px 15px;
  border-bottom: 1px solid transparent;
  border-top-right-radius: 1px;
  border-top-left-radius: 1px
}

.panel-heading>.dropdown .dropdown-toggle {
  color: inherit
}

.panel-title {
  margin-top: 0;
  margin-bottom: 0;
  font-size: 16px;
  color: inherit
}

.panel-title>a,
.panel-title>small,
.panel-title>.small,
.panel-title>small>a,
.panel-title>.small>a {
  color: inherit
}

.panel-footer {
  padding: 10px 15px;
  background-color: #f5f5f5;
  border-top: 1px solid #ddd;
  border-bottom-right-radius: 1px;
  border-bottom-left-radius: 1px
}

.panel>.list-group,
.panel>.panel-collapse>.list-group {
  margin-bottom: 0
}

.panel>.list-group .list-group-item,
.panel>.panel-collapse>.list-group .list-group-item {
  border-width: 1px 0;
  border-radius: 0
}

.panel>.list-group:first-child .list-group-item:first-child,
.panel>.panel-collapse>.list-group:first-child .list-group-item:first-child {
  border-top: 0;
  border-top-right-radius: 1px;
  border-top-left-radius: 1px
}

.panel>.list-group:last-child .list-group-item:last-child,
.panel>.panel-collapse>.list-group:last-child .list-group-item:last-child {
  border-bottom: 0;
  border-bottom-right-radius: 1px;
  border-bottom-left-radius: 1px
}

.panel>.panel-heading+.panel-collapse>.list-group .list-group-item:first-child {
  border-top-right-radius: 0;
  border-top-left-radius: 0
}

.panel-heading+.list-group .list-group-item:first-child {
  border-top-width: 0
}

.list-group+.panel-footer {
  border-top-width: 0
}

.panel>.table,
.panel>.table-responsive>.table,
.panel>.panel-collapse>.table {
  margin-bottom: 0
}

.panel>.table caption,
.panel>.table-responsive>.table caption,
.panel>.panel-collapse>.table caption {
  padding-left: 15px;
  padding-right: 15px
}

.panel>.table:first-child,
.panel>.table-responsive:first-child>.table:first-child {
  border-top-right-radius: 1px;
  border-top-left-radius: 1px
}

.panel>.table:first-child>thead:first-child>tr:first-child,
.panel>.table:first-child>tbody:first-child>tr:first-child,
.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child,
.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child {
  border-top-left-radius: 1px;
  border-top-right-radius: 1px
}

.panel>.table:first-child>thead:first-child>tr:first-child td:first-child,
.panel>.table:first-child>thead:first-child>tr:first-child th:first-child,
.panel>.table:first-child>tbody:first-child>tr:first-child td:first-child,
.panel>.table:first-child>tbody:first-child>tr:first-child th:first-child,
.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child td:first-child,
.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child th:first-child,
.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child td:first-child,
.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child th:first-child {
  border-top-left-radius: 1px
}

.panel>.table:first-child>thead:first-child>tr:first-child td:last-child,
.panel>.table:first-child>thead:first-child>tr:first-child th:last-child,
.panel>.table:first-child>tbody:first-child>tr:first-child td:last-child,
.panel>.table:first-child>tbody:first-child>tr:first-child th:last-child,
.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child td:last-child,
.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child th:last-child,
.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child td:last-child,
.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child th:last-child {
  border-top-right-radius: 1px
}

.panel>.table:last-child,
.panel>.table-responsive:last-child>.table:last-child {
  border-bottom-right-radius: 1px;
  border-bottom-left-radius: 1px
}

.panel>.table:last-child>tbody:last-child>tr:last-child,
.panel>.table:last-child>tfoot:last-child>tr:last-child,
.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child,
.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child {
  border-bottom-left-radius: 1px;
  border-bottom-right-radius: 1px
}

.panel>.table:last-child>tbody:last-child>tr:last-child td:first-child,
.panel>.table:last-child>tbody:last-child>tr:last-child th:first-child,
.panel>.table:last-child>tfoot:last-child>tr:last-child td:first-child,
.panel>.table:last-child>tfoot:last-child>tr:last-child th:first-child,
.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child td:first-child,
.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child th:first-child,
.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child td:first-child,
.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child th:first-child {
  border-bottom-left-radius: 1px
}

.panel>.table:last-child>tbody:last-child>tr:last-child td:last-child,
.panel>.table:last-child>tbody:last-child>tr:last-child th:last-child,
.panel>.table:last-child>tfoot:last-child>tr:last-child td:last-child,
.panel>.table:last-child>tfoot:last-child>tr:last-child th:last-child,
.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child td:last-child,
.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child th:last-child,
.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child td:last-child,
.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child th:last-child {
  border-bottom-right-radius: 1px
}

.panel>.panel-body+.table,
.panel>.panel-body+.table-responsive,
.panel>.table+.panel-body,
.panel>.table-responsive+.panel-body {
  border-top: 1px solid #CCCCCC
}

.panel>.table>tbody:first-child>tr:first-child th,
.panel>.table>tbody:first-child>tr:first-child td {
  border-top: 0
}

.panel>.table-bordered,
.panel>.table-responsive>.table-bordered {
  border: 0
}

.panel>.table-bordered>thead>tr>th:first-child,
.panel>.table-bordered>thead>tr>td:first-child,
.panel>.table-bordered>tbody>tr>th:first-child,
.panel>.table-bordered>tbody>tr>td:first-child,
.panel>.table-bordered>tfoot>tr>th:first-child,
.panel>.table-bordered>tfoot>tr>td:first-child,
.panel>.table-responsive>.table-bordered>thead>tr>th:first-child,
.panel>.table-responsive>.table-bordered>thead>tr>td:first-child,
.panel>.table-responsive>.table-bordered>tbody>tr>th:first-child,
.panel>.table-responsive>.table-bordered>tbody>tr>td:first-child,
.panel>.table-responsive>.table-bordered>tfoot>tr>th:first-child,
.panel>.table-responsive>.table-bordered>tfoot>tr>td:first-child {
  border-left: 0
}

.panel>.table-bordered>thead>tr>th:last-child,
.panel>.table-bordered>thead>tr>td:last-child,
.panel>.table-bordered>tbody>tr>th:last-child,
.panel>.table-bordered>tbody>tr>td:last-child,
.panel>.table-bordered>tfoot>tr>th:last-child,
.panel>.table-bordered>tfoot>tr>td:last-child,
.panel>.table-responsive>.table-bordered>thead>tr>th:last-child,
.panel>.table-responsive>.table-bordered>thead>tr>td:last-child,
.panel>.table-responsive>.table-bordered>tbody>tr>th:last-child,
.panel>.table-responsive>.table-bordered>tbody>tr>td:last-child,
.panel>.table-responsive>.table-bordered>tfoot>tr>th:last-child,
.panel>.table-responsive>.table-bordered>tfoot>tr>td:last-child {
  border-right: 0
}

.panel>.table-bordered>thead>tr:first-child>td,
.panel>.table-bordered>thead>tr:first-child>th,
.panel>.table-bordered>tbody>tr:first-child>td,
.panel>.table-bordered>tbody>tr:first-child>th,
.panel>.table-responsive>.table-bordered>thead>tr:first-child>td,
.panel>.table-responsive>.table-bordered>thead>tr:first-child>th,
.panel>.table-responsive>.table-bordered>tbody>tr:first-child>td,
.panel>.table-responsive>.table-bordered>tbody>tr:first-child>th {
  border-bottom: 0
}

.panel>.table-bordered>tbody>tr:last-child>td,
.panel>.table-bordered>tbody>tr:last-child>th,
.panel>.table-bordered>tfoot>tr:last-child>td,
.panel>.table-bordered>tfoot>tr:last-child>th,
.panel>.table-responsive>.table-bordered>tbody>tr:last-child>td,
.panel>.table-responsive>.table-bordered>tbody>tr:last-child>th,
.panel>.table-responsive>.table-bordered>tfoot>tr:last-child>td,
.panel>.table-responsive>.table-bordered>tfoot>tr:last-child>th {
  border-bottom: 0
}

.panel>.table-responsive {
  border: 0;
  margin-bottom: 0
}

.panel-group {
  margin-bottom: 20px
}

.panel-group .panel {
  margin-bottom: 0;
  border-radius: 2px
}

.panel-group .panel+.panel {
  margin-top: 5px
}

.panel-group .panel-heading {
  border-bottom: 0
}

.panel-group .panel-heading+.panel-collapse>.panel-body,
.panel-group .panel-heading+.panel-collapse>.list-group {
  border-top: 1px solid #ddd
}

.panel-group .panel-footer {
  border-top: 0
}

.panel-group .panel-footer+.panel-collapse .panel-body {
  border-bottom: 1px solid #ddd
}

.panel-default {
  border-color: #ddd
}

.panel-default>.panel-heading {
  color: #979797;
  background-color: #f5f5f5;
  border-color: #ddd
}

.panel-default>.panel-heading+.panel-collapse>.panel-body {
  border-top-color: #ddd
}

.panel-default>.panel-heading .badge {
  color: #f5f5f5;
  background-color: #979797
}

.panel-default>.panel-footer+.panel-collapse>.panel-body {
  border-bottom-color: #ddd
}

.panel-primary {
  border-color: #CC0000
}

.panel-primary>.panel-heading {
  color: #fff;
  background-color: #CC0000;
  border-color: #CC0000
}

.panel-primary>.panel-heading+.panel-collapse>.panel-body {
  border-top-color: #CC0000
}

.panel-primary>.panel-heading .badge {
  color: #CC0000;
  background-color: #fff
}

.panel-primary>.panel-footer+.panel-collapse>.panel-body {
  border-bottom-color: #CC0000
}

.panel-success {
  border-color: #06851F
}

.panel-success>.panel-heading {
  color: #fff;
  background-color: #06851F;
  border-color: #06851F
}

.panel-success>.panel-heading+.panel-collapse>.panel-body {
  border-top-color: #06851F
}

.panel-success>.panel-heading .badge {
  color: #06851F;
  background-color: #fff
}

.panel-success>.panel-footer+.panel-collapse>.panel-body {
  border-bottom-color: #06851F
}

.panel-info {
  border-color: #bce8f1
}

.panel-info>.panel-heading {
  color: #31708f;
  background-color: #d9edf7;
  border-color: #bce8f1
}

.panel-info>.panel-heading+.panel-collapse>.panel-body {
  border-top-color: #bce8f1
}

.panel-info>.panel-heading .badge {
  color: #d9edf7;
  background-color: #31708f
}

.panel-info>.panel-footer+.panel-collapse>.panel-body {
  border-bottom-color: #bce8f1
}

.panel-warning {
  border-color: #faebcc
}

.panel-warning>.panel-heading {
  color: #8a6d3b;
  background-color: #fcf8e3;
  border-color: #faebcc
}

.panel-warning>.panel-heading+.panel-collapse>.panel-body {
  border-top-color: #faebcc
}

.panel-warning>.panel-heading .badge {
  color: #fcf8e3;
  background-color: #8a6d3b
}

.panel-warning>.panel-footer+.panel-collapse>.panel-body {
  border-bottom-color: #faebcc
}

.panel-danger {
  border-color: #ebccd1
}

.panel-danger>.panel-heading {
  color: #a94442;
  background-color: #f2dede;
  border-color: #ebccd1
}

.panel-danger>.panel-heading+.panel-collapse>.panel-body {
  border-top-color: #ebccd1
}

.panel-danger>.panel-heading .badge {
  color: #f2dede;
  background-color: #a94442
}

.panel-danger>.panel-footer+.panel-collapse>.panel-body {
  border-bottom-color: #ebccd1
}

.embed-responsive {
  position: relative;
  display: block;
  height: 0;
  padding: 0;
  overflow: hidden
}

.embed-responsive .embed-responsive-item,
.embed-responsive iframe,
.embed-responsive embed,
.embed-responsive object,
.embed-responsive video {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  height: 100%;
  width: 100%;
  border: 0
}

.embed-responsive-16by9 {
  padding-bottom: 56.25%
}

.embed-responsive-4by3 {
  padding-bottom: 75%
}

.well {
  min-height: 20px;
  padding: 19px;
  margin-bottom: 20px;
  background-color: #f5f5f5;
  border: 1px solid #e3e3e3;
  border-radius: 2px;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05)
}

.well blockquote {
  border-color: #ddd;
  border-color: rgba(0, 0, 0, 0.15)
}

.well-lg {
  padding: 24px;
  border-radius: 2px
}

.well-sm {
  padding: 9px;
  border-radius: 2px
}

.close {
  float: right;
  font-size: 21px;
  font-weight: bold;
  line-height: 1;
  color: #fff;
  text-shadow: none;
  opacity: 0.2;
  filter: alpha(opacity=20)
}

.close:hover,
.close:focus {
  color: #fff;
  text-decoration: none;
  cursor: pointer;
  opacity: 0.5;
  filter: alpha(opacity=50)
}

button.close {
  padding: 0;
  cursor: pointer;
  background: transparent;
  border: 0;
  -webkit-appearance: none
}

.modal-open {
  overflow: hidden
}

.modal {
  display: none;
  overflow: hidden;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1050;
  -webkit-overflow-scrolling: touch;
  outline: 0
}

.modal.fade .modal-dialog {
  -webkit-transform: translate(0, -25%);
  -ms-transform: translate(0, -25%);
  transform: translate(0, -25%);
  -webkit-transition: -webkit-transform 0.3s ease-out;
  transition: transform 0.3s ease-out
}

.modal.in .modal-dialog {
  -webkit-transform: translate(0, 0);
  -ms-transform: translate(0, 0);
  transform: translate(0, 0)
}

.modal-open .modal {
  overflow-x: hidden;
  overflow-y: auto
}

.modal-dialog {
  position: relative;
  width: auto;
  margin: 10px
}

.modal-content {
  position: relative;
  background-color: #F9F9F9;
  border: 1px solid #F9F9F9;
  border: 1px solid #F9F9F9;
  border-radius: 2px;
  box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);
  background-clip: padding-box;
  outline: 0
}

.modal-backdrop {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1040;
  background-color: #333333
}

.modal-backdrop.fade {
  opacity: 0;
  filter: alpha(opacity=0)
}

.modal-backdrop.in {
  opacity: 0.8;
  filter: alpha(opacity=80)
}

.modal-header {
  padding: 15px;
  border-bottom: 1px solid #CCCCCC;
  min-height: 16.42857143px
}

.modal-header .close {
  margin-top: -2px
}

.modal-title {
  margin: 0;
  line-height: 1.42857143
}

.modal-body {
  position: relative;
  padding: 20px
}

.modal-footer {
  padding: 20px;
  text-align: right;
  border-top: 1px solid #CCCCCC
}

.modal-footer:before,
.modal-footer:after {
  content: " ";
  display: table
}

.modal-footer:after {
  clear: both
}

.modal-footer .btn+.btn {
  margin-left: 5px;
  margin-bottom: 0
}

.modal-footer .btn-group .btn+.btn {
  margin-left: -1px
}

.modal-footer .btn-block+.btn-block {
  margin-left: 0
}

.modal-scrollbar-measure {
  position: absolute;
  top: -9999px;
  width: 50px;
  height: 50px;
  overflow: scroll
}

@media (min-width: 768px) {
  .modal-dialog {
    width: 600px;
    margin: 30px auto
  }

  .modal-content {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5)
  }

  .modal-sm {
    width: 300px
  }
}

@media (min-width: 992px) {
  .modal-lg {
    width: 900px
  }
}

.tooltip {
  position: absolute;
  z-index: 1070;
  display: block;
  font-family: "Roboto", Helvetica, Arial, sans-serif;
  font-style: normal;
  font-weight: normal;
  letter-spacing: normal;
  line-break: auto;
  line-height: 1.42857143;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  white-space: normal;
  word-break: normal;
  word-spacing: normal;
  word-wrap: normal;
  font-size: 12px;
  opacity: 0;
  filter: alpha(opacity=0)
}

.tooltip.in {
  opacity: 0.9;
  filter: alpha(opacity=90)
}

.tooltip.top {
  margin-top: -3px;
  padding: 5px 0
}

.tooltip.right {
  margin-left: 3px;
  padding: 0 5px
}

.tooltip.bottom {
  margin-top: 3px;
  padding: 5px 0
}

.tooltip.left {
  margin-left: -3px;
  padding: 0 5px
}

.tooltip-inner {
  max-width: 200px;
  padding: 3px 8px;
  color: #fff;
  text-align: center;
  background-color: #000;
  border-radius: 2px
}

.tooltip-arrow {
  position: absolute;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid
}

.tooltip.top .tooltip-arrow {
  bottom: 0;
  left: 50%;
  margin-left: -5px;
  border-width: 5px 5px 0;
  border-top-color: #000
}

.tooltip.top-left .tooltip-arrow {
  bottom: 0;
  right: 5px;
  margin-bottom: -5px;
  border-width: 5px 5px 0;
  border-top-color: #000
}

.tooltip.top-right .tooltip-arrow {
  bottom: 0;
  left: 5px;
  margin-bottom: -5px;
  border-width: 5px 5px 0;
  border-top-color: #000
}

.tooltip.right .tooltip-arrow {
  top: 50%;
  left: 0;
  margin-top: -5px;
  border-width: 5px 5px 5px 0;
  border-right-color: #000
}

.tooltip.left .tooltip-arrow {
  top: 50%;
  right: 0;
  margin-top: -5px;
  border-width: 5px 0 5px 5px;
  border-left-color: #000
}

.tooltip.bottom .tooltip-arrow {
  top: 0;
  left: 50%;
  margin-left: -5px;
  border-width: 0 5px 5px;
  border-bottom-color: #000
}

.tooltip.bottom-left .tooltip-arrow {
  top: 0;
  right: 5px;
  margin-top: -5px;
  border-width: 0 5px 5px;
  border-bottom-color: #000
}

.tooltip.bottom-right .tooltip-arrow {
  top: 0;
  left: 5px;
  margin-top: -5px;
  border-width: 0 5px 5px;
  border-bottom-color: #000
}

.popover {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1060;
  display: none;
  max-width: 276px;
  padding: 1px;
  font-family: "Roboto", Helvetica, Arial, sans-serif;
  font-style: normal;
  font-weight: normal;
  letter-spacing: normal;
  line-break: auto;
  line-height: 1.42857143;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  white-space: normal;
  word-break: normal;
  word-spacing: normal;
  word-wrap: normal;
  font-size: 14px;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ccc;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 2px;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2)
}

.popover.top {
  margin-top: -10px
}

.popover.right {
  margin-left: 10px
}

.popover.bottom {
  margin-top: 10px
}

.popover.left {
  margin-left: -10px
}

.popover-title {
  margin: 0;
  padding: 8px 14px;
  font-size: 14px;
  background-color: #f7f7f7;
  border-bottom: 1px solid #ebebeb;
  border-radius: 1px 1px 0 0
}

.popover-content {
  padding: 9px 14px
}

.popover>.arrow,
.popover>.arrow:after {
  position: absolute;
  display: block;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid
}

.popover>.arrow {
  border-width: 11px
}

.popover>.arrow:after {
  border-width: 10px;
  content: ""
}

.popover.top>.arrow {
  left: 50%;
  margin-left: -11px;
  border-bottom-width: 0;
  border-top-color: #999999;
  border-top-color: rgba(0, 0, 0, 0.25);
  bottom: -11px
}

.popover.top>.arrow:after {
  content: " ";
  bottom: 1px;
  margin-left: -10px;
  border-bottom-width: 0;
  border-top-color: #fff
}

.popover.right>.arrow {
  top: 50%;
  left: -11px;
  margin-top: -11px;
  border-left-width: 0;
  border-right-color: #999999;
  border-right-color: rgba(0, 0, 0, 0.25)
}

.popover.right>.arrow:after {
  content: " ";
  left: 1px;
  bottom: -10px;
  border-left-width: 0;
  border-right-color: #fff
}

.popover.bottom>.arrow {
  left: 50%;
  margin-left: -11px;
  border-top-width: 0;
  border-bottom-color: #999999;
  border-bottom-color: rgba(0, 0, 0, 0.25);
  top: -11px
}

.popover.bottom>.arrow:after {
  content: " ";
  top: 1px;
  margin-left: -10px;
  border-top-width: 0;
  border-bottom-color: #fff
}

.popover.left>.arrow {
  top: 50%;
  right: -11px;
  margin-top: -11px;
  border-right-width: 0;
  border-left-color: #999999;
  border-left-color: rgba(0, 0, 0, 0.25)
}

.popover.left>.arrow:after {
  content: " ";
  right: 1px;
  border-right-width: 0;
  border-left-color: #fff;
  bottom: -10px
}

.carousel {
  position: relative
}

.carousel-inner {
  position: relative;
  overflow: hidden;
  width: 100%
}

.carousel-inner>.item {
  display: none;
  position: relative;
  -webkit-transition: 0.6s ease-in-out left;
  transition: 0.6s ease-in-out left
}

.carousel-inner>.item>img,
.carousel-inner>.item>a>img {
  display: block;
  max-width: 100%;
  height: auto;
  line-height: 1
}

@media all and (transform-3d),
(-webkit-transform-3d) {
  .carousel-inner>.item {
    -webkit-transition: -webkit-transform 0.6s ease-in-out;
    transition: transform 0.6s ease-in-out;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-perspective: 1000px;
    perspective: 1000px
  }

  .carousel-inner>.item.next,
  .carousel-inner>.item.active.right {
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
    left: 0
  }

  .carousel-inner>.item.prev,
  .carousel-inner>.item.active.left {
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
    left: 0
  }

  .carousel-inner>.item.next.left,
  .carousel-inner>.item.prev.right,
  .carousel-inner>.item.active {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    left: 0
  }
}

.carousel-inner>.active,
.carousel-inner>.next,
.carousel-inner>.prev {
  display: block
}

.carousel-inner>.active {
  left: 0
}

.carousel-inner>.next,
.carousel-inner>.prev {
  position: absolute;
  top: 0;
  width: 100%
}

.carousel-inner>.next {
  left: 100%
}

.carousel-inner>.prev {
  left: -100%
}

.carousel-inner>.next.left,
.carousel-inner>.prev.right {
  left: 0
}

.carousel-inner>.active.left {
  left: -100%
}

.carousel-inner>.active.right {
  left: 100%
}

.carousel-control {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 15%;
  opacity: 0.5;
  filter: alpha(opacity=50);
  font-size: 20px;
  color: #fff;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6)
}

.carousel-control.left {
  background-image: -webkit-linear-gradient(left, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.0001) 100%);
  background-image: linear-gradient(to right, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.0001) 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#80000000', endColorstr='#00000000', GradientType=1)
}

.carousel-control.right {
  left: auto;
  right: 0;
  background-image: -webkit-linear-gradient(left, rgba(0, 0, 0, 0.0001) 0%, rgba(0, 0, 0, 0.5) 100%);
  background-image: linear-gradient(to right, rgba(0, 0, 0, 0.0001) 0%, rgba(0, 0, 0, 0.5) 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#00000000', endColorstr='#80000000', GradientType=1)
}

.carousel-control:hover,
.carousel-control:focus {
  outline: 0;
  color: #fff;
  text-decoration: none;
  opacity: 0.9;
  filter: alpha(opacity=90)
}

.carousel-control .icon-prev,
.carousel-control .icon-next,
.carousel-control .glyphicon-chevron-left,
.carousel-control .glyphicon-chevron-right {
  position: absolute;
  top: 50%;
  margin-top: -10px;
  z-index: 5;
  display: inline-block
}

.carousel-control .icon-prev,
.carousel-control .glyphicon-chevron-left {
  left: 50%;
  margin-left: -10px
}

.carousel-control .icon-next,
.carousel-control .glyphicon-chevron-right {
  right: 50%;
  margin-right: -10px
}

.carousel-control .icon-prev,
.carousel-control .icon-next {
  width: 20px;
  height: 20px;
  line-height: 1;
  font-family: serif
}

.carousel-control .icon-prev:before {
  content: '\2039'
}

.carousel-control .icon-next:before {
  content: '\203a'
}

.carousel-indicators {
  position: absolute;
  bottom: 10px;
  left: 50%;
  z-index: 15;
  width: 60%;
  margin-left: -30%;
  padding-left: 0;
  list-style: none;
  text-align: center
}

.carousel-indicators li {
  display: inline-block;
  width: 10px;
  height: 10px;
  margin: 1px;
  text-indent: -999px;
  border: 1px solid #fff;
  border-radius: 10px;
  cursor: pointer;
  background-color: #000 \9;
  background-color: transparent
}

.carousel-indicators .active {
  margin: 0;
  width: 12px;
  height: 12px;
  background-color: #fff
}

.carousel-caption {
  position: absolute;
  left: 15%;
  right: 15%;
  bottom: 20px;
  z-index: 10;
  padding-top: 20px;
  padding-bottom: 20px;
  color: #fff;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6)
}

.carousel-caption .btn {
  text-shadow: none
}

@media screen and (min-width: 768px) {

  .carousel-control .glyphicon-chevron-left,
  .carousel-control .glyphicon-chevron-right,
  .carousel-control .icon-prev,
  .carousel-control .icon-next {
    width: 30px;
    height: 30px;
    margin-top: -15px;
    font-size: 30px
  }

  .carousel-control .glyphicon-chevron-left,
  .carousel-control .icon-prev {
    margin-left: -15px
  }

  .carousel-control .glyphicon-chevron-right,
  .carousel-control .icon-next {
    margin-right: -15px
  }

  .carousel-caption {
    left: 20%;
    right: 20%;
    padding-bottom: 30px
  }

  .carousel-indicators {
    bottom: 20px
  }
}

.clearfix:before,
.clearfix:after {
  content: " ";
  display: table
}

.clearfix:after {
  clear: both
}

.center-block {
  display: block;
  margin-left: auto;
  margin-right: auto
}

.pull-right {
  float: right !important
}

.pull-left {
  float: left !important
}

.hide {
  display: none !important
}

.show {
  display: block !important
}

.invisible {
  visibility: hidden
}

.text-hide {
  font: 0/0 a;
  color: transparent;
  text-shadow: none;
  background-color: transparent;
  border: 0
}

.hidden {
  display: none !important
}

.affix {
  position: fixed
}

@-ms-viewport {
  width: device-width
}

.visible-xs {
  display: none !important
}

.visible-sm {
  display: none !important
}

.visible-md {
  display: none !important
}

.visible-lg {
  display: none !important
}

.visible-xs-block,
.visible-xs-inline,
.visible-xs-inline-block,
.visible-sm-block,
.visible-sm-inline,
.visible-sm-inline-block,
.visible-md-block,
.visible-md-inline,
.visible-md-inline-block,
.visible-lg-block,
.visible-lg-inline,
.visible-lg-inline-block {
  display: none !important
}

@media (max-width: 767px) {
  .visible-xs {
    display: block !important
  }

  table.visible-xs {
    display: table !important
  }

  tr.visible-xs {
    display: table-row !important
  }

  th.visible-xs,
  td.visible-xs {
    display: table-cell !important
  }
}

@media (max-width: 767px) {
  .visible-xs-block {
    display: block !important
  }
}

@media (max-width: 767px) {
  .visible-xs-inline {
    display: inline !important
  }
}

@media (max-width: 767px) {
  .visible-xs-inline-block {
    display: inline-block !important
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  .visible-sm {
    display: block !important
  }

  table.visible-sm {
    display: table !important
  }

  tr.visible-sm {
    display: table-row !important
  }

  th.visible-sm,
  td.visible-sm {
    display: table-cell !important
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  .visible-sm-block {
    display: block !important
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  .visible-sm-inline {
    display: inline !important
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  .visible-sm-inline-block {
    display: inline-block !important
  }
}

@media (min-width: 992px) and (max-width: 1199px) {
  .visible-md {
    display: block !important
  }

  table.visible-md {
    display: table !important
  }

  tr.visible-md {
    display: table-row !important
  }

  th.visible-md,
  td.visible-md {
    display: table-cell !important
  }
}

@media (min-width: 992px) and (max-width: 1199px) {
  .visible-md-block {
    display: block !important
  }
}

@media (min-width: 992px) and (max-width: 1199px) {
  .visible-md-inline {
    display: inline !important
  }
}

@media (min-width: 992px) and (max-width: 1199px) {
  .visible-md-inline-block {
    display: inline-block !important
  }
}

@media (min-width: 1200px) {
  .visible-lg {
    display: block !important
  }

  table.visible-lg {
    display: table !important
  }

  tr.visible-lg {
    display: table-row !important
  }

  th.visible-lg,
  td.visible-lg {
    display: table-cell !important
  }
}

@media (min-width: 1200px) {
  .visible-lg-block {
    display: block !important
  }
}

@media (min-width: 1200px) {
  .visible-lg-inline {
    display: inline !important
  }
}

@media (min-width: 1200px) {
  .visible-lg-inline-block {
    display: inline-block !important
  }
}

@media (max-width: 767px) {
  .hidden-xs {
    display: none !important
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  .hidden-sm {
    display: none !important
  }
}

@media (min-width: 992px) and (max-width: 1199px) {
  .hidden-md {
    display: none !important
  }
}

@media (min-width: 1200px) {
  .hidden-lg {
    display: none !important
  }
}

.visible-print {
  display: none !important
}

@media print {
  .visible-print {
    display: block !important
  }

  table.visible-print {
    display: table !important
  }

  tr.visible-print {
    display: table-row !important
  }

  th.visible-print,
  td.visible-print {
    display: table-cell !important
  }
}

.visible-print-block {
  display: none !important
}

@media print {
  .visible-print-block {
    display: block !important
  }
}

.visible-print-inline {
  display: none !important
}

@media print {
  .visible-print-inline {
    display: inline !important
  }
}

.visible-print-inline-block {
  display: none !important
}

@media print {
  .visible-print-inline-block {
    display: inline-block !important
  }
}

@media print {
  .hidden-print {
    display: none !important
  }
}

.selectize-control.plugin-drag_drop.multi>.selectize-input>div.ui-sortable-placeholder {
  visibility: visible !important;
  background: #f2f2f2 !important;
  background: rgba(0, 0, 0, 0.06) !important;
  border: 0 none !important;
  box-shadow: inset 0 0 12px 4px #ffffff
}

.selectize-control.plugin-drag_drop .ui-sortable-placeholder::after {
  content: '!';
  visibility: hidden
}

.selectize-control.plugin-drag_drop .ui-sortable-helper {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2)
}

.selectize-dropdown-header {
  position: relative;
  padding: 3px 12px;
  border-bottom: 1px solid #d0d0d0;
  background: #f8f8f8;
  border-radius: 4px 4px 0 0
}

.selectize-dropdown-header-close {
  position: absolute;
  right: 12px;
  top: 50%;
  color: #333333;
  opacity: 0.4;
  margin-top: -12px;
  line-height: 20px;
  font-size: 20px !important
}

.selectize-dropdown-header-close:hover {
  color: #000000
}

.selectize-dropdown.plugin-optgroup_columns .optgroup {
  border-right: 1px solid #f2f2f2;
  border-top: 0 none;
  float: left;
  box-sizing: border-box
}

.selectize-dropdown.plugin-optgroup_columns .optgroup:last-child {
  border-right: 0 none
}

.selectize-dropdown.plugin-optgroup_columns .optgroup:before {
  display: none
}

.selectize-dropdown.plugin-optgroup_columns .optgroup-header {
  border-top: 0 none
}

.selectize-control.plugin-remove_button [data-value] {
  position: relative;
  padding-right: 24px !important
}

.selectize-control.plugin-remove_button [data-value] .remove {
  z-index: 1;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 17px;
  text-align: center;
  font-weight: bold;
  font-size: 12px;
  color: inherit;
  text-decoration: none;
  vertical-align: middle;
  display: inline-block;
  padding: 1px 0 0 0;
  border-left: 1px solid transparent;
  border-radius: 0 2px 2px 0;
  box-sizing: border-box
}

.selectize-control.plugin-remove_button [data-value] .remove:hover {
  background: rgba(0, 0, 0, 0.05)
}

.selectize-control.plugin-remove_button [data-value].active .remove {
  border-left-color: transparent
}

.selectize-control.plugin-remove_button .disabled [data-value] .remove:hover {
  background: none
}

.selectize-control.plugin-remove_button .disabled [data-value] .remove {
  border-left-color: rgba(77, 77, 77, 0)
}

.selectize-control {
  position: relative
}

.selectize-dropdown,
.selectize-input,
.selectize-input input {
  color: #333333;
  font-family: inherit;
  font-size: inherit;
  line-height: 20px;
  -webkit-font-smoothing: inherit
}

.selectize-input,
.selectize-control.single .selectize-input.input-active {
  background: #ffffff;
  cursor: text;
  display: inline-block
}

.selectize-input {
  border: 1px solid #cccccc;
  padding: 6px 12px;
  display: inline-block;
  width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 1;
  box-sizing: border-box;
  box-shadow: none;
  border-radius: 4px
}

.selectize-control.multi .selectize-input.has-items {
  padding: 5px 12px 2px
}

.selectize-input.full {
  background-color: #ffffff
}

.selectize-input.disabled,
.selectize-input.disabled * {
  cursor: default !important
}

.selectize-input.focus {
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.15)
}

.selectize-input.dropdown-active {
  border-radius: 4px 4px 0 0
}

.selectize-input>* {
  vertical-align: baseline;
  display: -moz-inline-stack;
  display: inline-block;
  zoom: 1;
  *display: inline
}

.selectize-control.multi .selectize-input>div {
  cursor: pointer;
  margin: 0 3px 3px 0;
  padding: 1px 3px;
  background: #efefef;
  color: #333333;
  border: 0 solid transparent
}

.selectize-control.multi .selectize-input>div.active {
  background: #428bca;
  color: #ffffff;
  border: 0 solid transparent
}

.selectize-control.multi .selectize-input.disabled>div,
.selectize-control.multi .selectize-input.disabled>div.active {
  color: #808080;
  background: #ffffff;
  border: 0 solid rgba(77, 77, 77, 0)
}

.selectize-input>input {
  display: inline-block !important;
  padding: 0 !important;
  min-height: 0 !important;
  max-height: none !important;
  max-width: 100% !important;
  margin: 0 !important;
  text-indent: 0 !important;
  border: 0 none !important;
  background: none !important;
  line-height: inherit !important;
  -webkit-user-select: auto !important;
  box-shadow: none !important
}

.selectize-input>input::-ms-clear {
  display: none
}

.selectize-input>input:focus {
  outline: none !important
}

.selectize-input::after {
  content: ' ';
  display: block;
  clear: left
}

.selectize-input.dropdown-active::before {
  content: ' ';
  display: block;
  position: absolute;
  background: #ffffff;
  height: 1px;
  bottom: 0;
  left: 0;
  right: 0
}

.selectize-dropdown {
  position: absolute;
  z-index: 10;
  border: 1px solid #d0d0d0;
  background: #ffffff;
  margin: -1px 0 0 0;
  border-top: 0 none;
  box-sizing: border-box;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-radius: 0 0 4px 4px
}

.selectize-dropdown [data-selectable] {
  cursor: pointer;
  overflow: hidden
}

.selectize-dropdown [data-selectable] .highlight {
  background: rgba(255, 237, 40, 0.4);
  border-radius: 1px
}

.selectize-dropdown [data-selectable],
.selectize-dropdown .optgroup-header {
  padding: 3px 12px
}

.selectize-dropdown .optgroup:first-child .optgroup-header {
  border-top: 0 none
}

.selectize-dropdown .optgroup-header {
  color: #777777;
  background: #ffffff;
  cursor: default
}

.selectize-dropdown .active {
  background-color: #f5f5f5;
  color: #262626
}

.selectize-dropdown .active.create {
  color: #262626
}

.selectize-dropdown .create {
  color: rgba(51, 51, 51, 0.5)
}

.selectize-dropdown-content {
  overflow-y: auto;
  overflow-x: hidden;
  max-height: 200px
}

.selectize-control.single .selectize-input,
.selectize-control.single .selectize-input input {
  cursor: pointer
}

.selectize-control.single .selectize-input.input-active,
.selectize-control.single .selectize-input.input-active input {
  cursor: text
}

.selectize-control.single .selectize-input:after {
  content: ' ';
  display: block;
  position: absolute;
  top: 50%;
  right: 17px;
  margin-top: -3px;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 5px 5px 0 5px;
  border-color: #333333 transparent transparent transparent
}

.selectize-control.single .selectize-input.dropdown-active:after {
  margin-top: -4px;
  border-width: 0 5px 5px 5px;
  border-color: transparent transparent #333333 transparent
}

.selectize-control.rtl.single .selectize-input:after {
  left: 17px;
  right: auto
}

.selectize-control.rtl .selectize-input>input {
  margin: 0 4px 0 -2px !important
}

.selectize-control .selectize-input.disabled {
  opacity: 0.5;
  background-color: #ffffff
}

.selectize-dropdown,
.selectize-dropdown.form-control {
  height: auto;
  padding: 0;
  margin: 2px 0 0 0;
  z-index: 1000;
  background: #ffffff;
  border: 1px solid #cccccc;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175)
}

.selectize-dropdown .optgroup-header {
  font-size: 12px;
  line-height: 1.42857143
}

.selectize-dropdown .optgroup:first-child:before {
  display: none
}

.selectize-dropdown .optgroup:before {
  content: ' ';
  display: block;
  height: 1px;
  margin: 9px 0;
  overflow: hidden;
  background-color: #e5e5e5;
  margin-left: -12px;
  margin-right: -12px
}

.selectize-dropdown-content {
  padding: 5px 0
}

.selectize-dropdown-header {
  padding: 6px 12px
}

.selectize-input {
  min-height: 34px
}

.selectize-input.dropdown-active {
  border-radius: 4px
}

.selectize-input.dropdown-active::before {
  display: none
}

.selectize-input.focus {
  border-color: #66afe9;
  outline: 0;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(102, 175, 233, 0.6)
}

.has-error .selectize-input {
  border-color: #a94442;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075)
}

.has-error .selectize-input:focus {
  border-color: #843534;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #ce8483
}

.selectize-control.multi .selectize-input.has-items {
  padding-left: 9px;
  padding-right: 9px
}

.selectize-control.multi .selectize-input>div {
  border-radius: 3px
}

.form-control.selectize-control {
  padding: 0;
  height: auto;
  border: none;
  background: none;
  box-shadow: none;
  border-radius: 0
}

.shepherd-element,
.shepherd-element:after,
.shepherd-element:before,
.shepherd-element *,
.shepherd-element *:after,
.shepherd-element *:before {
  box-sizing: border-box
}

.shepherd-element {
  position: absolute;
  display: none
}

.shepherd-element.shepherd-open {
  display: block
}

.shepherd-element.shepherd-theme-default {
  max-width: 100%;
  max-height: 100%
}

.shepherd-element.shepherd-theme-default .shepherd-content {
  border-radius: 5px;
  position: relative;
  font-family: inherit;
  background: #fff;
  color: #444;
  padding: 1em;
  font-size: 1.1em;
  line-height: 1.5em;
  webkit-transform: translateZ(0);
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-filter: drop-shadow(0 1px 4px rgba(0, 0, 0, 0.2));
  filter: drop-shadow(0 1px 4px rgba(0, 0, 0, 0.2))
}

.shepherd-element.shepherd-theme-default .shepherd-content:before {
  content: "";
  display: block;
  position: absolute;
  width: 0;
  height: 0;
  border-color: transparent;
  border-width: 16px;
  border-style: solid;
  pointer-events: none
}

.shepherd-element.shepherd-theme-default.shepherd-element-attached-bottom.shepherd-element-attached-center .shepherd-content {
  margin-bottom: 16px
}

.shepherd-element.shepherd-theme-default.shepherd-element-attached-bottom.shepherd-element-attached-center .shepherd-content:before {
  top: 100%;
  left: 50%;
  margin-left: -16px;
  border-top-color: #fff
}

.shepherd-element.shepherd-theme-default.shepherd-element-attached-top.shepherd-element-attached-center .shepherd-content {
  margin-top: 16px
}

.shepherd-element.shepherd-theme-default.shepherd-element-attached-top.shepherd-element-attached-center .shepherd-content:before {
  bottom: 100%;
  left: 50%;
  margin-left: -16px;
  border-bottom-color: #fff
}

.shepherd-element.shepherd-theme-default.shepherd-element-attached-right.shepherd-element-attached-middle .shepherd-content {
  margin-right: 16px
}

.shepherd-element.shepherd-theme-default.shepherd-element-attached-right.shepherd-element-attached-middle .shepherd-content:before {
  left: 100%;
  top: 50%;
  margin-top: -16px;
  border-left-color: #fff
}

.shepherd-element.shepherd-theme-default.shepherd-element-attached-left.shepherd-element-attached-middle .shepherd-content {
  margin-left: 16px
}

.shepherd-element.shepherd-theme-default.shepherd-element-attached-left.shepherd-element-attached-middle .shepherd-content:before {
  right: 100%;
  top: 50%;
  margin-top: -16px;
  border-right-color: #fff
}

.shepherd-element.shepherd-theme-default.shepherd-element-attached-top.shepherd-element-attached-left.shepherd-target-attached-bottom .shepherd-content {
  margin-top: 16px
}

.shepherd-element.shepherd-theme-default.shepherd-element-attached-top.shepherd-element-attached-left.shepherd-target-attached-bottom .shepherd-content:before {
  bottom: 100%;
  left: 16px;
  border-bottom-color: #fff
}

.shepherd-element.shepherd-theme-default.shepherd-element-attached-top.shepherd-element-attached-right.shepherd-target-attached-bottom .shepherd-content {
  margin-top: 16px
}

.shepherd-element.shepherd-theme-default.shepherd-element-attached-top.shepherd-element-attached-right.shepherd-target-attached-bottom .shepherd-content:before {
  bottom: 100%;
  right: 16px;
  border-bottom-color: #fff
}

.shepherd-element.shepherd-theme-default.shepherd-element-attached-bottom.shepherd-element-attached-left.shepherd-target-attached-top .shepherd-content {
  margin-bottom: 16px
}

.shepherd-element.shepherd-theme-default.shepherd-element-attached-bottom.shepherd-element-attached-left.shepherd-target-attached-top .shepherd-content:before {
  top: 100%;
  left: 16px;
  border-top-color: #fff
}

.shepherd-element.shepherd-theme-default.shepherd-element-attached-bottom.shepherd-element-attached-right.shepherd-target-attached-top .shepherd-content {
  margin-bottom: 16px
}

.shepherd-element.shepherd-theme-default.shepherd-element-attached-bottom.shepherd-element-attached-right.shepherd-target-attached-top .shepherd-content:before {
  top: 100%;
  right: 16px;
  border-top-color: #fff
}

.shepherd-element.shepherd-theme-default.shepherd-element-attached-top.shepherd-element-attached-right.shepherd-target-attached-left .shepherd-content {
  margin-right: 16px
}

.shepherd-element.shepherd-theme-default.shepherd-element-attached-top.shepherd-element-attached-right.shepherd-target-attached-left .shepherd-content:before {
  top: 16px;
  left: 100%;
  border-left-color: #fff
}

.shepherd-element.shepherd-theme-default.shepherd-element-attached-top.shepherd-element-attached-left.shepherd-target-attached-right .shepherd-content {
  margin-left: 16px
}

.shepherd-element.shepherd-theme-default.shepherd-element-attached-top.shepherd-element-attached-left.shepherd-target-attached-right .shepherd-content:before {
  top: 16px;
  right: 100%;
  border-right-color: #fff
}

.shepherd-element.shepherd-theme-default.shepherd-element-attached-bottom.shepherd-element-attached-right.shepherd-target-attached-left .shepherd-content {
  margin-right: 16px
}

.shepherd-element.shepherd-theme-default.shepherd-element-attached-bottom.shepherd-element-attached-right.shepherd-target-attached-left .shepherd-content:before {
  bottom: 16px;
  left: 100%;
  border-left-color: #fff
}

.shepherd-element.shepherd-theme-default.shepherd-element-attached-bottom.shepherd-element-attached-left.shepherd-target-attached-right .shepherd-content {
  margin-left: 16px
}

.shepherd-element.shepherd-theme-default.shepherd-element-attached-bottom.shepherd-element-attached-left.shepherd-target-attached-right .shepherd-content:before {
  bottom: 16px;
  right: 100%;
  border-right-color: #fff
}

.shepherd-element.shepherd-theme-default {
  z-index: 9999;
  max-width: 24em;
  font-size: 1em
}

.shepherd-element.shepherd-theme-default.shepherd-element-attached-top.shepherd-element-attached-center.shepherd-has-title .shepherd-content:before,
.shepherd-element.shepherd-theme-default.shepherd-element-attached-top.shepherd-element-attached-right.shepherd-target-attached-bottom.shepherd-has-title .shepherd-content:before,
.shepherd-element.shepherd-theme-default.shepherd-element-attached-top.shepherd-element-attached-left.shepherd-target-attached-bottom.shepherd-has-title .shepherd-content:before {
  border-bottom-color: #eee
}

.shepherd-element.shepherd-theme-default.shepherd-has-title .shepherd-content header {
  background: #eee;
  padding: 1em
}

.shepherd-element.shepherd-theme-default.shepherd-has-title .shepherd-content header a.shepherd-cancel-link {
  padding: 0;
  margin-bottom: 0
}

.shepherd-element.shepherd-theme-default.shepherd-has-cancel-link .shepherd-content header h3 {
  float: left
}

.shepherd-element.shepherd-theme-default .shepherd-content {
  padding: 0
}

.shepherd-element.shepherd-theme-default .shepherd-content * {
  font-size: inherit
}

.shepherd-element.shepherd-theme-default .shepherd-content header {
  *zoom: 1;
  border-radius: 5px 5px 0 0
}

.shepherd-element.shepherd-theme-default .shepherd-content header:after {
  content: "";
  display: table;
  clear: both
}

.shepherd-element.shepherd-theme-default .shepherd-content header h3 {
  margin: 0;
  line-height: 1;
  font-weight: normal
}

.shepherd-element.shepherd-theme-default .shepherd-content header a.shepherd-cancel-link {
  float: right;
  text-decoration: none;
  font-size: 1.25em;
  line-height: .8em;
  font-weight: normal;
  color: rgba(0, 0, 0, 0.5);
  opacity: 0.25;
  position: relative;
  top: .1em;
  padding: .8em;
  margin-bottom: -.8em
}

.shepherd-element.shepherd-theme-default .shepherd-content header a.shepherd-cancel-link:hover {
  opacity: 1
}

.shepherd-element.shepherd-theme-default .shepherd-content .shepherd-text {
  padding: 1em
}

.shepherd-element.shepherd-theme-default .shepherd-content .shepherd-text p {
  margin: 0 0 0.5em 0;
  line-height: 1.3em
}

.shepherd-element.shepherd-theme-default .shepherd-content .shepherd-text p:last-child {
  margin-bottom: 0
}

.shepherd-element.shepherd-theme-default .shepherd-content footer {
  padding: 0 1em 1em
}

.shepherd-element.shepherd-theme-default .shepherd-content footer .shepherd-buttons {
  text-align: right;
  list-style: none;
  padding: 0;
  margin: 0
}

.shepherd-element.shepherd-theme-default .shepherd-content footer .shepherd-buttons li {
  display: inline;
  padding: 0;
  margin: 0
}

.shepherd-element.shepherd-theme-default .shepherd-content footer .shepherd-buttons li .shepherd-button {
  display: inline-block;
  vertical-align: middle;
  *vertical-align: auto;
  *zoom: 1;
  *display: inline;
  border-radius: 3px;
  cursor: pointer;
  border: 0;
  margin: 0 0.5em 0 0;
  font-family: inherit;
  text-transform: uppercase;
  letter-spacing: .1em;
  font-size: .8em;
  line-height: 1em;
  padding: 0.75em 2em;
  background: #3288e6;
  color: #fff
}

.shepherd-element.shepherd-theme-default .shepherd-content footer .shepherd-buttons li .shepherd-button.shepherd-button-secondary {
  background: #eee;
  color: #888
}

.shepherd-element.shepherd-theme-default .shepherd-content footer .shepherd-buttons li:last-child .shepherd-button {
  margin-right: 0
}

.shepherd-start-tour-button.shepherd-theme-default {
  display: inline-block;
  vertical-align: middle;
  *vertical-align: auto;
  *zoom: 1;
  *display: inline;
  border-radius: 3px;
  cursor: pointer;
  border: 0;
  margin: 0 0.5em 0 0;
  font-family: inherit;
  text-transform: uppercase;
  letter-spacing: .1em;
  font-size: .8em;
  line-height: 1em;
  padding: 0.75em 2em;
  background: #3288e6;
  color: #fff
}

/*! tip.css v0.3.0 | MIT License | http://git.io/tip.css*/
[data-has-tip]:not([data-tip-content]):not([title]):before,
[data-has-tip]:not([data-tip-content]):not([title]):after {
  visibility: hidden
}

[data-has-tip][title=""]:not([data-tip-content]):before,
[data-has-tip][title=""]:not([data-tip-content]):after {
  visibility: hidden
}

[data-has-tip]:not([data-tip-content])::after {
  content: attr(title)
}

[data-has-tip][data-tip-content=""]::after {
  content: attr(title)
}

[data-has-tip] {
  position: relative;
  cursor: default
}

[data-has-tip]::before,
[data-has-tip]::after {
  position: absolute;
  visibility: hidden;
  z-index: 100000;
  pointer-events: none
}

[data-has-tip]:hover::before,
[data-has-tip]:hover::after {
  visibility: visible
}

[data-has-tip]::before {
  border: 5px solid;
  width: 0;
  height: 0;
  content: ""
}

[data-has-tip]::after {
  padding: 5px 8px;
  font-size: 13px;
  content: attr(data-tip-content);
  background-color: black;
  color: white;
  white-space: nowrap
}

[data-has-tip]::before,
[data-has-tip]::after {
  -webkit-transform: translate(-50%, 0);
  -ms-transform: translate(-50%, 0);
  transform: translate(-50%, 0)
}

[data-has-tip]::before {
  bottom: -5px;
  left: 50%;
  border-color: transparent;
  border-bottom-color: black
}

[data-has-tip]::after {
  top: 100%;
  left: 50%;
  margin-top: 5px
}

[data-has-tip~="top"]::before,
[data-has-tip~="top"]::after {
  -webkit-transform: translate(50%, 0);
  -ms-transform: translate(50%, 0);
  transform: translate(50%, 0)
}

[data-has-tip~="top"]::before {
  top: -5px;
  right: 50%;
  bottom: auto;
  left: auto;
  border-color: transparent;
  border-top-color: black
}

[data-has-tip~="top"]::after {
  right: 50%;
  bottom: 100%;
  top: auto;
  left: auto;
  margin-bottom: 5px
}

[data-has-tip~="right"]::before,
[data-has-tip~="right"]::after {
  -webkit-transform: translate(0, 50%);
  -ms-transform: translate(0, 50%);
  transform: translate(0, 50%)
}

[data-has-tip~="right"]::before {
  top: auto;
  right: -5px;
  bottom: 50%;
  left: auto;
  border-color: transparent;
  border-right-color: black
}

[data-has-tip~="right"]::after {
  top: auto;
  right: auto;
  bottom: 50%;
  left: 100%;
  margin-left: 5px
}

[data-has-tip~="bottom"]::before,
[data-has-tip~="bottom"]::after {
  -webkit-transform: translate(-50%, 0);
  -ms-transform: translate(-50%, 0);
  transform: translate(-50%, 0)
}

[data-has-tip~="bottom"]::before {
  top: auto;
  right: auto;
  bottom: -5px;
  left: 50%;
  border-color: transparent;
  border-bottom-color: black
}

[data-has-tip~="bottom"]::after {
  top: 100%;
  right: auto;
  bottom: auto;
  left: 50%;
  margin-top: 5px
}

[data-has-tip~="left"]::before,
[data-has-tip~="left"]::after {
  -webkit-transform: translate(0, 50%);
  -ms-transform: translate(0, 50%);
  transform: translate(0, 50%)
}

[data-has-tip~="left"]::before {
  top: auto;
  right: auto;
  bottom: 50%;
  left: -5px;
  border-color: transparent;
  border-left-color: black
}

[data-has-tip~="left"]::after {
  top: auto;
  right: 100%;
  bottom: 50%;
  left: auto;
  margin-right: 5px
}

[data-has-tip][data-has-tip~="anchor-right"]::before,
[data-has-tip][data-has-tip~="anchor-right"]::after,
[data-has-tip~="bottom"][data-has-tip~="anchor-right"]::before,
[data-has-tip~="bottom"][data-has-tip~="anchor-right"]::after {
  -webkit-transform: translate(50%, 0);
  -ms-transform: translate(50%, 0);
  transform: translate(50%, 0)
}

[data-has-tip][data-has-tip~="anchor-right"]::before,
[data-has-tip~="bottom"][data-has-tip~="anchor-right"]::before {
  top: auto;
  right: 20%;
  bottom: -5px;
  left: auto
}

[data-has-tip][data-has-tip~="anchor-right"]::after,
[data-has-tip~="bottom"][data-has-tip~="anchor-right"]::after {
  top: 100%;
  right: 20%;
  bottom: auto;
  left: auto
}

[data-has-tip][data-has-tip~="anchor-left"]::before,
[data-has-tip][data-has-tip~="anchor-left"]::after,
[data-has-tip~="bottom"][data-has-tip~="anchor-left"]::before,
[data-has-tip~="bottom"][data-has-tip~="anchor-left"]::after {
  -webkit-transform: translate(50%, 0);
  -ms-transform: translate(50%, 0);
  transform: translate(50%, 0)
}

[data-has-tip][data-has-tip~="anchor-left"]::before,
[data-has-tip~="bottom"][data-has-tip~="anchor-left"]::before {
  top: auto;
  right: 80%;
  bottom: -5px;
  left: auto
}

[data-has-tip][data-has-tip~="anchor-left"]::after,
[data-has-tip~="bottom"][data-has-tip~="anchor-left"]::after {
  top: 100%;
  right: 80%;
  bottom: auto;
  left: auto
}

[data-has-tip~="top"][data-has-tip~="anchor-right"]::before,
[data-has-tip~="top"][data-has-tip~="anchor-right"]::after {
  -webkit-transform: translate(50%, 0);
  -ms-transform: translate(50%, 0);
  transform: translate(50%, 0)
}

[data-has-tip~="top"][data-has-tip~="anchor-right"]::before {
  top: -5px;
  right: 20%;
  bottom: auto;
  left: auto
}

[data-has-tip~="top"][data-has-tip~="anchor-right"]::after {
  top: auto;
  right: 20%;
  bottom: 100%;
  left: auto
}

[data-has-tip~="top"][data-has-tip~="anchor-left"]::before,
[data-has-tip~="top"][data-has-tip~="anchor-left"]::after {
  -webkit-transform: translate(50%, 0);
  -ms-transform: translate(50%, 0);
  transform: translate(50%, 0)
}

[data-has-tip~="top"][data-has-tip~="anchor-left"]::before {
  top: -5px;
  right: 80%;
  bottom: auto;
  left: auto
}

[data-has-tip~="top"][data-has-tip~="anchor-left"]::after {
  top: auto;
  right: 80%;
  bottom: 100%;
  left: auto
}

[data-has-tip~="multiline"]::after {
  width: 200px;
  white-space: normal
}

.social-share-button-baidu {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: url("/assets/sprites/social-share-button-01bd4e9a7782620aab986c55abc6ecb9ee88d50bb51f28c2f00fe36d87a0944c.png") 0px 0px no-repeat
}

.social-share-button-delicious {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: url("/assets/sprites/social-share-button-01bd4e9a7782620aab986c55abc6ecb9ee88d50bb51f28c2f00fe36d87a0944c.png") -16px 0px no-repeat
}

.social-share-button-douban {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: url("/assets/sprites/social-share-button-01bd4e9a7782620aab986c55abc6ecb9ee88d50bb51f28c2f00fe36d87a0944c.png") 0px -16px no-repeat
}

.social-share-button-email {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: url("/assets/sprites/social-share-button-01bd4e9a7782620aab986c55abc6ecb9ee88d50bb51f28c2f00fe36d87a0944c.png") -16px -16px no-repeat
}

.social-share-button-facebook {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: url("/assets/sprites/social-share-button-01bd4e9a7782620aab986c55abc6ecb9ee88d50bb51f28c2f00fe36d87a0944c.png") -32px 0px no-repeat
}

.social-share-button-flickr {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: url("/assets/sprites/social-share-button-01bd4e9a7782620aab986c55abc6ecb9ee88d50bb51f28c2f00fe36d87a0944c.png") -32px -16px no-repeat
}

.social-share-button-google_bookmark {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: url("/assets/sprites/social-share-button-01bd4e9a7782620aab986c55abc6ecb9ee88d50bb51f28c2f00fe36d87a0944c.png") 0px -32px no-repeat
}

.social-share-button-google_plus {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: url("/assets/sprites/social-share-button-01bd4e9a7782620aab986c55abc6ecb9ee88d50bb51f28c2f00fe36d87a0944c.png") -16px -32px no-repeat
}

.social-share-button-kaixin001 {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: url("/assets/sprites/social-share-button-01bd4e9a7782620aab986c55abc6ecb9ee88d50bb51f28c2f00fe36d87a0944c.png") -32px -32px no-repeat
}

.social-share-button-pinterest {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: url("/assets/sprites/social-share-button-01bd4e9a7782620aab986c55abc6ecb9ee88d50bb51f28c2f00fe36d87a0944c.png") -48px 0px no-repeat
}

.social-share-button-plurk {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: url("/assets/sprites/social-share-button-01bd4e9a7782620aab986c55abc6ecb9ee88d50bb51f28c2f00fe36d87a0944c.png") -48px -16px no-repeat
}

.social-share-button-qq {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: url("/assets/sprites/social-share-button-01bd4e9a7782620aab986c55abc6ecb9ee88d50bb51f28c2f00fe36d87a0944c.png") -48px -32px no-repeat
}

.social-share-button-renren {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: url("/assets/sprites/social-share-button-01bd4e9a7782620aab986c55abc6ecb9ee88d50bb51f28c2f00fe36d87a0944c.png") 0px -48px no-repeat
}

.social-share-button-tqq {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: url("/assets/sprites/social-share-button-01bd4e9a7782620aab986c55abc6ecb9ee88d50bb51f28c2f00fe36d87a0944c.png") -16px -48px no-repeat
}

.social-share-button-tumblr {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: url("/assets/sprites/social-share-button-01bd4e9a7782620aab986c55abc6ecb9ee88d50bb51f28c2f00fe36d87a0944c.png") -32px -48px no-repeat
}

.social-share-button-twitter {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: url("/assets/sprites/social-share-button-01bd4e9a7782620aab986c55abc6ecb9ee88d50bb51f28c2f00fe36d87a0944c.png") -48px -48px no-repeat
}

.social-share-button-weibo {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: url("/assets/sprites/social-share-button-01bd4e9a7782620aab986c55abc6ecb9ee88d50bb51f28c2f00fe36d87a0944c.png") -64px 0px no-repeat
}

@media only screen and (-webkit-device-pixel-ratio: 2) {
  .social-share-button-baidu {
    display: inline-block;
    width: 16px;
    height: 16px;
    background: url("/assets/sprites/<EMAIL>") 0px 0px no-repeat;
    background-size: 80px 64px
  }

  .social-share-button-delicious {
    display: inline-block;
    width: 16px;
    height: 16px;
    background: url("/assets/sprites/<EMAIL>") -16px 0px no-repeat;
    background-size: 80px 64px
  }

  .social-share-button-douban {
    display: inline-block;
    width: 16px;
    height: 16px;
    background: url("/assets/sprites/<EMAIL>") 0px -16px no-repeat;
    background-size: 80px 64px
  }

  .social-share-button-email {
    display: inline-block;
    width: 16px;
    height: 16px;
    background: url("/assets/sprites/<EMAIL>") -16px -16px no-repeat;
    background-size: 80px 64px
  }

  .social-share-button-facebook {
    display: inline-block;
    width: 16px;
    height: 16px;
    background: url("/assets/sprites/<EMAIL>") -32px 0px no-repeat;
    background-size: 80px 64px
  }

  .social-share-button-flickr {
    display: inline-block;
    width: 16px;
    height: 16px;
    background: url("/assets/sprites/<EMAIL>") -32px -16px no-repeat;
    background-size: 80px 64px
  }

  .social-share-button-google_bookmark {
    display: inline-block;
    width: 16px;
    height: 16px;
    background: url("/assets/sprites/<EMAIL>") 0px -32px no-repeat;
    background-size: 80px 64px
  }

  .social-share-button-google_plus {
    display: inline-block;
    width: 16px;
    height: 16px;
    background: url("/assets/sprites/<EMAIL>") -16px -32px no-repeat;
    background-size: 80px 64px
  }

  .social-share-button-kaixin001 {
    display: inline-block;
    width: 16px;
    height: 16px;
    background: url("/assets/sprites/<EMAIL>") -32px -32px no-repeat;
    background-size: 80px 64px
  }

  .social-share-button-pinterest {
    display: inline-block;
    width: 16px;
    height: 16px;
    background: url("/assets/sprites/<EMAIL>") -48px 0px no-repeat;
    background-size: 80px 64px
  }

  .social-share-button-plurk {
    display: inline-block;
    width: 16px;
    height: 16px;
    background: url("/assets/sprites/<EMAIL>") -48px -16px no-repeat;
    background-size: 80px 64px
  }

  .social-share-button-qq {
    display: inline-block;
    width: 16px;
    height: 16px;
    background: url("/assets/sprites/<EMAIL>") -48px -32px no-repeat;
    background-size: 80px 64px
  }

  .social-share-button-renren {
    display: inline-block;
    width: 16px;
    height: 16px;
    background: url("/assets/sprites/<EMAIL>") 0px -48px no-repeat;
    background-size: 80px 64px
  }

  .social-share-button-tqq {
    display: inline-block;
    width: 16px;
    height: 16px;
    background: url("/assets/sprites/<EMAIL>") -16px -48px no-repeat;
    background-size: 80px 64px
  }

  .social-share-button-tumblr {
    display: inline-block;
    width: 16px;
    height: 16px;
    background: url("/assets/sprites/<EMAIL>") -32px -48px no-repeat;
    background-size: 80px 64px
  }

  .social-share-button-twitter {
    display: inline-block;
    width: 16px;
    height: 16px;
    background: url("/assets/sprites/<EMAIL>") -48px -48px no-repeat;
    background-size: 80px 64px
  }

  .social-share-button-weibo {
    display: inline-block;
    width: 16px;
    height: 16px;
    background: url("/assets/sprites/<EMAIL>") -64px 0px no-repeat;
    background-size: 80px 64px
  }
}

#admin_chart,
#admin_login_chart,
#admin_login_chart_last_year,
#admin_chart_previous_year {
  width: 100%;
  height: 500px
}

tr.highlight {
  background-color: yellow
}

.right {
  float: right
}

.left {
  float: left
}

.tank {
  overflow: hidden;
  clear: both
}

::-moz-selection {
  background: #CC0000;
  color: #fff;
  text-shadow: none
}

::-moz-selection {
  background: #CC0000;
  color: #fff;
  text-shadow: none
}

::selection {
  background: #CC0000;
  color: #fff;
  text-shadow: none
}

.selectize-caption {
  font-size: 12px;
  color: #888;
  font-style: italic
}

.sortable-placeholder {
  display: block;
  background: #ccc;
  height: 86px
}

#debug_info {
  display: block;
  padding: 5px;
  background: #fff;
  border: 1px solid #ccc;
  overflow: auto
}

#debug_info ul {
  margin: 0;
  padding: 0
}

#debug_info li {
  margin: 0;
  padding: .5em 0;
  border-bottom: 1px solid #ccc
}

#upload_progress {
  display: inline-block;
  margin-left: 1em
}

*[data-display="false"] {
  display: none
}

*[data-display="true"] {
  display: block
}

#putt_count {
  text-shadow: 0 1px rgba(0, 0, 0, 0.5);
  font-size: 16px;
  color: #fff;
  padding: 3px 10px;
  border-radius: 2em;
  text-align: center;
  font-weight: bold
}

#putt_count:hover {
  cursor: pointer
}

#my-clubs a {
  color: #333
}

#my-clubs a:hover {
  color: #999
}

#my-clubs i {
  color: #fff
}

.ol-overlay-container .tooltip.top {
  width: 150px;
  font-size: 13px
}

*:focus,
*:active {
  outline: none !important
}

.stroke_actions {
  background: #333;
  color: #fff
}

.stroke_actions .stroke_action a {
  display: inline-block;
  padding: .5em 1em;
  color: rgba(255, 255, 255, 0.8);
  border-left: 1px solid rgba(255, 255, 255, 0.1)
}

.stroke_actions .stroke_number {
  padding: 1em
}

.ol-overlay-container {
  overflow: visible
}

.ol-overlay-container .stroke_label {
  display: block;
  background: rgba(0, 0, 0, 0.5);
  border: 1px solid #fff;
  padding: .25em .5em;
  text-align: center;
  color: #fff;
  font-weight: 400
}

.ol-overlay-container .delete_stroke {
  display: inline-block;
  position: absolute;
  bottom: -35px;
  left: -20px;
  width: 30px;
  height: 30px;
  border: 1px solid #ccc
}

.ol-overlay-container.active .stroke_label {
  border-color: #CC0000
}

.ol-overlay-container.active .delete_stroke {
  display: inline-block
}

.neg_margin {
  margin-top: -30px
}

.profiler-results {
  display: none
}

.button-list-group {
  margin-top: -28px
}

@media (max-width: 991px) {
  .cc_banner-wrapper {
    height: 10px !important
  }
}

.cc_banner-wrapper .cc_container .cc_btn {
  background-color: #ccc;
  border-radius: 0;
  color: #979797
}

.cc_banner-wrapper .cc_container .cc_btn:hover {
  color: #fff;
  background: #CC0000
}

.cc_banner-wrapper .cc_more_info {
  color: #CC0000
}