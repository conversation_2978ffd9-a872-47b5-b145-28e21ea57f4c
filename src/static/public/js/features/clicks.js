$(document).on('ready, page:change', function() {

  $(document).on('click', 'tr[data-href]', function() {
    $this  = $(this);
    use_tl = !$this.data('no-turbolink');
    href   = $this.data('href');
    
    if(use_tl) { Turbolinks.visit(href); }
    else { document.location.href = href; }
  });

  $(document).on('click', '[href^=#][data-toggle!=tab]', function(event) {
    event.preventDefault();
    var $target = $($(event.currentTarget).attr('href'));
    if ($target.length) {
      $('body').animate({scrollTop: $target.offset().top}, 600);
    }
  });

});
