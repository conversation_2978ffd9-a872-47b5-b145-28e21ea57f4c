var help_mode = function(){
  switch (localStorage.getItem('help_mode')) {
    case 'false':
      $("#help_mode").slideUp();
      $('[data-has-tip]').addClass('hide_tooltip');
      break;
    case 'true':
      $("#help_mode").slideDown();
      $('[data-has-tip]').removeClass('hide_tooltip');
      break;
  }
};

$(window).on('load page:load', function() {
  if (localStorage.getItem('help_mode') === null) {
    localStorage.setItem('help_mode', true);
  };
  help_mode();

});

$(document).on('ready', function() {
  $('#toggle_help_mode').on('click', function(e){
    e.preventDefault();
    switch (localStorage.getItem('help_mode')) {
      case 'false':
        localStorage.setItem('help_mode', true);
        help_mode();
        break;
      case 'true':
        localStorage.setItem('help_mode', false);
        help_mode();
        break;
    }
  });
});

$(document).on('ready, page:change', function() {
  $("[data-dismiss='help_mode']").on('click', function(e){
    e.preventDefault();
    localStorage.setItem('help_mode', false);
    help_mode();
  })

  $('[data-toggle="tooltip"]').tooltip();

  $('input#user_handicap').on('input', function(){
    var val = String($(this).val());
    var positive = val.replace('-','+');
    $('.range-val').html(positive)
    if (val === '-7') {
      $('.range-val').html('Pro')
    }
  });
  if ($('input#user_handicap').length > 0) {
    if ($('input#user_handicap').val() === '-7') {
      $('.range-val').html('Pro');
    } else {
      $('.range-val').html($('.range-val').html().replace('-', '+'));
    }
  }

});
