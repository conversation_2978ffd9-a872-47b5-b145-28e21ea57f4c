#= require ./map/app
#= require ./map/styles
#= require ./map/controls
#= require ./map/layers
#= require ./map/animations
#= require ./map/shot_info
#= require ./map/interactions
#= require ./map/events
#= require ./map/pin_distance

KEYCODE_ENTER = 13
KEYCODE_ESC   = 27

$('[data-id="complete-round"]').tooltip()
$('[data-id="complete-round"]').on 'click', ->
  $(this).tooltip 'hide'
  return

app.ViewSwipe =
  el: document.getElementById('swipe')
  disable: ->
    @el.disabled = true
    return
  enable: ->
    @el.disabled = false
    return
  on: ->
    @el.value = 100
    @el.disabled = false
    return
  off: ->
    @el.value = 100
    @el.disabled = true
    return
  toggle: ->
    if @el.value > 50
      @el.value = 0
    else
      @el.value = 100
    return

app.addGreenOverlay = (green_count) ->
  app.putt_count = new (ol.Overlay)(
    element: document.getElementById('putt_count')
    position: app.fromLongLat(app.geojson_obj.green.center)
    positioning: 'center-center'
    offset: [ 0, 0 ]
    insertFirst: false
    stopEvent: false)
  app.hole.addOverlay app.putt_count
  $(app.putt_count.get('element')).text "#{green_count} #{app.putt_or_putts()}"

  return

app.requestAllFeatures = ->
  # console.log 'app.requestAllFeatures'
  $.ajax
    url: window.location.pathname + '.json'
    success: [
      (response) ->
        app.hole_id = response.hole_id
        app.url =
          get_strokes: "#{window.location.pathname}/strokes.json"
          put_stroke: "/my/holes/#{window.location.pathname.split('/').length - 1}/strokes/add"
        app.geojson_obj = response
        # console.log app.geojson_obj
      app.initLayers
      app.initMapEvents
      app.setMapView
      app.initControls
      app.initAnimations
      app.init_on_click
      app.initInteractions
      app.initRowSorting
      (response) -> $(document).trigger('map:init')
    ]

app.setMapView = (response) ->
  app.hole.getView().setRotation response.rotate.radian
  app.hole_extent = app.layer.strokes.getSource().getExtent()
  app.fitExtent app.layer.hole.getSource().getExtent()
  app.default_zoom = app.hole.getView().getResolution()

app.centerMap = ->
  app.hole_center = app.hole.getView().getCenter()
  app.hole.getView().setCenter(app.hole_center)

app.fitExtent = (extent) ->
  app.hole.getView().fit extent, app.hole.getSize
    padding: [ 0, 0, 0, 0, ]
    constrainResolution: false
    nearest: false

app.initRowReload = ->
  app.strokesapp.layer.strokes.once 'precompose', ->
    app.strokesapp.layer.strokes.getSource().clear()
    return
  app.strokesapp.layer.strokes.once 'render', ->
    app.hole.getOverlays().clear()
    app.load_stroke_labels()
    app.init_add_stroke_rows()
    return
  app.strokesapp.layer.strokes.setSource strokeSource()
  return

app.fromLongLat = (coordinate) ->
  ol.proj.transform coordinate, 'EPSG:4326', 'EPSG:3857'

app.toLongLat = (coordinate) ->
  ol.proj.transform coordinate, 'EPSG:3857', 'EPSG:4326'

app.getLocation = (feature) ->
  feature.getGeometry().getCoordinates()

app.addStrokeCallback = ->
  app.initRowReload()
  return

app.removeStrokeCallback = ->
  app.initRowReload()
  return

app.updateStrokeCallback = (stroke_id) ->

$('.dropdown-toggle').dropdown()

app.requestAllFeatures()

app.layerToggle = (layer) ->
  if layer.getVisible() == true
    layer.setVisible false
  else
    layer.setVisible true
  return

dev =
  toggle:
    svg: ->
      layer = app.layer.svg
      app.layerToggle layer
      return
    osm: ->
      layer = app.layer.satellite
      app.layerToggle layer
      return
    strokes: ->
      layer = app.layer.strokes
      app.layerToggle layer
      return
    putts: ->
      layer = app.layer.green_strokes
      app.layerToggle layer
      return
    bing: ->
      layer = app.layer.bing
      app.layerToggle layer
      return
  save: ->
  deselect: ->



#
# Document Ready, get busy!
#
$ ->
  #
  # MAP READY, Godspeed!
  #
  $(document).on 'map:init', ->
    console.log "map is ready"
    app.pinDistance ||= new PinDistance( app.Pin )

  #
  # Inline a input for changing the distance,
  # selects stroke and sets the circle radius.
  #
  editCircleDistance = (target) ->
    target    = $(target)
    info      = target.parents('.distance_to_pin').first()
    lie       = info.data('stroke-lie')
    stroke_id = info.data('stroke')
    distance  = parseFloat( $('.number', info).text() )

    # stop any previous edits
    cancelCircleDistance()

    # TODO: move styles to SASS for more control
    $('.number', info).html("<input type='text' value='#{distance}' size='4' style='width:50px;' />")
    $('.tools', info).hide()
    $('input', info).focus()

    app.pinDistance.start(stroke_id, distance)


  #
  # Updates the circle radius and moves the
  # selected stroke to closest point on the
  # circle.
  #
  changeCircleDistance = (input, cancel = false) ->
    $input   = $(input)
    info     = $input.parents('.distance_to_pin').first()
    lie      = info.data('stroke-lie')
    units    = if lie == "green" then 'FT' else 'YDS'
    distance = parseFloat( $input.val() )

    $('.number', info).text("#{distance} #{units}")
    $('.tools', info).show()

    if cancel
      app.pinDistance.cancel()
    else
      app.pinDistance.change( distance )


  #
  # Cancel any pending (manual) edits,
  # and clear the distance circle.
  #
  cancelCircleDistance = ->
    app.pinDistance.cancel()
    $('.distance_to_pin input').each (i) ->
      changeCircleDistance( this, true )

  #
  # Events to watch for interaction
  #
  $('#shot-table')
  .on 'distance:cancel', ->
    cancelCircleDistance()
  .on 'click', '.distance_to_pin .fa-plus-square', (e) ->
    editCircleDistance( e.target )
  .on 'change', '.distance_to_pin input', (e) ->
    changeCircleDistance( e.target )
  .on 'focus', '.distance_to_pin input', (e) ->
    $(e.target).select()
  .on 'keyup', '.distance_to_pin', (e) ->
    if e.which == KEYCODE_ESC
      changeCircleDistance( e.target, true )
