AmCharts.themes.taylormade = function (url) {
	return {
		themeName:'taylormade',

		AmChart: {
			color: '#CCCCCC',
			backgroundColor: '#FFFFFF',
			borderAlpha: 0,
			pathToImages: '/assets/amcharts/dist/amcharts/images/',
			responsive: {
				enabled: true,
				maxHeight: 240,
				minHeight: 240
			},
			dataLoader: {
				url: url,
				format: 'json'
			}
		},

		AmCoordinateChart: {
			colors: ['#67b7dc', '#ccc', '#979797', '#cc4748', '#cd82ad', '#2f4074', '#448e4d', '#b7b83f', '#b9783f', '#b93e3d', '#913167']
		},

		AmStockChart: {
			colors: ['#67b7dc', '#fdd400', '#84b761', '#cc4748', '#cd82ad', '#2f4074', '#448e4d', '#b7b83f', '#b9783f', '#b93e3d', '#913167']
		},

		AmSerial: {
			valueAxes: {
				offset: -30
      }
		},

		AmSlicedChart: {
			colors: ['#67b7dc', '#fdd400', '#84b761', '#cc4748', '#cd82ad', '#2f4074', '#448e4d', '#b7b83f', '#b9783f', '#b93e3d', '#913167'],
			outlineAlpha: 1,
			outlineThickness: 2,
			labelTickColor: '#CCCCCC',
			labelTickAlpha: 0.3
		},

		AmRectangularChart: {
			zoomOutButtonColor: '#CCCCCC',
			zoomOutButtonRollOverAlpha: 0.15,
			zoomOutButtonImage: 'lens.png'
		},

		AmXYChart: {
			valueAxes: {
				// title: 'Left / Right',
				title: '',
				position: 'bottom',
				id: 'x1',
				unit: "'",
        synchronizeWith: 'y1-0',
        synchronizationMultiplier: 1,
        inside: 1,
        labelsEnabled: 1,
        minimum: -90,
        maximum: 90
      },
      valueAxesYards:{
        // title: 'Left / Right',
				title: '',
        position: 'bottom',
        id: 'x1y',
        minimum: -60,
        maximum: 60,
        unit: ' YDS',
        inside: 1,
        labelsEnabled: 1
      },
			valueAxesShortDistance: {
				title: 'Distance',
				position: 'left',
				id: 'y1',
				minimum: 0,
				maximum: 100,
				unit: ' YDS'
			},
      valueAxesLongDistance: {
        title: 'Distance',
        position: 'left',
        id: 'y1d',
        dashLength: 6,
        unit: ' YDS',
        inside: 1,
        labelsEnabled: 1,
        labelOffset: 40
      },
      valueAxisDriving: {
        title: 'Distance',
        position: 'left',
        id: 'y1d',
        dashLength: 6,
        unit: ' YDS',
        inside: 1,
        labelsEnabled: 1,
        labelOffset: 40,
        minimum: 50
      },
			valueAxesCenteredZeroApproach: {
	      title: 'Distance',
	      position: 'left',
	      id: 'y1-0',
	      dashLength: 6,
	      unit: "'",
        minimum: -90,
        maximum: 90,
        inside: 1,
        labelsEnabled: 1,
        labelOffset: 16
      },
			valueAxesCenteredZeroShort: {
	      title: 'Distance',
	      position: 'left',
	      id: 'y1-0',
	      dashLength: 6,
	      unit: "'",
        minimum: -45,
        maximum: 45,
        inside: 1,
        labelsEnabled: 1,
        labelOffset: 16
      },
			valueAxesCenteredZero: {
	      title: 'Distance',
	      position: 'left',
	      id: 'y1-0',
	      dashLength: 6,
	      unit: "'",
        minimum: -50,
        maximum: 50,
        inside: 1,
        labelsEnabled: 1,
        labelOffset: 16
      },
      driving:{
        bullet: 'round',
        id: 'graph',
        lineAlpha: 0,
        valueField: 'bValue',
        xField: 'x',
        yField: 'y',
        bulletSize: 16,
        showBalloon: false,
        bulletAlpha: 0.33,
        bulletBorderAlpha: 1,
        colorField: 'color',
        guides: [
          {
            fillAlpha: 0.1,
            value: 0,
            toValue: 10
          }
        ]
      },
			graph: {
				bullet: 'round',
				id: 'graph',
				lineAlpha: 0,
				valueField: 'bValue',
				xField: 'x',
				yField: 'y',
				bulletSize: 16,
				showBalloon: false,
        bulletAlpha: 0.33,
        bulletBorderAlpha: 1,
        colorField: 'color'
			},
			graph2: {
				bullet: 'round',
				classNameField: 'bulletClass',
				id: 'amGraph-1',
				lineAlpha: 0,
				valueField: 'value',
				bulletBorderAlpha: 1,
				bulletAlpha: 0,
				bulletAxis: 'x1',
				xField: 'x2',
				yField: 'y2',
				showBalloon: false,
      },
			graph3: {
				bullet: 'round',
				classNameField: 'bulletClass',
				id: 'AmGraph-2',
				lineAlpha: 0,
				valueField: 'value2',
				bulletBorderAlpha: 1,
				bulletAlpha: 0.26,
				bulletAxis: 'x1',
				xField: 'x3',
				yField: 'y3',
				showBalloon: false,
			},
      graph4: {
        bullet: 'round',
        classNameField: 'bulletClass',
        id: 'AmGraph-4',
        lineAlpha: 0,
        valueField: 'value3',
        bulletBorderAlpha: 1,
        bulletAlpha: 1,
        bulletAxis: 'x1',
        xField: 'x4',
        yField: 'y4',
        showBalloon: false,
        colorField: 'color'
      }
		},

		AxisBase: {
			axisColor: '#CCCCCC',
			axisAlpha: 1,
			gridAlpha: 1,
			gridColor: '#CCCCCC'
		},

		ChartScrollbar: {
			backgroundColor: '#333333',
			backgroundAlpha: 0.12,
			graphFillAlpha: 0.5,
			graphLineAlpha: 0,
			selectedBackgroundColor: '#FFFFFF',
			selectedBackgroundAlpha: 0.4,
			gridAlpha: 0.15
		},

		ChartCursor: {
			cursorColor: '#333333',
			color: '#FFFFFF',
			cursorAlpha: 0.5
		},

		AmLegend: {
			color: '#333333'
		},

		AmGraph: {
			lineAlpha: 0.9
		},
		GaugeArrow: {
			color: '#333333',
			alpha: 0.8,
			nailAlpha: 0,
			innerRadius: '40%',
			nailRadius: 15,
			startWidth: 15,
			borderAlpha: 0.8,
			nailBorderAlpha: 0
		},

		GaugeAxis: {
			tickColor: '#333333',
			tickAlpha: 1,
			tickLength: 15,
			minorTickLength: 8,
			axisThickness: 3,
			axisColor: '#333333',
			axisAlpha: 1,
			bandAlpha: 0.8
		},

		TrendLine: {
			lineColor: '#CC0000',
			lineAlpha: 0.8
		},

		// ammap
		AreasSettings: {
			alpha: 0.8,
			color: '#CC0000',
			colorSolid: '#CC0000',
			unlistedAreasAlpha: 0.4,
			unlistedAreasColor: '#000000',
			outlineColor: '#FFFFFF',
			outlineAlpha: 0.5,
			outlineThickness: 0.5,
			rollOverColor: '#CC0000',
			rollOverOutlineColor: '#FFFFFF',
			selectedOutlineColor: '#FFFFFF',
			selectedColor: '#CC0000',
			unlistedAreasOutlineColor: '#FFFFFF',
			unlistedAreasOutlineAlpha: 0.5
		},

		LinesSettings: {
			color: '#CCCCCC',
			alpha: 0.8
		},

		ImagesSettings: {
			alpha: 0.8,
			labelColor: '#CCCCCC',
			color: '#CCCCCC',
			labelRollOverColor: '#CC0000'
		},

		ZoomControl: {
			buttonRollOverColor: '#CC0000',
			buttonFillColor: '#CC0000',
			buttonBorderColor: '#CC0000',
			buttonFillAlpha: 0.8,
			gridBackgroundColor: '#FFFFFF',
			buttonBorderAlpha:0,
			buttonCornerRadius:2,
			gridColor:'#FFFFFF',
			gridBackgroundColor:'#333333',
			buttonIconAlpha:0.6,
			gridAlpha: 0.6,
			buttonSize:20
		},

		SmallMap: {
			mapColor: '#333333',
			rectangleColor: '#f15135',
			backgroundColor: '#FFFFFF',
			backgroundAlpha: 0.7,
			borderThickness: 1,
			borderAlpha: 0.8
		},

		// the defaults below are set using CSS syntax, you can use any existing css property
		// if you don't use Stock chart, you can delete lines below
		PeriodSelector: {
			color: '#333333'
		},

		PeriodButton: {
			color: '#333333',
			background: 'transparent',
			opacity: 0.7,
			border: '1px solid rgba(0, 0, 0, .3)',
			MozBorderRadius: '5px',
			borderRadius: '5px',
			margin: '1px',
			outline: 'none',
			boxSizing: 'border-box'
		},

		PeriodButtonSelected: {
			color: '#333333',
			backgroundColor: '#b9cdf5',
			border: '1px solid rgba(0, 0, 0, .3)',
			MozBorderRadius: '5px',
			borderRadius: '5px',
			margin: '1px',
			outline: 'none',
			opacity: 1,
			boxSizing: 'border-box'
		},

		PeriodInputField: {
			color: '#333333',
			background: 'transparent',
			border: '1px solid rgba(0, 0, 0, .3)',
			outline: 'none'
		},

		DataSetSelector: {
			color: '#333333',
			selectedBackgroundColor: '#b9cdf5',
			rollOverBackgroundColor: '#a8b0e4'
		},

		DataSetCompareList: {
			color: '#000000',
			lineHeight: '100%',
			boxSizing: 'initial',
			webkitBoxSizing: 'initial',
			border: '1px solid rgba(0, 0, 0, .3)'
		},

		DataSetSelect: {
			border: '1px solid rgba(0, 0, 0, .3)',
			outline: 'none'
		}
	};
};
