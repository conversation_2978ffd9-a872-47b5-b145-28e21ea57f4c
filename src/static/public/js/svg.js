//= require jquery
//= require underscore
//= require underscore.inflection
//= require d3
//= require topojson
//= require textures
//= require saveSvgAsPng

// Constants
// -----------------------------------------------------
var TOOLTIP_OFFSET = 40;
var TOOLTIP_SPACING = 10;
var TOOLTIP_WIDTH = 100;
var TOOLTIP_HEIGHT = 40;
var TOOLTIP_PADDING = 12;

// Helpers
// -----------------------------------------------------
d3.selection.prototype.moveBehind = function(previousTooltip) {
  return this.each(function() {
    if (previousTooltip[0] && previousTooltip[0][0]) {
      this.parentNode.insertBefore(this, previousTooltip[0][0]);
    }
  });
};

function toRadians(num) {
  return num * (Math.PI / 180);
}

function distanceInMeters(lon1, lat1, lon2, lat2) {
  var R = 6371000; // Radius of the earth in meters
  var dLat = toRadians(lat2-lat1);
  var dLon = toRadians(lon2-lon1);
  var a = Math.sin(dLat/2) * Math.sin(dLat/2) +
          Math.cos(toRadians(lat1)) * Math.cos(toRadians(lat2)) *
          Math.sin(dLon/2) * Math.sin(dLon/2);
  var c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  var d = R * c;
  return d;
}

function strokeIsInGreen(path, stroke, green) {
  var greenBounds = path.bounds(green);
  var strokeBounds = path.bounds(stroke);

  var strokeBoundsX = strokeBounds[0][0];
  var strokeBoundsY = strokeBounds[0][1];

  return  strokeBoundsX > greenBounds[0][0]
          && strokeBoundsX < greenBounds[1][0]
          && strokeBoundsY > greenBounds[0][1]
          && strokeBoundsY < greenBounds[1][1];
}

function tooltipsOverlap(previousTooltipCoords, currentTooltipCoords) {
  if (!previousTooltipCoords || !previousTooltipCoords.x || !previousTooltipCoords.y || !currentTooltipCoords.x || !currentTooltipCoords.y) {
    return false;
  }

  return !(
    ((currentTooltipCoords.y + TOOLTIP_HEIGHT) < (previousTooltipCoords.y)) ||
    (currentTooltipCoords.y > (previousTooltipCoords.y + TOOLTIP_HEIGHT)) ||
    ((currentTooltipCoords.x + TOOLTIP_WIDTH) < previousTooltipCoords.x) ||
    (currentTooltipCoords.x > (previousTooltipCoords.x + TOOLTIP_WIDTH))
  );
}

// SvgBuilder
// -----------------------------------------------------
window.SvgBuilder = function(id) {
  this.id = id;
};

SvgBuilder.prototype.build = function(geojson, rotate, width, height) {
  this.geojson = geojson;
  this.rotate = rotate;
  this.width = width;
  this.height = height;
  return this.initSvgHole();
};

SvgBuilder.prototype.initSvgHole = function() {
  var holeSvg = d3.select(this.id)
      .attr('width', this.width)
      .attr('height', this.height);

  // Set the initial projection and rotation
  // -----------------------------------------------------
  var projection = d3.geo.mercator();
  var path = d3.geo.path().projection(projection);
  projection.rotate([0, -this.rotate]).scale(1).translate([0, 0]);

  // Gather all the 'feature' data
  // -----------------------------------------------------
  var lies = topojson.feature(this.geojson, this.geojson.objects.data);
  var data = {};
  data.boundary = lies.features.filter(function(d) {
    return d.id === 'Hole Boundary';
  })[0];
  data.tees = lies.features.filter(function(d) {
    return d.id === 'Tee';
  });
  data.hazards = lies.features.filter(function(d) {
    return d.id === 'Hazard';
  });
  data.bunkers = lies.features.filter(function(d) {
    return d.id === 'Bunker';
  });
  data.trees = lies.features.filter(function(d) {
    return d.id === 'Trees';
  });
  data.fairways = lies.features.filter(function(d) {
    return d.id === 'Fairway';
  });
  data.green = lies.features.filter(function(d) {
    return d.id === 'Green';
  })[0];
  data.strokes = lies.features.filter(function(d) {
    return d.id.match(/stroke/gi);
  });

  // Scale the map to fit the bounds
  // -----------------------------------------------------
  var bounds = path.bounds(data.boundary);
  var scale = .95 / Math.max((bounds[1][0] - bounds[0][0]) / (this.width-TOOLTIP_WIDTH), (bounds[1][1] - bounds[0][1]) / (this.height-TOOLTIP_WIDTH));
  var transform = [(this.width - scale * (bounds[1][0] + bounds[0][0])) / 2, (this.height - scale * (bounds[1][1] + bounds[0][1])) / 2];
  projection.scale(scale).translate(transform);

  // Draw each 'feature'
  // -----------------------------------------------------
  var boundary = holeSvg.selectAll('boundary').data([data.boundary]).enter().append('path')
    .attr('d', path)
    .attr('fill', 'url(#noise)')
    .attr('stroke', '#6FA044')
    .attr('stroke-width', 2);

  var tees = holeSvg.selectAll('tees').data(data.tees).enter().append('path')
    .attr('d', path)
    .attr('fill', '#6FA044')
    .attr('filter', 'url(#light-inner-shadow)');

  var hazards = holeSvg.selectAll('hazards').data(data.hazards).enter().append('path')
    .attr('d', path)
    .attr('fill', '#2490B0')
    .attr('filter', 'url(#inner-shadow)');

  var bunkers = holeSvg.selectAll('bunkers').data(data.bunkers).enter().append('path')
    .attr('d', path)
    .attr('fill', '#DAD78F')
    .attr('filter', 'url(#bunker-shadow)');

  var fairways = holeSvg.selectAll('fairways').data(data.fairways).enter().append('path')
    .attr('d', path)
    .attr('fill', '#558C3B')
    .attr('filter', 'url(#inner-shadow)');

  var green = holeSvg.selectAll('green').data([data.green]).enter().append('path')
    .attr('d', path)
    .attr('fill', '#6FA044');

  // Draw the strokes
  // -----------------------------------------------------
  var visibleStrokes = data.strokes.filter(function(stroke) {
    return !strokeIsInGreen(path, stroke, data.green);
  });

  var strokesInGreen = data.strokes.filter(function(stroke) {
    return strokeIsInGreen(path, stroke, data.green);
  });

  var lastStroke = data.strokes[data.strokes.length-1];
  visibleStrokes.push(lastStroke);

  var strokes = holeSvg.selectAll('strokes').data(visibleStrokes).enter().append('path')
    .attr('d', path)
    .attr('fill', '#ffffff');

  // Draw the tooltips
  // -----------------------------------------------------
  var previousTooltipCoords = [];
  var previousTooltip;
  var moveBehind = false;
  strokes[0].reverse();
  strokes.each(function(stroke, i) {
    if (!stroke) return;

    var distance = distanceInMeters(stroke.geometry.coordinates[0], stroke.geometry.coordinates[1], lastStroke.geometry.coordinates[0], lastStroke.geometry.coordinates[1]);
    var actualCoords = projection(stroke.geometry.coordinates);

    currentTooltipCoords = {
      x: actualCoords[0] - (TOOLTIP_WIDTH / 2),
      y: actualCoords[1] - (TOOLTIP_OFFSET + TOOLTIP_HEIGHT)
    };

    previousTooltipCoords.forEach(function(coords, n) {
      if (tooltipsOverlap(coords, currentTooltipCoords)) {
        currentTooltipCoords.y = (coords.y - TOOLTIP_HEIGHT - TOOLTIP_SPACING);
        moveBehind = true;
      }
    });

    var tooltip = holeSvg.append('g');

    var line = tooltip.append('line')
      .attr('stroke', '#ffffff')
      .attr('stroke-width', 1)
      .attr('x1', actualCoords[0])
      .attr('y1', actualCoords[1])
      .attr('x2', actualCoords[0])
      .attr('y2', currentTooltipCoords.y + TOOLTIP_HEIGHT);

    var tooltipBox = tooltip.append('rect')
      .attr('width', TOOLTIP_WIDTH)
      .attr('height', TOOLTIP_HEIGHT)
      .attr('stroke', '#ffffff')
      .attr('fill', '#000000')
      .attr('fill-opacity', 0.7)
      .attr('stroke-width', 1)
      .attr('x', currentTooltipCoords.x)
      .attr('y', currentTooltipCoords.y)
      .attr('rx', 2)
      .attr('ry', 2);

    var label;
    if (stroke === lastStroke) {
      var shotsLabel = _('Shot').pluralize(strokesInGreen.length);
      label = strokesInGreen.length + ' ' + shotsLabel;
    } else {
      label = Math.floor(distance * 1.0936) + ' YDS';
    }
    var tooltipText = tooltip.append('text').text(label)
      .attr('fill', '#ffffff')
      .attr('font-family', 'DIN Next W01')
      .attr('font-size', '22px')
      .attr('x', currentTooltipCoords.x + (TOOLTIP_WIDTH / 2))
      .attr('y', currentTooltipCoords.y + (TOOLTIP_HEIGHT - TOOLTIP_PADDING))
      .style('text-anchor', 'middle');

    if(moveBehind && previousTooltip) {
      tooltip.moveBehind(previousTooltip);
    }

    moveBehind = false;
    previousTooltip = tooltip;
    previousTooltipCoords.push(currentTooltipCoords);
  });
};
