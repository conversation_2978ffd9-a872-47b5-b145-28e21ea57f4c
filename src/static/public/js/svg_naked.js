//= require jquery
//= require underscore
//= require underscore.inflection
//= require d3
//= require topojson
//= require textures
//= require saveSvgAsPng

// Constants
// -----------------------------------------------------

// Helpers
// -----------------------------------------------------



// SvgBuilder
// -----------------------------------------------------
window.SvgBuilder = function(id) {
  this.id = id;
};

SvgBuilder.prototype.build = function(geojson, rotate, width, height) {
  this.geojson = geojson;
  this.rotate = rotate;
  this.width = width;
  this.height = height;
  return this.initSvgHole();
};

SvgBuilder.prototype.initSvgHole = function() {
  var holeSvg = d3.select(this.id)
      .attr('width', this.width)
      .attr('height', this.height);

  // Set the initial projection and rotation
  // -----------------------------------------------------
  var projection = d3.geo.mercator();
  var path = d3.geo.path().projection(projection);
  projection.rotate([0, -this.rotate]).scale(1).translate([0, 0]);

  // Gather all the 'feature' data
  // -----------------------------------------------------
  var lies = topojson.feature(this.geojson, this.geojson.objects.data);
  var data = {};
  data.boundary = lies.features.filter(function(d) {
    return d.id === 'Hole Boundary';
  })[0];
  data.tees = lies.features.filter(function(d) {
    return d.id === 'Tee';
  });
  data.hazards = lies.features.filter(function(d) {
    return d.id === 'Hazard';
  });
  data.bunkers = lies.features.filter(function(d) {
    return d.id === 'Bunker';
  });
  data.trees = lies.features.filter(function(d) {
    return d.id === 'Trees';
  });
  data.fairways = lies.features.filter(function(d) {
    return d.id === 'Fairway';
  });
  data.green = lies.features.filter(function(d) {
    return d.id === 'Green';
  })[0];

  // Scale the map to fit the bounds
  // -----------------------------------------------------
  var bounds = path.bounds(data.boundary);
  var scale = .95 / Math.max((bounds[1][0] - bounds[0][0]) / (this.width), (bounds[1][1] - bounds[0][1]) / (this.height));
  var transform = [(this.width - scale * (bounds[1][0] + bounds[0][0])) / 2, (this.height - scale * (bounds[1][1] + bounds[0][1])) / 2];
  projection.scale(scale).translate(transform);

  // Draw each 'feature'
  // -----------------------------------------------------
  var boundary = holeSvg.selectAll('boundary').data([data.boundary]).enter().append('path')
    .attr('d', path)
    .attr('fill', 'url(#noise)')
    .attr('stroke', '#6FA044')
    .attr('stroke-width', 2);

  var tees = holeSvg.selectAll('tees').data(data.tees).enter().append('path')
    .attr('d', path)
    .attr('fill', '#6FA044')
    .attr('filter', 'url(#light-inner-shadow)');

  var hazards = holeSvg.selectAll('hazards').data(data.hazards).enter().append('path')
    .attr('d', path)
    .attr('fill', '#2490B0')
    .attr('filter', 'url(#inner-shadow)');

  var bunkers = holeSvg.selectAll('bunkers').data(data.bunkers).enter().append('path')
    .attr('d', path)
    .attr('fill', '#DAD78F')
    .attr('filter', 'url(#bunker-shadow)');

  var fairways = holeSvg.selectAll('fairways').data(data.fairways).enter().append('path')
    .attr('d', path)
    .attr('fill', '#558C3B')
    .attr('filter', 'url(#inner-shadow)');

  var green = holeSvg.selectAll('green').data([data.green]).enter().append('path')
    .attr('d', path)
    .attr('fill', '#6FA044');

};
