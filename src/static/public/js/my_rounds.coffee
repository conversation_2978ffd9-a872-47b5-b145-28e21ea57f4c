#= require selectize

$(document).ready ->

  $('#round_facility_id').selectize
    dropdownParent: 'body'
    persist: false
    maxItems: null
    valueField: 'id'
    labelField: 'name'
    sortField: 'name'
    searchField: ['name', 'city', 'state']
    placeholder: "Type a course name"


  $("#rounds .page").infinitescroll
    navSelector: "nav.pagination" # selector for the paged navigation (it will be hidden)
    nextSelector: "nav.pagination a[rel=next]" # selector for the NEXT link (to page 2)
    itemSelector: "#rounds div.rounds-item" # selector for all items you'll retrieve
