# var sort_order = document.getElementById('shot-table');
# var sort_rows = Sortable.create(sort_order, { handle: ".ordinal_handle" });

app.putt_count_el = $("#putt_count")

info_tip = $('#info')
info_tip.tooltip
  animation: false
  trigger: 'manual'

app.initRowSorting = ->
  $('#shot-table').sortable handle: '.ordinal_handle'

  $('#shot-table').on 'sortupdate', (event, ui) ->
    $('#shot-table').find('li').each (i) ->
      ordinal = i + 1
      strokeId = $(this).attr('data-stroke')
      stroke = undefined
      if app.layer.strokes.getSource().getFeatureById(strokeId) != null
        stroke = app.layer.strokes.getSource().getFeatureById(strokeId)
      if app.layer.green_strokes.getSource().getFeatureById(strokeId) != null
        stroke = app.layer.green_strokes.getSource().getFeatureById(strokeId)
      stroke.set 'ordinal', ordinal

app.showGreen = ->
  app.ViewSwipe.off()
  feature = app.layer.features.getSource().getFeatureById(app.geojson_obj.green.id)
  feature.setStyle app.styles.green_selected
  app.layer.pin.setVisible true
  app.layer.green_strokes.setVisible true
  app.layer.strokes.setVisible false
  app.doPanAndZoom app.Pin.getGeometry().getCoordinates(), 3

  # -----------------------------------------------
  # Hide the put count overlay.
  # -----------------------------------------------
  $('#putt_count').hide()

app.hideGreen = ->
  app.ViewSwipe.on()
  feature = app.layer.hole.getSource().getFeatureById(app.geojson_obj.green.id)
  feature.setStyle app.styles.green
  app.layer.pin.setVisible false
  app.layer.green_strokes.setVisible false
  app.layer.strokes.setVisible true
  if $('#putt_count').is(':hidden')
    app.doPanAndZoom app.Pin.getGeometry().getCoordinates(), 1

  # -----------------------------------------------
  # Show the put count overlay.
  # -----------------------------------------------
  $('#putt_count').show()

$('.disabled').each ->
  $(this).prop 'disabled', true

app.disable_add_stroke_button = ->
  $('#add-stroke-btn').prop 'disabled', true
  $('#add-stroke-btn').addClass 'disabled'
  $('#add_shot_button').prop 'disabled', true
  $('#add_shot_button').addClass 'disabled'

app.enable_add_stroke_button = ->
  $('#add-stroke-btn').prop 'disabled', false
  $('#add-stroke-btn').removeClass 'disabled'
  $('#add_shot_button').prop 'disabled', false
  $('#add_shot_button').removeClass 'disabled'

app.init_on_click = ->
  app.disable_add_stroke_button() if app.geojson_obj.completed == true
  app.enableRemoveStrokeButtons()
  $('#change-lie').on 'click', 'a', (e) ->
    e.preventDefault
    val = $(this).attr('rel')
    stroke = app.Modify.select.getFeatures().getArray()[0]
    stroke.set 'lie', val
    $('#change-lie').dropdown 'toggle'
    false

  $('#change-club').on 'click', 'a', (e) ->
    e.preventDefault
    val = $(this).attr('rel')
    clubName = $(this).text()
    stroke = app.Modify.select.getFeatures().getArray()[0]
    stroke.set 'club_id', val
    $('#change-club').dropdown 'toggle'
    $(".stroke_row[data-stroke='#{app.selectedFeatureId}'] [data-row-club]").text "(#{clubName})"
    false

  # REGISTER EVENT FOR THE ADD SHOT BUTTON
  $('#add_shot_button').not('.disabled').on 'click', (e) ->
    e.preventDefault()
    if app.Modify.select.getFeatures().getArray().length > 0
      saveShot app.Modify.select.getFeatures().getArray()[0]
    app.init_add_stroke_rows()
    $('.add_shot_here').toggleClass 'hidden'
    $('.stroke_row').removeClass 'active'
    try
      app.Modify.select.getFeatures().pop()
    catch e

    Mousetrap.bind 'escape', (e) ->
      e.preventDefault()
      app.hole.removeInteraction app.draw
      false

  $('#mark-stroke-group').on 'click', 'button', (e) ->
    $('#mark-stroke-group').find('.active').removeClass 'active'

    mark_as = $(this).attr('id')
    switch mark_as
      when 'penalty-btn'
        $.ajax
          type: 'PUT'
          url: "/my/strokes/#{app.selectedFeatureId}/penalty"
        switch app.selectedFeature.get 'penalty'
          when true
            app.selectedFeature.set 'penalty', false
            $('#penalty-btn').removeClass 'active'
          else
            app.selectedFeature.set 'penalty', true
            $('#penalty-btn').addClass 'active'
        app.selectedFeature.set 'recovery', false
        app.selectedFeature.set 'difficult', false

      when 'difficult-btn'
        $.ajax
          type: 'PUT'
          url: "/my/strokes/#{app.selectedFeatureId}/difficult"
        switch app.selectedFeature.get 'difficult'
          when true
            app.selectedFeature.set 'difficult', false
            $('#difficult-btn').removeClass 'active'
          else
            app.selectedFeature.set 'difficult', true
            $('#difficult-btn').addClass 'active'
        app.selectedFeature.set 'recovery', false
        app.selectedFeature.set 'penalty', false

      when 'recovery-btn'
        $.ajax
          type: 'PUT'
          url: "/my/strokes/#{app.selectedFeatureId}/recovery"
        switch app.selectedFeature.get 'recovery'
          when true
            app.selectedFeature.set 'recovery', false
            $('#recovery-btn').removeClass 'active'
          else
            app.selectedFeature.set 'recovery', true
            $('#recovery-btn').addClass 'active'
        app.selectedFeature.set 'penalty', false
        app.selectedFeature.set 'difficult', false

  $('#add-stroke-btn').on 'click', (evt) ->
    try
      app.Modify.select.getFeatures().pop()
    catch e
    app.disable_add_stroke_button()
    app.hole.removeInteraction app.draw
    app.drawStroke()

  $('#remove-stroke-btn').click (evt) ->
    return if $(this).hasClass('disabled')

    app.removeStroke()
    app.enable_add_stroke_button()

    $('#remove-stroke-btn').prop 'disabled', true
    $('#remove-stroke-btn').addClass 'disabled'
    $('#mark-stroke-group').find('.active').removeClass 'active'
    $('#stroke-actions').find('[data-toggle-disabled]').each (i) ->
      $(this).addClass 'disabled'
      $(this).prop 'disabled', true

    app.Modify.select.getFeatures().pop if app.Modify.select.getFeatures().getArray().length > 0

  $('#shot-table').on 'click', '.stroke_link', (e) ->
    e.preventDefault()

  $('#shot-table').on 'click', '.add_shot_here', (e) ->
    before = $(this).attr('data-before-stroke')
    after = $(this).attr('data-after-stroke')
    $('.add_shot_here').addClass 'hidden'
    app.sendAddStroke before, after

app.putt_or_putts = ->
  if app.layer.green_strokes.getSource().getFeatures().length == 1 then ' putt' else ' putts'

app.reloadMap = ->
  $.when($.ajax(
    url: app.url.get_strokes
    success: (response) ->
      all_strokes = (new (ol.format.GeoJSON)).readFeatures(response.strokes,
        featureProjection: 'EPSG:3857'
        dataProjection: 'EPSG:4326')

      regular_strokes = response.no_green_strokes

      not_green = (new (ol.format.GeoJSON)).readFeatures(regular_strokes,
        featureProjection: 'EPSG:3857'
        dataProjection: 'EPSG:4326')

      green_strokes = response.green_strokes

      green = (new (ol.format.GeoJSON)).readFeatures(green_strokes,
        featureProjection: 'EPSG:3857'
        dataProjection: 'EPSG:4326')

      app.all_strokes.clear()

      app.layer.strokes.getSource().clear()

      app.layer.green_strokes.getSource().clear()

      app.layer.strokes.getSource().addFeatures not_green

      app.layer.green_strokes.getSource().addFeatures green

      app.all_strokes.addFeatures all_strokes

      app.enable_add_stroke_button()

      putt_length = app.layer.green_strokes.getSource().getFeatures().length

      app.putt_count_el.text "#{putt_length} #{app.putt_or_putts()}"

      app.hole.getInteractions().remove(app.Modify.select)

      app.hole.getInteractions().remove(app.Modify.modify)

      app.enableRemoveStrokeButtons()

  )).done ->
    app.init_stroke_change_events(app.layer.strokes.getSource().getFeatures())
    app.init_stroke_change_events(app.layer.green_strokes.getSource().getFeatures())
    # $('#mark-stroke-group').find('.active').removeClass 'active'
    disabled_actions = $('#stroke-actions').find('[data-toggle-disabled]')
    disabled_actions.addClass('disabled').prop 'disabled', true
    app.initInteractions()

app.init_stroke_change_events = (strokes) ->
  strokes.forEach (stroke) ->

    stroke.on 'change', (evt) ->
      app.update_stroke_ui evt.target

    stroke.on 'propertychange', (evt) ->
      stroke   = evt.target
      strokeID = stroke.getId()

      if evt.target.get(evt.key) is evt.oldValue
        return
      else
        switch evt.key
          when 'club_id' then app.updateStrokeObject evt.target, true

          when 'lie'
            green_strokes = app.layer.green_strokes.getSource().getFeatures()
            change_club = $('#change-club')
            putter = change_club.find('li:contains("Putter") a').attr('rel')
            putts = app.layer.green_strokes.getSource().getFeatures()
            club = $('#change-club').find('li:contains("Putter") a').attr('rel')
            club_id = stroke.get('club_id')

            $("#lie-condition-#{stroke.getId()}").text stroke.get('lie')
            # -----------------------------------------------
            # Return if the Lie hasn't changed.
            # -----------------------------------------------
            if stroke.get('lie') == evt.oldValue
              console.log "this should have been caught earlier"
              return

            else
              # -----------------------------------------------
              # If the lie was previously on the Green.
              # -----------------------------------------------
              if evt.oldValue == 'Green' and evt.target.get('lie') != 'Green'
                # -----------------------------------------------
                # Remove the stroke from the green strokes layer.
                # -----------------------------------------------
                app.layer.green_strokes.getSource().removeFeature stroke
                # -----------------------------------------------
                # Add the stroke to the regular stroke layer
                # -----------------------------------------------
                app.layer.strokes.getSource().addFeature stroke
                # -----------------------------------------------
                # Remove the club choice if it's a putter.
                # -----------------------------------------------
                if typeof club_id != 'undefined' and club_id == putter
                  stroke.set 'club_id', null

              # -----------------------------------------------
              # If the lie is now on the Green
              # -----------------------------------------------
              if evt.target.get('lie') == 'Green' and evt.oldValue != 'Green'
                # -----------------------------------------------
                # Remove the stroke from the regular strokes layer.
                # -----------------------------------------------
                app.layer.strokes.getSource().removeFeature stroke
                # -----------------------------------------------
                # Add the stroke to the green strokes layer.
                # -----------------------------------------------
                app.layer.green_strokes.getSource().addFeature stroke
                # -----------------------------------------------
                # Set the club to the putter.
                # -----------------------------------------------
                if typeof club_id == 'undefined' or stroke.get('club_id') == null
                  stroke.set 'club_id', club_id


              # -----------------------------------------------
              # Save the stroke object to the database.
              # -----------------------------------------------
              # if app.dragging != true
                # console.log 'save'
                # app.updateStrokeObject evt.target
                # body...

          when 'distance_to_pin'
            # -----------------------------------------------
            # Update the distance to pin UI.
            # -----------------------------------------------
            $(".stroke_row[data-stroke='#{strokeID}'] .distance_to_pin .number").text evt.target.get('distance_to_pin')

          when 'distance'
            # -----------------------------------------------
            # Update the distance from last stroke UI.
            # -----------------------------------------------
            $(".stroke_row[data-stroke='#{strokeID}'] [data-shot-distance]").text stroke.get('distance')

          when 'ordinal'
            app.updateStrokeObject evt.target, true

          # ONE OR MORE OF THE STROKES HAS HAD THEIR ACTIVE STATE ALTERED
          when 'active'

            if evt.target.get('active') is true

              $('.stroke_row[data-stroke="#{evt.target.getId()}"]').addClass 'active'

              $.when(

                $('#map').find('.btn-group.disabled').each () ->
                  $(this).removeClass 'disabled'

                $('#map').find('[data-toggle-disabled]').each (i) ->
                  $(this).removeClass 'disabled'
                  $(this).prop 'disabled', false

                $('#mark-stroke-group').find('.active').removeClass 'active'

              ).done ->

                switch evt.target.get('difficult')
                  when true then $('#difficult-btn').addClass 'active'
                  else $('#difficult-btn').removeClass 'active'

                switch evt.target.get('recovery')
                  when true then $('#recovery-btn').addClass 'active'
                  else $('#recovery-btn').removeClass 'active'

                switch evt.target.get('penalty')
                  when true then $('#penalty-btn').addClass 'active'
                  else $('#penalty-btn').removeClass 'active'

            if app.Modify.select.getFeatures().getArray().length == 0
              $('#mark-stroke-group').find('.active').removeClass 'active'
              $('#map').find('[data-toggle-disabled]').each (i) ->
                $(this).addClass 'disabled'
                $(this).prop 'disabled', true

app.init_pin_change_event = ->
  pin = app.layer.pin.getSource().getFeatures()[0]
  pin.on 'change', (e) ->
    app.layer.green_strokes.getSource().forEachFeature (stroke) ->
      stroke.set 'distance_to_pin', app.set_distance_to_pin(stroke)
    app.layer.strokes.getSource().forEachFeature (stroke) ->
      stroke.set 'distance_to_pin', app.set_distance_to_pin(stroke)

app.sendAddStroke = (before, after) ->
  data = stroke_played:
    before_stroke_id: parseInt(before)
    after_stroke_id: parseInt(after)
    hole_played_id: app.hole_id
  $.ajax
    type: 'POST'
    url: "/my/holes/#{app.geojson_obj.id}/strokes/add"
    data: data

app.init_add_stroke_rows = ->
  $('.add_shot_here').each ->
    prev_stroke = $(this).prev().attr('data-stroke')
    next_stroke = $(this).next().attr('data-stroke')
    $(this).attr('data-before-stroke', prev_stroke).attr 'data-after-stroke', next_stroke

app.enableRemoveStrokeButtons = ->
  $('.remove_stroke_button').click (event) ->
    if confirm("Are you sure?")
      strokeId = $(this).closest('li.stroke_row').data('stroke')
      # console.log(strokeId)
      $.ajax
        type: 'DELETE'
        url: '/my/strokes/' + strokeId
