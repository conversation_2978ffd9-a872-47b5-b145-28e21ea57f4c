#
# Distance circle from pin 
#
class PinDistance
  constructor: (pin) ->
    @circle = null
    @stroke = null
    @lie    = null
    @pin    = pin
    @layer  = app.layer.tools
    @style  = app.styles.distance

    @setup()

  #
  # Setup circle
  #
  setup: ->    
    if @layer
      @circle = new ol.Feature()
      @circle.setStyle( @style )
      @layer.getSource().addFeature( @circle )

  #
  # Coordinates for pin
  #
  pinLocation: ->
    app.getLocation( @pin )

  pinLatLon: ->
    ol.proj.transform(@pinLocation(), 'EPSG:3857', 'EPSG:4326')

  #
  # Set circle radius (meters)
  #
  setRadius: (meters) ->
    # console.log "radius in meters: ", meters
    @circle.setGeometry(
      new ol.geom.Polygon.circular(new ol.Sphere(6378137), @pinLatLon(), meters, 128).
        transform('EPSG:4326', 'EPSG:3857')
    )

  #
  # Set circle radius (yards)
  #
  setRadiusYards: (yards) ->
    meters = (yards * 0.9144)
    @setRadius(meters)

  #
  # Set circle radius (feet)
  #
  setRadiusFeet: (feet) ->  
    meters = (feet * 0.3048)
    @setRadius(meters)

  #
  # Find/set stroke by ID
  #
  setStrokeById: (id) ->
    return unless id
    stroke = app.layer.strokes.getSource().getFeatureById(id)
    stroke ||= app.layer.green_strokes.getSource().getFeatureById(id)
    @setStroke( stroke )

  setStroke: (stroke) ->
    @stroke = stroke
    @lie    = stroke.get('lie').toLowerCase()
    @stroke.set('snapToDistance', true)

    # show/hide the green based on lie
    if @lie == 'green' then app.showGreen() else app.hideGreen()

    # manually select current stroke
    app.Modify.select.getFeatures().clear()
    app.Modify.select.getFeatures().push( @stroke )

  #
  # Clear circle
  # 
  clear: ->
    @circle.setGeometry(null)
    @stroke.set('snapToDistance', false) if @stroke
    @stroke = null
    @lie    = null

  #
  # Move feature to closest point
  # on circle.
  #
  moveToClosest: (feature, save = false) ->
    feature ||= @stroke
    return unless feature && feature.get('snapToDistance') == true
    
    geo   = @circle.getGeometry()
    point = geo.getClosestPoint( feature.getGeometry().getCoordinates() )
    feature.getGeometry().setCoordinates( point )

    if save 
      app.updateStrokeObject( feature )

  #
  # Start the stroke edit (w/circle)
  #
  start: (stroke_id, distance) ->
    @cancel()
    @setStrokeById( stroke_id )

    if @lie == "green" 
      @setRadiusFeet( distance ) 
    else 
      @setRadiusYards( distance )

  #
  # Change the radius
  #
  change: (distance) ->
    if @lie == "green"
      @setRadiusFeet( distance )
    else
      @setRadiusYards( distance )
    
    if @stroke
      @moveToClosest(@stroke, true)

  #
  # Cancel edit
  #
  cancel: ->
    @clear()

# export class
window.PinDistance = PinDistance
