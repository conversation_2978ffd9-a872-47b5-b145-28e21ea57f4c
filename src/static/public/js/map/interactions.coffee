app.initInteractions = ->

  if app.geojson_obj.completed == true then return

  app.Modify =
    init: ->
      @select = new (ol.interaction.Select)(
        multi: false
        layers: [
          app.layer.strokes
          app.layer.green_strokes
          app.layer.pin
        ]
        # addCondition: ol.events.condition.never
        # removeCondition: ol.events.condition.never
        # toggleCondition: ol.events.condition.never
        style: (feature, resolution) ->
          switch feature.get('kind')
            when 'stroke'
              app.styles.stroke_selected(feature)

            when 'pin'
              app.styles.pin_selected

      )
      app.hole.addInteraction @select

      @modify = new (ol.interaction.Modify)(
        features: @select.getFeatures()
        pixelTolerance: 40

        style: ->
          feature = app.Modify.select.getFeatures().getArray()[0]
          switch feature.get('kind')
            when 'pin'
              app.styles.pin_selected

            when 'stroke'
              app.styles.stroke_selected(feature)
      )

      @modify.on 'modifystart', (event) ->
        app.dragging = true
        event.target.set('moved', true)

      @modify.on 'modifyend', (event) ->
        app.dragging = false
        kind = event.features.getArray()[0].get('kind')
        # console.log event.target.get('moved')
        switch kind
          when 'pin'
            app.savePin()

          when 'stroke'
            stroke = event.features.getArray()[0]
            if(stroke)
              if(stroke.get('status') != 'detected')
                stroke.set('status', 'manual')
              app.pinDistance.moveToClosest( stroke )
              app.updateStrokeObject( stroke )

      app.hole.addInteraction @modify

      @select.on 'select', (event) ->
        switch event.selected.length
          when 1
            app.selectedFeature = event.selected[0]
            app.selectedFeatureId = event.selected[0].getId()
            app.disable_add_stroke_button()
            event.selected[0].set 'active', true
            # I believe this should be immutable.
            # event.selected[0].set 'auto_detected', true
          when 0
            app.enable_add_stroke_button()

        switch event.deselected.length
          when 1
            event.deselected[0].set 'active', false
            $('#shot-table').trigger('distance:cancel')


    setActive: (active) ->
      @select.setActive active
      @modify.setActive active

  app.Modify.init()
  app.Modify.setActive true

  app.drawStroke = (e) ->
    closest = undefined
    second_closest = undefined
    source = undefined
    if app.layer.strokes.getVisible() == true and app.layer.green_strokes.getVisible() == false
      source = app.layer.strokes.getSource()
    if app.layer.strokes.getVisible() == false and app.layer.green_strokes.getVisible() == true
      source = app.layer.green_strokes.getSource()
    app.draw = new (ol.interaction.Draw)(
      source: source
      type: 'Point'
      style: app.styles.stroke_add)
    app.draw.on 'drawstart', (evt) ->

      # if there's a selection when the drawing starts ... it's saved
      if app.Modify.select.getFeatures().getArray().length > 0
        app.updateStrokeObject app.Modify.select.getFeatures().getArray()[0]
        app.updateStrokeObject app.Modify.select.getFeatures().pop()

      evt.feature.on 'change', (e) ->
        closest = source.getClosestFeatureToCoordinate(e.target.getGeometry().getCoordinates())
        further_distance = 9999999
        source.forEachFeature (feature) ->
          if closest.getId() != feature.getId() and raw_distance(e.target, feature) < further_distance
            further_distance = raw_distance(e.target, feature)
            second_closest = feature

    app.draw.on 'drawend', (evt) ->
      app.hole.removeInteraction app.draw
      app.addShot evt.feature
      app.enable_add_stroke_button()

    app.hole.addInteraction app.draw
