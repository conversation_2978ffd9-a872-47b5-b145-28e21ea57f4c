app.initLayers = ->
  # console.log 'app.initLayers'
  pin_feature = new (ol.Feature)(
    id: 'pin'
    geometry: new (ol.geom.Point)(app.geojson_obj.pin.location)
    hole_played_id: app.hole_id
    kind: 'pin'
  )
  app.pin_feature = (new (ol.format.GeoJSON)).writeFeature(pin_feature)

  app.hole_source = new (ol.source.Vector)
  app.hole_features = (new (ol.format.GeoJSON)).readFeatures(app.geojson_obj.hole,
    featureProjection: 'EPSG:3857'
    dataProjection: 'EPSG:4326'
  )
  app.hole_source.on 'addfeature', (evt) ->
    # console.log("addfeature", evt);
    return
  app.hole_source.addFeatures app.hole_features

  green_strokes = app.geojson_obj.green_strokes
  no_green_strokes = app.geojson_obj.no_green_strokes

  app.layer = {

    osm: new (ol.layer.Tile)(
      source: new (ol.source.OSM)
    )

    tools: new (ol.layer.Vector)(
     source: new (ol.source.Vector)
    )

    bing: new (ol.layer.Tile)(
      useInterimTilesOnError: false
      source: new (ol.source.BingMaps)(
        # key: 'Ak-dzM4wZjSqTlzveKz5u0d4IQ4bRzVI309GxmkgSVr1ewS6iPSrOvOKhA-CJlm3'
        key: "<%= ENV['BING_API_TOKEN'] %>"
        imagerySet: 'Aerial'
      )
    )

    satellite: new (ol.layer.Tile)(
      name: 'satellite'
      useInterimTilesOnError: false
      opacity: 0.4
      preload: 5
      source: new (ol.source.BingMaps)(
        # key: 'Ak-dzM4wZjSqTlzveKz5u0d4IQ4bRzVI309GxmkgSVr1ewS6iPSrOvOKhA-CJlm3'
        key: "<%= ENV['BING_API_TOKEN'] %>"
        imagerySet: 'Aerial'
      )
    )

    pin: new (ol.layer.Vector)(
      name: 'pin'
      style: app.styles.pin
      visible: false
      source: new (ol.source.Vector)(
        features: (new (ol.format.GeoJSON)).readFeatures(app.pin_feature,
          featureProjection: 'EPSG:3857'
          dataProjection: 'EPSG:4326'
        )
      )
    )

    strokes: new (ol.layer.Vector)(
      name: 'strokes'
      updateWhileAnimating: true
      updateWhileInteracting: false
      renderBuffer: 1000
      style: app.styles.stroke
      source: new (ol.source.Vector)(
        features: (new (ol.format.GeoJSON)).readFeatures(no_green_strokes,
          featureProjection: 'EPSG:3857'
          dataProjection: 'EPSG:4326'
        )
      )
    )

    green_strokes: new (ol.layer.Vector)(
      name: 'putts'
      style: app.styles.stroke
      visible: false
      source: new (ol.source.Vector)(
        features: (new (ol.format.GeoJSON)).readFeatures(green_strokes,
          featureProjection: 'EPSG:3857'
          dataProjection: 'EPSG:4326'
        )
      )
    )

    features: new (ol.layer.Vector)(
      name: 'features'
      opacity: .8
      updateWhileAnimating: true
      updateWhileInteracting: false
      renderBuffer: 1000
      style: (feature) ->
        app.styles.styleFunction(feature)
      source: new (ol.source.Vector)(
        features: (new (ol.format.GeoJSON)).readFeatures(app.geojson_obj.hole,
          featureProjection: 'EPSG:3857'
          dataProjection: 'EPSG:4326'
        )
      )
    )

    hole: new (ol.layer.Vector)(
      name: 'features'
      opacity: 1
      source: app.hole_source
      updateWhileAnimating: true
      updateWhileInteracting: false
      style: app.styles.hidden
      renderBuffer: 1000
    )
  }

  app.Pin = app.layer.pin.getSource().getFeatures()[0]

  app.hole = new (ol.Map)(
    target: 'map'
    units: 'm'
    interactions: ol.interaction.defaults(
      mouseWheelZoom: false
    )
    controls: [
      # app.strokeControls
      # app.removeStrokeControl
      # app.addStrokeControl
      # app.zoom
      # app.zoomslider
    ]
    layers: [
      app.layer.satellite
      app.layer.features
      app.layer.hole
      app.layer.bing
      app.layer.strokes
      app.layer.green_strokes
      app.layer.pin
      app.layer.tools
    ]
    view: new (ol.View)(
      center: [0, 0]
      minResolution: .05
      maxResolution: 1
      zoomFactor: 2
      zoom: 1
    )
  )

app.initMapEvents = ->
  app.Pin = app.layer.pin.getSource().getFeatures()[0]

  app.layer.green_strokes.getSource().on "addfeature", ->
    green_strokes = app.layer.green_strokes.getSource().getFeatures()
    $("#putt_count").text "#{green_strokes.length} #{app.putt_or_putts()}"

  app.layer.green_strokes.getSource().on "removefeature", ->
    green_strokes = app.layer.green_strokes.getSource().getFeatures()
    $("#putt_count").text "#{green_strokes.length} #{app.putt_or_putts()}"

  app.layer.bing.on 'precompose', (event) ->
    ctx = event.context
    width = ctx.canvas.width * swipe.value / 100
    ctx.save()
    ctx.beginPath()
    ctx.rect width, 0, ctx.canvas.width - width, ctx.canvas.height
    ctx.clip()
    return

  app.layer.bing.on 'postcompose', (event) ->
    ctx = event.context
    ctx.restore()
    return

  # app.hole.events.register 'mousedown', map, ((e) ->
  #   console.log 'mousedown'
  #   true
  # ), true
  # app.hole.on 'mousedown'

  # app.click_timer = ->

  # app.hole.on 'pointerdown', (evt) ->
  #   # app.pointerdown_time = evt.originalEvent.timeStamp
  #   app.hole.forEachFeatureAtPixel evt.pixel, (feature, layer) ->
  #     if feature.get('kind') is 'stroke'
  #       app.Modify.select.getFeatures().push(feature)
  #       # app.click_timeout = setTimeout (->
  #       #   console.log "app.click_timeout"
  #       #   app.Modify.select.getFeatures().push(feature)
  #       #   return
  #       # ), 1000000
  # #
  # #
  # app.hole.on 'pointerup', (evt) ->
  #   app.hole.forEachFeatureAtPixel evt.pixel, (feature, layer) ->
  #     if feature.get('moved') is true
  #       app.Modify.select.getFeatures().pop()
  #     if feature.get('kind') is 'stroke'
  #       app.Modify.select.getFeatures().push(feature)
  # #   app.pointerup_time = evt.originalEvent.timeStamp
  # #   click_speed = app.pointerup_time - app.pointerdown_time
    # app.hole.forEachFeatureAtPixel evt.pixel, (feature, layer) ->
    #   clearTimeout(app.click_timeout)
    #   if feature.get('kind') is 'stroke' and app.Modify.select.getFeatures().length > 0
    #     console.log "stroke_already selected"

  app.hole.on 'click', (evt) ->
    features = []
    # console.log "map click"
    app.hole.forEachFeatureAtPixel evt.pixel, (feature, layer) ->
      if feature.get('label')
        features.push feature.get('label')
    lie = features[0]

    # select_length = app.Modify.select.getFeatures().getLength()

    # if lie != "pin" and lie != "stroke" and select_length? and select_length > 0
    #   console.log app.Modify.select
      # app.Modify.select.getFeatures().pop()

    switch lie
      when 'Green' then app.showGreen()
      when 'pin', 'stroke' then return
      else app.hideGreen()

    return

  app.green_count = app.layer.green_strokes.getSource().getFeatures().length

  app.addGreenOverlay app.green_count

  app.init_stroke_change_events(app.layer.strokes.getSource().getFeatures())
  app.init_stroke_change_events(app.layer.green_strokes.getSource().getFeatures())

  # app.layer.green_strokes.getSource().forEachFeature (stroke) ->
  #   app.init_stroke_change_events stroke
  #   return

  app.all_strokes = new (ol.source.Vector)(
    features: (new (ol.format.GeoJSON)).readFeatures(app.geojson_obj.strokes,
      featureProjection: 'EPSG:3857'
      dataProjection: 'EPSG:4326')
  )

  app.init_pin_change_event()

  $.ajax
    url: app.url.get_strokes
    success: (data) ->
      # data.png = data.svg.substr(0, data.svg.lastIndexOf(".")) + ".png";
      # console.log(data.png);
      switch BrowserDetect.browser
        when 'Explorer', 'Edge' then return
        else
          app.layer.svg = new (ol.layer.Image)(
            opacity: 1
            source: new (ol.source.ImageStatic)(
              url: data.svg
              imageExtent: app.layer.features.getSource().getExtent()
              projection: 'EPSG:3857'
              dataProjection: 'EPSG:4326'))
          app.hole.getLayers().insertAt 3, app.layer.svg
