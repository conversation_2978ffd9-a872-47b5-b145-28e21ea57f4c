(function() {

  window.app.color = {
    BUNKER_FILL:           '#FBD581',
    BUNKER_STROKE:         '#FBD581',
    FAIRWAY_FILL:          '#87B85C',
    FAIRWAY_STROKE:        '#3F701F',
    GREEN_FILL:            '#B3DD8D',
    G<PERSON><PERSON>_STROKE:          '#87B85C',
    GREEN_SELECTED_FILL:   '#B3DD8D',
    GREEN_SELECTED_STROKE: 'transparent',
    HAZARD_FILL:           '#96CBFE',
    HAZARD_STROKE:         'rgba(0,0,0,.05)',
    HOLE_BOUNDARY_FILL:    '#4D8324',
    HOLE_BOUNDARY_STROKE:  '#87B85C',
    RED_FILL:              '#CA0612',
    RED_STROKE:            '#CA0612',
    SHOT_MANUAL_FILL:      'rgba(0,0,0,.05)',
    SHOT_MANUAL_STROKE:    '#CA0612',
    SHOT_DETECTED_FILL:    'rgba(0,0,0,.05)',
    SHOT_DETECTED_STROKE:  'rgba(0,0,0,.05)',
    SHOT_ALERT_FILL:       '#CA0612',
    SHOT_ALERT_STROKE:     '#FFF',
    TEE_AREA_FILL:         'transparent',
    TEE_AREA_STROKE:       'transparent',
    TEE_BOX_FILL:          '#87B85C',
    TEE_BOX_STROKE:        '#87B85C',
    TREES_FILL:            'magenta',
    TREES_STROKE:          '#FFFFFF',
    NO_FILL:               'rgba(0,0,0,0)',
    NO_STROKE:             'rgba(0,0,0,0)',
    DISTANCE_FILL:         'rgba(202,6,18, 0.3)'
  };

  window.app.styles = {

    distance: new ol.style.Style({
      zIndex: 23,
      fill: new ol.style.Fill({
        color: app.color.DISTANCE_FILL
      }),
      stroke: new ol.style.Stroke({
       color: app.color.RED_STROKE,
       width: 1,
       lineDash: [4,5]
      })
    }),

    hidden: new ol.style.Style({
      fill: new ol.style.Fill({
        color: app.color.NO_FILL
      }),
      stroke: new ol.style.Stroke({
        color: app.color.NO_STROKE,
      })
    }),

    pin: [new ol.style.Style({
      zIndex: 26,
      image: new ol.style.Icon({
        anchor: [.5, 1],
        anchorXUnits: 'fraction',
        anchorYUnits: 'fraction',
        opacity: 1,
        scale: .75,
        src: "<%= asset_path 'pin.png' %>"
      })
    })],

    pin_selected: [new ol.style.Style({
      zIndex: 26,
      image: new ol.style.Icon({
        anchor: [.5, 1],
        anchorXUnits: 'fraction',
        anchorYUnits: 'fraction',
        opacity: 1,
        scale: .75,
        src: "<%= asset_path 'pin-red.png' %>"
      })

    })],

    green: [new ol.style.Style({
      zIndex: 22,
      fill: new ol.style.Fill({
        color: app.color.GREEN_FILL
      }),
      stroke: new ol.style.Stroke({
        // color: stroke.green,
        color: app.color.GREEN_STROKE,
        width: 3,
        lineDash: [4,5]
      })
    })],

    green_selected: [new ol.style.Style({
      zIndex: 22,
      fill: new ol.style.Fill({
        color: app.color.GREEN_FILL
      }),
      stroke: new ol.style.Stroke({
        color: app.color.GREEN_SELECTED_FILL,
        width: 0,
        lineDash: [0,0]
      })
    })],

    hole_boundary: [new ol.style.Style({
      zIndex: 10,
      fill: new ol.style.Fill({
        color: app.color.HOLE_BOUNDARY_FILL
      }),
      stroke: new ol.style.Stroke({
        color: app.color.HOLE_BOUNDARY_STROKE,
        width: 4
      })
    })],

    tee_boundary: [new ol.style.Style({
      zIndex: 18,
      fill: new ol.style.Fill({
        color: app.color.TEE_BOUNDARY_FILL
      })
    })],

    tee: [new ol.style.Style({
      zIndex: 20,
      fill: new ol.style.Fill({
        color: app.color.TEE_BOX_FILL
      })
    })],

    fairway: [new ol.style.Style({
      zIndex: 12,
      fill: new ol.style.Fill({
        color: app.color.FAIRWAY_FILL
      }),
      stroke: new ol.style.Stroke({
        color: app.color.FAIRWAY_STROKE,
        width: 1
      })

    })],

    bunker: [new ol.style.Style({
      zIndex: 14,
      fill: new ol.style.Fill({
        color: app.color.BUNKER_FILL
      })
    })],

    hazard: [new ol.style.Style({
      zIndex: 16,
      fill: new ol.style.Fill({
        color: app.color.HAZARD_FILL
      }),
      stroke: new ol.style.Stroke({
        color: app.color.HAZARD_STROKE,
        width: 3
      })

    })],

    modify: [new ol.style.Style({
      fill: new ol.style.Fill({
        color: "rgba(255,255,255,1)"
      })
    })],

    stroke: function(feature){
      var fill_color, stroke_color;
      var ordinal = feature.get('ordinal') || 0;

      switch (feature.get('status')) {
        case "detected":
          text_color = "rgba(255,255,255,.8)";
          fill_color   = "rgba(0,0,0,.5)";
          stroke_color = 'rgba(255,255,255,.5)';
          line_dash = [ 0, 0 ];
          break;
        case "manual":
        case "moved":
          text_color = "rgba(255,255,255,.8)";
          fill_color   = app.color.RED_FILL;
          stroke_color = 'rgba(255,255,255,1)';
          line_dash = [ 0, 0 ];
          break;
        default:
          text_color = "rgba(255,255,255,.8)";
          fill_color   = "rgba(0,0,0, 0.5)";
          stroke_color = 'rgba(255,255,255,.5)';
          line_dash = [ 0, 0 ];
      }
      return [
        new ol.style.Style({
          text: new ol.style.Text({
            text: ordinal.toString(),
            font: "bold 10px Clubhaus",
            fill: new ol.style.Fill({ color: text_color }),
            offsetX: 0,
            offsetY: 0
          }),
          image: new ol.style.Circle({
            radius: 12,
            fill: new ol.style.Fill({ color: fill_color}),
            stroke: new ol.style.Stroke({
              color: stroke_color,
              width: 2,
              lineDash: line_dash
            })
          })
        }),
        new ol.style.Style({
          text: new ol.style.Text({
            text: app.get_formatted_distance(feature),
            font: "bold 14px Clubhaus",
            fill: new ol.style.Fill({ color: text_color }),
            stroke: new ol.style.Stroke({color: "rgba(0,0,0,.2)", width: 10}),
            offsetX: 0,
            offsetY: -30
          })
        })
      ]
    },

    stroke_selected: function(feature){
      var fill_color, stroke_color;
      var ordinal = feature.get('ordinal');
      if (feature.get('auto_detected')) {
        // Stroke was auto detected
        text_color = app.color.RED_FILL;
        fill_color = "rgba(255,255,255,1)";
        stroke_color = "rgba(255,255,255,.5)";
      } else {
        // Stroke was manually added
        text_color = app.color.RED_FILL;
        fill_color = "rgba(255,255,255,1)";
        stroke_color = "rgba(255,255,255,.5)";
      }
      return [
        new ol.style.Style({
          text: new ol.style.Text({
            text: ordinal.toString(),
            font: "bold 10px Clubhaus",
            fill: new ol.style.Fill({
              color: text_color
            }),
            offsetX: 0,
            offsetY: 0
          }),
          image: new ol.style.Circle({
            radius: 12,
            fill: new ol.style.Fill({
              color: fill_color
            }),
            stroke: new ol.style.Stroke({
              color: "rgba(0,0,0,.2)",
              width: 4
            })
          })
        }),


        new ol.style.Style({
          text: new ol.style.Text({
            text: app.get_formatted_distance(feature),
            font: "bold 14px Clubhaus",
            fill: new ol.style.Fill({
              color: "#FFF"
            }),
            offsetX: 0,
            offsetY: -30,
            stroke: new ol.style.Stroke({
              color: "rgba(0,0,0,.2)",
              width: 10
            })
          })
        })
      ]
    },

    stroke_add: function(feature){
      return [
        new ol.style.Style({
          text: new ol.style.Text({
            text: '?',
            font: "bold 10px Clubhaus",
            fill: new ol.style.Fill({ color: "#ffffff" }),
            offsetX: 0,
            offsetY: 0
          }),
          image: new ol.style.Circle({
            radius: 12,
            fill: new ol.style.Fill({
              color: app.color.RED_FILL
            }),
            stroke: new ol.style.Stroke({
              color: "rgba(255,255,255,.5)",
              width: 3
            })
          })
        }),
        new ol.style.Style({
          text: new ol.style.Text({
            text: app.get_formatted_distance(feature),
            font: "bold 14px Clubhaus",
            fill: new ol.style.Fill({
              color: "#ffffff"
            }),
            offsetX: 0,
            offsetY: -30
          })
        })
      ]
    },

    styleFunction: function(feature, resolution){
      label = feature.getProperties().type.label;
      feature_style = label.toLowerCase().replace(' ', '_');
      return app.styles[feature_style];
    }

  }


}).call(this);
