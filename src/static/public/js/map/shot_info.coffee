app.savePin = ->
  pin = app.layer.pin.getSource().getFeatures()[0]
  pin_longlat = app.toLongLat(app.getLocation(pin))
  pin_coords = pin.getGeometry().getCoordinates()
  pin_data =
    id: app.hole_id
    hole_played: pin_location: pin_longlat
  $.ajax
    type: 'PUT'
    url: "/my/holes/#{app.geojson_obj.id}"
    data: pin_data

app.addShot = (stroke) ->
  console.log "app.addShot"
  lie = app.set_lie(stroke)
  # stroke.set('lie', 'rough')
  data = stroke_played:
    ordinal: stroke.get('ordinal')
    geometry: app.toLongLat(app.getLocation(stroke))
    lie: lie
  $.ajax
    type: 'POST'
    url: "/my/holes/#{app.geojson_obj.id}/strokes/map_add"
    data: data

app.saveShot = (obj) ->
  $.ajax
    type: 'PUT'
    url: "/my/strokes/#{obj.id}"
    data: obj
    success: (data) ->
      false
    error: (data) ->
      false

app.updateStrokeObject = (stroke, save = true) ->
  props = stroke.getProperties()
  data =
    id: props.id
    hole_id: props.hole_played_id
    hole_played_id: props.hole_played_id
    stroke_played:
      auto_detected: stroke.get('auto_detected')
      club_id: stroke.get('club_id')
      lie: stroke.get('lie')
      difficult: stroke.get('difficult')
      recovery: stroke.get('recovery')
      penalty: stroke.get('penalty')
      ordinal: props.ordinal
      geometry: app.toLongLat(app.getLocation(stroke))

  # -----------------------------------------------
  # I'm thinking this might just need to save as a default.
  # -----------------------------------------------
  # if app.layer.green_strokes.getVisible() == false and lie == "Green"
  # if app.layer.strokes.getVisible() == false and lie != "Green"
  if save == true
    app.saveShot data

app.raw_distance = (f1, f2) ->
  wgs84Sphere = new (ol.Sphere)(6378137)
  c1 = app.getLocation(f1)
  c2 = app.getLocation(f2)
  # -----------------------------------------------
  # Haversine distance calculation
  # -----------------------------------------------
  length = 0
  c1 = ol.proj.transform(c1, 'EPSG:3857', 'EPSG:4326')
  c2 = ol.proj.transform(c2, 'EPSG:3857', 'EPSG:4326')
  length += wgs84Sphere.haversineDistance(c1, c2)
  length = Math.round(length * 100) / 100
  @meters = length
  @yards = Math.round(length * 1.09361) + ' YDS'
  @feet = (length * 1.09361 * 3).toFixed(1) + ' FT'
  this

app.dogleg_distance = (shot, dogleg, pin) ->
  wgs84Sphere = new (ol.Sphere)(6378137)
  length = 0
  p1 = app.getLocation(shot)
  p2 = dogleg
  p3 = app.getLocation(pin)
  p1 = ol.proj.transform(p1, 'EPSG:3857', 'EPSG:4326')
  p2 = [ p2[1], p2[0] ]
  p3 = ol.proj.transform(p3, 'EPSG:3857', 'EPSG:4326')
  p1_p2 = wgs84Sphere.haversineDistance(p1, p2)
  p2_p3 = wgs84Sphere.haversineDistance(p2, p3)
  length += parseFloat(p1_p2) + parseFloat(p2_p3)
  length = Math.round(length * 100) / 100
  @meters = length
  @yards = Math.round(length * 1.09361) + ' YDS'
  @feet = (length * 1.09361 * 3).toFixed(1) + ' FT'
  this

app.get_formatted_distance = (stroke) ->
  stroke_lie = stroke.get('lie')
  switch stroke_lie
    when 'Green'
      distance = app.raw_distance(stroke, app.Pin)
      return distance.feet

    when 'Tee'
      if app.geojson_obj.dogleg.center.length > 0
        dogleg = app.geojson_obj.dogleg.center
        distance = app.dogleg_distance(stroke, dogleg, app.Pin)
        return distance.yards
      else
        distance = app.raw_distance(stroke, app.Pin)
        return distance.yards

    else
      distance = app.raw_distance(stroke, app.Pin)
      return distance.yards

app.set_distance_to_pin = (stroke) ->
  stroke_lie = stroke.get('lie')
  switch stroke_lie
    when 'Green'
      distance = app.raw_distance(stroke, app.Pin)
      stroke.set 'distance_to_pin', distance.feet
    when 'Tee'
      if app.geojson_obj.dogleg.center.length > 0
        dogleg = app.geojson_obj.dogleg.center
        distance = app.dogleg_distance(stroke, dogleg, app.Pin)
      else
        distance = app.raw_distance(stroke, app.Pin)
      stroke.set 'distance_to_pin', distance.yards
    else
      distance = app.raw_distance(stroke, app.Pin)
      stroke.set 'distance_to_pin', distance.yards

  # false

app.set_distance_on_strokes = (stroke) ->
  order = stroke.get('ordinal')
  all_strokes = app.all_strokes.getFeatures()
  if order != 1
    # -----------------------------------------------
    # if it's not the first stroke ordinal.
    # -----------------------------------------------
    previous_stroke_lie = all_strokes[order - 2].get('lie')
    previous_stroke_id = all_strokes[order - 2].getId()
    previous_stroke = undefined
    previous_distance = undefined
    if previous_stroke_lie == 'Green'
      previous_stroke = app.layer.green_strokes.getSource().getFeatureById(previous_stroke_id)
    if previous_stroke_lie != 'Green'
      previous_stroke = app.layer.strokes.getSource().getFeatureById(previous_stroke_id)
    previous_distance = app.raw_distance(stroke, previous_stroke)
    if previous_stroke_lie == 'Green'
      previous_stroke.set 'distance', previous_distance.feet
    else
      previous_stroke.set 'distance', previous_distance.yards
  if order != all_strokes.length
    next_stroke_lie = all_strokes[order].get('lie')
    next_stroke_id = all_strokes[order].getId()
    next_stroke = undefined
    next_distance = undefined
    if next_stroke_lie == 'Green'
      next_stroke = app.layer.green_strokes.getSource().getFeatureById(next_stroke_id)
      next_distance = app.raw_distance(stroke, next_stroke)
      if stroke.get('lie') == 'Green'
        stroke.set 'distance', next_distance.feet
      else
        stroke.set 'distance', next_distance.yards
    else
      next_stroke = app.layer.strokes.getSource().getFeatureById(next_stroke_id)
      next_distance = app.raw_distance(stroke, next_stroke)
      stroke.set 'distance', next_distance.yards

app.set_lie = (stroke) ->
  features = []
  pixel = app.hole.getPixelFromCoordinate(app.getLocation(stroke))
  app.hole.forEachFeatureAtPixel pixel, (stroke, layer) ->
    if stroke.get('label')
      features.push stroke.get('label')
    index = $.inArray('stroke', features)
    if index != -1
      features.splice index
    return
  lie = features[0]
  if lie is undefined then lie = "Rough"
  console.log lie
  stroke.set 'lie', lie
  return lie

app.update_stroke_ui = (stroke) ->
  app.set_lie(stroke)
  app.set_distance_on_strokes(stroke)
  app.set_distance_to_pin(stroke)
