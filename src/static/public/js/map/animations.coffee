app.initAnimations = ->

  app.doPan = (location) ->
    pan = ol.animation.pan(
      source: app.hole.getView().getCenter()
      duration: 500)
    app.hole.beforeRender pan
    app.hole.getView().setCenter location
    return

  app.doPanAndZoom = (location, factor) ->
    pan = ol.animation.pan(
      source: app.hole.getView().getCenter()
      duration: 500)
    zoom = ol.animation.zoom(
      resolution: app.hole.getView().getResolution()
      duration: 500)
    # app.hole.getView().setResolution(factor);
    app.hole.getView().setZoom factor
    app.hole.getView().setCenter location
    app.hole.beforeRender zoom
    app.hole.beforeRender pan
    return

  app.doZoom = (factor) ->
    zoom = ol.animation.zoom(
      resolution: app.hole.getView().getResolution()
      duration: 500)
    app.hole.getView().setZoom factor
    app.hole.beforeRender zoom
    return

  return
