app.removeStroke = (e) ->
  if confirm("Are you sure?")
    stroke_to_delete = app.Modify.select.getFeatures().getArray()[0].getId()
    app.Modify.select.getFeatures().pop()
    $.ajax
      type: 'DELETE'
      url: '/my/strokes/' + stroke_to_delete

app.initControls = ->

  app.swipe = document.getElementById('swipe')
  app.swipe.addEventListener 'input', (->
    app.hole.render()
    return
  ), false


  app.strokeControls = new (ol.control.Control)(
    element: document.getElementById('stroke-actions')
  )

  app.addStrokeControl = new (ol.control.Control)(
    element: document.getElementById('add_stroke_control')
  )

  app.removeStrokeControl = new (ol.control.Control)(
    element: document.getElementById('remove_stroke_control')
  )

  app.zoom = new (ol.control.Zoom)
  app.zoomslider = new (ol.control.ZoomSlider)

  app.hole.addControl app.zoom
  app.hole.addControl app.zoomslider
  $zoomSlider = $('.ol-zoomslider').detach()
  $zoomSlider.insertAfter $('.ol-zoom-in')
  if app.geojson_obj.completed == true then return
  else
    app.hole.addControl app.strokeControls
    app.hole.addControl app.addStrokeControl
    app.hole.addControl app.removeStrokeControl
