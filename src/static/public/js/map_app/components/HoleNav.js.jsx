'use strict';

var HoleNav = React.createClass({
  propTypes: {
    holeIds: React.PropTypes.array.isRequired,
    holeId: React.PropTypes.number.isRequired
  },

  getDefaultProps: function(){
    return {
      holeId: null,
      holeIds: [],
    }
  },

  handleBack: function(e){
    if(e) e.stopPropagation();

    var idx    = this.props.holeIds.indexOf( this.props.holeId );
    var prevId = this.props.holeIds[idx-1];
    this.redirectToId( prevId );
  },

  handleNext: function(e){
    if(e) e.stopPropagation();

    var idx    = this.props.holeIds.indexOf( this.props.holeId );
    var nextId = this.props.holeIds[idx+1];
    this.redirectToId( nextId );
  },

  redirectToId: function( id ) {
    if( _.isNumber(id) )
      Turbolinks.visit( window.location.href.replace(this.props.holeId, id) );
  },

  render: function() {
    var idx    = this.props.holeIds.indexOf( this.props.holeId );
    var nextId = this.props.holeIds[idx+1];
    var prevId = this.props.holeIds[idx-1];

    return (
      <div className="hole-map-nav">
        <i ref="backBtn" className={classNames("fa","fa-chevron-left", {'disabled': !prevId})} onClick={this.handleBack}></i>
        <i ref="nextBtn" className={classNames("fa","fa-chevron-right", {'disabled': !nextId})} onClick={this.handleNext}></i>
      </div>
    )
  }

});

