var ScoreCard = React.createClass({
  propTypes: {
    score: React.PropTypes.number,
    card: React.PropTypes.object
  },

  getDefaultProps: function(){
    return {
      score: StrokeStore.getScore() || 0,
      card: HoleStore.scorecard || {}
    }
  },

  cssForScore: function(score){
    return HoleStore.getCssForScore(score);
  },

  render: function() {
    var score    = this.props.score;
    var card     = this.props.card;
    var scoreCSS = classNames("scorecard-score", this.cssForScore(score));

    return (
      <div id="score_card_legend">

        <table className="table scorecard">
          <thead>
            <tr>
              <th>Hole</th>
              <th>Yards</th>
              <th>Par</th>
              <th>HDCP</th>
              <th>Score</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="scorecard-number">{card.name}</td>
              <td>{card.yards}</td>
              <td>{card.par}</td>
              <td>{card.handicap}</td>
              <td className={scoreCSS}>{score}</td>
            </tr>
          </tbody>
        </table>

      </div>
    )
  }

});
