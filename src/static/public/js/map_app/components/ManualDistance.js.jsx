'use strict';

var ManualDistance = React.createClass({

  getDefaultProps: function(){
    return {
      stroke: { properties:{} }
    }
  },

  getInitialState: function(){
    var units     = this.props.stroke.properties.distance_to_pin;
    var precision = units == "ft" ? 1 : 0;
    var distance  = parseFloat(this.props.stroke.properties.distance_units).
      toFixed( precision );

    return {
      editing: false,
      distance: distance,
      units: units
    }
  },

  componentWillMount: function() {
    Map.Signals.manualDistance.started.add( this.stopEdit )
    Map.Signals.manualDistance.canceled.add( this.stopEdit )
  },

  componentWillUnmount: function() {
    Map.Signals.manualDistance.started.remove( this.stopEdit )
    Map.Signals.manualDistance.canceled.remove( this.stopEdit )
  },

  componentWillReceiveProps: function( nextProps ) {
    if( !this.state.editing ) {
      var units     = nextProps.stroke.properties.distance_units;
      var precision = units == "ft" ? 1 : 0;
      var distance  = parseFloat(nextProps.stroke.properties.distance_to_pin).
        toFixed( precision );

      this.setState({ distance: distance, units: units });
    }
  },

  startEdit: function(e){
    if(e) e.stopPropagation();
    Map.Signals.manualDistance.started.dispatch();

    this.setState({editing: true});
    StrokeStore.selectById( this.props.stroke.id );
    //this.onChange(this.state.distance, this.state.units);
  },

  stopEdit: function(e){
    if(e) e.stopPropagation();
    this.setState({editing: false})
    Map.Signals.manualDistance.stopped.dispatch();
  },

  handleChangeDistance: function(e){
    e.stopPropagation();
    this.setState({distance: e.target.value});
    this.onChange(e.target.value, this.state.units);
  },

  handleChangeUnits: function(e){
    this.setState({units: e.target.value});
    this.onChange(this.state.distance, e.target.value);
  },

  onChange: function(distance, units) {
    var meters = Utils.distanceToMeters( distance, units );
    Map.Signals.manualDistance.changed.dispatch( meters, this.props.stroke );
  },

  handleClick: function(e){
    e.stopPropagation();
  },

  handleFocus: function(e){
    e.stopPropagation();
    e.target.select();
  },

  handleKeyUp: function(e) {
    e.stopPropagation();
    if( e.keyCode == 27 )
      this.stopEdit(e);
  },

  renderForDisplay: function() {
    var completed = HoleStore.completed;
    var stroke    = this.props.stroke;
    var distance  = stroke.properties.distance_to_pin_formatted;

    return (
      <span>
        {distance}
        <span ref="tools" className={classNames({'hidden': completed})}>
          <i ref="startBtn" className="fa fa-plus-square" onClick={this.startEdit}></i>
          <span data-toggle="tooltip" data-placement="top" title="Click on the + icon to manually enter the start distance of your shot and then hit Tab, Return, or click outside of the input box."><i className="fa fa-question"></i></span>
        </span>
      </span>
    )
  },

  renderForEdit: function() {
    var units     = this.state.units;
    var step      = units == "ft" ? 0.1 : 1;
    var distance  = this.state.distance;

    return (
      <div className="manual-distance" onKeyUp={this.handleKeyUp} onClick={this.handleClick}>
        <input ref="input" autoFocus={true} type="number" step={step} min="0" value={distance} onChange={this.handleChangeDistance} onFocus={this.handleFocus} />
        <select ref="units" value={units} onChange={this.handleChangeUnits} >
          <option value="yds">Yds</option>
          <option value="ft">Ft</option>
        </select>
        <button ref="doneBtn" onClick={this.stopEdit}>
          <i className="fa fa-check"></i>
        </button>
      </div>
    )
  },

  render: function() {
    if(this.state.editing)
      return this.renderForEdit();
    else
      return this.renderForDisplay();
  }
});
