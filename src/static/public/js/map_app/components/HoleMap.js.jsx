'use strict';

var HoleMap = React.createClass({
  propTypes: {
    token: React.PropTypes.string.isRequired,
    holeDataUrl: React.PropTypes.string.isRequired,
    clubDataUrl: React.PropTypes.string.isRequired,
    center: React.PropTypes.array,
    holeIds: React.PropTypes.array,
    holeId: React.PropTypes.number
  },

  getDefaultProps: function(){
    return {
      token: null,
      center: [0,0],
      holeDataUrl: null,
      clubDataUrl: null
    }
  },

  getInitialState: function() {
    return {
      strokes: StrokeStore,
      hole: HoleStore,
      clubs: ClubStore
    }
  },

  componentDidMount: function() {
    this.map = new Map('map',
      this.props.token,
      this.props.center
    );

    window.map = this.map; // for debugging

    // register signals
    HoleStore.signals.changed.add( this.onHoleChange );
    StrokeStore.signals.changed.add( this.onStrokeChange );
    ClubStore.signals.changed.add( this.onClubChange );

    // Load data stores from URLs
    HoleStore.loadUrl( this.props.holeDataUrl );
    ClubStore.loadUrl( this.props.clubDataUrl );

    // initialize swiper settings from storage
    // this.getSwipeValue() hide swipe
    this.setSwipeValue( 0);
  },

  componentWillUnmount: function() {
    HoleStore.signals.changed.remove( this.onHoleChange );
    StrokeStore.signals.changed.remove( this.onStrokeChange );
    ClubStore.signals.changed.remove( this.onClubChange );

    if(this.map) {
      this.map.tearDown();
      delete this.map;
      this.map   = null;
      window.map = null; // for debugging
    }
  },

  onClubChange: function(store){
    this.setState({clubs: store});
  },

  onStrokeChange: function(store){
    this.setState({strokes: store});
    this.map.loadStrokes( store.all() );
  },

  onHoleChange: function(store) {
    this.setState({hole: store});

    this.map.rotate( store.getRadiansFacingEast() );
    this.map.loadHole( store.hole );
    this.map.loadSVG( store.svg );

    // activate/deactivate selector
    if(store.completed)
      this.map.selector.deactivate()
    else
      this.map.selector.activate()
  },

  handleSwipe: function(e) {
    this.setSwipeValue( e.target.value );
  },

  getSwipeValue: function() {
    return localStorage.getItem('swipeVal') || 100;
  },

  setSwipeValue: function(val) {
    localStorage.setItem('swipeVal', val);
    this.map.setClipPercent( val );
  },

  render: function() {
    return (
      <div>
        <div id="map" className="map">
          <StrokeControls map={this.map} completed={HoleStore.completed} />
          <HoleNav holeId={this.props.holeId} holeIds={this.props.holeIds} />
        </div>
        <Range ref="swipe" id="swipe" type="range" style={{width: "100%", display: 'none'}} value={this.getSwipeValue()} onChange={this.handleSwipe} data-has-tip="bottom" data-tip-content="Swipe to remove overlays and view satellite imagery." />
      </div>
    )
  }

});
