'use strict';

var StrokeControls = React.createClass({

  getDefaultProps: function(){
    return {
      completed: false,
      map: null
    }
  },

  getInitialState: function(){
    return{
      editing: false,
      stroke: {properties:{}}
    }
  },

  componentDidMount: function(){
    StrokeStore.signals.selected.add( this.handleSelectedStroke );
    StrokeStore.signals.deselected.add( this.handleDeselectedStroke );

    this.domItems = jQuery( ReactDOM.findDOMNode( this.refs["stroke-actions"] ) );
    this.domItems.hide();
  },

  componentWillUnmount: function(){
    StrokeStore.signals.selected.remove( this.handleSelectedStroke );
    StrokeStore.signals.deselected.remove( this.handleDeselectedStroke );

    if(this.domItems){
      this.domItems = null;
    }
  },

  handleSelectedStroke: function(stroke) {
    this.setState({stroke: stroke, editing: true});
    if(!this.props.completed){
      this.domItems.clearQueue();
      this.domItems.slideOpen(250);
    }
  },

  handleDeselectedStroke: function(stroke) {
    this.setState({stroke: null, editing: false});
    this.domItems.delay(1800).slideClose(250);
  },

  handlePenalty: function(e) {
    e.stopPropagation()
    StrokeStore.togglePenalty( this.state.stroke )
  },

  handleRecovery: function(e) {
    e.stopPropagation()
    StrokeStore.toggleRecovery( this.state.stroke )
  },

  handleDifficult: function(e) {
    e.stopPropagation()
    StrokeStore.toggleDifficult( this.state.stroke )
  },

  handleClubClick: function(e, club) {
    e.stopPropagation()
    if(this.state.stroke)
      StrokeStore.setClubById( this.state.stroke.id, club )
  },

  handleLieClick: function(e, lie) {
    e.stopPropagation()
    if(this.state.stroke)
      StrokeStore.setLieById( this.state.stroke.id, lie )
  },

  handleAddBefore: function(e) {
    e.stopPropagation()
    if(this.props.map)
      StrokeStore.createBefore( this.state.stroke );
  },

  handleAddAfter: function(e) {
    e.stopPropagation()
    if(this.props.map)
      StrokeStore.createAfter( this.state.stroke );
  },

  handleRemove: function(e) {
    e.stopPropagation()
    if( this.state.stroke && confirm("Are you sure?") )
      StrokeStore.removeById( this.state.stroke.id );
  },

  renderClubs: function() {
    var clubs = ClubStore.clubs.filter(club => !!club.type);
    const families = ['driver', 'fw', 'hybrid', 'iron', 'wedge', 'putter'];
    let wedgeClubs = [];
    let wClubs = [];
    let putterClubs = [];
    let orderClubs = [];
    families.forEach((family) => {
      const c = clubs.filter((club) => {
        return club.type.toLowerCase().includes(family);
      });
      if (c.length > 0) {
        if (family === 'wedge') {
          wedgeClubs = wedgeClubs.concat(c);
        } else if (family === 'putter') {
          putterClubs = putterClubs.concat(c);
        } else {
          orderClubs = orderClubs.concat(c);
        }
      }
    });

    ['Pitching Wedge', 'PW', 'Approach Wedge', 'AW', 'Sand Wedge', 'SW', 'Lob Wedge', 'LW', 'Specialist Wedge'].forEach((family) => {
      const c = wedgeClubs.filter((club) => {
        return club.type.toLowerCase().includes(family.toLowerCase());
      });
      wClubs = wClubs.concat(c);
    });


    orderClubs = orderClubs.concat(wClubs).concat(putterClubs);
    return orderClubs.map(function(c){
      return(
        <li key={c.clubId} onClick={(e) => this.handleClubClick(e, c)}>
          <a href="#">{c.type}</a>
        </li>
      )
    }.bind(this));
  },

  renderLies: function() {
    var lies = ["Tee", "Fairway", "Bunker", "Hazard", "Rough", "Green"]
    return lies.map(function(lie, i){
      return(
        <li key={i} onClick={(e) => this.handleLieClick(e, lie)}>
          <a href="#">{lie}</a>
        </li>
      )
    }.bind(this));
  },

  render: function() {
    var editing       = this.state.editing;
    var stroke        = this.state.stroke;
    var isFirst       = StrokeStore.isFirst( stroke );
    var disabled      = classNames({'disabled': !editing});
    var disabledLie   = classNames({'disabled': isFirst || disabled});
    var penalty       = stroke ? stroke.properties.penalty : false
    var recovery      = stroke ? stroke.properties.recovery : false
    var difficult     = stroke ? stroke.properties.difficult : false
    var disabledClub  = classNames({'disabled': !editing || penalty});

    return (
      <div className="stroke-controls">

        <div className={classNames("draw-stroke", {'hidden' : this.props.completed})}>
          <button className={classNames("btn", "btn-primary", "btn-remove", {"disabled": !editing})} disabled={!editing} onClick={this.handleRemove}>Remove Stroke</button>

          <div className="btn-group btn-add">
            <button type="button" className={classNames("btn btn-default dropdown-toggle", {"disabled": !editing})} data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
              Add Stroke <span className="caret"></span>
            </button>
            <ul className="dropdown-menu">
              <li><a href="#" onClick={this.handleAddBefore}>Before</a></li>
              <li><a href="#" onClick={this.handleAddAfter}>After</a></li>
            </ul>
          </div>
        </div>

        <div className="selected-stroke" ref="stroke-actions">
          <div id="change-lie-group" className={classNames("btn-group", "dropup", disabledLie)}>
            <button id="change-lie-btn" className={classNames("btn","btn-primary","dropdown-toggle", disabledLie)} data-toggle="dropdown" disabled={disabledLie} data-toggle-disabled>Change Lie <span className="caret"></span> </button>
            <ul id="change-lie" className='dropdown-menu' aria-labelledby="change-lie-btn">
              {this.renderLies()}
            </ul>
          </div>

          <div id="change-club-group" className={classNames("btn-group","dropup", disabledClub)}>
            <button id="change-club-btn" className={classNames("btn","btn-primary","dropdown-toggle", disabledClub)} data-toggle="dropdown" disabled={disabledClub} data-toggle-disabled>Change Club <span className='caret'></span></button>
            <ul id="change-club" className='dropdown-menu' aria-labelledby="change-lie-btn">
              {this.renderClubs()}
            </ul>
          </div>

          <div className="btn-group">
            <button className={classNames("btn", "btn-primary", disabled, {'active': penalty})} onClick={this.handlePenalty} data-toggle-disabled>Penalty</button>
            <button className={classNames("btn", "btn-primary", disabled, {'active': recovery})} onClick={this.handleRecovery} data-toggle-disabled>Recovery</button>
            <button className={classNames("btn", "btn-primary", disabled, {'active': difficult})} onClick={this.handleDifficult} data-toggle-disabled>Difficult</button>
          </div>
        </div>

      </div>
    )
  }

});
