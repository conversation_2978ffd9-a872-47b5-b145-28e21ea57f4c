'use strict';

var StrokeItem = React.createClass({

  getDefaultProps: function(){
    return {
      stroke: null,
      adding: false,
      completed: false
    }
  },

  handleStrokeClick: function(e){
    e.stopPropagation();
    if( this.props.stroke.properties.selected == true )
      StrokeStore.deselectById( this.props.stroke.id );
    else
      StrokeStore.selectById( this.props.stroke.id );
  },

  handleAddClick: function(e){
    e.stopPropagation();
    StrokeStore.addAfterId( this.props.stroke.id );
  },

  handleRemoveClick: function(e){
    e.stopPropagation();
    if( confirm("Are you sure?") )
      StrokeStore.removeById( this.props.stroke.id );
  },

  render: function() {
    var completed     = this.props.completed;
    var adding        = this.props.adding;
    var stroke        = this.props.stroke;
    var selected      = classNames({'selected': stroke.properties.selected == true});
    var liClasses     = classNames("stroke_row", selected);
    var addClasses    = classNames("add_shot_here",{'hidden': !adding});
    var strokesGained = stroke.properties.strokes_gained.toFixed(1);

    var penalty       = stroke ? stroke.properties.penalty : false
    var club_name     = penalty ? "" : ( stroke.properties.club_type || '(add club)' )

    return(
      <li className={liClasses} data-sortable={stroke.id} onClick={this.handleStrokeClick}>
        <div className="stroke_link list-item has-score has-accessory">
          <span className="stroke_ordinal score-badge">{stroke.properties.ordinal}</span>

          <h3 className="stroke_lie">
            <span className="lie_condition">{stroke.properties.lie}</span>
            <span className="club_name">
              {club_name}
            </span>
          </h3>

          <div className="stroke-stats">
            <div className="stat">
              Distance to Pin: <ManualDistance stroke={stroke} />
            </div>
            <div className="stat">Shot Distance: {stroke.properties.distance_formatted}</div>

            <div className="stat">
              <div className="col-sm-8"> Strokes Gained: {strokesGained}</div>
              <div ref="tools" className={classNames("tools","col-sm-4", "text-right", {'hidden': completed})}>
                <i ref="sortBtn" className="fa fa-sort sort-handle"></i>
                <i ref="trashBtn" className="fa fa-trash" onClick={this.handleRemoveClick}></i>
              </div>
            </div>

          </div>
        </div>

        <div ref="addBtn" className={addClasses} onClick={this.handleAddClick}></div>
      </li>
    )
  }

})
