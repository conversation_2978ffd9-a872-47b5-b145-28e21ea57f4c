'use strict';

var StrokeTable = React.createClass({

  getInitialState: function(){
    return{
      adding: false,
      addingTee: false,
      strokes: StrokeStore,
      score: StrokeStore.getScore(),
      completed: HoleStore.completed
    }
  },

  componentDidMount: function(){
    StrokeStore.signals.changed.add(this.onStrokeChange);
    HoleStore.signals.changed.add(this.onHoleChange);

    // loop in jQuery sortable for reordering the strokes
    this.domItems = jQuery( ReactDOM.findDOMNode( this.refs.items ) )
    this.domItems.sortable({
      handle: ".sort-handle",
      cursor: "move",
      stop: this.handleReorder
    });
  },

  componentWillUnmount: function(){
    StrokeStore.signals.changed.remove(this.onStrokeChange);
    HoleStore.signals.changed.remove(this.onHoleChange);

    if(this.domItems){
      this.domItems.sortable( "destroy" );
      this.domItems = null;
    }
  },

  //
  // Strokes changed, update our state.
  //
  onStrokeChange: function( store ){
    this.setState({
      strokes: store,
      score: store.getScore()
    });
  },

  //
  // Hole chaged, update our state.
  //
  onHoleChange: function( store ){
    this.setState({
      completed: store.completed
    });
  },

  //
  // Grab list of 'stroke ids' from the jQuery
  // sortable object and update the StrokeStore.
  //
  handleReorder: function(event, ui) {
    var reorderedIds = this.domItems.
      sortable('toArray', {attribute: 'data-sortable'})

    // cancel the sort, let react render new order
    this.domItems.sortable('cancel')

    // reorder strokes at thte store
    StrokeStore.reorderByIds( reorderedIds );
  },

  //
  // Toggle "adding" state
  //
  handleAddClick: function(){
    this.setState({adding: !this.state.adding})
  },

  //
  // Toggle "addingTee" state
  //
  handleAddTeeClick: function(){
    this.setState({addingTee: !this.state.addingTee})
    // send request here
    StrokeStore.createTeeShot()
    this.setState({addingTee: !this.state.addingTee})
  },

  renderStrokes: function() {
    var adding    = this.state.adding;
    var addingTee = this.state.addingTee;
    var completed = this.state.completed;
    var strokes   = this.state.strokes.all();

    return strokes.map( function(s) {
      return( <StrokeItem key={s.id} stroke={s} adding={adding} addingTee={addingTee} completed={completed} /> )
    }.bind(this));
  },

  addButtonText: function(){
    if(this.state.adding)
      return ("Done Adding");
    else
      return (<span><i className="icon ion-plus"></i>Add Stroke</span>);
  },

  addTeeButtonText: function() {
    if(this.state.addingTee)
      return("Done Adding");
    else
      return (<span><i className="icon ion-plus"></i>Add Tee Shot</span>);
  },

  render: function() {
    var card            = HoleStore.scorecard;
    var score           = this.state.score;
    var completed       = this.state.completed;
    var hideAdd         = (completed || score<1);
    var hideAddTeeShot  = (completed || score>0);

    return (
      <div>
        <ScoreCard score={score} card={card} />

        <div className="list-item list-item-divider">
          Strokes
        </div>

        <ul id="shot-table" ref="items">
          {this.renderStrokes()}
        </ul>

        <div className={classNames("add-stroke-container", {'hidden': hideAdd})}>
          <button ref="addBtn" onClick={this.handleAddClick}>
            {this.addButtonText()}
          </button>
        </div>

        <div className={classNames("add-stroke-container", {'hidden': hideAddTeeShot})}>
          <button ref="addTeeBtn" onClick={this.handleAddTeeClick}>
            {this.addTeeButtonText()}
          </button>
        </div>
      </div>
    )
  }
});
