var RoundScore = React.createClass({
  propTypes: {
    par: React.PropTypes.number,
    roundScore: React.PropTypes.number,
    holeScore: React.PropTypes.number
  },

  getDefaultProps: function(){
    return {
      par: 0,
      roundScore: 0,
      holeScore: 0
    }
  },

  getInitialState: function(){
    return {
      totalScore: this.props.roundScore
    }
  },

  componentDidMount: function(){
    StrokeStore.signals.changed.add(this.onStrokeChange);
  },

  componentWillUnmount: function(){
    StrokeStore.signals.changed.remove(this.onStrokeChange);
  },

  onStrokeChange: function() {
    //var base  = (this.props.roundScore - this.props.holeScore);
    //var score = (base + StrokeStore.getScore());
    var score = this.props.roundScore
    this.setState({totalScore: score});
  },

  render: function() {
    var par      = this.props.par;
    var score    = this.state.totalScore;
    var scoreCSS = classNames("score-badge", this.props.css);

    return (
      <span className={scoreCSS}>{score}</span>
    )
  }

});
