@FILLS =
  default: new ol.style.Fill
    color: 'rgba(77, 131, 36, 1)'
  dark: new ol.style.Fill
    color: 'rgba(107, 113, 125, 1)'
  light: new ol.style.Fill
    color: "rgba(255,255,255, 0.8)"
  white: new ol.style.Fill
    color: "rgba(255,255,255,1)"
  red: new ol.style.Fill
    color: 'rgba(202, 6, 18, 1)'
  red_trans: new ol.style.Fill
    color: 'rgba(202,6,18, 0.3)'
  green: new ol.style.Fill
    color: 'rgba(179, 221, 141, 1)'
  fairway: new ol.style.Fill
    color: 'rgba(135, 184, 92, 1)'
  tee: new ol.style.Fill
    color: 'rgba(135, 184, 92, 1)'
  bunker: new ol.style.Fill
    color: 'rgba(251, 213, 129, 1)'
  hazard: new ol.style.Fill
    color: 'rgba(150, 203, 254, 1)'
  manual: new ol.style.Fill
    color: 'rgba(107, 113, 125, 1)'
  detected: new ol.style.Fill
    color: 'rgba(0,0,0, 0.5)'

@STROKES =
  default: new ol.style.Stroke
    color: '#87B85C'
    width: 2
  white: new ol.style.Stroke
    color: 'rgba(255,255,255, 1)'
    width: 2
  light: new ol.style.Stroke
    color: 'rgba(255,255,255, 0.8)'
    width: 2
  red_dashed: new ol.style.Stroke
    color: '#CA0612'
    width: 1
    lineDash: [4, 5]
  green: new ol.style.Stroke
    color: 'rgba(135, 184, 92, 1)'
    width: 3
  fairway: new ol.style.Stroke
    color: 'rgba(63, 112, 31, 1)'
    width: 2
  tee: new ol.style.Stroke
    color: 'rgba(135, 184, 92, 1)'
    width: 1
  bunker: new ol.style.Stroke
    color: 'rgba(251, 213, 129, 1)'
    width: 1
  hazard: new ol.style.Stroke
    color: 'rgba(0,0,0,.05)'
    width: 3
  manual: new ol.style.Stroke
    color: 'rgba(255,255,255,.5)'
    width: 3
  detected: new ol.style.Stroke
    color: 'rgba(255,255,255,.5)'
    width: 3
  selected: new ol.style.Stroke
    color: 'rgba(0,0,0,.2)'
    width: 4

@STYLES =
  hidden: new ol.style.Style(null)

  default: new ol.style.Style
    zIndex: 1
    fill: FILLS.default
    stroke: STROKES.default

  green: new ol.style.Style
    zIndex: 10
    fill: FILLS.green
    stroke: STROKES.green

  fairway: new ol.style.Style
    zIndex: 5
    fill: FILLS.fairway
    stroke: STROKES.fairway

  tee: new ol.style.Style
    zIndex: 6
    fill: FILLS.tee
    stroke: STROKES.tee

  bunker: new ol.style.Style
    zIndex: 11
    fill: FILLS.bunker
    stroke: STROKES.bunker

  hazard: new ol.style.Style
    zIndex: 6
    fill: FILLS.hazard
    stroke: STROKES.hazard

  putts: new ol.style.Style
    text: new ol.style.Text
      text: 'Putts'
      font: 'bold 10px Clubhaus'
      fill: FILLS.light
      offsetX: 0
      offsetY: 0
    fill: FILLS.default
    stroke: STROKES.default

  stroke_distance: new ol.style.Style
    text: new ol.style.Text
      text: "0 yds",
      font: "bold 14px Clubhaus",
      fill: new ol.style.Fill
        color: 'rgba(255,255,255,.8)'
      stroke: new ol.style.Stroke
        color: "rgba(0,0,0,.2)"
        width: 10
      offsetX: 0,
      offsetY: -30

  stroke_distance_selected: new ol.style.Style
    text: new ol.style.Text
      text: "0 yds",
      font: "bold 14px Clubhaus",
      fill: FILLS.white
      stroke: new ol.style.Stroke
        color: "rgba(0,0,0,.2)"
        width: 10
      offsetX: 0,
      offsetY: -30

  stroke: new ol.style.Style
    text: new ol.style.Text
      text: '0'
      font: 'bold 10px Clubhaus'
      fill: new ol.style.Fill
        color: 'rgba(255,255,255,.8)'
      offsetX: 0
      offsetY: 0
    image: new ol.style.Circle
      fill: FILLS.manual
      stroke: STROKES.manual
      radius: 12

  stroke_detected: new ol.style.Style
    text: new ol.style.Text
      text: '0'
      font: 'bold 10px Clubhaus'
      fill: new ol.style.Fill
        color: 'rgba(255,255,255,.8)'
      offsetX: 0
      offsetY: 0
    image: new ol.style.Circle
      fill: FILLS.detected
      stroke: STROKES.detected
      radius: 12

  stroke_moved: new ol.style.Style
    text: new ol.style.Text
      text: '0'
      font: 'bold 10px Clubhaus'
      fill: FILLS.white
      offsetX: 0
      offsetY: 0
    image: new ol.style.Circle
      fill: FILLS.red
      stroke: STROKES.white
      radius: 12

  stroke_selected: new ol.style.Style
    text: new ol.style.Text
      text: '0'
      font: 'bold 10px Clubhaus'
      fill: FILLS.red
      offsetX: 0
      offsetY: 0
    image: new ol.style.Circle
      fill: FILLS.white
      stroke: STROKES.selected
      radius: 12

  pin: new ol.style.Style
    image: new ol.style.Icon
      anchor: [.5,1]
      anchorXUnits: 'fraction'
      anchorYUnits: 'fraction'
      opacity: 1
      scale: .75
      src: '<%= asset_path "pin.png" %>'

  pin_selected: new ol.style.Style
    image: new ol.style.Icon
      anchor: [.5,1]
      anchorXUnits: 'fraction'
      anchorYUnits: 'fraction'
      opacity: 1
      scale: .75
      src: '<%= asset_path "pin-red.png" %>'

  pin_circle: new ol.style.Style
    fill: FILLS.red_trans
    stroke: STROKES.red_dashed
