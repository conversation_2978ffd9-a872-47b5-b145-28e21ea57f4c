#
# Map Object
#
class @Map
  constructor: (domID, token, startLatLon = null) ->
    @domID         = domID
    @token         = token
    @initialLatLon = startLatLon
    @olMap         = @_createOLMap()
    @greens        = new Map.GreenKeeper(@)
    @selector      = new Map.Selector(@)
    @tools         = new Map.Tools(@)
    @clipPercent   = 100

    # handle canvas clipping and shading
    @getLayer("svg").on('precompose', @_clipToPercent)
    @getLayer("svg").on('postcompose', @_restoreCanvas)

    @getLayer("polygons").on('precompose', @_clipToPercent)
    @getLayer("polygons").on('precompose', @_tintCanvas)
    @getLayer("polygons").on('postcompose', @_restoreCanvas)

  #
  # Tear down the map
  #
  tearDown: ->
    @getLayer("svg").un('precompose', @_clipToPercent)
    @getLayer("svg").un('postcompose', @_restoreCanvas)

    @getLayer("polygons").un('precompose', @_clipToPercent)
    @getLayer("polygons").un('precompose', @_tintCanvas)
    @getLayer("polygons").un('postcompose', @_restoreCanvas)

    @greens.tearDown()
    @selector.tearDown()
    @tools.tearDown()
    @unmount()
    @olMap.getLayers().clear()

  #
  # Set clip percentage
  #
  setClipPercent: (val) ->
    @clipPercent = val
    @olMap.render()

  #
  # Get center of current view (in Lon/Lat)
  #
  getCenterLonLat: ->
    center = @olMap.getView().getCenter()
    ol.proj.toLonLat( center )

  #
  # When clicking on the green
  #
  onClickGreen: (onGreen) ->
    if onGreen
      @zoomTo( @greens.getGreenCenterCoordinates(), 20 )
    else
      @zoomToHole()

  #
  # When moving stroke
  #
  onMoveStroke: (stroke) ->
    lonlat = ol.proj.toLonLat( stroke.getGeometry().getCoordinates() )

    if @tools.snapToCircle
      lonlat = @tools.getCircleClosestPoint(lonlat)

    StrokeStore.moveToLngLatById( stroke.getId(), lonlat )

  #
  # When stroke is selected
  #
  onSelectStroke: (stroke) ->
    StrokeStore.selectById( stroke.getId() )

  #
  # When stroke is dropped
  #
  onDropStroke: (stroke) ->
    StrokeStore.saveById( stroke.getId() )

  #
  # When stroke is unselected
  #
  onDeselectStroke: (stroke) ->
    Map.Signals.manualDistance.canceled.dispatch()
    StrokeStore.deselectById( stroke.getId() )

  #
  # When pin is selected
  #
  onSelectPin: (pin) ->
    pin.set("selected", true)

  #
  # When pin is unselected
  #
  onDeselectPin: (pin) ->
    pin.set("selected", false)

  #
  # When moving pin
  #
  onMovePin: (pin) ->
    lonlat = ol.proj.toLonLat( pin.getGeometry().getCoordinates() )
    HoleStore.movePinToLngLat( lonlat )

  #
  # When pin is dropped
  #
  onDropPin: (pin) ->
    lnglat = ol.proj.toLonLat( pin.getGeometry().getCoordinates() )
    HoleStore.savePinLngLat( lnglat )

  #
  # Mount the map to a DOM ID
  #
  mount: (domID = @domID) ->
    @olMap.setTarget( domID )

  #
  # Unmount the map to a DOM ID
  #
  unmount: ->
    @olMap.setTarget( null )

  #
  # Rotate the map view
  #
  rotate: (radian) ->
    @olMap.getView().rotate( radian )

  #
  # Add new stroke to map
  #
  addStrokeToMap: (geojson) ->
    format = new ol.format.GeoJSON()
    source = @getSource("strokes")

    feature = format.readFeature(geojson,
      featureProjection: 'EPSG:3857'
      dataProjection: 'EPSG:4326')

    source.addFeature( feature )
    if feature.get('selected') == true
      @selector.selectFeature( feature )

  #
  # Remove stroke from map
  #
  removeStrokeFromMap: (feature) ->
    @removeStrokesById( [feature.getId()] )

  #
  # Update properties of existing stroke
  # on map
  #
  updateStrokeOnMap: (stroke, geojson) ->
    coord = ol.proj.fromLonLat( geojson.geometry.coordinates )

    stroke.setProperties( geojson.properties )
    stroke.getGeometry().setCoordinates( coord )

    if stroke.get('selected') == true
      @selector.selectFeature( stroke )
    else
      @selector.unselectFeature( stroke )

  #
  # Remove strokes by ID
  #
  removeStrokesById: (ids) ->
    return unless ids
    source = @getSource("strokes")

    _.flatten([ids]).forEach (id) =>
      if feature = source.getFeatureById(id)
        @selector.unselectFeature(feature)
        source.removeFeature(feature)

  #
  # Remove any strokes on the map
  # that aren't in the 'keepIds'.
  #
  cleanupStrokes: (keepIds) ->
    return unless _.isArray(keepIds)

    source = @getSource("strokes")
    mapIds = _.compact( source.getFeatures().
      map((f) -> if f.get("kind") == "stroke" then f.getId() else null)
    )

    @removeStrokesById( _.difference(mapIds, keepIds) )

  #
  # Load the strokes
  #
  loadStrokes: (geojson) ->
    return unless geojson
    source = @getSource("strokes")

    # update or add each features
    geojson.forEach (strokeJSON) =>
      if found = source.getFeatureById( strokeJSON.id )
        @updateStrokeOnMap( found, strokeJSON )
      else
        @addStrokeToMap( strokeJSON )

    # now, cleanup unwanted strokes
    @cleanupStrokes( geojson.map((s) -> s.id) )

    # notify greens keeper
    @greens.strokesChange()

  #
  # Load the SVG image of the hole
  #
  loadSVG: (url) ->
    return if _.contains(['Explorer','Edge'], BrowserDetect.browser ) # don't load SVG for these

    polygons = @getLayer("polygons")
    source   = @getSource("polygons")
    layer    = @getLayer("svg")

    # show polygons
    polygons.setOpacity(0.8)
    return unless url

    image = new ol.source.ImageStatic
      url: url,
      imageExtent: source.getExtent()
      projection: 'EPSG:3857'
      dataProjection: 'EPSG:4326'

    # hide polygons
    polygons.setOpacity(0)
    layer.setSource( image )

  #
  # Load the hole polygons
  #
  loadHole: (geojson) ->
    return unless geojson

    format = new ol.format.GeoJSON()
    source = new ol.source.Vector
      features: format.readFeatures( geojson,
        featureProjection: 'EPSG:3857',
        dataProjection: 'EPSG:4326')

    @getLayer('polygons').setSource( source )

    @greens.holeChange()  # notify greens keeper
    @mount()              # mount map to DOM ID
    @zoomToHole(false)    # zoom to hole (don't animate)


  #
  # Zoom to location w/animation
  #
  zoomTo: (coord, zoom = null) ->
    view = @olMap.getView()
    zoom ?= view.getZoom()

    @_addPanZoomAnimations()
    view.setCenter(coord)
    view.setZoom(zoom)

  #
  # Zoom to fit hole
  #
  zoomToHole: (animate = true)->
    source = @getSource("polygons")

    @_addPanZoomAnimations() if animate
    @olMap.getView().fit source.getExtent(), @olMap.getSize(),
      padding: [ 0, 50, 0, 50 ]
      constrainResolution: false
      nearest: false

  #
  # Toggle layer visibility
  #
  toggleLayer: (name) ->
    layer = @getLayer(name)
    layer.setVisible( !layer.getVisible() )
    layer

  #
  # Find the "green" feature
  #
  getGreenFeature: ->
    source = @getSource("polygons")
    source.forEachFeature (f) ->
      if f.get('label') == "Green" then f else null

  #
  # Find source by name
  #
  getSource: (name) ->
    @getLayer(name).getSource()

  #
  # Find layer by name
  #
  getLayer: (name) ->
    _.find @olMap.getLayers().getArray(), (layer) ->
      layer.get('name') == name

  #
  # --------------------------------
  # PRIVATE METHODS BELOW
  # --------------------------------
  #

  #
  # restore canvas settings
  #
  _restoreCanvas: (event) =>
    event.context.restore()

  #
  # Tint the canvas
  #
  _tintCanvas: (event) =>
    ctx   = event.context
    width = ctx.canvas.width * (@clipPercent / 100)

    ctx.globalAlpha = 0.6
    ctx.fillStyle   = "#333"
    ctx.fillRect 0, 0, width, ctx.canvas.height

  #
  # clip polygons & SVG layers
  #
  _clipToPercent: (event) =>
    ctx   = event.context
    width = ctx.canvas.width * (@clipPercent / 100)

    ctx.save()
    ctx.beginPath()
    ctx.rect 0, 0, width, ctx.canvas.height
    ctx.clip()

  #
  # Add Rotate animation to
  # pre-render stack.
  #
  _addRotateAnimation: ->
    view = @olMap.getView()

    rotateAni = ol.animation.rotate
      rotation: view.getRotation(),
      duration: 500

    @olMap.beforeRender rotateAni

  #
  # Add Pan/Zoom animations to
  # pre-render stack.
  #
  _addPanZoomAnimations: ->
    view = @olMap.getView()

    panAni = ol.animation.pan
      source: view.getCenter(),
      duration: 500

    zoomAni = ol.animation.zoom
      resolution: view.getResolution(),
      duration: 500

    @olMap.beforeRender panAni, zoomAni


  #
  # Create the OpenLayers Map object
  #
  _createOLMap: ->
    lonlat = @initialLatLon.reverse() # change lat/lon => lon/lat

    new ol.Map
      units: 'm'
      controls: [
        new (ol.control.Zoom)
        new (ol.control.ZoomSlider)
      ]
      interactions: new ol.interaction.defaults
        mouseWheelZoom: false
      layers: [
        new ol.layer.Tile
          name: 'sat'
          zIndex: 0
          opacity: 1
          source: new ol.source.BingMaps
            key: @token
            imagerySet: 'Aerial'

        new ol.layer.Vector
          name: 'polygons'
          zIndex: 1
          opacity: 0.8
          updateWhileAnimating: true
          updateWhileInteracting: false
          style: StyleUtil.styleFeature

        new ol.layer.Image
          name: 'svg'
          zIndex: 2

        new ol.layer.Vector
          name: 'strokes'
          zIndex: 3
          updateWhileAnimating: true
          updateWhileInteracting: false
          style: StyleUtil.styleFeature
          source: new ol.source.Vector()

        new ol.layer.Vector
          name: 'tools'
          zIndex: 4
          source: new ol.source.Vector()
      ]
      view: new ol.View
        center: ol.proj.fromLonLat( lonlat )
        zoom: 18
        minZoom: 14
        maxZoom: 19
