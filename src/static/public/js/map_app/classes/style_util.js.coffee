#
#  Style util object for helpers
#
class @StyleUtil

  #
  # Main style function for OL
  #
  @styleFeature: (feature, resolution) ->
    # hide feature
    if feature.get('visible') == false
      return [STYLES.hidden]

    # label strokes
    if feature.get('kind') == 'stroke'
      return StyleUtil._strokeStyle(feature, resolution)

    # label putts on green
    if feature.get('label') == 'PuttsCount'
      return StyleUtil._puttLabelStyle(feature, resolution)

    #  for the pin
    if feature.get('kind') == 'pin'
      return StyleUtil._pinStyle(feature, resolution)

    #  for polygons
    if feature.get('kind') == 'polygon'
      return StyleUtil._polygonStyle(feature, resolution)

    # default
    return [STYLES.default]

  ################
  # Private
  ################

  #
  # Polygon style
  #
  @_polygonStyle: (feature, resolution) ->
    label = feature.get('label').toString().toLowerCase()
    style = STYLES[label]
    if style then [style] else [STYLES.default]

  #
  # Pin style
  #
  @_pinStyle: (feature, resolution) ->
    if feature.get('selected') == true
      [STYLES.pin_selected]
    else
      [STYLES.pin]

  #
  # Setup number of putts label (for over the green)
  #
  @_puttLabelStyle: (feature, resolution) ->
    style = STYLES.putts
    putts = feature.get('putts') || 0
    text  = putts.toString() + " putts"

    style.getText().setText( if putts > 0 then text else '' )
    [style]

  #
  # Create style for a stroke
  #
  @_strokeStyle: (feature, resolution) ->
    style    = STYLES.stroke
    label    = STYLES.stroke_distance
    distance = feature.get('distance_to_pin_formatted') || ""
    status   = feature.get('status').toString().toLowerCase()
    status   = "selected" if feature.get('selected') == true

    switch status
      when 'selected'
        style = STYLES.stroke_selected
        label = STYLES.stroke_distance_selected
      when 'detected', 'default'
        style = STYLES.stroke_detected
      when 'moved', 'manual'
        style = STYLES.stroke_moved

    if style
      style.getText().setText( feature.get('ordinal').toString() )

    if label
      label.getText().setText( distance.toString() )

    [style, label]
