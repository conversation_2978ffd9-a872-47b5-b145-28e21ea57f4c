#
# Stroke API interface to backend
#
class @StrokeAPI
  #
  # Add new stroke
  #
  @create: (beforeStroke = null, afterStroke = null, onSuccess = null) ->
    @_create(beforeStroke, afterStroke, onSuccess)

  #
  # Append new stroke
  #
  @append: (stroke, onSuccess = null) -> @_append(stroke, onSuccess)

  #
  # Add a Tee SHot
  #
  @addTeeShot: (onSuccess = null) -> @_addTeeShot(onSuccess)

  #
  # Update existing stroke
  #
  @update: (stroke, onSuccess = null) -> @_update(stroke, onSuccess)

  #
  # Send new order of strokes
  #
  @reorder: (ids, onSuccess = null) -> @_reorder(ids, onSuccess)

  #
  # Delete stroke
  #
  @delete: (id, onSuccess = null) -> @_delete( id, onSuccess )

  #
  # Toggle penalty stroke
  #
  @penalty: (id, onSuccess = null) -> @_penalty( id, onSuccess )

  #
  # Toggle recovery stroke
  #
  @recovery: (id, onSuccess = null) -> @_recovery( id, onSuccess )

  #
  # Toggle difficult stroke
  #
  @difficult: (id, onSuccess = null) -> @_difficult( id, onSuccess )

  #
  # --------------------------------
  # PRIVATE METHODS BELOW
  # --------------------------------
  #

  #
  # Difficult stroke
  #
  # PUT    /my/strokes/:stroke_id/difficult(.:format)
  #
  @_difficult: (id, onSuccess = null) ->
    $.ajax
      type: 'PUT'
      url: "/my/strokes/#{id}/difficult.geojson"
      dataType: 'json'
      success: (geojson) ->
        onSuccess( geojson ) if onSuccess
      error: ( jqXHR, textStatus, errorThrown ) ->
        console.warn( "StrokeAPI ERROR (difficult): ", errorThrown )

  #
  # Recovery stroke
  #
  # PUT    /my/strokes/:stroke_id/recovery(.:format)
  #
  @_recovery: (id, onSuccess = null) ->
    $.ajax
      type: 'PUT'
      url: "/my/strokes/#{id}/recovery.geojson"
      dataType: 'json'
      success: (geojson) ->
        onSuccess( geojson ) if onSuccess
      error: ( jqXHR, textStatus, errorThrown ) ->
        console.warn( "StrokeAPI ERROR (recovery): ", errorThrown )

  #
  # Penalty stroke
  #
  # PUT    /my/strokes/:stroke_id/penalty(.:format)
  #
  @_penalty: (id, onSuccess = null) ->
    $.ajax
      type: 'PUT'
      url: "/my/strokes/#{id}/penalty.geojson"
      dataType: 'json'
      success: (geojson) ->
        onSuccess( geojson ) if onSuccess
      error: ( jqXHR, textStatus, errorThrown ) ->
        console.warn( "StrokeAPI ERROR (penalty): ", errorThrown )

  #
  # Delete stroke
  #
  # DELETE   /my/strokes/:id(.:format)
  #
  @_delete: (id, onSuccess = null) ->
    $.ajax
      type: 'DELETE'
      url: "/my/strokes/#{id}.geojson"
      dataType: 'json'
      success: (geojson) ->
        onSuccess( geojson ) if onSuccess
      error: ( jqXHR, textStatus, errorThrown ) ->
        console.warn( "StrokeAPI ERROR (delete): ", errorThrown )

  #
  # Add new stroke
  #
  # POST   /my/holes/:hole_id/strokes/add(.:format)
  #
  @_create: (beforeStroke = null, afterStroke = null, onSuccess = null) ->
    beforeStroke ||= {}
    afterStroke  ||= {}

    $.ajax
      type: 'POST'
      url: "/my/holes/#{HoleStore.id}/strokes/add.geojson"
      dataType: 'json'
      data:
        stroke_played:
          before_stroke_id: beforeStroke.id
          after_stroke_id: afterStroke.id
      success: (geojson) ->
        onSuccess( geojson ) if onSuccess
      error: ( jqXHR, textStatus, errorThrown ) ->
        console.warn( "StrokeAPI ERROR (create): ", errorThrown )

  #
  # Send new order of strokes
  #
  # PUT   /my/holes/:hole_id/strokes/reorder(.:format)
  #
  @_reorder: (ids, onSuccess = null) ->
    $.ajax
      type: 'PUT'
      url: "/my/holes/#{HoleStore.id}/strokes/reorder.geojson"
      dataType: 'json'
      data:
        ids: ids
      success: (geojson) ->
        onSuccess( geojson ) if onSuccess
      error: ( jqXHR, textStatus, errorThrown ) ->
        console.warn( "StrokeAPI ERROR (reorder): ", errorThrown )

  #
  # Append stroke
  #
  # POST   /my/holes/:hole_id/strokes/map_add(.:format)
  #
  @_append: (stroke, onSuccess = null) ->
    stroke_params = @_strokeToParams(stroke)

    $.ajax
      type: 'POST'
      url: "/my/holes/#{HoleStore.id}/strokes/map_add.geojson"
      dataType: 'json'
      data:
        hole_id: stroke.properties.hole_id
        hole_played_id: stroke.properties.hole_played_id
        stroke_played: stroke_params
      success: (geojson) ->
        onSuccess( geojson ) if onSuccess
      error: ( jqXHR, textStatus, errorThrown ) ->
        console.warn( "StrokeAPI ERROR (append): ", errorThrown )

  #
  # Add a Tee Shot a hole with no strokes
  #
  # POST    /my/holes/:hole_id/strokes/add_tee_shot(.:format)
  #
  @_addTeeShot: (onSuccess = null) ->

    $.ajax
      type: 'POST'
      url: "/my/holes/#{HoleStore.id}/strokes/add_tee_shot.geojson"
      dataType: 'json'
      success: (geojson) ->
        onSuccess( geojson ) if onSuccess
      error: ( jqXHR, textStatus, errorThrown ) ->
        console.warn( "StrokeAPI ERROR (append): ", errorThrown )

  #
  # Update strokes
  #
  # PUT    /my/strokes/:id(.:format)
  #
  @_update: (stroke, onSuccess = null) ->
    stroke_id     = stroke.id
    stroke_params = @_strokeToParams(stroke)

    $.ajax
      type: 'PUT'
      url: "/my/strokes/#{stroke_id}.geojson"
      dataType: 'json'
      data:
        id: stroke_id
        hole_id: stroke.properties.hole_id
        hole_played_id: stroke.properties.hole_played_id
        stroke_played: stroke_params
      success: (geojson) ->
        onSuccess( geojson ) if onSuccess
      error: ( jqXHR, textStatus, errorThrown ) ->
        console.warn( "StrokeAPI ERROR (update): ", errorThrown )

  #
  # Convert a Stroke into API params for Rails.
  #
  @_strokeToParams: (stroke) ->
    params = _.pick(stroke.properties,
      'lie', 'club_id', 'distance', 'distance_from_pin', 'penalty',
      'recovery', 'difficult', 'ordinal', 'auto_detected'
    )
    _.extend({}, params, {geometry: stroke.geometry.coordinates})
