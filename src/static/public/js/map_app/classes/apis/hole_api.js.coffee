#
# Hole API interface to backend
#
class @HoleAPI
  #
  # Update existing stroke
  #
  @update: (id, lonlat, onSuccess = null) -> @_update(id, lonlat, onSuccess)

  #
  # --------------------------------
  # PRIVATE METHODS BELOW
  # --------------------------------
  #

  #
  # Update Hole
  #
  # PUT     /my/holes/:hole_id
  #
  @_update: (id, lonlat, onSuccess = null) ->
    $.ajax
      type: 'PUT'
      url: "/my/holes/#{id}.json"
      dataType: 'json'
      data: 
        hole_played:
          pin_location: lonlat
      success: (geojson) ->
        onSuccess( geojson ) if onSuccess
      error: ( jqXHR, textStatus, errorThrown ) ->
        console.error( "HoleAPI ERROR (update): ", errorThrown )
