#
# General Utils
#
class @Utils

  #
  # Trim string
  #
  @trim: (str) ->
    str.toString().trim()

  #
  # Convert Yards/FT to meters
  #
  @distanceToMeters: (dist, units='yds') ->
    units = units.toLowerCase()
    switch units
      when 'yds' then (dist * 0.9144)
      when 'ft' then (dist * 0.3048)
      else dist

  #
  # Captialize string
  #
  @capitalize: (string) -> 
    string.charAt(0).toUpperCase() + string.slice(1)
