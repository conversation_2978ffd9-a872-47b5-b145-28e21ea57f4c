#
# Map Tools Object
#
class @Map.Tools
  constructor: (map) ->
    @map          = map
    @source       = @map.getSource('tools')

    @snapToCircle = false
    @_setupCircle()
    
    # signal handlers
    Map.Signals.manualDistance.changed.add( @onEnableCircle )
    Map.Signals.manualDistance.stopped.add( @onDisableCircle )

  #
  # Tear down the tools
  #
  tearDown: ->
    # remove signal handlers
    Map.Signals.manualDistance.changed.remove( @onEnableCircle )
    Map.Signals.manualDistance.stopped.remove( @onDisableCircle )

    @source.clear()

  #
  # Turn on the "Circle" and snap
  # the stroke to nearest point.
  #
  onEnableCircle: (meters, stroke) =>
    @setCircleInMeters(meters)

    # snap stroke to closest point on circle
    if stroke
      lonlat = @getCircleClosestPoint( stroke.geometry.coordinates )
      StrokeStore.moveToLngLatById( stroke.id, lonlat )
      StrokeStore.saveById( stroke.id )

  #
  # Turn off the "Circle"
  #
  onDisableCircle: =>
    @clearCircle()

  #
  # Get closes point on 'Circle'
  #
  getCircleClosestPoint: (lonlat) ->
    coord = ol.proj.fromLonLat( lonlat )
    geo   = @circle.getGeometry()

    ol.proj.toLonLat( geo.getClosestPoint( coord ) )

  #
  # Remove 'Circle'
  #
  clearCircle: ->
    @snapToCircle = false
    @circle.setGeometry(null)

  #
  # Set the 'Circle' radius (in feet)
  #
  setCircleInFeet: (feet) ->
    @setCircleInMeters( feet * 0.3048 )

  #
  # Set the 'Circle' radius (in yards)
  #
  setCircleInYards: (yards) ->
    @setCircleInMeters( yards * 0.9144 )

  #
  # Set the 'Circle' radius (in meters)
  #
  setCircleInMeters: (meters) ->
    @snapToCircle = true
    @circle.setGeometry(
      new ol.geom.Polygon.circular(new ol.Sphere(6378137), HoleStore.pin, meters, 128).
        transform('EPSG:4326', 'EPSG:3857')
    )

  #################
  # Private
  #################

  #
  # Add the pin circle to the map (tools layer)
  #
  _setupCircle: ->
    return if @circle

    @circle = new ol.Feature
    @circle.setStyle( [STYLES.pin_circle] )
    @circle.setId('PinCircle')

    @source.addFeature( @circle )

