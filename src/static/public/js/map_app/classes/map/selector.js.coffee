#
# Select manager
#
class Map.Selector
  constructor: (@map) ->
    @_addSelectInteraction()
    @_addTranslateInteraction()

  tearDown: ->
    @_removeSelectInteraction()
    @_removeTranslateInteraction()

  #
  # Anything selected?
  #
  isEmpty: ->
    @select.getFeatures().getLength() < 1

  #
  # Select the feature
  #
  selectFeature: (feature) ->
    unless _.contains( @select.getFeatures().getArray(), feature )
      @select.getFeatures().clear()
      @select.getFeatures().push( feature )

  #
  # Unselect a feature
  #
  unselectFeature: (feature) ->
    @select.getFeatures().remove( feature )

  #
  # On Feature select/deselect
  #
  onSelect: (e) =>
    e.deselected.forEach (f) ->
      switch f.get('kind')
        when "stroke" then @map.onDeselectStroke(f)
        when "pin" then @map.onDeselectPin(f)
        else f.set('selected', false)
    e.selected.forEach (f) ->
      switch f.get('kind')
        when "stroke" then @map.onSelectStroke(f)
        when "pin" then @map.onSelectPin(f)
        else f.set('selected', true)


  #
  # When drag ends, usually good time to
  # save to backend.
  #
  onDragEnd: (e) =>
    e.features.forEach (f) ->
      # console.log("dropping: ", f.get("kind"))
      switch f.get('kind')
        when "stroke" then @map.onDropStroke(f)
        when "pin" then @map.onDropPin(f)

  #
  # When feature moves, call the
  # map callback.
  #
  onMove: (e) =>
    e.features.forEach (f) ->
      # console.log("moving: ", f.get("kind"))
      switch f.get('kind')
        when "stroke" then @map.onMoveStroke(f)
        when "pin" then @map.onMovePin(f)

  #
  # Capture "context" clicks to stop
  # them from disrupting the map
  # experience.
  #
  onContextMenu: (e) =>
    e.preventDefault()

  #
  # Activate selections
  #
  activate: ->
    @select.setActive(true)
    @translate.setActive(true)

  #
  # Dectivate selections
  #
  deactivate: ->
    @select.setActive(false)
    @translate.setActive(false)


  ################
  # Private
  ################

  #
  # Add select interaction to map
  #
  _addSelectInteraction: ->
    @select = new ol.interaction.Select
      style: StyleUtil.styleFeature
      filter: (feature, layer) ->
        _.contains( ["stroke", "pin"], feature.get('kind') )

    @select.on('select', @onSelect)
    @map.olMap.addInteraction( @select )
    @map.olMap.getViewport().addEventListener('contextmenu', @onContextMenu)


  #
  # Remove select interaction from map
  #
  _removeSelectInteraction: ->
    @select.un('select', @onSelect)
    @map.olMap.removeInteraction( @select )
    @map.olMap.getViewport().removeEventListener('contextmenu', @onContextMenu)


  #
  # Add translate interaction to map
  #
  _addTranslateInteraction: ->
    @translate = new ol.interaction.Translate
      features: @select.getFeatures()

    @translate.on('translateend', @onDragEnd)
    @translate.on('translating', @onMove)
    @map.olMap.addInteraction( @translate )

  #
  # Remove translate interaction to map
  #
  _removeTranslateInteraction: ->
    @translate.un('translateend', @onDragEnd)
    @translate.un('translating', @onMove)
    @map.olMap.removeInteraction( @translate )
