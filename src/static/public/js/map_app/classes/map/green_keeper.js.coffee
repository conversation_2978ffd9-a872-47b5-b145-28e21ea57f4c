#
# Green Keeper: manages the Green
#
class @Map.GreenKeeper
  constructor: (@map) ->
    @map.olMap.getView().on('change:resolution', @onZoom)
    @map.olMap.on('singleclick', @onClickGreen)

  tearDown: ->
    @map.olMap.getView().un('change:resolution', @onZoom)
    @map.olMap.un('singleclick', @onClickGreen)

  #
  # Clicking on Green will zoom to
  # the green to show pin and shots
  # on the green. Then, clicking other
  # polygons on the hole will zoom
  # back to entire hole.
  #
  # TODO: this could be optimized more!
  #
  onClickGreen: (e) =>
    green = @map.olMap.forEachFeatureAtPixel e.pixel, (feature, layer) ->
      if feature.get('label') == "Green" then feature else null

    selected = @map.olMap.forEachFeatureAtPixel e.pixel, (feature, layer) ->
      if _.contains( ["stroke", "pin"], feature.get('kind') ) then feature else null

    if !@onGreen && green
      e.stopPropagation()
      @map.onClickGreen( @onGreen = true )
    else if @onGreen && !green && !selected
      e.stopPropagation()
      @map.onClickGreen( @onGreen = false )

  #
  # Get OL map zoom level
  #
  getZoom: ->
    @map.olMap.getView().getZoom()

  #
  # Handle zoom change
  #
  onZoom: =>
    zoom = @getZoom()
    if zoom > 18 then @showGreen() else @hideGreen()
    # console.log("GreensKeeper: map zoomed to #{zoom}")

  #
  # Handle a hole change
  #
  holeChange: ->
    @addGreenCenter()
    @addPin()

  #
  # Handle stroke change
  #
  strokesChange: ->
    @hideGreen() if @getZoom() < 19

  #
  # Add the PIN to the "strokes" layer
  #
  addPin: ->
    pin     = HoleStore.pin
    source  = @map.getSource("strokes")
    feature = new ol.Feature
      geometry: new ol.geom.Point( ol.proj.fromLonLat( HoleStore.pin ) )
      lie: "Green"
      kind: "pin"

    feature.setId("Pin")
    source.addFeature( feature )

  #
  # Add a Point to strokes layer at the
  # center of the green for labeling
  # the putt count.
  #
  addGreenCenter: ->
    extent  = @getGreen().getGeometry().getExtent()
    source  = @map.getSource("strokes")
    feature = new ol.Feature
      geometry: new ol.geom.Point( ol.extent.getCenter( extent ) )
      label: "PuttsCount"

    feature.setId("GreenCenter")
    source.addFeature( feature )

  #
  # Set the number of putts (label)
  #
  setPutts: (count) ->
    source = @map.getSource("strokes")
    label  = source.getFeatureById("GreenCenter")
    label.set('putts', count) if label

  #
  # Show features on the green
  #
  showGreen: ->
    source = @map.getSource("strokes")
    source.getFeatures().forEach (feature) ->
      if feature.get('lie') == "Green"
        feature.set('visible', true)

    @setPutts(0) # hides the label via style

  #
  # Hide features on the green
  #
  hideGreen: ->
    source       = @map.getSource("strokes")
    onGreenCount = 0
    @onGreen     = false

    source.getFeatures().forEach (feature) ->
      return unless feature.get('lie') == "Green"

      feature.set('visible', false)
      if feature.get('kind') == 'stroke'
        onGreenCount++

    @setPutts( onGreenCount ) # adds putts to green label

  #
  # Get the green center point
  #
  getGreenCenterCoordinates: ->
    source = @map.getSource("strokes")
    if center = source.getFeatureById("GreenCenter")
      center.getGeometry().getCoordinates()

  #
  # Find the "green" feature
  #
  getGreen: ->
    source = @map.getSource("polygons")
    source.forEachFeature (f) ->
      if f.get('label') == "Green" then f else null
