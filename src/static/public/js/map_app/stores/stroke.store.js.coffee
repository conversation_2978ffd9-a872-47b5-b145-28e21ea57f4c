
 # Stroke store for maintaining
 # all the stroke state data

 # Note: be sure to call "changed()"
 # after any mutations so they get
 # propagated.

class @StrokeStore
  @signals   =
    changed: new signals.Signal()
    selected: new signals.Signal()
    deselected: new signals.Signal()

  #
  # Reset the store
  #
  @reset: ->
    @strokes = []

  #
  # getter for all strokes
  #
  @all: ->
    @strokes || []

  #
  # Gets first stroke
  #
  @first: ->
    _.first( @strokes )

  #
  # Get current score (number of strokes)
  #
  @getScore: ->
    @strokes.length

  #
  # Check to see if stroke is
  # the first stroke (on tee usually).
  #
  @isFirst: (stroke) ->
    stroke == @first()

  #
  # Reorder strokes based on a sorted
  # list of stroke ids.
  #
  @reorderByIds: (reorderedIds) ->
    @strokes = _.sortBy @strokes, (s) ->
      reorderedIds.indexOf( s.id.toString() )

    # send stroke order to backend
    StrokeAPI.reorder( @getIds() )

    @_resetAllOrdinals()
    @_updateAllStats()
    @changed()


  #
  # Set the strokes lie
  #
  @setLieById: (id, lie) ->
    stroke = @getById(id)
    stroke.properties.lie = lie

    # send to server & reload
    StrokeAPI.update stroke, (geojson) =>
      @load( geojson )

  #
  # Set the strokes club
  #
  @setClubById: (id, club) ->
    stroke = @getById(id)
    stroke.properties.club_id   = club.clubId
    stroke.properties.club_type = club.type

    # send to server & reload
    StrokeAPI.update stroke, (geojson) =>
      @load( geojson )

  #
  # Toggle stroke as difficult & reload
  #
  @toggleDifficult: (stroke) ->
    StrokeAPI.difficult stroke.id, (geojson) =>
      @load( geojson )

  #
  # Toggle stroke as recovery & reload
  #
  @toggleRecovery: (stroke) ->
    StrokeAPI.recovery stroke.id, (geojson) =>
      @load( geojson )

  #
  # Toggle stroke as penalty & reload
  #
  @togglePenalty: (stroke) ->
    StrokeAPI.penalty stroke.id, (geojson) =>
      @load( geojson )

  #
  # Remove the stroke & reload
  #
  @removeById: (id) ->
    @_deselectStroke( @getById(id) )
    StrokeAPI.delete id, (geojson) =>
      @load( geojson )

  #
  # Add new Stroke
  #
  @addAfterId: (id) ->
    beforeStroke = @getById(id)
    afterStroke  = @getNext(beforeStroke)

    # send to server & reload
    StrokeAPI.create beforeStroke, afterStroke, (geojson) =>
      @load( geojson )

  #
  # Add new stroke before selected stroke.
  #
  @createBefore: (stroke) ->
    beforeStroke = @getPrevious( stroke )
    StrokeAPI.create beforeStroke, stroke, (geojson) =>
      @load( geojson )

  #
  # Add new stroke after selected stroke.
  #
  @createAfter: (stroke) ->
    afterStroke = @getNext( stroke )
    StrokeAPI.create stroke, afterStroke, (geojson) =>
      @load( geojson )

  #
  # Add a Tee Shot
  #
  @createTeeShot: () ->
    StrokeAPI.addTeeShot (geojson) =>
      @load( geojson )

  #
  # Add new stroke at center of hole
  #
  @createAtLonLat: (lonlat) ->
    lastStroke = _.last(@strokes)

    # make clone of last stroke
    if lastStroke
      stroke = JSON.parse( JSON.stringify(lastStroke) )
    else
      stroke = turf.point(lonlat)

    # create a new stroke
    stroke.id                   = _.uniqueId("stroke")
    stroke.properties.id        = stroke.id
    stroke.properties.club_id   = null
    stroke.properties.selected  = true
    stroke.properties.lie       = HoleStore.getLie( lonlat )
    stroke.geometry.coordinates = lonlat
    stroke.properties.ordinal   = stroke.properties.ordinal + 1
    @_updateStats(stroke)

    # send to server & add to strokes
    StrokeAPI.append stroke, (geojson) =>
      @strokes.push( geojson )  # add to strokes
      @_updateAllStats()        # update all stats
      @selectById( geojson.id ) # immediately select stroke


  #
  # Moving the Pin affects the stroke
  # distances, regenerate the stats.
  #
  @movePinToLngLat: (pin) ->
    @_updateAllStats(pin)
    @changed()

  #
  #  Deselect the active stroke.
  #
  @deselectById: (id) ->
    stroke = @getById( id )
    return unless stroke

    if @_deselectStroke( stroke )
      @changed()

  #
  #  Deselect ALL active strokes.
  #
  @deselectAll: ->
    @strokes.forEach (s) => @_deselectStroke( s )

  #
  #  Select a single stroke, by using
  #  its ID.
  #
  @selectById: (id) ->
    stroke = @getById( id )
    return unless stroke

    @deselectAll()
    if @_selectStroke( stroke )
      @changed()

  #
  # Get the first "selected" stroke
  #
  @getSelected: ->
    _.find @strokes, (s) -> s.properties.selected == true

  #
  #  Move stroke, it affects all
  #  stroke distances, so refresh
  #  them as well.
  #
  @moveToLngLatById: (id, lnglat) ->
    stroke = @getById( id )
    return unless stroke

    # don't update unless changed
    return if _.isEqual(stroke.geometry.coordinates, lnglat)

    # update position & lie
    stroke.geometry.coordinates = lnglat
    stroke.properties.lie       = HoleStore.getLie(lnglat)

    # refresh stats
    @_updateAllStats()
    @changed()

  #
  #  Find stroke by its ID.
  #
  @getById: (id) ->
    _.find @strokes, (s) -> s.id == id

  #
  # Get list of Stroke IDs
  #
  @getIds: ->
    @strokes.map (s) -> s.id

  #
  # Get next stroke
  #
  @getNext: (stroke) ->
    idx = @strokes.indexOf( stroke )
    @strokes[idx+1]

  #
  # Get previous stroke
  #
  @getPrevious: (stroke) ->
    idx = @strokes.indexOf( stroke )
    @strokes[idx-1]

  #
  #  Save stroke to backend & reload
  #
  @saveById: (id) ->
    StrokeAPI.update @getById(id), (geojson) =>
      @load( geojson )

  #
  # Replace stroke with new geojson
  #
  @replaceById: (id, geojson) ->
    stroke     = @getById(id)
    isSelected = stroke.properties.selected
    idx        = @strokes.indexOf( stroke )

    # replace stroke and reselect if needed
    @strokes.splice(idx, 1, geojson)
    @selectById( id ) if isSelected

    @_updateAllStats()
    @changed()

  #
  #  Initial load of storkes.
  #
  @load: ( strokes ) ->
    @assertStrokes(strokes)

    # cache selected stroke
    prevSelectedId = (@getSelected() || {}).id

    # load the new strokes
    @strokes = strokes

    # reselect the previously selected
    # after loading new strokes
    @_selectStroke( @getById( prevSelectedId ) )

    @_updateAllStats()
    @changed()


  #
  # Assert there are no empty strokes
  #
  @assertStrokes: (strokes) ->
    _.flatten([strokes]).forEach (s) ->
      unless s
        alert('ERROR: stroke(s) are missing! Cannot proceed. Try reloading page.')
        thow 'ERROR: stroke was missing! Cannot proceed.'

  #
  #  Trigger 'changed' signal
  #  so components can update
  #  accordingly.
  #
  @changed: ->
    @signals.changed.dispatch(@)

  # -----------------------
  #  PRIVATE
  # -----------------------

  #
  # Select stroke and dispatch signal.
  #
  # Returns true if stroke was selected.
  #
  @_selectStroke: (stroke) ->
    if stroke && !stroke.properties.selected == true
      stroke.properties.selected = true
      @signals.selected.dispatch(stroke)
      return true
    false

  #
  # Deselect stroke and dispatch signal.
  #
  # Returns true if stroke was deselected.
  #
  @_deselectStroke: (stroke) ->
    if stroke && stroke.properties.selected == true
      stroke.properties.selected = false
      @signals.deselected.dispatch(stroke)
      return true
    false

  #
  # Lat/Lon distance (in meters)
  #
  @_distance: (c1, c2) ->
    sphere = new ol.Sphere(6378137)
    sphere.haversineDistance(c1, c2)

  #
  #  Refresh all stroke ordinals
  #
  @_resetAllOrdinals: ->
    @strokes.forEach (stroke, idx) =>
      stroke.properties.ordinal = (idx+1)

  #
  #  Refresh all strokes
  #
  @_updateAllStats: (pin = HoleStore.pin) ->
    @strokes.forEach (s) => @_updateStats(s, pin)

  #
  #  Refresh strokes distances.
  #
  @_updateStats: (stroke, pin = HoleStore.pin) ->
    loc       = stroke.geometry.coordinates
    onGreen   = stroke.properties.lie == "Green" ? true : false
    unit      = if onGreen then " ft" else " yds"
    precision = if onGreen then 1 else 0
    isPenalty = stroke.properties.penalty == true

    idx     = @strokes.indexOf( stroke )
    next    = @strokes[idx+1]
    nextLoc = if next then next.geometry.coordinates else pin

    # consider if there is dogleg for Tee shot
    if HoleStore.dogleg && @isFirst( stroke )
      dogleg = HoleStore.dogleg.center
      distToPinMeters = @_distance( loc, dogleg ) + @_distance( dogleg, pin )
    else
      distToPinMeters = @_distance( loc, pin )

    distToPinYards  = (1.09361 * distToPinMeters)
    distToPinFeet   = (3.28084 * distToPinMeters)

    # TODO: Make the server return the proper distance.
    #   When the server returns the correct "distance",
    #   these calculation will no longer be needed here.
    distMeters = if isPenalty then 0 else @_distance( loc, nextLoc )
    distYards  = (1.09361 * distMeters)
    distFeet   = (3.28084 * distMeters)

    # add distance units
    stroke.properties.distance_units = unit.trim()

    # add onGreen
    stroke.properties.on_green = onGreen

    # set to "putter" if ongreen with no club defined
    # NOTE: this really should happen in the backend
    if onGreen && _.isNull( stroke.properties.club_id )
      stroke.properties.club_type = "Putter"

    # update distance to pin
    stroke.properties.distance_to_pin =
      if onGreen then distToPinFeet else distToPinYards

    # update dis to pin (w/units)
    stroke.properties.distance_to_pin_formatted =
      stroke.properties.distance_to_pin.toFixed(precision).toString() + unit

    # update shot distance
    stroke.properties.distance =
      if onGreen then distFeet else distYards

    # update shot distance formatted
    stroke.properties.distance_formatted =
      stroke.properties.distance.toFixed(precision).toString() + unit

  #
  # Initialize static vars
  #
  @reset()
