#
#  Hole store for maintaining
#  all the hole state data
#
#  Note: be sure to call "dispatch()"
#  after any mutations so they get
#  propagated.
#
class @HoleStore
  @signals   =
    changed: new signals.Signal()

  #
  # Reset static variables
  #
  @reset: ->
    @id        = null
    @scorecard = {}
    @hole      = {}
    @pin       = null
    @bearing   = {}
    @svg       = null
    @dogleg    = null
    @completed = false


  #
  # Get center point of hole
  #
  @getCenter: ->
    turf.center( @hole )

  #
  # Get CSS classes for particular
  # score.
  #
  @getCssForScore: (score) ->
    return null unless _.isNumber(score) && @scorecard.css

    # colors & ends
    matrix = @scorecard.css
    diff   = parseInt(score) - parseInt(@scorecard.par)
    keys   = _.keys(matrix).map (k) -> parseInt(k)
    min    = _.min(keys)
    max    = _.max(keys)

    # cap at ends
    diff = max if(diff > max)
    diff = min if(diff < min)

    matrix[diff]

  #
  # Get radians facing east for orienting the hole
  #
  @getRadiansFacingEast: ->
    adjustedBearing = (@bearing - 90) * -1
    adjustedBearing * (Math.PI/180)

  #
  # Update Pin location to the
  # backend.
  #
  @savePinLngLat: (lnglat = null) ->
    @movePinToLngLat(lnglat) if lnglat
    HoleAPI.update(@id, @pin)

  #
  # Move the pin, and notify the strokes.
  #
  @movePinToLngLat: (lnglat) ->
    unless _.isEqual(@pin, lnglat)
      @pin = lnglat
      StrokeStore.movePinToLngLat( @pin )

  #
  # Get the Pin as Feature
  #
  @pinAsFeature: ->
    turf.point @pin, {name: "Pin", kind: "pin"}

  #
  # Find lie at given lonlat. Sort
  # features found at coordinate by their
  # position (Green -> Rough). Choose
  # the first (lowest position) as
  # the highest priority lie.
  #
  @getLie: (lnglat) ->
    point   = turf.point(lnglat)
    feature = _.chain(@hole.features)
      .filter (f) -> turf.inside(point, f)
      .sortBy (f) -> f.properties.position
      .first()
      .value()

    if feature
      feature.properties.label
    else
      "Rough"

  #
  # Load the hole data
  #
  @loadUrl: ( dataUrl ) ->
    $.get(dataUrl)
    .success (data) =>
      @loadData(data)
    .fail (jqXHR, textStatus, errorThrown) =>
      console.warn( "[WARN] HoleStore.loadUrl: #{dataUrl} - ", errorThrown )

  #
  # Load data into store
  #
  @loadData: (data) ->
    @id        = data.id
    @hole      = @filterPolygons( data.hole )
    @scorecard = data.scorecard
    @pin       = data.pin.location
    @bearing   = data.rotate.bearing
    @svg       = data.svg
    @dogleg    = (if data.dogleg.center && data.dogleg.center.length>0 then data.dogleg else null)
    @completed = (data.completed == true)

    # load array of strokes
    StrokeStore.load( data.strokes.features )
    @changed()

  #
  # Filter unwanted polygons
  #
  @filterPolygons: (geojson) ->
    rejectedTypes    = ['trees', 'tee-boundary', 'course-boundary']
    geojson.features = _.reject geojson.features, (f) ->
      _.contains(rejectedTypes, f.properties.type)
    return geojson

  #
  # Trigger a "changed" signal
  #
  @changed: ->
    @signals.changed.dispatch(@)

  #
  # Initialize static vars
  #
  @reset()
