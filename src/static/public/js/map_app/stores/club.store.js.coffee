# 
#  Club store
#
class @ClubStore
  @signals = 
    changed: new signals.Signal()

  #
  # Used to "reset" the static variables.
  #
  @reset: ->
    @clubs = []

  #
  # Load the hole data
  #
  @loadUrl: ( dataUrl ) ->
    $.get(dataUrl)
    .success (data) =>
      @clubs = data.clubs
      @changed()
    .fail (jqXHR, textStatus, errorThrown) =>
      console.warn( "[WARN] ClubStore.loadUrl: #{dataUrl} - ", errorThrown )

  #
  # Trigger a "changed" signal
  #
  @changed: ->
    @signals.changed.dispatch(@)

  #
  # Initialize static vars
  #
  @reset()
