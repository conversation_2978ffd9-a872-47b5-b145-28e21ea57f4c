//= require amcharts/dist/amcharts/amcharts
//= require amcharts/dist/amcharts/xy
//= require amcharts/dist/amcharts/serial
//= require amcharts/dist/amcharts/plugins/dataloader/dataloader
//= require amcharts/dist/amcharts/plugins/responsive/responsive
//= require charts/theme

function makeFit(elt) {
  if (typeof jQuery === "function" && elt instanceof jQuery) {
    elt = elt[0];
  }

  var rect = elt.getBoundingClientRect();
  var windowHeight = $(window).innerHeight()
  if (rect.bottom > windowHeight) {
    var possibleSize = rect.height - rect.bottom + windowHeight + Math.min(rect.top, 0)
  } else if (rect.top < 0) {
    var possibleSize = rect.height + rect.top
  } else {
    possibleSize = rect.height
  }

  //adjust for extra sutff we want to fit in the bottom - the averages and maybe the nav bar
  if ($('.app-tabs').height() > 100) {
    var adjustment = 30
  } else {
    adjustment = 80
  }
  var chosenSize = Math.max(Math.min(Math.max(possibleSize, 150), 922) - adjustment, 400)

  elt = $(elt)
  elt.width(chosenSize)
  elt.height(chosenSize)
}

var writeGraphs = function (tab) {
  var id    = $(tab).data('chartId');
  var url   = $(tab).data('chartUrl');
  var type  = $(tab).data('chartType');
  var param = $(tab).data('chartParam');

  switch(id) {
    case 'driving-distance-and-dispersion-chart':
      makeDrivingChart(id, url, type);
    break;
    case 'short-proximity-to-hole-chart':
      makeShortChart(id, url, type);
    break;
    case 'approach-proximity-to-hole-chart':
      makeApproachChart(id, url, type);
    break;
    case 'stats-by-club-chart':
      if(param == 'distance')
          makeClubsDistanceChart(id, url, type);
      else
          makeClubsBarChart(id, url, type, param);
    break;
    case 'percentage-holed-chart':
      var axis_type = $(tab).data('axisType');
      makePuttingChart(id, url, type, axis_type);
    break;
  }
};

function updatePercentages(evt) {
  var chart = evt.chart
  var data = chart.chartData

  if (data.length > 0) {
    $(data).each(function(i, val){
      var cell = $('#percentages td:nth-child(' + (i + 1) + ')')
      if (val.dataContext.percentage) {
        cell.html(Math.round(val.dataContext.percentage).toString() + '%')
      } else {
        cell.html(val.dataContext.value || '-')
      }
    })
  }


}

function getRange() {
  var val = $('#graph-tabs > .active a').first().data('chartParam').toString().split('-')
  if (val.length < 2) {
    if (val[0] > 100) {
      return val[0]
    } else {
      return 0}
  } else {
    return val[0]
  }
}

function pgaMedian(dist) {
  if (dist <25) {
    return 4.6
  } else if (dist < 50) {
    return 10
  } else if (dist < 75) {
    return 14.3
  } else if (dist < 100) {
    return 15.1
  } else if (dist < 150) {
    return 21
  } else if (dist < 200) {
    return 29.9
  } else if (dist < 250) {
    return 41.4
  } else {
    return 56.1
  }
}

function updateMissed(evt) {
  var chart = evt.chart
  var data = chart.chartData
  if (data.length === 0) {
    return
  }

  var sumLeft = 0;
  var sumRight = 0;

  $(data).each(function(k, v) {
    var info = v.axes.x1y.graphs.graph
    if(info.color !== '#06851F') {
      if (info.values.x < 0) {
        sumLeft++
      } else {
        sumRight++
      }
    }
    var length = data.length

    var targets = $('.stat-driving-number')
    $(targets[0]).html(Math.round(sumLeft / length * 1000) / 10 + '%')
    $(targets[1]).html(Math.round(sumRight / length * 1000) / 10 + '%')
  })
}

function updateAverages(evt) {
  var range  = getRange()
  var pga    = pgaMedian( range );
  var median = chartMedian( evt.chart );

  $('.stat-proximity-number').html( median );
  $('.pga-average').html( pga );
}



//
// Render chart and update user AVG and pro AVG fields.
//
function renderGraph(id, type, url, inputRange) {
  AmCharts.theme = AmCharts.themes.taylormade(url);

  var chart = AmCharts.makeChart(id, {
    type: type,
    addClassNames: true,
    classNamePrefix: 'amcharts',
    graphs: [
      AmCharts.theme.AmXYChart.graph,
      AmCharts.theme.AmXYChart.graph2,
      AmCharts.theme.AmXYChart.graph3,
      AmCharts.theme.AmXYChart.graph4
    ],

    // get the valueAxes, depending on whether it is an approach
    // or short.
    valueAxes: [
      AmCharts.theme.AmXYChart.valueAxes,
      id.match(/^approach/i) ? AmCharts.theme.AmXYChart.valueAxesCenteredZeroApproach : AmCharts.theme.AmXYChart.valueAxesCenteredZeroShort
    ]
  });

  chart.addListener('drawn', hideLabels);
  // chart.addListener('dataUpdated', function(e){
  //   var range     = parseInt( inputRange.toString() );
  //   var pga       = pgaMedian( range );
  //   var median    = chartMedian( e.chart );
  //   var container = $(e.chart.div).closest('.chart-container');
  //
  //   if( container ) {
  //     $('.stat-proximity-number', container).html(median);
  //     $('.pga-average', container).html(pga);
  //   }
  // });

  return chart;
}



//
// Extract median from chart data (x-axis)
//
function chartMedian( chart ) {
  if(!chart) return;

  var data = chart.chartData
  if (data.length === 0) return;

  var values = $(data).map(function(k, v) {
    if (v.bulletClass === undefined) {
      var value = v.axes.x1.graphs.graph.values
      if (value.x !== undefined) {
        return Math.sqrt(value.x * value.x + value.y * value.y)
      }
    }
  }).sort(function(a,b) {return a - b})

  var half = Math.floor(values.length / 2)
  if (values.length % 2) {
    var median = values[half]
  } else {
    var median = (values[half - 1] + values[half]) / 2
  }

  return Math.round(median * 10) / 10
}

function hideLabels() {
  $('tspan:contains("-100\'")').hide();
}




var makeDrivingChart = function(id, url, type) {
  AmCharts.theme = AmCharts.themes.taylormade(url);
  var chart = AmCharts.makeChart(id, {
    type: type,
    addClassNames: true,
    classNamePrefix: 'amcharts',
    graphs: [
      AmCharts.theme.AmXYChart.driving
    ],
    valueAxes: [
      AmCharts.theme.AmXYChart.valueAxesYards,
      AmCharts.theme.AmXYChart.valueAxisDriving
    ]
  })

  // chart.addListener('dataUpdated', updateMissed)
};



var makeShortChart = function(id, url, type) {
  AmCharts.theme = AmCharts.themes.taylormade(url);
  var chart = AmCharts.makeChart(id, {
    type: type,
    addClassNames: true,
    classNamePrefix: 'amcharts',
    graphs: [
      AmCharts.theme.AmXYChart.graph,
      AmCharts.theme.AmXYChart.graph2,
      AmCharts.theme.AmXYChart.graph3,
      AmCharts.theme.AmXYChart.graph4
    ],
    valueAxes: [
      // AmCharts.theme.AmXYChart.valueAxes,
      // AmCharts.theme.AmXYChart.valueAxesCenteredZeroShort
      {
				// title: 'Left / Right',
				title: '',
				position: 'bottom',
				id: 'x1',
				unit: "'",
        synchronizeWith: 'y1-0',
        synchronizationMultiplier: 1,
        inside: 1,
        labelsEnabled: 1,
        minimum: -50,
        maximum: 50
      },
      {
	      title: 'Distance',
	      position: 'left',
	      id: 'y1-0',
	      dashLength: 6,
	      unit: "'",
        minimum: -50,
        maximum: 50,
        inside: 1,
        labelsEnabled: 1,
        labelOffset: 16
      }
    ]
  });

  chart.addListener('dataUpdated', updateAverages);
  chart.addListener('drawn', hideLabels);
};

var makeApproachChart = function (id, url, type) {
  AmCharts.theme = AmCharts.themes.taylormade(url);
  var chart = AmCharts.makeChart(id, {
    type: type,
    addClassNames: true,
    classNamePrefix: 'amcharts',
    graphs: [
      AmCharts.theme.AmXYChart.graph,
      AmCharts.theme.AmXYChart.graph2,
      AmCharts.theme.AmXYChart.graph3,
      AmCharts.theme.AmXYChart.graph4
    ],
    valueAxes: [
      // AmCharts.theme.AmXYChart.valueAxes,
      // AmCharts.theme.AmXYChart.valueAxesCenteredZeroApproach
      {
				// title: 'Left / Right',
				title: '',
				position: 'bottom',
				id: 'x1',
				unit: "'",
        synchronizeWith: 'y1-0',
        synchronizationMultiplier: 1,
        inside: 1,
        labelsEnabled: 1,
        minimum: -90,
        maximum: 90
      },
      {
	      title: 'Distance',
	      position: 'left',
	      id: 'y1-0',
	      dashLength: 6,
	      unit: "'",
        minimum: -90,
        maximum: 90,
        inside: 1,
        labelsEnabled: 1,
        labelOffset: 16
      }
    ]
  });

  chart.addListener('dataUpdated', updateAverages);
  chart.addListener('drawn', hideLabels);
};

var makeClubsBarChart = function (id, url, type, param) {
  AmCharts.theme = AmCharts.themes.taylormade(url);
  var barColor   = (param=="shots-gained") ? '#06851F' : '#CCC';
  var lineColor  = (param=="shots-gained") ? '#06851F' : '#000';

  AmCharts.makeChart(id, {
    type: type,
    autoMargins: false,
    graphs: [{
      type: 'column',
      showBalloon: false,
      valueField: 'value',
      fillAlphas: 0.33,
      lineThickness: 1,
      lineColor: lineColor,
      fillColors: barColor,
      negativeLineColor: '#CC0000',
      negativeFillColors: '#CC0000'
    }],
    categoryField: 'club',
    categoryAxis: {
      autoGridCount: false,
      gridCount: 14,
      tickLength: 0
    },
    valueAxes: [{
      id: 'ValueAxis-1',
      dashLength: 6,
      inside: true,
      showFirstLabel: false
    }]
  });
};

var makeClubsDistanceChart = function (id, url, type) {
  console.log('xxxx')
  AmCharts.theme = AmCharts.themes.taylormade(url);
  AmCharts.makeChart(id, {
    type: type,
    autoMargins: false,
    balloon: {
      adjustBorderColor:  false,
      horizontalPadding:  10,
      verticalPadding:    8,
      color:              "#ffffff"
    },
  	graphs: [{
      id: 'g1',
      type: 'candlestick',
      showBalloon: true,
      balloonText: "[[balloon]]",
      valueField: 'close',
      openField: 'open',
      closeField: 'close',
      highField: 'high',
      lowField: 'low',
      fillAlphas: 0.33,
      lineColor: '#CC0000',
      lineThickness: 2
  	}],
    categoryField: 'club',
    categoryAxis: {
      autoGridCount: false,
      gridCount: 14,
      tickLength: 0
    },
  	valueAxes: [{
      id: 'ValueAxis-1',
      dashLength: 6,
      inside: true,
      showFirstLabel: false
  	}]
  });
};

var makePuttingChart = function (id, url, type, axis_type) {
  AmCharts.theme = AmCharts.themes.taylormade(url);
  if (axis_type === 'value') {
    var chart = AmCharts.makeChart(id, {
      type:           type,
      'autoMargins':  false,
      "balloon": {
        "adjustBorderColor":  false,
        "horizontalPadding":  10,
        "verticalPadding":    8,
        "color":              "#ffffff"
      },
      graphs: [
        {
          id:               'graph',
          fillAlphas:       0.33,
          type:             'column',
          valueField:       'value',
          columnWidth:      0.4,
          showBalloon:      false,
          lineColor:        '#000000',
          fillColors:       '#CCC',
          lineThickness:    1
        },

        // # putts/round pro graph
        {
          id:                           'pro_graph',
          title:                        "Pro Average:",
          fillAlphas:                   0.3,
          type:                         'line',
          showBalloon:                  true,
          valueField:                   'pro_value',
          lineColor:                    'black',
          lineThickness:                1,
          lineAlpha:                    1,
          dashLengthColumn:             5,
          alpha:                        0.2,
          fillAlphas:                   0,
          balloonText:                  "<span style='font-size:16px;'>[[title]] [[value]] putt(s)</span>",
          bullet:                       "round",
          bulletBorderAlpha:            1,
          useLineColorForBulletBorder:  true,
          bulletBorderThickness:        3,
          bulletSize:                   7,
        }
      ],
      categoryField: 'distance',
      categoryAxis: {
        gridAlpha: 0,
        tickLength: 0,
      },
      valueAxes: [{
        minimum: 0,
        axesAlpha: 0,
        showFirstLabel: false,
        gridAlpha: 1,
        dashLength: 6,
        inside: true
      }]

  })} else if(axis_type === 'percentage') {
    var chart = AmCharts.makeChart(id, {
      type: type,
      'autoMargins': false,
      graphs: [
        {
          id:             'graph',
          fillAlphas:     0.33,
          type:           'column',
          valueField:     'percentage',
          columnWidth:    0.4,
          showBalloon:    false,
          lineColor:      '#000000',
          fillColors:     '#CCC',
          lineThickness:  1
        },

         // % holed pro graph
        {
          id:                           'pro_graph',
          title:                        "Pro Average:",
          fillAlphas:                   0.3,
          type:                         'line',
          showBalloon:                  true,
          valueField:                   'pro_percentage',
          lineColor:                    'black',
          lineThickness:                1,
          lineAlpha:                    1,
          dashLengthColumn:             5,
          alpha:                        0.2,
          fillAlphas:                   0,
          balloonText:                  "<span style='font-size:16px;'>[[title]] [[value]]%</span>",
          bullet:                       "round",
          bulletBorderAlpha:            1,
          useLineColorForBulletBorder:  true,
          bulletBorderThickness:        3,
          bulletSize:                   7,
        }
      ],
      categoryField: 'distance',
      categoryAxis: {
        gridAlpha: 0,
        tickLength: 0,
      },
      valueAxes: [
        {
          minimum: 0,
          maximum: 100,
          axesAlpha: 0,
          showFirstLabel: false,
          gridAlpha: 1,
          dashLength: 6,
          inside: true,
          unit: '%',
        }
      ]
    })
  } else {
    var chart = AmCharts.makeChart(id, {
      type: type,
      'autoMargins': false,
      graphs: [
        {
          id:                 'graph',
          fillAlphas:         0.33,
          type:               'column',
          valueField:         'value',
          columnWidth:        0.4,
          showBalloon:        false,
          lineColor:          '#000000',
          fillColors:         'green',
          negativeFillColors: 'red',
          negativeLineColor:  'red',
          lineThickness:      1
        },
        // pro graph
        {
          id:                           'pro_graph',
          title:                        "Pro",
          fillAlphas:                   0.3,
          type:                         'line',
          showBalloon:                  true,
          valueField:                   'pro_value',
          lineColor:                    'black',
          lineThickness:                1,
          lineAlpha:                    1,
          fillAlphas:                   0,
          dashLengthColumn:             5,
          alpha:                        0.2,
          balloonText:                  "<span style='font-size:12px;'>[[title]] in [[category]]:<br><span style='font-size:20px;'>[[value]]</span> [[additional]]</span>",
          bullet:                       "round",
          bulletBorderAlpha:            1,
          useLineColorForBulletBorder:  true,
          bulletBorderThickness:        3,
          bulletSize:                   7,
        }
      ],
      categoryField: 'distance',
      categoryAxis: {
        gridAlpha: 0,
        tickLength: 0,
      },
      valueAxes: [{
        minimum: -3,
        axesAlpha: 0,
        showFirstLabel: false,
        gridAlpha: 1,
        dashLength: 6,
        inside: true
      }]

  })
  };
  chart.addListener('drawn', updatePercentages);
};


//
// Bind to graph tab 'click' to render graph
//
$(document).on('click', '#graph-tabs a', function(event){
  writeGraphs(event.target);
});

//
// Document ready event (w/Turbolinks support)
//
$(document).ready(function(){
  // console.log("doc ready event fired from: stats.js")

  var chart_div = $('#chart-holder')
  if (chart_div.length > 0) {
    makeFit(chart_div)
  }

  // render the first active graph
  var tab = $('#graph-tabs li.active a')[0];
  if(tab) writeGraphs(tab);
});
