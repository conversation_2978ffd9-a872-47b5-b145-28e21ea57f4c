import { <PERSON>, Get, Res } from '@nestjs/common';
import * as path from 'path';

@Controller('')
export class StaticController {
  @Get('/faq')
  getFAQ(@Res() res) {
    res.sendFile(path.join(__dirname, './public/faq.html'));
  }
  @Get('/terms')
  getTerm(@Res() res) {
    res.sendFile(path.join(__dirname, './public/term.html'));
  }
  @Get('/privacy')
  getPrivacy(@Res() res) {
    res.sendFile(path.join(__dirname, './public/privacy.html'));
  }
}
