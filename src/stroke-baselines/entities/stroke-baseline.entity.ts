import { ApiProperty } from '@nestjs/swagger';
import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

@Entity({ name: 'stroke_baselines' })
export class StrokeBaseline {
  @PrimaryGeneratedColumn()
  @ApiProperty({ example: 1 })
  id: number;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  lie: string;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  distance: number;

  @Column({ type: 'float' })
  @ApiProperty({ example: 1.0 })
  pga_value: number;

  @Column({ type: 'float' })
  @ApiProperty({ example: 1.0 })
  scratch_value: number;

  @CreateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  created_at: Date;

  @UpdateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  updated_at: Date;
}
