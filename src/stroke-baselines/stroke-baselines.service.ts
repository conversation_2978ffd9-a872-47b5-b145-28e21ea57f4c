import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { StrokeBaseline } from './entities/stroke-baseline.entity';

@Injectable()
export class StrokeBaselinesService {
  constructor(
    @InjectRepository(StrokeBaseline)
    private strokeBaselineRepository: Repository<StrokeBaseline>
  ) {}

  findAll() {
    return `This action returns all strokeBaselines`;
  }

  findOne(id: number) {
    return `This action returns a #${id} strokeBaseline`;
  }

  remove(id: number) {
    return `This action removes a #${id} strokeBaseline`;
  }
}
