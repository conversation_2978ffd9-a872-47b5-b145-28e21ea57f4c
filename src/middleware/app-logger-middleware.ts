import { Injectable, Logger, NestMiddleware } from '@nestjs/common';
import { NextFunction, Request, Response } from 'express';

@Injectable()
export class AppLoggerMiddleware implements NestMiddleware {
  private logger = new Logger('HTTP');

  use(request: Request, response: Response, next: NextFunction): void {
    const { ip, method, path: url } = request;
    const userAgent = request.get('user-agent') || '';
    const baseUrl = request.baseUrl;
    this.logger.log(`REQUEST: ${baseUrl}`);
    const exceptLog = ['sign_in', 'users.json'];
    const isInExceptLog = exceptLog.some((e) => baseUrl.includes(e));
    if (!isInExceptLog) {
      this.logger.log(`USER-AGENT: ${userAgent} `);
      this.logger.log(`AUTHORIZATION: ${JSON.stringify(request?.headers?.authorization)} `);
      this.logger.log(`CLIENT: ${JSON.stringify(request?.headers?.client)} `);
      this.logger.log(`PAYLOAD: ${JSON.stringify(request.body)} `);
      this.logger.log(`QUERY: ${JSON.stringify(request.query)} `);
    }
    response.on('close', () => {
      const { statusCode } = response;
      this.logger.log(`${method} ${url} ${statusCode} - ${userAgent} ${ip}`);
    });

    next();
  }
}
