import { ValidationPipe, VersioningType } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { json, urlencoded } from 'body-parser';
import { useContainer } from 'class-validator';
import * as express from 'express';
import * as path from 'path';
import { AppModule } from './app.module';
import validationOptions from './utils/validation-options';
import initBullBoard from './workers/bullboard';

async function bootstrap() {
  const app = await NestFactory.create(AppModule, { cors: true });
  useContainer(app.select(AppModule), { fallbackOnErrors: true });
  const configService = app.get(ConfigService);
  app.use('/', express.static(path.join(__dirname, './static/public/')));

  app.use(json({ limit: '20mb' }));
  app.use(urlencoded({ limit: '20mb', extended: true }));

  app.enableShutdownHooks();
  app.setGlobalPrefix(configService.get('app.apiPrefix'), {
    exclude: [
      '/',
      '/users/login_by_access_token.json',
      '/users/login_by_access_token',
      '/users/by_access_token.json',
      '/users/by_access_token',
      '/users/by_email.json',
      '/users/by_email',
      '/users/update_si_email',
      '/users/by_si_email',
      '/users/sign_in.json',
      '/users/sign_in',
      '/users/revoke_token.json',
      '/users/revoke_token',
      '/users',
      '/users/update/(.*)',
      '/faq',
      '/terms',
      '/privacy',
      '/tag-user.json',
      '/users.json',
      '/user/update_password',
      '/user/update_password.json',
    ],
  });
  app.enableVersioning({
    type: VersioningType.URI,
  });
  app.useGlobalPipes(new ValidationPipe(validationOptions));

  const options = new DocumentBuilder()
    .setTitle('MyTM OC API')
    .setDescription('MyTM OC API docs')
    .setVersion('1.0')
    .addBearerAuth()
    .build();

  const document = SwaggerModule.createDocument(app, options);
  SwaggerModule.setup('docs', app, document);

  initBullBoard(app);

  await app.listen(process.env.PORT || configService.get('app.port'));
}
void bootstrap();
