import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { State } from './entities/state.entity';

@Injectable()
export class StatesService {
  constructor(
    @InjectRepository(State)
    private readonly stateRepository: Repository<State>
  ) {}

  async findAll(countryId: number) {
    const data = await this.stateRepository.find({
      where: { country_id: countryId },
      select: ['id', 'name'],
      order: { name: 'ASC' },
    });
    return { data };
  }

  findOne(id: number) {
    return this.stateRepository.findOneBy({ id });
  }
}
