import { Controller, Get, Param } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { StatesService } from './states.service';

@ApiTags('State')
@Controller('state')
export class StatesController {
  constructor(private readonly statesService: StatesService) {}

  @Get('countries/:country_id/states')
  findAll(@Param('country_id') countryId: number) {
    return this.statesService.findAll(countryId);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.statesService.findOne(+id);
  }
}
