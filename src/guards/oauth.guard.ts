import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';

@Injectable()
export class OAuthGuard implements CanActivate {
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  constructor() {}
  canActivate(context: ExecutionContext) {
    const req = context.switchToHttp().getRequest();
    const OAuthVersion = req.headers['OAuth2-Version'.toLowerCase()];
    const OAuthAccessToken = req.headers['OAuth2-Access-Token'.toLowerCase()];
    if ((OAuthVersion && parseInt(OAuthVersion, 10) === 2) || OAuthAccessToken) {
      req.isOAuthRequest = true;
    }
    return true;
  }
}
