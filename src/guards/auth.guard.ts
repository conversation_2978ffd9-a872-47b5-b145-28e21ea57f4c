import {
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  Inject,
  Injectable,
  UnauthorizedException,
  forwardRef,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AuthService } from 'src/auth/auth.service';
import { ClientsService } from 'src/clients/clients.service';

@Injectable()
export class AuthGuard implements CanActivate {
  constructor(
    private readonly config: ConfigService,
    @Inject(forwardRef(() => AuthService)) private authService: AuthService,
    @Inject(forwardRef(() => ClientsService)) private clientsService: ClientsService
  ) {}
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const req = context.switchToHttp().getRequest();
    const OAuthAccessToken = req.headers['OAuth2-Access-Token'.toLowerCase()];
    if (!req.headers.authorization && !OAuthAccessToken) {
      this.throwUnauthorizedError();
    }
    if (OAuthAccessToken) {
      if (OAuthAccessToken.length !== 64) {
        const user = await this.authService.getUserByToken(OAuthAccessToken);
        if (!user) {
          this.throwUnauthorizedError();
        }
        req.user = user;
        req.token = OAuthAccessToken;
        return true;
      }
      const accessToken = await this.authService.getOAuthAccessTokenByToken(OAuthAccessToken);
      if (!accessToken) {
        this.throwUnauthorizedError();
      }
      req.user = await this.authService.getUserById(accessToken.resource_owner_id);
      return true;
    }
    const token = req.headers.authorization.replace('Bearer ', '')?.trim();
    if (token.length === 52) {
      const client = await this.clientsService.findOne({ api_token: token });
      if (client) {
        req.client = client;
        return true;
      }
    }
    if (token.length === 64) {
      const accessToken = await this.authService.getOAuthAccessTokenByToken(OAuthAccessToken);
      if (!accessToken) {
        this.throwUnauthorizedError();
      }
      req.user = await this.authService.getUserById(accessToken.resource_owner_id);
      return true;
    }
    const user = await this.authService.getUserByToken(token);
    if (!user) {
      this.throwUnauthorizedError();
    }
    req.user = user;
    req.token = token;
    return true;
  }

  throwUnauthorizedError() {
    throw new UnauthorizedException({
      code: 'UNAUTHORIZED',
      errorMessage: 'Unauthorized',
    });
  }

  throwPermissionDeniedError() {
    throw new ForbiddenException({
      code: 'PERMISSION_DENIED',
      errorMessage: 'Permission denied!',
    });
  }

  throwUserNotFoundError() {
    throw new ForbiddenException({
      code: 'USER_NOT_FOUND',
      errorMessage: 'Unable to authenticate!',
    });
  }

  throwUserActiveError() {
    throw new UnauthorizedException({
      code: 'UNAUTHORIZED',
      errorMessage: 'Your account needs to be reactivated!',
    });
  }
}
