import { Inject, Injectable, Logger, forwardRef } from '@nestjs/common';
import _ from 'lodash';
import { HolesPlayedService } from 'src/holes-played/holes-played.service';
import { RoundService } from 'src/rounds/rounds.service';
import { StrokesPlayedService } from 'src/strokes-played/strokes-played.service';
import { StrokesStatsService } from 'src/strokes-stats/strokes-stats.service';
import { throwBadRequestError } from 'src/utils/exception';
import { StatsHelper } from 'src/utils/helper/stats_helper';
import { SmartGolfStats, Stats } from 'src/utils/smart-golf/stats';
import { SmartGolfStatsTypesShortTee } from 'src/utils/smart-golf/stats/types/short_tee';
import { includeStr } from 'src/utils/utils';
import { StatsOverallService } from './stats-overall.service';
import { DISTANCE_BUCKETS_TYPES } from './stats.const';

const STROKE_GAINED_KEYS = [
  'strokes_gained_by_round',
  'strokes_gained_by_shot',
  'greens_hit',
  'missed_left',
  'missed_right',
  'missed_short',
  'missed_long',
];
const BUCKET_METERS = [
  DISTANCE_BUCKETS_TYPES.METERS_BUCKET_ONE,
  DISTANCE_BUCKETS_TYPES.METERS_BUCKET_TWO,
  DISTANCE_BUCKETS_TYPES.METERS_BUCKET_THREE,
  DISTANCE_BUCKETS_TYPES.METERS_BUCKET_FOUR,
];
const BUCKETS_YARDS = [
  DISTANCE_BUCKETS_TYPES.BUCKET_ONE,
  DISTANCE_BUCKETS_TYPES.BUCKET_TWO,
  DISTANCE_BUCKETS_TYPES.BUCKET_THREE,
  DISTANCE_BUCKETS_TYPES.BUCKET_FOUR,
];
@Injectable()
export class StatsShortService {
  private readonly logger = new Logger(StatsShortService.name);
  private readonly statsHelper = new StatsHelper();
  constructor(
    @Inject(forwardRef(() => StatsOverallService)) private readonly statsOverallService: StatsOverallService,
    @Inject(forwardRef(() => StrokesStatsService)) private readonly strokesStatsService: StrokesStatsService,
    @Inject(forwardRef(() => HolesPlayedService)) private readonly holePlayedService: HolesPlayedService,
    @Inject(forwardRef(() => RoundService)) private readonly roundService: RoundService,
    @Inject(forwardRef(() => StrokesPlayedService)) private readonly strokesPlayedService: StrokesPlayedService
  ) {}
  async short(userId, query, currentUser) {
    currentUser = await this.statsOverallService.initCurrentUser(currentUser, userId);
    if (!currentUser) {
      throwBadRequestError('User NotFound');
    }
    query.units = query.units ? query.units : 'yards';
    this.logger.debug(`STATS SHORT...`);
    const round_ids = await this.getRoundIds(query, currentUser);
    const stroke_stats = await this.strokesStatsService.stats_for_rounds(round_ids);
    const holes = [];
    const stats = new SmartGolfStats().calculate(
      stroke_stats,
      stroke_stats,
      holes,
      this.holePlayedService,
      this.roundService,
      this.strokesPlayedService,
      currentUser.strokes_gained_baseline
    );
    const approach = stats.short_tee();
    const units = this.statsHelper.determine_units(query.units);
    let statsRes = {
      round_ids,
      units,
    };
    if (round_ids.length == 0) {
      return { data: statsRes };
    }

    statsRes = this.shortStatsForYardsUnits(approach, statsRes, units);
    return { data: statsRes };
  }
  private shortStatsForYardsUnits(short: SmartGolfStatsTypesShortTee, statsRes: any, units = 'yards') {
    let buckets = BUCKETS_YARDS;
    if (units == 'meters') {
      buckets = BUCKET_METERS;
    }
    const sgRes = [];
    const lstBuckets = [];
    for (const bucket of buckets) {
      lstBuckets[bucket] = short.calculateDistanceBucket(bucket);
    }
    for (const sg of STROKE_GAINED_KEYS) {
      sgRes[sg] = {};
      for (const bucket of buckets) {
        const statsBucket = lstBuckets[bucket];
        const bucketKey = this.getShortBucketKey(bucket);
        this.calculateStatsValue(sg, statsBucket, sgRes, bucketKey);
      }
    }

    statsRes = {
      ...statsRes,
      ...sgRes,
    };
    return statsRes;
  }
  /**
   * getApproachBucketKey
   * @param bucket
   * @returns
   */
  private getShortBucketKey(bucket: DISTANCE_BUCKETS_TYPES) {
    let bucketKey = '';
    switch (bucket) {
      case DISTANCE_BUCKETS_TYPES.METERS_BUCKET_ONE:
      case DISTANCE_BUCKETS_TYPES.BUCKET_ONE:
        bucketKey = '25';
        break;
      case DISTANCE_BUCKETS_TYPES.METERS_BUCKET_TWO:
      case DISTANCE_BUCKETS_TYPES.BUCKET_TWO:
        bucketKey = '25-50';
        break;
      case DISTANCE_BUCKETS_TYPES.METERS_BUCKET_THREE:
      case DISTANCE_BUCKETS_TYPES.BUCKET_THREE:
        bucketKey = '50-75';
        break;
      case DISTANCE_BUCKETS_TYPES.METERS_BUCKET_FOUR:
      case DISTANCE_BUCKETS_TYPES.BUCKET_FOUR:
        bucketKey = '75-100';
        break;
    }
    return bucketKey;
  }
  /**
   * calculateStatsValue
   * @param sg
   * @param statsBucket
   * @param sgRes
   * @param bucketKey
   */
  private calculateStatsValue(sg: string, statsBucket: any, sgRes: any[], bucketKey: string) {
    let statsValue = 0;
    switch (sg) {
      case 'strokes_gained_by_round':
        statsValue = statsBucket.strokes_gained();
        break;
      case 'strokes_gained_by_shot':
        statsValue = statsBucket.calculate_strokes_gained_per_shot();
        break;
      case 'greens_hit':
        statsValue = statsBucket.greens_hit_percentage();
        break;
      case 'missed_left':
        statsValue = statsBucket.missed_left_of_green_percentage();
        break;
      case 'missed_right':
        statsValue = statsBucket.missed_right_of_green_percentage();
        break;
      case 'missed_short':
        statsValue = statsBucket.missed_short_of_green_percentage();
        break;
      case 'missed_long':
        statsValue = statsBucket.missed_long_of_green_percentage();
        break;
    }
    sgRes[sg][bucketKey] = statsValue;
  }
  private async getRoundIds(query: any, currentUser: any) {
    const rounds = await this.statsOverallService.getRounds(query, currentUser);
    const round_ids = rounds.map((round) => round.id);
    return round_ids;
  }

  /**
   * proximity_to_hole
   * @param userId
   * @param query
   * @param currentUser
   * @returns
   */
  async proximity_to_hole(userId, query, currentUser) {
    currentUser = await this.statsOverallService.initCurrentUser(currentUser, userId);
    if (!currentUser) {
      throwBadRequestError('User NotFound');
    }
    const distance = query.distance;
    if (!distance) {
      throwBadRequestError('distance is required');
    }
    query.units = query.units ? query.units : 'yards';
    const units = this.statsHelper.determine_units(query.units);

    const [start_distance, end_distance] = this.get_requested_distance(query.distance, query.units);
    const starting_distance = distance.split('-')[0];
    const distance_in_yards = 30;

    const round_ids = await this.getRoundIds(query, currentUser);
    const stroke_stats = await this.strokesStatsService.stats_for_rounds(round_ids);

    const stats = new SmartGolfStats().calculate(
      stroke_stats,
      stroke_stats,
      [],
      this.holePlayedService,
      this.roundService,
      this.strokesPlayedService,
      currentUser.strokes_gained_baseline
    );

    const short = stats.short_tee();
    const bucket = this.determine_bucket(distance);
    const statsBucket = short.calculateDistanceBucket(bucket);

    const strokes_proximity = await this.strokesStatsService.stats_short_proximity_to_hole(
      round_ids,
      +start_distance,
      +end_distance,
      distance_in_yards
    );

    const average = this.statsHelper.calculate_mean_with_units(strokes_proximity, units);
    let statsRes = {
      round_ids,
      units,
      average,
      percentage_missed_left: statsBucket.missed_left_of_green_percentage(),
      percentage_missed_right: statsBucket.missed_right_of_green_percentage(),
      percentage_missed_short: statsBucket.missed_short_of_green_percentage(),
      percentage_missed_long: statsBucket.missed_long_of_green_percentage(),
      percentage_greens_hit: statsBucket.greens_hit_percentage(),
      pro_average: this.statsHelper.get_pga_median(starting_distance, units),
    };
    const strokes = this.transformStrokeProximity(strokes_proximity, units);
    statsRes = { ...statsRes, ...{ strokes } };
    return { data: statsRes };
  }

  /**
   * get_requested_distance
   * @param distance
   * @param units
   * @returns
   */
  get_requested_distance(distance, units) {
    let [start_distance, end_distance] = distance.split('-');

    if (includeStr(units, 'meter')) {
      start_distance = start_distance / Stats.METERS_TO_YARDS;
      end_distance = end_distance / Stats.METERS_TO_YARDS;
    }

    return [start_distance, end_distance];
  }
  /**
   * determine_bucket
   * @param distance_string
   * @returns
   */
  determine_bucket(distance_string) {
    switch (distance_string) {
      case '0-25':
        return DISTANCE_BUCKETS_TYPES.BUCKET_ONE;
      case '25-50':
        return DISTANCE_BUCKETS_TYPES.BUCKET_TWO;
      case '50-75':
        return DISTANCE_BUCKETS_TYPES.BUCKET_THREE;
      default:
        return DISTANCE_BUCKETS_TYPES.BUCKET_FOUR;
    }
  }
  /**
   * transformStrokeProximity
   * @param strokes_proximity
   * @param units
   * @returns
   */
  private transformStrokeProximity(strokes_proximity: any, units: string) {
    let strokes = [];
    for (const stroke of strokes_proximity) {
      const data = {
        x: this.statsHelper.calculate_x_axis(stroke.green_distances, units),
        y: this.statsHelper.calculate_y_axis(stroke.green_distances, units),
        color: '#979797',
        bullet_border_color: '#979797',
        stroke_id: stroke.stroke_played_id,
        start_lie: stroke.starting_lie,
        end_lie: stroke.ending_lie,
        distance_to_pin: this.statsHelper.calculate_with_units(units, 'meters', stroke.starting_distance_to_pin),
        end_to_pin: this.statsHelper.calculate_with_units(units, 'meters', stroke.ending_distance_to_pin),
        hole_name: stroke.hole_name,
        ordinal: stroke.stroke_ordinal,
        location: stroke.bearing_to_dogleg,
        left: stroke.left,
        right: stroke.right,
        long: stroke.long,
        short: stroke.short,
      };
      strokes.push(data);
    }
    strokes = _.sortBy(strokes, 'stroke_id').reverse();
    return strokes;
  }
}
