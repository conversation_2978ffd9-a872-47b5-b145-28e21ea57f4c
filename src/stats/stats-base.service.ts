import { Logger } from '@nestjs/common';
import _ from 'lodash';
import { isEmpty } from 'lodash';
import { In } from 'typeorm';
import { StrokesPlayedService } from 'src/strokes-played/strokes-played.service';

export class StatsBase {
  strokes: any;
  strokePlayedService: StrokesPlayedService;
  private readonly logger = new Logger(StatsBase.name);
  constructor(strokes?: any, strokePlayedService?: StrokesPlayedService) {
    if (strokes) {
      this.strokes = strokes;
    }
    if (strokePlayedService) {
      this.strokePlayedService = strokePlayedService;
    }
  }
  total_rounds() {
    //   @total_rounds ||= rounds.size.to_i
  }

  total_holes() {
    //   @total_holes ||= holes.size.to_i
  }

  number_of_greens_hit(front, back) {
    console.log({ front, back });

    //   shots = select_distance_between(front, back)
    //   next_stroke_within_lie_percentage(shots, :green)
  }

  finish_less_than(strokes, yards = 30) {
    console.log({ strokes, yards });
    //   strokes.select do |stroke|
    //     next if stroke.next_stroke.blank? || stroke.next_stroke.missing_coordinates?
    //     stroke.next_stroke.coords.distance(stroke.hole_played.flag_location) <= ( yards * StrokePlayed::YARDS_TO_METERS)
    //   end
  }

  // Percentage of Greens in Regulation
  //
  // ==== Parameters
  //
  //   * strokes_array:  Array of Strokes
  //   * lie:            String
  //   * yards:          Fixnum/Float
  //
  // ==== Returns
  //
  //   Floating Point Number
  //
  //
  // Greens in Regulation should be a % and is the number of times a golfer hits the green in Par -2 or better / number of holes played.
  //
  percentage_of_greens_hit(shots, allStrokesInListHoles) {
    return this.next_stroke_within_lie_percentage(shots, 'green', allStrokesInListHoles);
  }

  percentage_of_fairways_in_regulation(shots, allStrokesInListHoles) {
    console.log(shots);
    return this.next_stroke_within_lie_percentage(shots, 'fairway', allStrokesInListHoles);
    // next_stroke_within_lie_percentage(shots, :fairway)
  }

  // #
  // # Selects strokes between a start (front) distance and an end (back)
  // # distance.
  // #
  select_distance_between(front, back) {
    console.log(front, back);
    //   strokes.select do |stroke|
    //     stroke.distance_to_pin_in_yards.round(0) >= front.to_i &&
    //     stroke.distance_to_pin_in_yards.round(0) < back.to_i
    //   end
  }
  select_distances_for(shots, front, back) {
    console.log(shots, front, back);
    //   shots.select do |stroke|
    //     stroke.distance_to_pin_in_yards.round(0) > front.to_i &&
    //     stroke.distance_to_pin_in_yards.round(0) <= back.to_i
    //   end
  }

  calculate_strokes_gained_for_distances(shots, front, back) {
    console.log(shots, front, back);
    //   calculate_strokes_gained(
    //     select_distances_for(shots, front, back)
    //   )
  }
  // #
  // # Calculates strokes gained for a specific set of strokes, determined by
  // # a start (front) and end (back) distance.
  // #
  strokes_gained_for(front, back) {
    console.log(front, back);
    //   calculate_strokes_gained( select_distance_between(front, back) )
  }

  // Averages the strokes gained calculation for a given set of strokes (shots)
  //
  // ==== Parameters
  //
  //   * shots: An array of strokes
  //
  // ==== Returns
  //
  // A percentage.
  //
  calculate_strokes_gained(shots) {
    console.log(shots);
    //   return 0 if shots.empty? || total_holes.to_f.zero?
    //   sg = shots.inject(0) { |sum, p| sum += p.get_strokes_gained }
    //   return 0 if sg.zero?
    //   # sg_avg  = sg / total_holes.to_f
    //   # avg     = total_holes.to_f / total_rounds.to_f
    //   # sg_avg * avg
    //   sg / total_holes.to_f * 18
  }

  // Averages the strokes per round
  //
  // ==== Parameters
  //
  //   * total_shots: integer
  //
  // ==== Returns
  //
  // Float of average strokes per round
  //
  calculate_strokes_per_round(total_shots) {
    console.log(total_shots);
    //   return 0 if total_holes.zero?
    //   ( ( total_shots.to_f / total_holes.to_f ) * 18 )
  }

  //
  // SG / Holes Played * 18
  //
  calculate_strokes_gained_for_range(front, back) {
    console.log(front, back);
    //   return 0 if holes.blank?
    //   sg = strokes_gained_for_range(front, back)
    //   ( (sg / holes.size) * 18 ).round(2)
  }

  // Next Stroke inside a specific Lie Condition
  //
  // ==== Parameters
  //
  //   * shots:  Array of Strokes
  //   * lie:    Symbol
  //
  // ==== Returns
  //
  //   Percentage
  //
  // ==== Example
  //
  //   * next_stroke_within_lie_percentage(shots, :fairway?)
  //   * next_stroke_within_lie_percentage(shots, :green?)
  //
  next_stroke_within_lie_percentage(shots, lie, allStrokesInListHoles) {
    //   return 0 if shots.blank?
    //   num = shots.select do |stroke|
    //     stroke.next_stroke.try(:lie) =~ /#{lie.to_s.sub("?", "")}/i
    //   end
    //   return 0 if num.empty?
    //   (( num.size.to_f / shots.size.to_f ) * 100).round(2)
    if (isEmpty(shots)) {
      return 0;
    }
    const cacheStrokesHole = [];
    const num = shots.filter((stroke) => {
      let allStrokes = [];
      if (cacheStrokesHole[stroke.hole_played_id]) {
        allStrokes = cacheStrokesHole[stroke.hole_played_id];
      } else {
        allStrokes = this.mapStrokesByHolePlayedId(allStrokesInListHoles, stroke.hole_played_id);
        cacheStrokesHole[stroke.hole_played_id] = allStrokes;
      }

      const nextStroke = this.strokePlayedService.generateNextStroke(allStrokes, stroke);
      return this.strokePlayedService.lie(nextStroke) == lie.toString().replace(/\?/g, '');
    });
    this.logger.debug(`NUM: ${num.length}`);
    this.logger.debug(`SHOTS: ${shots.length}`);
    if (isEmpty(num)) {
      return 0;
    }

    return ((num.length / shots.length) * 100).toFixed(2);
  }

  private mapStrokesByHolePlayedId(allStrokesOfShots: any, holePlayedId: any) {
    if (!allStrokesOfShots || isEmpty(allStrokesOfShots)) {
      return [];
    }
    let allStrokes = allStrokesOfShots.filter((s) => s.hole_played_id == holePlayedId);
    allStrokes = _.sortBy(allStrokes, 'ordinal');
    return allStrokes;
  }

  private async getStrokesInHoles(holePlayedIds: any) {
    return await this.strokePlayedService.find({
      where: { hole_played_id: In(holePlayedIds) },
      relationLoadStrategy: false,
      select: ['id', 'hole_played_id', 'lie', 'ordinal'],
    });
  }

  shots_landing_on_fairway(shots) {
    console.log(shots);
    //   return 0 if shots.blank?
    //   shots.select do |stroke|
    //     stroke.next_stroke.try(:lie) =~ /fairway/i ||
    //     stroke.next_stroke.try(:lie) =~ /green/i
    //   end
  }

  to_percentage(selected_shots, total_shots_taken) {
    console.log(selected_shots, total_shots_taken);
    // selected_shots.size.to_f / total_shots_taken.size.to_f * 100
  }

  remove_penalty_shots(shots) {
    console.log(shots);

    //   return if shots.blank?
    //   shots.select do |stroke|
    //     stroke.next_stroke.try(:penalty?).nil? ? false : !stroke.next_stroke.try(:penalty?)
    //   end
  }

  number_of_penalty_shots(shots) {
    console.log(shots);
    //   return if shots.blank?
    //   shots.select do |stroke|
    //     stroke.next_stroke.try(:penalty?).nil? ? false : !stroke.next_stroke.try(:penalty?)
    //   end
  }
}
