import { Module } from '@nestjs/common';
import { SharedModule } from 'src/shared/shared.module';
import { StatsApproachService } from './stats-approarch.service';
import { StatsClubService } from './stats-club.service';
import { StatsDrivingService } from './stats-driving.service';
import { StatsOverallService } from './stats-overall.service';
import { StatsPuttingService } from './stats-putting.service';
import { StatsShortService } from './stats-short.service';

@Module({
  imports: [SharedModule],
  controllers: [],
  providers: [
    StatsOverallService,
    StatsClubService,
    StatsDrivingService,
    StatsPuttingService,
    StatsShortService,
    StatsApproachService,
  ],
})
export class StatsModule {}
