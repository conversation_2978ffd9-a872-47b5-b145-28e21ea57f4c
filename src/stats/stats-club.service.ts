import { Inject, Injectable, Logger, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import _, { isEmpty, isNumber } from 'lodash';
import { In, IsNull, Not, Repository } from 'typeorm';
import { AverageScoresService } from 'src/average-scores/average-scores.service';
import { Club } from 'src/clubs/entities/club.entity';
import { HolePlayed } from 'src/holes-played/entities/hole-played.entity';
import { HolesPlayedService } from 'src/holes-played/holes-played.service';
import { IGolfService } from 'src/igolf/igolf.service';
import { Round } from 'src/rounds/entities/round.entity';
import { RoundService } from 'src/rounds/rounds.service';
import { StrokePlayed } from 'src/strokes-played/entities/stroke-played.entity';
import { StrokesPlayedService } from 'src/strokes-played/strokes-played.service';
import { StrokeStat } from 'src/strokes-stats/entities/stroke-stat.entity';
import { User } from 'src/users/entities/user.entity';
import { ClubUtil } from 'src/utils/club';
import { Stats } from 'src/utils/smart-golf/stats';
import { StatsBase } from './stats-base.service';
import {
  DRIVER_REGEX,
  HYBRIDS_REGEX,
  IRONS_REGEX,
  PUTTER_REGEX,
  WEDGES_LIST,
  WEDGES_REGEX,
  WOODS_REGEX,
} from './stats.const';

@Injectable()
export class StatsClubService {
  private readonly logger = new Logger(StatsClubService.name);
  constructor(
    @InjectRepository(StrokePlayed)
    private strokePlayedRepo: Repository<StrokePlayed>,
    @InjectRepository(HolePlayed)
    private holePlayedRepo: Repository<HolePlayed>,
    @InjectRepository(Round)
    private roundRepo: Repository<Round>,
    @InjectRepository(User)
    private userRepo: Repository<User>,
    @InjectRepository(Club)
    private clubRepo: Repository<Club>,
    @InjectRepository(StrokeStat)
    private strokeStatRepo: Repository<StrokeStat>,
    @Inject(forwardRef(() => IGolfService)) private readonly igolfService: IGolfService,
    @Inject(forwardRef(() => AverageScoresService)) private readonly avgScoreService: AverageScoresService,
    @Inject(forwardRef(() => HolesPlayedService)) private readonly holePlayedService: HolesPlayedService,
    @Inject(forwardRef(() => RoundService)) private readonly roundService: RoundService,
    @Inject(forwardRef(() => StrokesPlayedService)) private readonly strokesPlayedService: StrokesPlayedService
  ) {}
  async getClubs(userId, club = null) {
    let clubs;
    if (club) {
      clubs = this.user_default_clubs([club]);
    } else {
      clubs = await this.getUserClubs(userId, clubs);
    }
    return clubs;
  }

  private async getUserClubs(userId: any, clubs: any) {
    const queryClub = this.clubRepo.createQueryBuilder();
    queryClub.where({
      user_id: userId,
      club_type: Not(IsNull()),
      disabled: false,
    });
    queryClub.select(['id', 'in_bag', 'disabled', 'club_type', 'cdm_witb_id']);
    const clubsRaw = await queryClub.getRawMany();
    clubs = this.user_default_clubs(clubsRaw);
    return clubs;
  }

  async strokes(userId, club, roundIds) {
    this.logger.log(`STROKES with STATS`);
    const holeIds = await this.holes(club, roundIds);
    const strokeQuery = this.strokePlayedRepo.createQueryBuilder('SP');
    const condition = { hole_played_id: In(holeIds) };
    if (club) {
      condition['club_id'] = club.id;
    }
    strokeQuery.where(condition);
    strokeQuery.innerJoinAndSelect(
      (subQuery) => {
        return subQuery
          .from(StrokeStat, 'SS')
          .where(condition)
          .select([
            'stroke_played_id',
            'strokes_gained',
            'strokes_gained_pro',
            'strokes_gained_five',
            'strokes_gained_ten',
            'strokes_gained_fifteen',
            'strokes_gained_twenty',
          ]);
      },
      'SS',
      '"SP".id = "SS".stroke_played_id'
    );
    strokeQuery.leftJoinAndSelect(
      (subQuery) => {
        return subQuery
          .from(Club, 'clubs')
          .select(['id', 'club_type'])
          .where('disabled IS NULL OR disabled IS FALSE')
          .andWhere(`club_type IS NOT NULL OR club_type <> ''`);
      },
      'clubs',
      `clubs.id = "SP".club_id`
    );
    // strokeQuery.innerJoin(
    //   (subQuery) => {
    //     return subQuery
    //       .from(HolePlayed, 'HP')
    //       .select(['id'])
    //       .where({ round_id: In(roundIds) });
    //   },
    //   'HP',
    //   '"SP".hole_played_id = "HP".id'
    // );
    strokeQuery.select([
      '"SP".id AS id',
      'round_id',
      'lie',
      '"SP".hole_played_id',
      'ordinal',
      'clubs.club_type AS club_type',
      'clubs.id as club_id',
      '"SS".*',
    ]);
    const strokes = await strokeQuery.printSql().getRawMany();
    return { strokes, holeIds };
  }
  async strokesNoStats(userId, club, roundIds) {
    this.logger.log(`STROKES NO STATS`);
    const holeIds = await this.holes(club, roundIds);
    const strokeQuery = this.strokePlayedRepo.createQueryBuilder('SP');
    const condition = { hole_played_id: In(holeIds) };
    if (club) {
      condition['club_id'] = club.id;
    }
    strokeQuery.where(condition);
    strokeQuery.leftJoinAndSelect(
      (subQuery) => {
        return subQuery
          .from(StrokeStat, 'SS')
          .where(condition)
          .select([
            'stroke_played_id',
            'strokes_gained',
            'strokes_gained_pro',
            'strokes_gained_five',
            'strokes_gained_ten',
            'strokes_gained_fifteen',
            'strokes_gained_twenty',
          ]);
      },
      'SS',
      '"SP".id = "SS".stroke_played_id'
    );
    strokeQuery.leftJoinAndSelect(
      (subQuery) => {
        return subQuery
          .from(Club, 'clubs')
          .select(['id', 'club_type'])
          .where('disabled IS NULL OR disabled IS FALSE')
          .andWhere(`club_type IS NOT NULL OR club_type <> ''`);
      },
      'clubs',
      `clubs.id = "SP".club_id`
    );

    strokeQuery.select([
      '"SP".id AS id',
      'round_id',
      'lie',
      '"SP".hole_played_id',
      'ordinal',
      'clubs.club_type AS club_type',
      'clubs.id as club_id',
      '"SS".*',
    ]);
    const strokes = await strokeQuery.printSql().getRawMany();
    return { strokes, holeIds };
  }

  async holes(club, rounds) {
    let roundIds = [];
    if (club) {
      roundIds = await this.getRoundIdsByClub(club, rounds);
    } else {
      // roundIds = rounds.map((r) => r.id);
      roundIds = rounds;
    }
    const holeQuery = this.holePlayedRepo.createQueryBuilder('holePlayed');
    holeQuery.select(['holePlayed.id as id']);
    holeQuery.where({ round_id: In(roundIds) });
    holeQuery.innerJoin(
      (subQuery) => {
        return subQuery
          .from(StrokePlayed, 'strokes')
          .select(['hole_played_id'])
          .groupBy('hole_played_id')
          .where({ round_id: In(roundIds) })
          .having('count(hole_played_id) > 0');
      },
      'strokes',
      'holePlayed.id = strokes.hole_played_id'
    );
    const holes = await holeQuery.getRawMany();
    if (holes) {
      return holes.map((h) => h.id);
    }
    return [];
  }

  private async getRoundIdsByClub(club: any, roundIds: any[]) {
    const strokes_for_club = await this.getStrokesForClub(club, roundIds);

    roundIds = await this.getRoundIdsInStrokes(roundIds, strokes_for_club);
    return roundIds;
  }

  private async getRoundIdsInStrokes(roundIds: any[], strokes_for_club: any[]) {
    roundIds = strokes_for_club.map((stroke) => stroke.round_id);
    roundIds = _.uniq(roundIds);
    const roundQuery = this.roundRepo.createQueryBuilder();
    roundQuery.where({ id: In(roundIds), completed: true });
    roundQuery.select(['id']);
    const roundsCompleted = await roundQuery.getRawMany();
    roundIds = roundsCompleted.map((round) => round.id);
    return roundIds;
  }

  private async getStrokesForClub(club: any, roundIds?: any) {
    const queryStrokes = this.strokePlayedRepo.createQueryBuilder();
    queryStrokes.where({ club_id: club.id });
    if (roundIds && roundIds.length > 0) {
      queryStrokes.andWhere({ round_id: In(roundIds) });
    }

    queryStrokes.select(['round_id']);
    const strokes_for_club = await queryStrokes.getRawMany();
    return strokes_for_club;
  }

  total_holes() {
    // holes.length
  }

  async distances(units = 'yards', round_ids, club, userId) {
    const clubs = await this.getClubs(userId, club);
    if (isEmpty(round_ids)) {
      return Object.values(clubs);
    }
    const unit_conversion = units.toString().toLowerCase().includes('meter') ? 1 : Stats.METERS_TO_YARDS;
    // # merge club data from round stats
    const queryClub = club ? ` AND c.id = ${club.id} ` : '';
    const sql = `
      SELECT
        COALESCE(c.club_type,'Unknown') AS club,
        min(s.distance) * ${unit_conversion} AS low,
        max(s.distance) * ${unit_conversion} AS high,
        (percentile_cont(0.25) WITHIN GROUP(ORDER BY s.distance)) * ${unit_conversion} AS open,
        (percentile_cont(0.75) WITHIN GROUP(ORDER BY s.distance)) * ${unit_conversion} AS close,
        avg(s.distance) * ${unit_conversion} AS avg
      FROM strokes_played s
      JOIN clubs c ON c.id = s.club_id
      WHERE s.round_id IN (${round_ids.join(',')})
      ${queryClub}
      AND c.disabled = false
      GROUP BY c.club_type
    `;
    const strokes = await this.strokePlayedRepo.query(sql);
    const strokesResult = [];
    if (strokes) {
      for (const stroke of strokes) {
        const clubName = this.normalize_type(stroke.club);
        let balloon = '';
        balloon += `<strong>Max</strong>: ${stroke?.high?.toFixed(0)}`;
        balloon += `<strong>75%</strong>: ${stroke?.close?.toFixed(0)}`;
        balloon += `<strong>Avg</strong>: ${stroke?.avg?.toFixed(0)}`;
        balloon += `<strong>Min</strong>: ${stroke?.low?.toFixed(0)}`;
        strokesResult[clubName] = { ...stroke, club: clubName, balloon };
      }
      const clubSort = this.sortClubs(clubs, strokesResult);
      return clubSort;
    }
    return Object.values(clubs);
  }

  async shots(round_ids, club, userId) {
    const clubs = await this.getClubs(userId, club);
    if (isEmpty(round_ids)) {
      return Object.values(clubs);
    }
    const queryClub = club ? ` AND c.id = ${club.id} ` : '';
    const sql = `
      SELECT
        COALESCE(c.club_type,'Unknown') AS club,
        count(s.id) as strokes
      FROM strokes_played s
      LEFT JOIN clubs c ON c.id = s.club_id
      WHERE s.round_id IN (${round_ids.join(',')})
      ${queryClub}
      AND c.disabled = false
      GROUP BY c.club_type
    `;
    const strokes = await this.strokePlayedRepo.query(sql);
    const strokesResult = [];

    if (strokes) {
      const holes = await this.holes(club, round_ids);
      const numberHolePlayed = _.uniq(holes).length;
      for (const stroke of strokes) {
        const clubName = this.normalize_type(stroke.club);

        strokesResult[clubName] = {
          club: clubName,
          value: club ? stroke.strokes : parseFloat(((stroke.strokes / numberHolePlayed) * 18).toFixed(1)),
        };
      }
      const clubSort = this.sortClubs(clubs, strokesResult);
      return clubSort;
    } else {
      return Object.values(clubs);
    }
  }

  private sortClubs(clubs: any, strokesResult: any[]) {
    const baseClubs: any = Object.values(clubs);
    const strokeValues = Object.values(strokesResult);
    const unionValues = _.unionBy(strokeValues, baseClubs, 'club');

    const clubSort = this.sort_clubs(this.reject_unwanted_clubs(unionValues))?.map((c) => {
      delete c.order;
      return c;
    });
    return clubSort;
  }

  async shots_gained(round_ids, club, currentUser) {
    // # clubs = default_clubs
    // gains = {}
    const clubs = await this.getClubs(currentUser.id, club);
    if (isEmpty(round_ids)) {
      return Object.values(clubs);
    }

    let result;
    if (club) {
      result = await this.strokesNoStats(currentUser.id, club, round_ids);
    } else {
      result = await this.strokes(currentUser.id, club, round_ids);
    }
    const { strokes, holeIds } = result;
    // const { strokes, holeIds } = await this.strokes(currentUser.id, club, round_ids);
    if (!strokes) {
      return Object.values(clubs);
    }

    let holes = null;
    let numberHolePlayed = null;
    if (club) {
      holes = holeIds;
    } else {
      holes = strokes.map((stroke) => stroke.hole_played_id);
    }
    numberHolePlayed = _.uniq(holes).length;

    let sgBaseLine = this.getUserStrokesGainedBaseLine(currentUser);
    if (sgBaseLine && sgBaseLine.toLowerCase() == 'scratch') {
      sgBaseLine = '';
    }
    if (isNumber(+sgBaseLine)) {
      switch (+sgBaseLine) {
        case 5:
          sgBaseLine = 'five';
          break;
        case 10:
          sgBaseLine = 'ten';
          break;
        case 15:
          sgBaseLine = 'fifteen';
          break;
        case 20:
          sgBaseLine = 'twenty';
          break;
      }
    }
    const sgKeyValue = sgBaseLine != '' ? 'strokes_gained_' + sgBaseLine : 'strokes_gained';

    const convertClubTypes = strokes.map((stroke) => {
      stroke.club_type = this.normalize_type(stroke.club_type || 'unknown');
      return stroke;
    });

    const clubTypes = _.groupBy(convertClubTypes, 'club_type');
    const types = Object.keys(clubTypes);
    const gains = [];
    for (const clubType of types) {
      this.logger.debug(`CLUB TYPE: ${clubType}`);
      this.logger.debug(clubTypes[clubType].length);
      const totalSG = clubTypes[clubType].reduce((total, item) => total + +item[sgKeyValue], 0);
      this.logger.debug({ totalSG });
      this.logger.debug({ numberHolePlayed });
      gains[clubType] = { club: clubType, value: (totalSG / numberHolePlayed) * 18 };
    }
    const clubSort = this.sortClubs(clubs, gains);
    return clubSort;
  }

  private getUserStrokesGainedBaseLine(currentUser: any) {
    let uSGBaseLine = currentUser.strokes_gained_baseline;
    uSGBaseLine = uSGBaseLine ? uSGBaseLine : 'scratch';

    uSGBaseLine = uSGBaseLine.includes('scratch') ? '' : uSGBaseLine;
    uSGBaseLine = uSGBaseLine.includes('pga') ? 'pro' : uSGBaseLine;
    uSGBaseLine = uSGBaseLine.includes('pro') ? 'pro' : uSGBaseLine;
    return uSGBaseLine;
  }

  async green_percentage(round_ids, club, currentUser) {
    // # clubs  = default_clubs
    // greens = {}
    const clubs = await this.getClubs(currentUser.id, club);
    if (isEmpty(round_ids)) {
      return Object.values(clubs);
    }
    let result;
    if (club) {
      result = await this.strokesNoStats(currentUser.id, club, round_ids);
    } else {
      result = await this.strokes(currentUser.id, club, round_ids);
    }
    const { strokes, holeIds } = result;

    this.logger.debug(`strokes, holeIds`, strokes.length, holeIds.length);
    this.logger.debug(strokes.map((s) => s.id));
    if (!strokes) {
      return Object.values(clubs);
    }
    const uniqStrokes: any = _.uniqBy(strokes, 'id');
    const holePlayedIds = _.uniq(strokes.map((s) => s.hole_played_id));

    const convertClubTypes = uniqStrokes.map((stroke) => {
      stroke.club_type = this.normalize_type(stroke.club_type || 'unknown');
      return stroke;
    });

    const clubTypes = _.groupBy(convertClubTypes, 'club_type');
    const types = Object.keys(clubTypes);
    const gains = [];
    const allStrokesInListHoles = await this.strokesPlayedService.find({
      where: { hole_played_id: In(holePlayedIds) },
      select: ['id', 'hole_played_id', 'lie', 'ordinal'],
    });
    for (const clubType of types) {
      this.logger.debug(`------------------------------------------`);
      this.logger.debug(`CLUB TYPE: ${clubType}`);
      const percent = new StatsBase(clubTypes[clubType], this.strokesPlayedService).percentage_of_greens_hit(
        clubTypes[clubType],
        allStrokesInListHoles
      );
      this.logger.debug(`------------------------------------------`);
      gains[clubType] = { club: clubType, value: +percent };
    }

    const clubSort = this.sortClubs(clubs, gains);
    return clubSort;

    // strokes_played = strokes.reject{|s| s.club && s.club.disabled? }
    // strokes_played.group_by{|s| normalize_type( s.club.try(:club_type) || 'unknown' )}.each do |club, shots|
    //   greens[club] = {club: club, value: percentage_of_greens_hit(shots)}
    // end
    // sort_clubs( reject_unwanted_clubs(clubs.merge!( greens ).values) )
  }

  reject_unwanted_clubs(clubs) {
    const rejected_clubs = ['Putter', 'N/A', 'Unknown', ''];
    return clubs?.filter((c) => !rejected_clubs.includes(c.club));
  }

  // #
  // # Sort clubs by type:
  // #   Driver, Woods, Hybrids, Irons, Wedges, Putter, Other
  // #
  sort_clubs(data) {
    // wedges = ["PW", "AW", "SW", "LW"].freeze
    // sort_data = data.sort_by do |d|
    //   case d[:club]
    //   when /driver/i          # driver
    //     0
    //   when /(\d+)fw$/i        # woods
    //     $1.to_i * 1
    //   when /(\d+)h$/i         # hybrids
    //     $1.to_i * 5
    //   when /(\d+)i$/i         # irons
    //     $1.to_i * 10
    //   when /(.*w)$/i            # wedges
    //     wedges = ["PW", "AW", "SW", "LW"]
    //     600 + (wedges.index( $1.upcase ) || wedges.length+1)
    //   when /putter/i          # putter
    //     700
    //   else                    # others
    //     1000
    //   end
    // sort_data.map do |c|
    //   c[:club] = case c[:club].to_s.downcase
    //   when 'lob wedge'        # driver
    //     "LW"
    //   when 'approach wedge'
    //     "AW"
    //   when 'sand wedge'
    //     "SW"
    //   when 'specialist wedge'
    //     "SpW"
    //   else
    //     c[:club]
    //   end
    //   c
    // end
    // sort_data

    let sort_data = [];
    for (const c of data) {
      const clubType = c.club;
      if (DRIVER_REGEX.test(clubType)) {
        c['order'] = 0;
        continue;
      }
      if (WOODS_REGEX.test(clubType)) {
        c['order'] = +ClubUtil.getValueMatch(WOODS_REGEX, clubType, '');
        continue;
      }
      if (IRONS_REGEX.test(clubType)) {
        c['order'] = +ClubUtil.getValueMatch(IRONS_REGEX, clubType, '') * 10;
        continue;
      }
      if (HYBRIDS_REGEX.test(clubType)) {
        c['order'] = +ClubUtil.getValueMatch(HYBRIDS_REGEX, clubType, '') * 5;
        continue;
      }
      if (WEDGES_REGEX.test(clubType)) {
        const value = ClubUtil.getValueMatch(WEDGES_REGEX, clubType, '');
        let indexWedge = WEDGES_LIST.indexOf(value.toUpperCase());
        if (indexWedge == -1) {
          indexWedge = WEDGES_LIST.length + 1;
        }
        c['order'] = 600 + indexWedge;
        continue;
      }
      if (PUTTER_REGEX.test(clubType)) {
        c['order'] = 700;
        continue;
      }
      c['club'] = this.updateClubTypeDriver(clubType);
      c['order'] = 1000;
    }

    sort_data = _.sortBy(data, ['order']); //.sort((a, b) => a.order - b.order);
    return sort_data;
  }
  updateClubTypeDriver(clubType) {
    if (!clubType) {
      return '';
    }
    const type = clubType.toString().toLowerCase().trim();
    switch (type) {
      case 'lob wedge': //# driver
        return 'LW';
      case 'approach wedge':
        return 'AW';
      case 'sand wedge':
        return 'SW';
      case 'specialist wedge':
        return 'SpW';
      default:
        return clubType;
    }
  }
  default_clubs(userId) {
    this.logger.log(`DEFAULT_CLUBS`);
    // const clubs = await this.initialize(userId);
    // return this.user_default_clubs(clubs);
    return userId;
  }

  user_default_clubs(active_clubs) {
    this.logger.log(`USER_DEFAULT_CLUBS`);
    // Hash[
    //   active_clubs.map do |club|
    //     next if club.disabled? || !club.in_bag
    //     # if @club
    //     #   next if club.disabled?
    //     # else
    //     #   next unless club.in_bag? && !club.disabled?
    //     # end
    //     [ normalize_type(club.club_type), {club: normalize_type(club.club_type), value: 0} ]
    //   end.compact
    // ]
    const clubsValid = [];
    if (active_clubs) {
      for (const club of active_clubs) {
        if (!club.disabled && club.in_bag) {
          this.logger.debug(`club.club_type: ${club.club_type}`);
          const clubType = this.normalize_type(club.club_type);
          clubsValid[clubType] = { club: clubType, value: 0 };
        }
      }
    }
    return clubsValid;
    // Hash[
    //   active_clubs.map do |club|
    //     next if club.disabled? || !club.in_bag
    //     [ normalize_type(club.club_type), {club: normalize_type(club.club_type), value: 0} ]
    //   end.compact
    // ]
  }
  normalize_type(type) {
    return ClubUtil.normalize_type(type);
  }
}
