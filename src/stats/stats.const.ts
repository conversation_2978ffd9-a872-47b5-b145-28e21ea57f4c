export const MISSING_ROUND = 'MISSING_ROUND';
export const WEDGES_LIST = ['PW', 'AW', 'SW', 'LW'];
export const DRIVER_REGEX = /driver/i;
export const PUTTER_REGEX = /putter/i;
export const WOODS_REGEX = /(\d+)fw$/i;
export const HYBRIDS_REGEX = /(\d+)h$/i;
export const IRONS_REGEX = /(\d+)i$/i;
export const WEDGES_REGEX = /(.*w)$/i;
export enum DISTANCE_BUCKETS_TYPES {
  BUCKET_ONE = 'BUCKET_ONE',
  BUCKET_TWO = 'BUCKET_TWO',
  BUCKET_THREE = 'BUCKET_THREE',
  BUCKET_FOUR = 'BUCKET_FOUR',
  BUCKET_FIVE = 'BUCKET_FIVE',
  BUCKET_SIX = 'BUCKET_SIX',
  BUCKET_SEVEN = 'BUCKET_SEVEN',
  METERS_BUCKET_ONE = 'METERS_BUCKET_ONE',
  METERS_BUCKET_TWO = 'METERS_BUCKET_TWO',
  METERS_BUCKET_THREE = 'METERS_BUCKET_THREE',
  METERS_BUCKET_FOUR = 'METERS_BUCKET_FOUR',
  METERS_BUCKET_FIVE = 'METERS_BUCKET_FIVE',
  METERS_BUCKET_SIX = 'METERS_BUCKET_SIX',
  METERS_BUCKET_SEVEN = 'METERS_BUCKET_SEVEN',
}
export const BUILD_GRAPH_TYPES = {
  HOLED: 'HOLED',
  SG_PER_SHOT: 'SG_PER_SHOT',
  SG: 'SG',
  PUTTS: 'PUTTS',
};
export const BUCKETS_PRO = {
  BUCKET_ONE: {
    pro_percentage_holed: 99.2,
    pro_number_of_putts_per_round: 11.14,
  },
  BUCKET_TWO: {
    pro_percentage_holed: 81.7,
    pro_number_of_putts_per_round: 4.46,
  },
  BUCKET_THREE: {
    pro_percentage_holed: 51.2,
    pro_number_of_putts_per_round: 2.44,
  },
  BUCKET_FOUR: {
    pro_percentage_holed: 31.3,
    pro_number_of_putts_per_round: 3.4,
  },
  BUCKET_FIVE: {
    pro_percentage_holed: 17.6,
    pro_number_of_putts_per_round: 1.89,
  },
  BUCKET_SIX: {
    pro_percentage_holed: 10.0,
    pro_number_of_putts_per_round: 2.62,
  },
  BUCKET_SEVEN: {
    pro_percentage_holed: 4.0,
    pro_number_of_putts_per_round: 3.14,
  },
};
export enum AVG_SCORE_TYPES {
  DEFAULT = 0,
  PAR_THREE = 3,
  PAR_FOUR = 4,
  PAR_FIVE = 5,
}
