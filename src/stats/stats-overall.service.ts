import { BadRequestException, Inject, Injectable, Logger, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import _, { isEmpty } from 'lodash';
import { Repository } from 'typeorm';
import { AverageScoresService } from 'src/average-scores/average-scores.service';
import { Club } from 'src/clubs/entities/club.entity';
import { HolesPlayedService } from 'src/holes-played/holes-played.service';
import { IGolfService } from 'src/igolf/igolf.service';
import { Round } from 'src/rounds/entities/round.entity';
import { ROUND } from 'src/rounds/round.const';
import { RoundService } from 'src/rounds/rounds.service';
import { StrokePlayed } from 'src/strokes-played/entities/stroke-played.entity';
import { StrokesPlayedService } from 'src/strokes-played/strokes-played.service';
import { StrokeStat } from 'src/strokes-stats/entities/stroke-stat.entity';
import { StrokesStatsService } from 'src/strokes-stats/strokes-stats.service';
import { User } from 'src/users/entities/user.entity';
import { ClubUtil } from 'src/utils/club';
import { throwBadRequestError, throwNotFoundError } from 'src/utils/exception';
import { StatsHelper } from 'src/utils/helper/stats_helper';
import { RoundScoreUtil } from 'src/utils/round-score';
import { RoundsFilterUtil } from 'src/utils/rounds-filter-util';
import { SmartGolfStats } from 'src/utils/smart-golf/stats';
import { CalculateStrokeStats } from 'src/utils/smart-golf/stats/calculate_stroke_stats';
import { SmartGolfStatsTypesOverall } from 'src/utils/smart-golf/stats/types/overall';
import { SmartGolfStatsTypesProximity } from 'src/utils/smart-golf/stats/types/proximity';
import { includeStr } from 'src/utils/utils';
import { StrokesGainedQueryDto } from '../users/dto/strokes-gained-query.dto';
import { StatsClubService } from './stats-club.service';
import { AVG_SCORE_TYPES, BUCKETS_PRO, DISTANCE_BUCKETS_TYPES, MISSING_ROUND } from './stats.const';
import { AvgScoreSummary } from './stats.types';

@Injectable()
export class StatsOverallService {
  private readonly logger = new Logger(StatsOverallService.name);
  constructor(
    @InjectRepository(StrokePlayed)
    private strokePlayedRepo: Repository<StrokePlayed>,
    @InjectRepository(Round)
    private roundRepo: Repository<Round>,
    @InjectRepository(User)
    private userRepo: Repository<User>,
    @InjectRepository(Club)
    private clubRepo: Repository<Club>,
    @InjectRepository(StrokeStat)
    private strokeStatRepo: Repository<StrokeStat>,
    @Inject(forwardRef(() => IGolfService)) private readonly igolfService: IGolfService,
    @Inject(forwardRef(() => AverageScoresService)) private readonly avgScoreService: AverageScoresService,
    @Inject(forwardRef(() => HolesPlayedService)) private readonly holePlayedService: HolesPlayedService,
    @Inject(forwardRef(() => RoundService)) private readonly roundService: RoundService,
    @Inject(forwardRef(() => StrokesPlayedService)) private readonly strokesPlayedService: StrokesPlayedService,
    @Inject(forwardRef(() => StrokesStatsService)) private readonly strokeStatsService: StrokesStatsService,
    private readonly statsClubService: StatsClubService
  ) {}

  /**
   * GET ROUNDS
   * @param query
   * @param currentUser
   * @returns
   */
  async getRounds(query, currentUser) {
    const roundFilter = new RoundsFilterUtil(this.roundRepo, currentUser, this.roundService);
    return await roundFilter.search(query);
  }
  /**
   * GET TAG ROUNDS
   * @param query
   * @param currentUser
   * @returns
   */
  async getTAGRounds(query, currentUser) {
    const roundFilter = new RoundsFilterUtil(this.roundRepo, currentUser, this.roundService);
    return await roundFilter.searchTAG(query);
  }
  /**
   * GET ROUNDS ADVANCED
   * @param currentUser
   * @returns
   */
  async getRoundsAdvanced(currentUser) {
    const queryBuilder = this.roundRepo.createQueryBuilder();
    queryBuilder.where({
      user_id: currentUser.id,
      completed: true,
      // inprogress: false,
      round_mode: ROUND.ROUND_MODE_ADVANCED,
    });
    queryBuilder.select(['id', 'stats_completed', 'round_mode']);
    queryBuilder.orderBy({ id: 'DESC' });
    let rounds = await queryBuilder.getRawMany();
    rounds = await this.filter_round_stats_completed(rounds);
    return rounds;
  }
  /**
   * FILTER_ROUND_STATS_COMPLETED
   * @param rounds
   * @returns
   */
  async filter_round_stats_completed(rounds) {
    const roundFilter = [];
    for (const round of rounds) {
      const isCompleted = await this.roundService.statsCompletedProcessing(
        round,
        [undefined, null].includes(round.stats_completed)
      );
      if (isCompleted) {
        roundFilter.push(round);
      }
    }
    return roundFilter;
  }
  /**
   * OVERALLS
   *
   * @param userId
   * @param query
   * @param currentUser
   * @returns
   */
  async overalls(userId, query, currentUser) {
    currentUser = await this.initCurrentUser(currentUser, userId);
    this.logger.debug(`STATS OVERALL`);
    this.logger.debug({ userId, query });
    this.validateQuery(query);
    if (!currentUser) {
      return {};
    }
    const isFilterRoundIds = query['round_ids'] != null;

    const rounds = await this.getRounds(query, currentUser);
    const {
      round_multiplayer_ids,
      round_basic_ids,
      round_classic_ids,
      round_ids,
      round_simple_ids,
      total_rounds,
      all_round_ids,
    } = RoundScoreUtil.formatRoundIds(rounds);

    let overall_advanced: any = MISSING_ROUND;
    let stats: CalculateStrokeStats;
    if (!isEmpty(round_ids)) {
      const holes = await this.holePlayedService.getHolesOfRoundIds(round_ids);
      const holeIds = holes.map((hole) => hole.id);

      const strokes = await this.holePlayedService.getStrokesPlayedListHole(holeIds);

      stats = new SmartGolfStats().calculate(
        strokes,
        strokes,
        holes,
        this.holePlayedService,
        this.roundService,
        this.strokesPlayedService,
        currentUser.strokes_gained_baseline
      );
      overall_advanced = stats.overall();
    }

    let overall_basic: any = MISSING_ROUND;
    if (!isEmpty(round_basic_ids)) {
      const holes_played_basic = await this.holePlayedService.findHolesInRounds(round_basic_ids);
      overall_basic = new SmartGolfStatsTypesOverall(
        [],
        [],
        holes_played_basic,
        this.holePlayedService,
        this.roundService,
        this.strokesPlayedService
      );
    }

    let overall_classic: any = MISSING_ROUND;
    if (!isEmpty(round_classic_ids)) {
      const holes_played_classic = await this.holePlayedService.findHolesInRounds(round_classic_ids);
      overall_classic = new SmartGolfStatsTypesOverall(
        [],
        [],
        holes_played_classic,
        this.holePlayedService,
        this.roundService,
        this.strokesPlayedService
      );
    }

    let overall_multiplayer: any = MISSING_ROUND;
    if (!isEmpty(round_multiplayer_ids)) {
      const holes_played_multiplayer = await this.holePlayedService.findHolesInRounds(round_multiplayer_ids);
      overall_multiplayer = new SmartGolfStatsTypesOverall(
        [],
        [],
        holes_played_multiplayer,
        this.holePlayedService,
        this.roundService,
        this.strokesPlayedService
      );
    }
    // this.logger.debug({ overall_basic, overall_multiplayer, overall_advanced, overall_classic });
    // if !@round_multiplayer_ids.blank?
    //   holes_played = @round_multiplayer_ids.map {|round_id| HolePlayed.where(:round_id => round_id)}.flatten
    //   overall_advanced_multiplayer ||= ::SmartGolf::Stats::Types::Overall.new([], [], holes_played)
    // else
    //   overall_advanced_multiplayer = 'MissingRounds'
    // end
    if (
      isEmpty(round_simple_ids) &&
      overall_advanced == MISSING_ROUND &&
      overall_basic == MISSING_ROUND &&
      overall_classic == MISSING_ROUND &&
      overall_multiplayer == MISSING_ROUND
    ) {
      return {};
    }
    return this.transformStats({
      all_round_ids,
      total_rounds,
      overall_advanced,
      overall_basic,
      overall_classic,
      overall_multiplayer,
      round_simple_ids,
      round_ids,
      stats,
      isFilterRoundIds,
    });
  }

  /**
   * TAG Strokes Gained Overall
   *
   * @param email
   * @param query
   * @param currentUser
   * @returns
   */
  async tagStrokesGainedOverall(email: string, query: StrokesGainedQueryDto) {
    const strokesGainedBaseline = query.strokes_gained_baseline
      ? query?.strokes_gained_baseline.toLowerCase()
      : 'scratch';
    const baselineFormat = this.formatStrokesGainedBaseline(strokesGainedBaseline);
    const currentUser = await this.userRepo.findOne({
      where: { email },
      select: ['email', 'id', 'gender'],
    });
    if (!currentUser) {
      throwNotFoundError('User not found');
    }
    this.logger.debug(`TAG Strokes Gained OVERALL`);
    this.validateQuery(query);
    const isFilterRoundIds = query['round_ids'] != null;

    const rounds = await this.getTAGRounds(query, currentUser);
    const { round_ids, round_simple_ids, total_rounds, all_round_ids } = RoundScoreUtil.formatRoundIds(rounds);

    let overall_advanced: any = MISSING_ROUND;
    let stats: CalculateStrokeStats;
    if (!isEmpty(round_ids)) {
      const holes = await this.holePlayedService.getHolesOfRoundIds(round_ids);
      const holeIds = holes.map((hole) => hole.id);

      const strokes = await this.holePlayedService.getStrokesPlayedListHole(holeIds);

      stats = new SmartGolfStats().calculate(
        strokes,
        strokes,
        holes,
        this.holePlayedService,
        this.roundService,
        this.strokesPlayedService,
        baselineFormat
      );
      overall_advanced = stats.overall();
    }

    return this.transformTAGStats({
      all_round_ids,
      total_rounds,
      overall_advanced,
      round_simple_ids,
      round_ids,
      stats,
      isFilterRoundIds,
      strokesGainedBaseline,
    });
  }

  validateQuery(query: any) {
    const values = Object.values(query);
    if (values) {
      if (values?.join(',')?.includes('undefined')) {
        throw new BadRequestException('Invalid Query');
      }
    }
  }

  formatStrokesGainedBaseline(input: string): string {
    const mapping: Record<string, string> = {
      scratch: 'scratch',
      pro: 'pga',
      '5': 'five',
      '10': 'ten',
      '15': 'fifteen',
      '20': 'twenty',
    };
    return mapping[input] || input;
  }

  formatStrokesGainedBaselineText(input: string): string {
    const mapping: Record<string, string> = {
      scratch: 'Scratch',
      pro: 'Pro',
      '5': '5 HCP',
      '10': '10 HCP',
      '15': '15 HCP',
      '20': '20 HCP',
    };
    return mapping[input] || input;
  }
  /**
   * TRANS FORM TAG STATS
   *
   * @param options
   * @returns
   */
  async transformTAGStats(options: any) {
    const { all_round_ids, total_rounds, overall_advanced, round_simple_ids, stats, strokesGainedBaseline } = options;

    const iGolfIds =
      _.uniq(all_round_ids.map((round) => round.igolf_course_id))?.filter((c) => c != null && c != '') || [];
    const courseIds =
      _.uniq(all_round_ids.map((round) => round.course_id))?.filter((c) => c != 0 && c != undefined) || [];
    let statsRes = {
      round_ids: all_round_ids.map((r) => r.id),
      count_round_stats: total_rounds,
      course_ids: courseIds,
      igolf_course_ids: iGolfIds,
    };

    const avg_score = [];
    const avg_score_par_three = [];
    const avg_score_par_four = [];
    const avg_score_par_five = [];
    const avgSummary: AvgScoreSummary = {
      totalScore: 0,
      totalHolePlayed: 0,
      totalScoreParThree: 0,
      totalHolePlayedParThree: 0,
      totalScoreParFour: 0,
      totalHolePlayedParFour: 0,
      totalScoreParFive: 0,
      totalHolePlayedParFive: 0,
    };
    // let isParThree = false;
    if (overall_advanced != MISSING_ROUND) {
      this.avgOverallAdvanced(
        overall_advanced,
        avg_score,
        avg_score_par_three,
        avg_score_par_four,
        avg_score_par_five,
        avgSummary
      );

      const strokesGained = this.transformStrokeGainRoundAdvanced(overall_advanced, stats);
      statsRes = { ...statsRes, ...strokesGained, ...{ has_advanced_round: true } };
    } else {
      statsRes = {
        ...statsRes,
        ...{
          strokes_gained_total: null,
          strokes_gained_driving: null,
          strokes_gained_approach: null,
          strokes_gained_short: null,
          strokes_gained_putting: null,
          approach_greens_hit: null,
          short_greens_hit: null,
          has_advanced_round: false,
        },
      };
    }

    await this.avgOverallSimple(round_simple_ids, avg_score, avgSummary);

    const avg_scores = this.calcAvgScore(avgSummary, AVG_SCORE_TYPES.DEFAULT);
    // const avg_scores = this.calcAvgScore(avg_score);
    const avg_score_par_threes = this.calcAvgScore(avgSummary, AVG_SCORE_TYPES.PAR_THREE);
    const avg_score_par_fours = this.calcAvgScore(avgSummary, AVG_SCORE_TYPES.PAR_FOUR);
    const avg_score_par_fives = this.calcAvgScore(avgSummary, AVG_SCORE_TYPES.PAR_FIVE);

    statsRes = {
      ...statsRes,
      ...{
        average_score: avg_scores,
        average_score_for_par_three: avg_score_par_threes == 0 ? null : avg_score_par_threes,
        average_score_for_par_four: avg_score_par_fours == 0 ? null : avg_score_par_fours,
        average_score_for_par_five: avg_score_par_fives == 0 ? null : avg_score_par_fives,
      },
    };

    return this.transformTAGStatsToNewFormat(statsRes, strokesGainedBaseline);
  }

  /**
   * Transform TAG statsRes to new format (similar to transformStatsToNewFormat but for TAG stats)
   * @param statsRes - The original TAG stats result object
   * @param strokesGainedBaseline - The baseline for strokes gained (e.g., 'Scratch')
   * @returns Transformed data in the new format
   */
  transformTAGStatsToNewFormat(statsRes: any, strokesGainedBaseline: string) {
    return {
      data: {
        round_ids: statsRes.round_ids || [],
        count_round_stats: statsRes.count_round_stats || 0,
        course_ids: statsRes.igolf_course_ids || [],
        strokes_gained: {
          strokes_gained_baseline: this.formatStrokesGainedBaselineText(strokesGainedBaseline),
          strokes_gained_total: statsRes.strokes_gained_total || 0,
          strokes_gained_driving: statsRes.strokes_gained_driving || 0,
          strokes_gained_approach: statsRes.strokes_gained_approach || 0,
          strokes_gained_short: statsRes.strokes_gained_short || 0,
          strokes_gained_putting: statsRes.strokes_gained_putting || 0,
        },
        approach_greens_hit: statsRes.approach_greens_hit || 0,
        short_greens_hit: statsRes.short_greens_hit || 0,
        average_score: statsRes.average_score || '0.00',
        average_score_for_par_three: statsRes.average_score_for_par_three || '0.00',
        average_score_for_par_four: statsRes.average_score_for_par_four || '0.00',
        average_score_for_par_five: statsRes.average_score_for_par_five || '0.00',
      },
    };
  }

  /**
   * TRANS FORM STATS
   *
   * @param options
   * @returns
   */
  async transformStats(options: any) {
    const {
      all_round_ids,
      total_rounds,
      overall_advanced,
      overall_basic,
      overall_classic,
      overall_multiplayer,
      round_simple_ids,
      stats,
      isFilterRoundIds,
      // round_ids,
    } = options;

    const iGolfIds =
      _.uniq(all_round_ids.map((round) => round.igolf_course_id))?.filter((c) => c != null && c != '') || [];
    const courseIds =
      _.uniq(all_round_ids.map((round) => round.course_id))?.filter((c) => c != 0 && c != undefined) || [];
    let statsRes = {
      round_ids: all_round_ids.map((r) => r.id),
      count_round_stats: total_rounds,
      course_ids: courseIds,
      igolf_course_ids: iGolfIds,
    };
    let have_gr_stats_advance = false;
    let gr_stas_classic: any = 0;

    let classic_stats_fairways_hit = 0;
    let classic_stats_greens_in_regulation = 0;
    let classic_stats_putts_per_round = 0;
    let classic_stats_sand_saves = 0;
    let putting_one_putt = 0;
    let sand = 0;
    let bunkerHit = 0;

    // let total_score = 0;
    // let holes_played_total = 0;

    const avg_score = [];
    const avg_score_par_three = [];
    const avg_score_par_four = [];
    const avg_score_par_five = [];
    const avgSummary: AvgScoreSummary = {
      totalScore: 0,
      totalHolePlayed: 0,
      totalScoreParThree: 0,
      totalHolePlayedParThree: 0,
      totalScoreParFour: 0,
      totalHolePlayedParFour: 0,
      totalScoreParFive: 0,
      totalHolePlayedParFive: 0,
    };
    let isParThree = false;
    if (overall_advanced != MISSING_ROUND) {
      have_gr_stats_advance = true;
      isParThree = overall_advanced?.strokes?.every((stroke) => stroke.holes_par == 3);

      putting_one_putt = +stats.putting().one_putt_per_hole().toFixed(2);
      classic_stats_fairways_hit = +overall_advanced.fairways_in_regulation().toFixed(2);
      classic_stats_greens_in_regulation = +overall_advanced.greens_in_regulation().toFixed(2);

      classic_stats_putts_per_round = +overall_advanced.number_of_putts_per_round().toFixed(2);
      classic_stats_sand_saves = overall_advanced.sand_saved_percentage();
      if (classic_stats_sand_saves != null) {
        classic_stats_sand_saves = +classic_stats_sand_saves.toFixed(2);
      }
      this.avgOverallAdvanced(
        overall_advanced,
        avg_score,
        avg_score_par_three,
        avg_score_par_four,
        avg_score_par_five,
        avgSummary
      );

      const strokesGained = this.transformStrokeGainRoundAdvanced(overall_advanced, stats);
      statsRes = { ...statsRes, ...strokesGained, ...{ has_advanced_round: true } };
    } else {
      statsRes = {
        ...statsRes,
        ...{
          strokes_gained_total: null,
          strokes_gained_driving: null,
          strokes_gained_approach: null,
          strokes_gained_short: null,
          strokes_gained_putting: null,
          approach_greens_hit: null,
          short_greens_hit: null,
          has_advanced_round: false,
        },
      };
    }
    // ShortGame
    let one_putt_percent = null;
    let two_putts_percent = null;
    let total_putts = null;
    let more_three_putts_percent = null;
    let scramble = null;
    let total_hole_mis_gir = null;
    let total_hole_mis_gir_putt = null;

    if (overall_classic != MISSING_ROUND) {
      this.avgOverallClassic(
        overall_classic,
        avg_score,
        avg_score_par_three,
        avg_score_par_four,
        avg_score_par_five,
        avgSummary
      );
      const holes_gir_miss = overall_classic?.holes_played.filter(
        (hole) => hole.gr_stats != 'gr_hit' && +hole.score > 0
      );

      const holes_have_putt = overall_classic?.holes_played.filter(
        (hole) => +hole.putts_number >= 0 && +hole.score > 0
      );

      const total_hole_putts = holes_have_putt.length;

      const hole_have_one_putt = holes_have_putt.filter((hole) => +hole.putts_number == 1)?.length;
      const hole_have_two_putt = holes_have_putt.filter((hole) => +hole.putts_number == 2)?.length;
      const hole_have_three_putt = holes_have_putt.filter((hole) => +hole.putts_number >= 3)?.length;
      if (total_hole_putts > 0) {
        total_putts = this.getTotalPuttsRoundClassic(overall_classic, holes_have_putt, total_putts, isFilterRoundIds);

        const one_putt_percent_raw = (hole_have_one_putt / total_hole_putts) * 100;
        const two_putts_percent_raw = (hole_have_two_putt / total_hole_putts) * 100;
        const more_three_putts_percent_raw = (hole_have_three_putt / total_hole_putts) * 100;
        one_putt_percent = +one_putt_percent_raw.toFixed(0);
        two_putts_percent = +two_putts_percent_raw.toFixed(0);
        more_three_putts_percent = +more_three_putts_percent_raw.toFixed(0);
      }

      const holes_gir_miss_putt = holes_gir_miss?.filter((hole) => +hole.putts_number <= 1 && +hole.score <= +hole.par);
      total_hole_mis_gir_putt = holes_gir_miss_putt?.length || null;
      total_hole_mis_gir = holes_gir_miss?.length || null;
      if (total_hole_mis_gir > 0) {
        scramble = +((total_hole_mis_gir_putt / total_hole_mis_gir) * 100).toFixed(0);
      }

      classic_stats_fairways_hit = this.getClassicStatsFairwaysHit(classic_stats_fairways_hit, overall_classic);

      if (overall_classic.greens_in_regulation_classic() == null) {
        gr_stas_classic = null;
      }
      const shortGameFW = overall_classic.classic_round_fairway_stats_total();
      classic_stats_greens_in_regulation = +this.getClassicStatsGreensInRegulation(
        classic_stats_greens_in_regulation,
        overall_classic
      );
      const shortGameData = {
        putts: {
          total_putts,
          one_putt_percent,
          two_putts_percent,
          more_three_putts_percent,
        },
        short_game: {
          driving: {
            ...shortGameFW,
          },
          total_hole_mis_gir,
          total_hole_mis_gir_putt,
          scramble,
        },
      };
      classic_stats_putts_per_round = this.getClassicStatsPuttPerRound(classic_stats_putts_per_round, overall_classic);
      classic_stats_sand_saves = this.getClassicStatsSandSaves(classic_stats_sand_saves, overall_classic);
      const sandAndbunkerHit = overall_classic.getSandAndBunkerHit();
      sand = sandAndbunkerHit?.sand || 0;
      bunkerHit = sandAndbunkerHit?.bunkerHit || 0;

      putting_one_putt = this.getPuttingOnePutt(putting_one_putt, overall_classic);

      const fwGrMissPercent = this.transformFairwayGreenMissedPercent(overall_classic);
      statsRes = { ...statsRes, ...fwGrMissPercent, ...shortGameData };
    } else {
      statsRes = {
        ...statsRes,
        ...{
          fw_missed_left_percent: null,
          fw_missed_right_percent: null,
          gr_missed_left_percent: null,
          gr_missed_right_percent: null,
          gr_missed_long_percent: null,
          gr_missed_short_percent: null,
        },
      };
    }
    if (overall_classic != MISSING_ROUND || overall_advanced != MISSING_ROUND) {
      let driving_fairways_hit = null;
      // classic_stats_fairways_hit = null;
      ({ driving_fairways_hit, classic_stats_fairways_hit } = this.getDrivingFwHitClassicFwHit(
        have_gr_stats_advance,
        driving_fairways_hit,
        classic_stats_fairways_hit,
        overall_classic,
        isParThree
      ));

      classic_stats_greens_in_regulation =
        gr_stas_classic == null && !have_gr_stats_advance ? null : +classic_stats_greens_in_regulation.toFixed(2);

      classic_stats_putts_per_round = classic_stats_putts_per_round == 0 ? null : classic_stats_putts_per_round;

      classic_stats_sand_saves = classic_stats_sand_saves;

      putting_one_putt = putting_one_putt == 0 ? null : putting_one_putt;

      statsRes = {
        ...statsRes,
        ...{
          driving_fairways_hit,
          classic_stats_fairways_hit,
          classic_stats_greens_in_regulation,
          classic_stats_putts_per_round,
          classic_stats_sand_saves,
          putting_one_putt,
          sand,
          bunkerHit,
        },
      };
    } else {
      statsRes = {
        ...statsRes,
        ...{
          driving_fairways_hit: null,
          classic_stats_fairways_hit: null,
          classic_stats_greens_in_regulation: null,
          classic_stats_putts_per_round: null,
          classic_stats_sand_saves: null,
          putting_one_putt: null,
        },
      };
    }
    if (overall_basic != MISSING_ROUND) {
      this.avgOverallBasic(
        overall_basic,
        avg_score,
        avg_score_par_three,
        avg_score_par_four,
        avg_score_par_five,
        avgSummary
      );
    }

    if (overall_multiplayer != MISSING_ROUND) {
      this.avgOverallMultiplayer(
        overall_multiplayer,
        avg_score,
        avg_score_par_three,
        avg_score_par_four,
        avg_score_par_five,
        avgSummary
      );
    }
    await this.avgOverallSimple(round_simple_ids, avg_score, avgSummary);

    const avg_scores = this.calcAvgScore(avgSummary, AVG_SCORE_TYPES.DEFAULT);
    // const avg_scores = this.calcAvgScore(avg_score);
    const avg_score_par_threes = this.calcAvgScore(avgSummary, AVG_SCORE_TYPES.PAR_THREE);
    const avg_score_par_fours = this.calcAvgScore(avgSummary, AVG_SCORE_TYPES.PAR_FOUR);
    const avg_score_par_fives = this.calcAvgScore(avgSummary, AVG_SCORE_TYPES.PAR_FIVE);

    statsRes = {
      ...statsRes,
      ...{
        average_score: avg_scores,
        average_score_for_par_three: avg_score_par_threes == 0 ? null : avg_score_par_threes,
        average_score_for_par_four: avg_score_par_fours == 0 ? null : avg_score_par_fours,
        average_score_for_par_five: avg_score_par_fives == 0 ? null : avg_score_par_fives,
      },
    };

    return { data: statsRes };
  }

  private getTotalPuttsRoundClassic(
    overall_classic: any,
    holes_have_putt: any,
    total_putts: any,
    isFilterRoundIds: boolean
  ) {
    const groupRound = _.groupBy(overall_classic?.holes_played, 'round_id');
    const roundHolesPlayedMultiple = {};
    const roundIds = Object.keys(groupRound);
    const duplicatePutt = roundIds.length > 1 || !isFilterRoundIds ? 2 : 1;
    for (const id of roundIds) {
      roundHolesPlayedMultiple[id] = groupRound[id].length == 18 ? 1 : duplicatePutt;
    }
    const groupHoleRoundPutt = _.groupBy(holes_have_putt, 'round_id');
    const roundIdHavePutt = Object.keys(groupHoleRoundPutt);
    for (const id of roundIdHavePutt) {
      let totalPutOfRound = 0;
      for (const hole of groupHoleRoundPutt[id]) {
        totalPutOfRound += +hole.putts_number;
      }
      total_putts += totalPutOfRound * roundHolesPlayedMultiple[id];
    }
    return +(total_putts / roundIds.length).toFixed(0);
  }

  /**
   * AVG OVERALL SIMPLE
   * @param round_simple_ids
   * @param avg_score
   */
  private async avgOverallSimple(round_simple_ids: any, avg_score: any[], avgSummary: AvgScoreSummary) {
    if (!isEmpty(round_simple_ids)) {
      const simple_holes_played_total = 0;
      const simple_total_score = 0;
      const avgSimple = await this.avgScoreService.totalScoreSimpleMode(
        round_simple_ids,
        [],
        simple_holes_played_total,
        simple_total_score
      );
      if (avgSimple.holes_played_total) {
        avg_score.push((avgSimple.total_score / avgSimple.holes_played_total) * 18);
        avgSummary.totalScore += avgSimple.total_score;
        avgSummary.totalHolePlayed += avgSimple.holes_played_total;
      }
    }
  }

  /**
   * GET DRIVING FW HIT CLASSIC FW HIT
   * @param have_gr_stats_advance
   * @param driving_fairways_hit
   * @param classic_stats_fairways_hit
   * @param overall_classic
   * @returns
   */
  private getDrivingFwHitClassicFwHit(
    have_gr_stats_advance: boolean,
    driving_fairways_hit: any,
    classic_stats_fairways_hit: number,
    overall_classic: any,
    isParThree
  ) {
    const isFwHitZero = classic_stats_fairways_hit == 0;

    if (have_gr_stats_advance) {
      driving_fairways_hit = isFwHitZero ? null : +classic_stats_fairways_hit;
      classic_stats_fairways_hit = isFwHitZero ? null : +classic_stats_fairways_hit;
      if (driving_fairways_hit == null && !isParThree) {
        driving_fairways_hit = 0;
        classic_stats_fairways_hit = 0;
      }
      return { driving_fairways_hit, classic_stats_fairways_hit };
    }

    const isFWNil = overall_classic.is_fairway_nil();

    driving_fairways_hit = isFwHitZero && isFWNil ? null : +classic_stats_fairways_hit;
    classic_stats_fairways_hit = isFwHitZero && isFWNil ? null : +classic_stats_fairways_hit;

    return { driving_fairways_hit, classic_stats_fairways_hit };
  }

  /**
   * getPuttingOnePutt
   * @param putting_one_putt
   * @param overall_classic
   * @returns
   */
  private getPuttingOnePutt(putting_one_putt: number, overall_classic: any) {
    if (putting_one_putt == 0) {
      return +overall_classic.putting_one_putt_classic().toFixed(2);
    }

    const putting_one_putt_classic = overall_classic.putting_one_putt_classic();

    if (putting_one_putt_classic != 0) {
      return +((+putting_one_putt + putting_one_putt_classic) / 2).toFixed(2);
    }
    return +putting_one_putt.toFixed(2);
  }

  /**
   * getClassicStatsSandSaves
   * @param classic_stats_sand_saves
   * @param overall_classic
   * @returns
   */
  private getClassicStatsSandSaves(classic_stats_sand_saves: number, overall_classic: any) {
    if ([0, null].includes(classic_stats_sand_saves)) {
      return overall_classic.sand_saved_percentage_classic();
    }
    const classic_stats_sand_saves_classic = overall_classic.sand_saved_percentage_classic();
    if (![0, null].includes(classic_stats_sand_saves_classic)) {
      return +((+classic_stats_sand_saves + +classic_stats_sand_saves_classic) / 2).toFixed(2);
    }
    return classic_stats_sand_saves;
  }

  /**
   * getClassicStatsFairwaysHit
   * @param classic_stats_fairways_hit
   * @param overall_classic
   * @returns
   */
  getClassicStatsFairwaysHit(classic_stats_fairways_hit: number, overall_classic: any) {
    if (classic_stats_fairways_hit == 0) {
      const fw = overall_classic.fairways_in_regulation_classic();
      return fw == null ? null : overall_classic.fairways_in_regulation_classic().toFixed(2);
    }

    const classic_stats_fairways_hit_classic = overall_classic.fairways_in_regulation_classic();

    if (classic_stats_fairways_hit_classic == 0) {
      return classic_stats_fairways_hit;
    }
    return +((+classic_stats_fairways_hit + +classic_stats_fairways_hit_classic) / 2).toFixed(2);
  }

  /**
   * getClassicStatsGreensInRegulation
   * @param classic_stats_greens_in_regulation
   * @param overall_classic
   * @returns
   */
  private getClassicStatsGreensInRegulation(classic_stats_greens_in_regulation: number, overall_classic: any) {
    if (classic_stats_greens_in_regulation == 0) {
      return +overall_classic.greens_in_regulation_classic();
    }

    const greenInRegulation = +overall_classic.greens_in_regulation_classic();

    if (![0, null].includes(greenInRegulation)) {
      return ((+classic_stats_greens_in_regulation + +greenInRegulation) / 2).toFixed(2);
    }
    if (greenInRegulation == null || classic_stats_greens_in_regulation == null) {
      return 0;
    }

    return greenInRegulation;
  }

  /**
   * getClassicStatsPuttPerRound
   * @param classic_stats_putts_per_round
   * @param overall_classic
   * @returns
   */
  private getClassicStatsPuttPerRound(classic_stats_putts_per_round: number, overall_classic: any) {
    if (classic_stats_putts_per_round == 0) {
      return overall_classic.number_of_putts_per_round_classic().toFixed(2);
    }
    const puttPRound = overall_classic.number_of_putts_per_round_classic();
    if (puttPRound != 0) {
      return +((+classic_stats_putts_per_round + +puttPRound) / 2).toFixed(2);
    }
    return +classic_stats_putts_per_round.toFixed(2);
  }

  /**
   * avgOverallClassic
   *
   * @param overall_classic
   * @param avg_score
   * @param avg_score_par_three
   * @param avg_score_par_four
   * @param avg_score_par_five
   */
  private avgOverallClassic(
    overall_classic: any,
    avg_score: any[],
    avg_score_par_three: any[],
    avg_score_par_four: any[],
    avg_score_par_five: any[],
    avgSummary: AvgScoreSummary
  ) {
    const { total, hole_numbers_players } = overall_classic.total_score_basic(); //toFixed(2);
    const classic_strokes_for_par_three = +overall_classic.number_of_strokes_basic_for_par(3).toFixed(2);
    const classic_holes_for_par_three = +overall_classic.number_of_holes_basic_for_par(3).toFixed(2);
    const classic_strokes_for_par_four = +overall_classic.number_of_strokes_basic_for_par(4).toFixed(2);
    const classic_holes_for_par_four = +overall_classic.number_of_holes_basic_for_par(4).toFixed(2);
    const classic_strokes_for_par_five = +overall_classic.number_of_strokes_basic_for_par(5).toFixed(2);
    const classic_holes_for_par_five = +overall_classic.number_of_holes_basic_for_par(5).toFixed(2);

    if (hole_numbers_players) {
      avg_score.push((total / hole_numbers_players) * 18);
      avgSummary.totalScore += total;
      avgSummary.totalHolePlayed += hole_numbers_players;
    }
    if (classic_holes_for_par_three) {
      avg_score_par_three.push(classic_strokes_for_par_three / classic_holes_for_par_three);
      avgSummary.totalScoreParThree += classic_strokes_for_par_three;
      avgSummary.totalHolePlayedParThree += classic_holes_for_par_three;
    }
    if (classic_holes_for_par_four) {
      avg_score_par_four.push(classic_strokes_for_par_four / classic_holes_for_par_four);
      avgSummary.totalScoreParFour += classic_strokes_for_par_four;
      avgSummary.totalHolePlayedParFour += classic_holes_for_par_four;
    }
    if (classic_holes_for_par_five) {
      avg_score_par_five.push(classic_strokes_for_par_five / classic_holes_for_par_five);
      avgSummary.totalScoreParFive += classic_strokes_for_par_five;
      avgSummary.totalHolePlayedParFive += classic_holes_for_par_five;
    }
  }

  /**
   * avgOverallAdvanced
   * @param overall_advanced
   * @param avg_score
   * @param avg_score_par_three
   * @param avg_score_par_four
   * @param avg_score_par_five
   */
  private avgOverallAdvanced(
    overall_advanced: any,
    avg_score: any[],
    avg_score_par_three: any[],
    avg_score_par_four: any[],
    avg_score_par_five: any[],
    avgSummary: AvgScoreSummary
  ) {
    const advanced_total_score = +overall_advanced.total_score().toFixed(2);
    const advanced_holes_played_total = +overall_advanced.holes_played_total().toFixed(2);
    const advanced_strokes_for_par_three = +overall_advanced.number_of_strokes_for_par(3).toFixed(2);
    const advanced_holes_for_par_three = +overall_advanced.number_of_holes_for_par(3);
    const advanced_strokes_for_par_four = +overall_advanced.number_of_strokes_for_par(4).toFixed(2);
    const advanced_holes_for_par_four = +overall_advanced.number_of_holes_for_par(4);
    const advanced_strokes_for_par_five = +overall_advanced.number_of_strokes_for_par(5).toFixed(2);
    const advanced_holes_for_par_five = +overall_advanced.number_of_holes_for_par(5);

    if (advanced_holes_played_total) {
      avg_score.push((advanced_total_score / advanced_holes_played_total) * 18);
      avgSummary.totalScore += advanced_total_score;
      avgSummary.totalHolePlayed += advanced_holes_played_total;
    }
    if (advanced_holes_for_par_three) {
      avg_score_par_three.push(advanced_strokes_for_par_three / advanced_holes_for_par_three);
      avgSummary.totalScoreParThree += advanced_strokes_for_par_three;
      avgSummary.totalHolePlayedParThree += advanced_holes_for_par_three;
    }
    if (advanced_holes_for_par_four) {
      avg_score_par_four.push(advanced_strokes_for_par_four / advanced_holes_for_par_four);
      avgSummary.totalScoreParFour += advanced_strokes_for_par_four;
      avgSummary.totalHolePlayedParFour += advanced_holes_for_par_four;
    }
    if (advanced_holes_for_par_five) {
      avg_score_par_five.push(advanced_strokes_for_par_five / advanced_holes_for_par_five);
      avgSummary.totalScoreParFive += advanced_strokes_for_par_five;
      avgSummary.totalHolePlayedParFive += advanced_holes_for_par_five;
    }
  }

  /**
   * avgOverallMultiplayer
   * @param overall_multiplayer
   * @param avg_score
   * @param avg_score_par_three
   * @param avg_score_par_four
   * @param avg_score_par_five
   */
  private avgOverallMultiplayer(
    overall_multiplayer: any,
    avg_score: any[],
    avg_score_par_three: any[],
    avg_score_par_four: any[],
    avg_score_par_five: any[],
    avgSummary: AvgScoreSummary
  ) {
    const { total, hole_numbers_players } = overall_multiplayer.total_score_multiplayer();

    const multiplayer_strokes_for_par_three = +overall_multiplayer.number_of_strokes_multiplayer_for_par(3).toFixed(2);
    const multiplayer_holes_for_par_three = +overall_multiplayer.number_of_holes_multiplayer_for_par(3).toFixed(2);

    const multiplayer_strokes_for_par_four = +overall_multiplayer.number_of_strokes_multiplayer_for_par(4).toFixed(2);
    const multiplayer_holes_for_par_four = +overall_multiplayer.number_of_holes_multiplayer_for_par(4).toFixed(2);

    const multiplayer_strokes_for_par_five = +overall_multiplayer.number_of_strokes_multiplayer_for_par(5).toFixed(2);
    const multiplayer_holes_for_par_five = +overall_multiplayer.number_of_holes_multiplayer_for_par(5).toFixed(2);

    if (hole_numbers_players) {
      avg_score.push((+total / +hole_numbers_players) * 18);
      avgSummary.totalScore += total;
      avgSummary.totalHolePlayed += hole_numbers_players;
    }
    if (multiplayer_holes_for_par_three) {
      avg_score_par_three.push(multiplayer_strokes_for_par_three / multiplayer_holes_for_par_three);
      avgSummary.totalScoreParThree += multiplayer_strokes_for_par_three;
      avgSummary.totalHolePlayedParThree += multiplayer_holes_for_par_three;
    }
    if (multiplayer_holes_for_par_four) {
      avg_score_par_four.push(multiplayer_strokes_for_par_four / multiplayer_holes_for_par_four);
      avgSummary.totalScoreParFour += multiplayer_strokes_for_par_four;
      avgSummary.totalHolePlayedParFour += multiplayer_holes_for_par_four;
    }
    if (multiplayer_holes_for_par_five) {
      avg_score_par_five.push(multiplayer_strokes_for_par_five / multiplayer_holes_for_par_five);
      avgSummary.totalScoreParFive += multiplayer_strokes_for_par_five;
      avgSummary.totalHolePlayedParFive += multiplayer_holes_for_par_five;
    }
  }

  /**
   * avgOverallBasic
   * @param overall_basic
   * @param avg_score
   * @param avg_score_par_three
   * @param avg_score_par_four
   * @param avg_score_par_five
   */
  private avgOverallBasic(
    overall_basic: any,
    avg_score: any[],
    avg_score_par_three: any[],
    avg_score_par_four: any[],
    avg_score_par_five: any[],
    avgSummary: AvgScoreSummary
  ) {
    const { total, hole_numbers_players } = overall_basic.total_score_basic();

    const basic_strokes_for_par_three = +overall_basic.number_of_strokes_basic_for_par(3).toFixed(2);
    const basic_holes_for_par_three = +overall_basic.number_of_holes_basic_for_par(3).toFixed(2);

    const basic_strokes_for_par_four = +overall_basic.number_of_strokes_basic_for_par(4).toFixed(2);
    const basic_holes_for_par_four = +overall_basic.number_of_holes_basic_for_par(4).toFixed(2);

    const basic_strokes_for_par_five = +overall_basic.number_of_strokes_basic_for_par(5).toFixed(2);
    const basic_holes_for_par_five = +overall_basic.number_of_holes_basic_for_par(5).toFixed(2);

    if (hole_numbers_players) {
      avg_score.push((total / hole_numbers_players) * 18);
      avgSummary.totalScore += total;
      avgSummary.totalHolePlayed += hole_numbers_players;
    }
    if (basic_holes_for_par_three) {
      avg_score_par_three.push(basic_strokes_for_par_three / basic_holes_for_par_three);
      avgSummary.totalScoreParThree += basic_strokes_for_par_three;
      avgSummary.totalHolePlayedParThree += basic_holes_for_par_three;
    }
    if (basic_holes_for_par_four) {
      avg_score_par_four.push(basic_strokes_for_par_four / basic_holes_for_par_four);
      avgSummary.totalScoreParFour += basic_strokes_for_par_four;
      avgSummary.totalHolePlayedParFour += basic_holes_for_par_four;
    }
    if (basic_holes_for_par_five) {
      avg_score_par_five.push(basic_strokes_for_par_five / basic_holes_for_par_five);
      avgSummary.totalScoreParFive += basic_strokes_for_par_five;
      avgSummary.totalHolePlayedParFive += basic_holes_for_par_five;
    }
  }
  /**
   * calcAvgScore
   * @param array
   * @returns
   */
  calcAvgScore(avgSummary: AvgScoreSummary, avgScoreType: AVG_SCORE_TYPES) {
    // if (!array || isEmpty(array)) {
    //   return 0;
    // }
    // return (this.getTotalValueArr(array) / array.length).toFixed(2);
    let totalScore = 0;
    let totalHolePlayed = 0;
    switch (avgScoreType) {
      case AVG_SCORE_TYPES.PAR_THREE:
        totalScore = avgSummary.totalScoreParThree;
        totalHolePlayed = avgSummary.totalHolePlayedParThree;
        break;
      case AVG_SCORE_TYPES.PAR_FOUR:
        totalScore = avgSummary.totalScoreParFour;
        totalHolePlayed = avgSummary.totalHolePlayedParFour;
        break;
      case AVG_SCORE_TYPES.PAR_FIVE:
        totalScore = avgSummary.totalScoreParFive;
        totalHolePlayed = avgSummary.totalHolePlayedParFive;
        break;
      default:
        totalScore = avgSummary.totalScore;
        totalHolePlayed = avgSummary.totalHolePlayed;
        break;
    }
    if (totalHolePlayed == 0 || !totalHolePlayed) {
      return 0;
    }
    if (avgScoreType != AVG_SCORE_TYPES.DEFAULT) {
      return (totalScore / totalHolePlayed).toFixed(2);
    } else {
      return ((totalScore / totalHolePlayed) * 18).toFixed(2);
    }
  }

  /**
   * getTotalValueArr
   * @param array
   * @returns
   */
  getTotalValueArr(array) {
    if (!isEmpty(array)) {
      return array.reduce((total, item) => total + +item, 0);
    }
  }

  /**
   *
   * @param overall_classic
   * @returns
   */
  transformFairwayGreenMissedPercent(overall_classic: any) {
    return {
      fw_missed_left_percent: overall_classic.classic_round_fairway_stats_percent('fw_missed_left'),
      fw_missed_right_percent: overall_classic.classic_round_fairway_stats_percent('fw_missed_right'),
      gr_missed_left_percent: overall_classic.classic_round_green_stats_percent('gr_missed_left'),
      gr_missed_right_percent: overall_classic.classic_round_green_stats_percent('gr_missed_right'),
      gr_missed_long_percent: overall_classic.classic_round_green_stats_percent('gr_missed_long'),
      gr_missed_short_percent: overall_classic.classic_round_green_stats_percent('gr_missed_short'),
    };
  }

  /**
   *
   * @param overall_advanced
   * @param stats
   * @returns
   */
  transformStrokeGainRoundAdvanced(overall_advanced: SmartGolfStatsTypesOverall, stats: CalculateStrokeStats) {
    this.logger.debug(`TRANSFORM STROKE GAIN ROUND ADVANCED`);
    const res = {
      strokes_gained_total: +overall_advanced.strokes_gained_for_all().toFixed(2),
      strokes_gained_driving:
        +overall_advanced.strokes_gained_for_driving() == 0
          ? null
          : +overall_advanced.strokes_gained_for_driving().toFixed(2),
      strokes_gained_approach: +overall_advanced.strokes_gained_for_approach().toFixed(2),
      strokes_gained_short: +overall_advanced.strokes_gained_for_short().toFixed(2),
      strokes_gained_putting: +overall_advanced.strokes_gained_for_putting().toFixed(2),
      approach_greens_hit: +stats.approach().greens_hit_percentage().toFixed(2),
      short_greens_hit: +stats.short_tee().greens_hit_percentage().toFixed(2),
    };

    return res;
  }

  async overallByClub(userId, params, currentUser) {
    currentUser = await this.initCurrentUser(currentUser, userId);
    this.logger.debug(`CLUB STATS`);
    this.logger.debug({ userId, params });
    const roundsIds = await this.getRounds(params, currentUser);
    const rounds = roundsIds.map((r) => r.id);
    const club = params.club;
    const unit = params.unit;
    if (!rounds || isEmpty(rounds)) {
      return {};
    }
    switch (club) {
      case 'shots':
        return { data: await this.club_status_by_shots(roundsIds, null, userId) };
      case 'shots-gained':
        return { data: await this.club_status_by_shots_gained(roundsIds, null, currentUser) };
      case 'greens':
        return { data: await this.club_status_by_greens(roundsIds, null, currentUser) };
      default:
        return { data: await this.club_stats_by_distance(unit, rounds, null, userId) };
    }
  }

  async overallByOneClub(userId, params, currentUser, clubId) {
    this.logger.debug(`OVERALL BY ONE CLUB`);
    currentUser = await this.initCurrentUser(currentUser, userId);
    const club = await this.findClub(clubId, userId);
    if (!club) {
      return {
        message: "Club don't have club type",
      };
    }
    const roundsIds = await this.getRoundsAdvanced(currentUser);
    if (isEmpty(roundsIds)) {
      return { message: 'Please add round first' };
    }

    const rounds = roundsIds.map((r) => r.id);

    let [shot, distance, shots_gained] = await Promise.all([
      this.club_status_by_shots(roundsIds, club, currentUser.id),
      this.club_stats_by_distance(params.unit, rounds, club, currentUser.id),
      this.club_status_by_shots_gained(roundsIds, club, currentUser),
    ]);
    shot = shot.filter((gain) => gain.club == ClubUtil.normalize_type(club.club_type));
    distance = distance.filter((gain) => gain.club == ClubUtil.normalize_type(club.club_type));
    shots_gained = shots_gained.filter((gain) => gain.club == ClubUtil.normalize_type(club.club_type));

    if (shot.length > 0) {
      shot = shot[0];
    }
    if (distance.length > 0) {
      distance = distance[0];
    }
    if (shots_gained.length > 0) {
      shots_gained = shots_gained[0];
    }

    this.logger.log({ shot, distance, shots_gained });
    const data = {
      shot,
      distance,
      shots_gained,
    };
    if (params.club) {
      switch (params.club) {
        case 'fairway':
          const ids = roundsIds.map((r) => r.id);
          const stats_for_club = await this.strokeStatsService.calculate_for_club(ids, club.id);
          if (stats_for_club == MISSING_ROUND) {
            data['fairway_hit'] = {};
          } else {
            const overall_for_club = stats_for_club.overall();
            data['fairway_hit'] = {
              club: ClubUtil.normalize_type(club.club_type),
              value: +overall_for_club.fairways_in_regulation().toFixed(1),
            };
          }
          break;
        case 'green':
          const statsGreen = await this.club_status_by_greens(roundsIds, club, currentUser);
          const green = statsGreen.filter((c) => c.club == ClubUtil.normalize_type(club.club_type));
          data['green'] = green ? green[0] : {};
          break;
      }
    }
    return { data };
  }
  async overallByOneClubPutter(userId, params, currentUser, clubId) {
    this.logger.debug(`OVERALL BY ONE CLUB PUTTER`);
    this.logger.debug({ userId, params, clubId });
    const statsHelper = new StatsHelper();
    params.units = statsHelper.determine_units(params.units);
    currentUser = await this.initCurrentUser(currentUser, userId);
    if (!currentUser) {
      throwBadRequestError('User NotFound');
    }
    const club = await this.findClub(clubId, userId);
    if (!club) {
      return {
        message: "Club don't have club type",
      };
    }
    const roundsIds = await this.getRoundsAdvanced(currentUser);
    if (isEmpty(roundsIds)) {
      return { message: 'Please add round first' };
    }

    const rounds = roundsIds.map((r) => r.id);
    const holes = await this.holePlayedService.getHolesOfRoundIds(rounds);
    const holeIds = holes.map((hole) => hole.id);

    const strokes = await this.holePlayedService.getStrokesPlayedListHole(holeIds);

    const stats = new SmartGolfStats().calculate(
      strokes,
      strokes,
      holes,
      this.holePlayedService,
      this.roundService,
      this.strokesPlayedService,
      currentUser.strokes_gained_baseline
    );
    // const putting = stats.putting();
    const strokes_gained = +stats.putting_for_club(club.id).strokes_gained().toFixed(2);
    const strokes_gained_for_shots = +stats.putting_for_club(club.id).strokes_gained_per_shot().toFixed(2);
    let statsRes = {
      units: params.units,
      strokes_gained,
      strokes_gained_for_shots,
    };
    if (includeStr(params.units, 'meter')) {
      statsRes = this.puttForMeters(stats, club, statsRes);
    } else {
      statsRes = this.puttForYards(stats, club, statsRes);
    }

    return { data: statsRes };
  }
  puttForYards(stats: CalculateStrokeStats, club: any, statsRes: any) {
    const puttForClub = [];
    const buckets = [
      DISTANCE_BUCKETS_TYPES.BUCKET_ONE,
      DISTANCE_BUCKETS_TYPES.BUCKET_TWO,
      DISTANCE_BUCKETS_TYPES.BUCKET_THREE,
      DISTANCE_BUCKETS_TYPES.BUCKET_FOUR,
      DISTANCE_BUCKETS_TYPES.BUCKET_FIVE,
      DISTANCE_BUCKETS_TYPES.BUCKET_SIX,
      DISTANCE_BUCKETS_TYPES.BUCKET_SEVEN,
    ];
    for (const bucket of buckets) {
      const statsBucket = stats.putting_for_club(club.id).calculateDistanceBucket(bucket);
      let bucketKey = '';
      let proPercent: any;
      switch (bucket) {
        case DISTANCE_BUCKETS_TYPES.BUCKET_ONE:
          bucketKey = '<3';
          proPercent = BUCKETS_PRO['BUCKET_ONE'];
          break;
        case DISTANCE_BUCKETS_TYPES.BUCKET_TWO:
          bucketKey = '3-6';
          proPercent = BUCKETS_PRO['BUCKET_TWO'];
          break;
        case DISTANCE_BUCKETS_TYPES.BUCKET_THREE:
          bucketKey = '6-9';
          proPercent = BUCKETS_PRO['BUCKET_THREE'];
          break;
        case DISTANCE_BUCKETS_TYPES.BUCKET_FOUR:
          bucketKey = '9-15';
          proPercent = BUCKETS_PRO['BUCKET_FOUR'];
          break;
        case DISTANCE_BUCKETS_TYPES.BUCKET_FIVE:
          bucketKey = '15-21';
          proPercent = BUCKETS_PRO['BUCKET_FIVE'];
          break;
        case DISTANCE_BUCKETS_TYPES.BUCKET_SIX:
          bucketKey = '21-30';
          proPercent = BUCKETS_PRO['BUCKET_SIX'];
          break;
        case DISTANCE_BUCKETS_TYPES.BUCKET_SEVEN:
          bucketKey = '>30';
          proPercent = BUCKETS_PRO['BUCKET_SEVEN'];
          break;
      }
      const statsValue = this.transformStatsClubPutter(proPercent, statsBucket);
      puttForClub[bucketKey] = statsValue;
    }
    statsRes = {
      ...statsRes,
      ...puttForClub,
    };
    return statsRes;
  }
  private transformStatsClubPutter(proPercent: any, statsBucket: SmartGolfStatsTypesProximity) {
    return {
      ...proPercent,
      percentage_holed: +statsBucket.percentage_of_shots_holed().toFixed(2),
      number_of_putts_per_round: +statsBucket.number_of_putts_per_round(),
      strokes_gained_per_round: +statsBucket.strokes_gained().toFixed(2),
      strokes_gained_per_shot: +statsBucket.strokes_gained_per_shot().toFixed(2),
      number_of_strokes: +statsBucket.strokes_total(),
    };
  }

  private puttForMeters(stats: CalculateStrokeStats, club: any, statsRes: any) {
    const puttForClub = [];
    const bucketsMeters = [
      DISTANCE_BUCKETS_TYPES.METERS_BUCKET_ONE,
      DISTANCE_BUCKETS_TYPES.METERS_BUCKET_TWO,
      DISTANCE_BUCKETS_TYPES.METERS_BUCKET_THREE,
      DISTANCE_BUCKETS_TYPES.METERS_BUCKET_FOUR,
      DISTANCE_BUCKETS_TYPES.METERS_BUCKET_FIVE,
      DISTANCE_BUCKETS_TYPES.METERS_BUCKET_SIX,
      DISTANCE_BUCKETS_TYPES.METERS_BUCKET_SEVEN,
    ];
    for (const bucket of bucketsMeters) {
      const statsBucket = stats.putting_for_club(club.id).calculateDistanceBucket(bucket);
      let bucketKey = '';
      let proPercent: any;
      switch (bucket) {
        case DISTANCE_BUCKETS_TYPES.METERS_BUCKET_ONE:
          bucketKey = '<1';
          proPercent = BUCKETS_PRO['BUCKET_ONE'];

          break;
        case DISTANCE_BUCKETS_TYPES.METERS_BUCKET_TWO:
          bucketKey = '1-2';
          proPercent = BUCKETS_PRO['BUCKET_TWO'];
          break;
        case DISTANCE_BUCKETS_TYPES.METERS_BUCKET_THREE:
          bucketKey = '2-3';
          proPercent = BUCKETS_PRO['BUCKET_THREE'];
          break;
        case DISTANCE_BUCKETS_TYPES.METERS_BUCKET_FOUR:
          bucketKey = '3-5';
          proPercent = BUCKETS_PRO['BUCKET_FOUR'];
          break;
        case DISTANCE_BUCKETS_TYPES.METERS_BUCKET_FIVE:
          bucketKey = '5-7';
          proPercent = BUCKETS_PRO['BUCKET_FIVE'];
          break;
        case DISTANCE_BUCKETS_TYPES.METERS_BUCKET_SIX:
          bucketKey = '7-10';
          proPercent = BUCKETS_PRO['BUCKET_SIX'];
          break;
        case DISTANCE_BUCKETS_TYPES.METERS_BUCKET_SEVEN:
          bucketKey = '>10';
          proPercent = BUCKETS_PRO['BUCKET_SEVEN'];

          break;
      }
      const statsValue = this.transformStatsClubPutter(proPercent, statsBucket);
      puttForClub[bucketKey] = statsValue;
    }
    statsRes = {
      ...statsRes,
      ...puttForClub,
    };
    return statsRes;
  }

  private async findClub(clubId: any, userId: any) {
    const condition = isNaN(+clubId) ? ` cdm_witb_id = :clubId ` : `id = :clubId`;
    const club = await this.clubRepo
      .createQueryBuilder()
      .where(`${condition} AND user_id = :userId AND disabled = false AND club_type IS NOT NULL`, { clubId, userId })
      .select(['id', 'club_type', 'disabled', 'in_bag'])
      .getRawOne();
    return club;
  }

  club_stats_by_distance(unit, rounds, club, userId) {
    this.logger.log(`CLUB_STATS_BY_DISTANCE`);
    // ::Stats::Club.new( @round_ids, club ).distances( (params[:units] || "yards") )
    return this.statsClubService.distances(unit, rounds, club, userId);
  }

  club_status_by_shots(rounds, club, userId) {
    this.logger.log(`CLUB_STATUS_BY_SHOTS`);
    const { round_ids } = RoundScoreUtil.formatRoundIds(rounds);
    return this.statsClubService.shots(round_ids, club, userId);
    // ::Stats::Club.new( @round_ids, club ).shots
  }

  club_status_by_shots_gained(rounds, club, currentUser) {
    this.logger.log(`CLUB_STATUS_BY_SHOTS_GAINED`);
    const { round_ids } = RoundScoreUtil.formatRoundIds(rounds);
    // update_stroke_gained(StrokeStat.calculate(@round_ids).overall)
    return this.statsClubService.shots_gained(round_ids, club, currentUser);
  }

  club_status_by_greens(rounds, club, currentUser) {
    this.logger.log(`CLUB_STATUS_BY_GREENS`);
    const { round_ids } = RoundScoreUtil.formatRoundIds(rounds);
    return this.statsClubService.green_percentage(round_ids, club, currentUser);
    // ::Stats::Club.new( @round_ids, club ).green_percentage
  }

  async initCurrentUser(currentUser: any, userId: any) {
    if (!currentUser) {
      if (isNaN(+userId)) {
        return null;
      }
      currentUser = await this.userRepo.findOne({
        where: { id: userId },
        select: ['email', 'id', 'gender', 'strokes_gained_baseline'],
      });
    }
    return currentUser;
  }

  overallByMode() {
    return;
  }

  overallByDate() {
    return;
  }

  overallByCourse() {
    return;
  }

  overallByLastRound() {
    return;
  }

  overallByRoundType() {
    return;
  }
}
