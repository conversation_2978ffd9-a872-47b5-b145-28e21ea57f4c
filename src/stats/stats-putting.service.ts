import { Inject, Injectable, Logger, forwardRef } from '@nestjs/common';
import { HolesPlayedService } from 'src/holes-played/holes-played.service';
import { RoundService } from 'src/rounds/rounds.service';
import { StrokesPlayedService } from 'src/strokes-played/strokes-played.service';
import { StrokesStatsService } from 'src/strokes-stats/strokes-stats.service';
import { throwBadRequestError } from 'src/utils/exception';
import { StatsHelper } from 'src/utils/helper/stats_helper';
import { SmartGolfStats, Stats } from 'src/utils/smart-golf/stats';
import { SmartGolfStatsTypesProximity } from 'src/utils/smart-golf/stats/types/proximity';
import { SmartGolfStatsTypesPutting } from 'src/utils/smart-golf/stats/types/putting';
import { includeStr } from 'src/utils/utils';
import { StatsOverallService } from './stats-overall.service';
import { BUCKETS_PRO, BUILD_GRAPH_TYPES, DISTANCE_BUCKETS_TYPES } from './stats.const';

const BUCKET_METERS = [
  DISTANCE_BUCKETS_TYPES.METERS_BUCKET_ONE,
  DISTANCE_BUCKETS_TYPES.METERS_BUCKET_TWO,
  DISTANCE_BUCKETS_TYPES.METERS_BUCKET_THREE,
  DISTANCE_BUCKETS_TYPES.METERS_BUCKET_FOUR,
  DISTANCE_BUCKETS_TYPES.METERS_BUCKET_FIVE,
  DISTANCE_BUCKETS_TYPES.METERS_BUCKET_SIX,
  DISTANCE_BUCKETS_TYPES.METERS_BUCKET_SEVEN,
];
const BUCKETS_YARDS = [
  DISTANCE_BUCKETS_TYPES.BUCKET_ONE,
  DISTANCE_BUCKETS_TYPES.BUCKET_TWO,
  DISTANCE_BUCKETS_TYPES.BUCKET_THREE,
  DISTANCE_BUCKETS_TYPES.BUCKET_FOUR,
  DISTANCE_BUCKETS_TYPES.BUCKET_FIVE,
  DISTANCE_BUCKETS_TYPES.BUCKET_SIX,
  DISTANCE_BUCKETS_TYPES.BUCKET_SEVEN,
];
@Injectable()
export class StatsPuttingService {
  private readonly logger = new Logger(StatsPuttingService.name);
  private readonly statsHelper = new StatsHelper();
  constructor(
    @Inject(forwardRef(() => StatsOverallService)) private readonly statsOverallService: StatsOverallService,
    @Inject(forwardRef(() => StrokesStatsService)) private readonly strokesStatsService: StrokesStatsService,
    @Inject(forwardRef(() => HolesPlayedService)) private readonly holePlayedService: HolesPlayedService,
    @Inject(forwardRef(() => RoundService)) private readonly roundService: RoundService,
    @Inject(forwardRef(() => StrokesPlayedService)) private readonly strokesPlayedService: StrokesPlayedService
  ) {}
  async putting(userId, query, currentUser) {
    currentUser = await this.statsOverallService.initCurrentUser(currentUser, userId);
    if (!currentUser) {
      throwBadRequestError('User NotFound');
    }
    query.units = query.units ? query.units : 'yards';
    this.logger.debug(`STATS PUTTING...`);
    const round_ids = await this.getRoundIds(query, currentUser);
    const stroke_stats = await this.strokesStatsService.stats_for_rounds(round_ids);
    const holes = [];
    const stats = new SmartGolfStats().calculate(
      stroke_stats,
      stroke_stats,
      holes,
      this.holePlayedService,
      this.roundService,
      this.strokesPlayedService,
      currentUser.strokes_gained_baseline
    );
    const putting = stats.putting();
    const units = this.statsHelper.determine_units(query.units);
    let statsRes: any = {
      round_ids,
      units,
    };
    if (round_ids.length == 0) {
      return { data: statsRes };
    }
    statsRes = {
      ...statsRes,
      strokes_gained: +putting.strokes_gained().toFixed(2),
      strokes_gained_for_shots: +putting.strokes_gained_per_shot().toFixed(2),
    };
    statsRes = this.puttingStatsForUnits(putting, statsRes, units);
    return { data: statsRes };
  }
  private puttingStatsForUnits(short: SmartGolfStatsTypesPutting, statsRes: any, units = 'yards') {
    let buckets = BUCKETS_YARDS;
    if (units == 'meters') {
      buckets = BUCKET_METERS;
    }
    const statsBucketRes = {};
    for (const bucket of buckets) {
      const statsBucket = short.calculateDistanceBucket(bucket);
      const { bucketKey, proPercent } = this.getPuttingBucketKey(bucket);
      statsBucketRes[bucketKey] = this.transformStatsPutting(proPercent, statsBucket);
    }

    statsRes = {
      ...statsRes,
      ...statsBucketRes,
    };
    return statsRes;
  }
  /**
   * getPuttingBucketKey
   * @param bucket
   * @returns
   */
  private getPuttingBucketKey(bucket: DISTANCE_BUCKETS_TYPES) {
    let bucketKey = '';
    let proPercent: any;
    switch (bucket) {
      case DISTANCE_BUCKETS_TYPES.METERS_BUCKET_ONE:
        bucketKey = '<1';
        proPercent = BUCKETS_PRO['BUCKET_ONE'];
        break;
      case DISTANCE_BUCKETS_TYPES.METERS_BUCKET_TWO:
        bucketKey = '1-2';
        proPercent = BUCKETS_PRO['BUCKET_TWO'];
        break;
      case DISTANCE_BUCKETS_TYPES.METERS_BUCKET_THREE:
        bucketKey = '2-3';
        proPercent = BUCKETS_PRO['BUCKET_THREE'];
        break;
      case DISTANCE_BUCKETS_TYPES.METERS_BUCKET_FOUR:
        bucketKey = '3-5';
        proPercent = BUCKETS_PRO['BUCKET_FOUR'];
        break;
      case DISTANCE_BUCKETS_TYPES.METERS_BUCKET_FIVE:
        bucketKey = '5-7';
        proPercent = BUCKETS_PRO['BUCKET_FIVE'];
        break;
      case DISTANCE_BUCKETS_TYPES.METERS_BUCKET_SIX:
        bucketKey = '7-10';
        proPercent = BUCKETS_PRO['BUCKET_SIX'];
        break;
      case DISTANCE_BUCKETS_TYPES.METERS_BUCKET_SEVEN:
        bucketKey = '>10';
        proPercent = BUCKETS_PRO['BUCKET_SEVEN'];
        break;
      case DISTANCE_BUCKETS_TYPES.BUCKET_ONE:
        bucketKey = '<3';
        proPercent = BUCKETS_PRO['BUCKET_ONE'];
        break;
      case DISTANCE_BUCKETS_TYPES.BUCKET_TWO:
        bucketKey = '3-6';
        proPercent = BUCKETS_PRO['BUCKET_TWO'];
        break;
      case DISTANCE_BUCKETS_TYPES.BUCKET_THREE:
        bucketKey = '6-9';
        proPercent = BUCKETS_PRO['BUCKET_THREE'];
        break;
      case DISTANCE_BUCKETS_TYPES.BUCKET_FOUR:
        bucketKey = '9-15';
        proPercent = BUCKETS_PRO['BUCKET_FOUR'];
        break;
      case DISTANCE_BUCKETS_TYPES.BUCKET_FIVE:
        bucketKey = '15-21';
        proPercent = BUCKETS_PRO['BUCKET_FIVE'];
        break;
      case DISTANCE_BUCKETS_TYPES.BUCKET_SIX:
        bucketKey = '21-30';
        proPercent = BUCKETS_PRO['BUCKET_SIX'];
        break;
      case DISTANCE_BUCKETS_TYPES.BUCKET_SEVEN:
        bucketKey = '>30';
        proPercent = BUCKETS_PRO['BUCKET_SEVEN'];
        break;
    }
    return { bucketKey, proPercent };
  }
  private transformStatsPutting(proPercent: any, statsBucket: SmartGolfStatsTypesProximity) {
    return {
      ...proPercent,
      percentage_holed: +statsBucket.percentage_of_shots_holed().toFixed(2),
      number_of_putts_per_round: +statsBucket.number_of_putts_per_round(),
      strokes_gained_per_round: +statsBucket.strokes_gained().toFixed(2),
      strokes_gained_per_shot: +statsBucket.strokes_gained_per_shot().toFixed(2),
      number_of_strokes: +statsBucket.strokes_total(),
    };
  }

  /**
   * getRoundIds
   * @param query
   * @param currentUser
   * @returns
   */
  private async getRoundIds(query: any, currentUser: any) {
    const rounds = await this.statsOverallService.getRounds(query, currentUser);
    const round_ids = rounds.map((round) => round.id);
    return round_ids;
  }

  /**
   * proximity_to_hole
   * @param userId
   * @param query
   * @param currentUser
   * @returns
   */
  async proximity_to_hole(userId, query, currentUser) {
    currentUser = await this.statsOverallService.initCurrentUser(currentUser, userId);
    if (!currentUser) {
      throwBadRequestError('User NotFound');
    }
    const distance = query.distance ? query.distance : BUILD_GRAPH_TYPES.HOLED;
    query.units = query.units ? query.units : 'yards';
    const units = this.statsHelper.determine_units(query.units);

    const round_ids = await this.getRoundIds(query, currentUser);
    const stroke_stats = await this.strokesStatsService.stats_for_rounds(round_ids);

    const stats = new SmartGolfStats().calculate(
      stroke_stats,
      stroke_stats,
      [],
      this.holePlayedService,
      this.roundService,
      this.strokesPlayedService,
      currentUser.strokes_gained_baseline
    );

    const putting = stats.putting();

    let strokes = null;
    switch (distance) {
      case 'sg/round':
        strokes = putting.build_graph_data_for(BUILD_GRAPH_TYPES.SG);
        break;
      case 'sg/shot':
        strokes = putting.build_graph_data_for(BUILD_GRAPH_TYPES.SG_PER_SHOT);
        break;
      case '# putts':
        strokes = putting.build_graph_data_for(BUILD_GRAPH_TYPES.PUTTS);
        break;
      default:
        strokes = putting.build_graph_data_for(BUILD_GRAPH_TYPES.HOLED);
        break;
    }

    const statsRes = {
      round_ids,
      units,
    };
    // const dataStrokes = this.transformDataPuttingProximity(data);
    return { data: strokes, ...statsRes };
  }

  /**
   * get_requested_distance
   * @param distance
   * @param units
   * @returns
   */
  get_requested_distance(distance, units) {
    let [start_distance, end_distance] = distance.split('-');

    if (includeStr(units, 'meter')) {
      start_distance = start_distance / Stats.METERS_TO_YARDS;
      end_distance = end_distance / Stats.METERS_TO_YARDS;
    }

    return [start_distance, end_distance];
  }
  /**
   * determine_bucket
   * @param distance_string
   * @returns
   */
  determine_bucket(distance_string) {
    switch (distance_string) {
      case '100-150':
        return DISTANCE_BUCKETS_TYPES.BUCKET_ONE;
      case '150-200':
        return DISTANCE_BUCKETS_TYPES.BUCKET_TWO;
      case '200-250':
        return DISTANCE_BUCKETS_TYPES.BUCKET_THREE;
      default:
        return DISTANCE_BUCKETS_TYPES.BUCKET_FOUR;
    }
  }
  /**
   * transformDataPuttingProximity
   * @param strokes_proximity
   * @returns
   */
  private transformDataPuttingProximity(strokes_proximity: any) {
    const strokes = [];
    for (const stroke of strokes_proximity) {
      const data = {
        distance: stroke['distance'],
        percentage: stroke['value'],
        pro_percentage: stroke['pro_value'],
      };
      strokes.push(data);
    }
    return strokes;
  }
}
