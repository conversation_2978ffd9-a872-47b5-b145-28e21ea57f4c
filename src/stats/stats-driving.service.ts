import { Inject, Injectable, Logger, forwardRef } from '@nestjs/common';
import { isArray, isEmpty } from 'lodash';
import { HolesPlayedService } from 'src/holes-played/holes-played.service';
import { RoundService } from 'src/rounds/rounds.service';
import { StrokesPlayedService } from 'src/strokes-played/strokes-played.service';
import { StrokesStatsService } from 'src/strokes-stats/strokes-stats.service';
import { throwBadRequestError } from 'src/utils/exception';
import { StatsHelper } from 'src/utils/helper/stats_helper';
import { RoundScoreUtil } from 'src/utils/round-score';
// import { RoundScoreUtil } from 'src/utils/round-score';
import { SmartGolfStats } from 'src/utils/smart-golf/stats';
import { SmartGolfStatsTypesOverall } from 'src/utils/smart-golf/stats/types/overall';
import { calculateAverageValues, percentRound } from 'src/utils/utils';
import { StatsOverallService } from './stats-overall.service';

@Injectable()
export class StatsDrivingService {
  private readonly logger = new Logger(StatsDrivingService.name);
  constructor(
    @Inject(forwardRef(() => StatsOverallService)) private readonly statsOverallService: StatsOverallService,
    @Inject(forwardRef(() => StrokesStatsService)) private readonly strokesStatsService: StrokesStatsService,
    @Inject(forwardRef(() => HolesPlayedService)) private readonly holePlayedService: HolesPlayedService,
    @Inject(forwardRef(() => RoundService)) private readonly roundService: RoundService,
    @Inject(forwardRef(() => StrokesPlayedService)) private readonly strokesPlayedService: StrokesPlayedService,
    @Inject(forwardRef(() => StrokesStatsService)) private readonly strokeStatsService: StrokesStatsService
  ) {}
  async driving(userId, query, currentUser) {
    currentUser = await this.statsOverallService.initCurrentUser(currentUser, userId);
    if (!currentUser) {
      throwBadRequestError('User NotFound');
    }
    query.units = query.units ? query.units : 'yards';
    this.logger.debug(`STATS DRIVING...`);
    const rounds = await this.statsOverallService.getRounds(query, currentUser);
    if (!rounds || isEmpty(rounds)) {
      return {
        data: [],
        units: query.units,
      };
    }
    // const round_ids = rounds.map((round) => round.id);
    const { round_ids, round_classic_ids } = RoundScoreUtil.formatRoundIds(rounds);
    let classic_stats_fairways_hit = null;
    if (!isEmpty(round_classic_ids) && round_classic_ids.length > 0) {
      const holes_played_classic = await this.holePlayedService.findHolesInRounds(round_classic_ids);
      const overall_classic = new SmartGolfStatsTypesOverall(
        [],
        [],
        holes_played_classic,
        this.holePlayedService,
        this.roundService,
        this.strokesPlayedService
      );
      classic_stats_fairways_hit = this.statsOverallService.getClassicStatsFairwaysHit(0, overall_classic);
    }
    // const roundsAdvancedIds = rounds.map((r) => r.id);
    const stroke_stats = await this.strokesStatsService.stats_for_rounds(round_ids);

    const holes = [];
    const stats = new SmartGolfStats().calculate(
      stroke_stats,
      stroke_stats,
      holes,
      this.holePlayedService,
      this.roundService,
      this.strokesPlayedService,
      currentUser.strokes_gained_baseline
    );
    const longTees = stats.long_tee();
    const statsHelper = new StatsHelper();
    let statsRes = {
      round_ids: [...round_ids, ...round_classic_ids],
      units: statsHelper.determine_units(query.units),
    };
    const longTeeWithDriverClub = longTees.with_driver_club();
    const longTeeWithThreeWoodClub = longTees.with_three_wood_club();
    const longTeeWithOtherClub = longTees.with_other_clubs();
    const firstLongTee = stats.first_long_tee();

    const driver_fairways_hit = +firstLongTee.with_driver_club().fairways_in_regulation().toFixed(2);
    const driver_seventy_fifth_percentile = +longTeeWithDriverClub.get_percentile(75, query.units).toFixed(2);
    const driver_strokes_gained_per_round = +longTeeWithDriverClub.strokes_gained().toFixed(2);
    const driver_number_of_shots_per_round = +longTeeWithDriverClub.strokes_per_round().toFixed(2);

    // #three_fw
    const three_fw_fairways_hit = +firstLongTee.with_three_wood_club().fairways_in_regulation().toFixed(2);
    const three_fw_seventy_fifth_percentile = +longTeeWithThreeWoodClub.get_percentile(75, query.units).toFixed(2);
    const three_fw_strokes_gained_per_round = +longTeeWithThreeWoodClub.strokes_gained().toFixed(2);
    const three_fw_number_of_shots_per_round = +longTeeWithThreeWoodClub.strokes_per_round().toFixed(2);

    // #other
    const other_fairways_hit = +firstLongTee.with_other_clubs().fairways_in_regulation().toFixed(2);
    const other_seventy_fifth_percentile = +longTeeWithOtherClub.get_percentile(75, query.units).toFixed(2);
    const other_strokes_gained_per_round = +longTeeWithOtherClub.strokes_gained().toFixed(2);
    const other_number_of_shots_per_round = +longTeeWithOtherClub.strokes_per_round().toFixed(2);

    const all_strokes_gained_per_round =
      driver_strokes_gained_per_round + three_fw_strokes_gained_per_round + other_strokes_gained_per_round;
    const all_number_of_shots_per_round =
      driver_number_of_shots_per_round + three_fw_number_of_shots_per_round + other_number_of_shots_per_round;

    let fairwaysHitAll = +firstLongTee.fairways_in_regulation().toFixed(2);
    classic_stats_fairways_hit = classic_stats_fairways_hit != null ? +classic_stats_fairways_hit : null;
    if (fairwaysHitAll != 0 && classic_stats_fairways_hit != null) {
      fairwaysHitAll = calculateAverageValues(fairwaysHitAll, classic_stats_fairways_hit);
    } else {
      fairwaysHitAll = fairwaysHitAll || classic_stats_fairways_hit;
    }
    if (fairwaysHitAll != null) {
      fairwaysHitAll = +fairwaysHitAll.toFixed(1);
    }
    const strokesFirstLongTee = firstLongTee.strokes;
    const strokesLongTee = longTees.strokes;
    let stats_driver_fairways_hit = +driver_fairways_hit.toFixed(2);
    let stats_three_fw_fairways_hit = +three_fw_fairways_hit.toFixed(2);
    let stats_other_fairways_hit = +other_fairways_hit.toFixed(2);
    if (strokesFirstLongTee?.length == 0 && classic_stats_fairways_hit == null) {
      fairwaysHitAll = null;
    }
    if (strokesLongTee?.length == 0) {
      stats_driver_fairways_hit = null;
      stats_three_fw_fairways_hit = null;
      stats_other_fairways_hit = null;
    }
    const drivingAll = {
      fairways_hit: fairwaysHitAll,
      seventy_fifth_percentile: +longTees.get_percentile(75, query.units).toFixed(2),
      strokes_gained_per_round: +all_strokes_gained_per_round.toFixed(2),
      number_of_shots_per_round: +all_number_of_shots_per_round.toFixed(2),
    };

    const driver = {
      fairways_hit: stats_driver_fairways_hit,
      seventy_fifth_percentile: +driver_seventy_fifth_percentile.toFixed(2),
      strokes_gained_per_round: +driver_strokes_gained_per_round.toFixed(2),
      number_of_shots_per_round: +driver_number_of_shots_per_round.toFixed(2),
    };

    const three_fw = {
      fairways_hit: stats_three_fw_fairways_hit,
      seventy_fifth_percentile: +three_fw_seventy_fifth_percentile.toFixed(2),
      strokes_gained_per_round: +three_fw_strokes_gained_per_round.toFixed(2),
      number_of_shots_per_round: +three_fw_number_of_shots_per_round.toFixed(2),
    };

    const other = {
      fairways_hit: stats_other_fairways_hit,
      seventy_fifth_percentile: +other_seventy_fifth_percentile.toFixed(2),
      strokes_gained_per_round: +other_strokes_gained_per_round.toFixed(2),
      number_of_shots_per_round: +other_number_of_shots_per_round.toFixed(2),
    };

    statsRes = { ...statsRes, ...{ all: drivingAll }, ...{ driver }, ...{ three_fw }, ...{ other } };
    return { data: statsRes };
  }
  async distance_and_dispersion(userId, query, currentUser) {
    query.units = query.units ? query.units : 'yards';
    this.logger.debug(`DISTANCE_AND_DISPERSION`);
    currentUser = await this.statsOverallService.initCurrentUser(currentUser, userId);
    const rounds = await this.statsOverallService.getRounds(query, currentUser);
    if (!rounds || isEmpty(rounds)) {
      return {
        data: {
          round_ids: [],
          units: 'yards',
          average: 0,
          percentage_missed_left: 0,
          percentage_fairway_hit: 0,
          percentage_missed_right: 0,
          strokes: [],
        },
      };
    }
    const { round_ids, round_classic_ids } = RoundScoreUtil.formatRoundIds(rounds);
    let classic_stats_fairways_hit = null;
    let fw_missed_left_percent = null;
    let fw_missed_right_percent = null;

    const queryStats = this.strokeStatsService.dispersion_graph(round_ids, currentUser.id);
    let queryClub = query.club || 'all';
    if (isArray(queryClub)) {
      queryClub = queryClub[0];
    }
    let isParThree = false;
    const strokes = await this.get_stats_for_club_type(queryStats, queryClub.toLowerCase());
    if (strokes && strokes.length > 0) {
      isParThree = strokes.every((s) => s.holes_par == 3);
    }
    const statsHelper = new StatsHelper();
    let percent_left = statsHelper.calculate_percentage_left(strokes);
    let percent_right = statsHelper.calculate_percentage_right(strokes);
    let percent_fairway = statsHelper.calculate_percentage_fairway_hit(strokes);
    let percent_fairway_hit = null;
    if (percent_fairway) {
      percent_fairway_hit = +(100 - percent_left - percent_right).toFixed(2);
    }
    if (percent_fairway == 0 && percent_left == 0 && percent_right == 0) {
      percent_fairway = null;
      percent_left = null;
      percent_right = null;
    }

    if (queryClub.toLowerCase() == 'all') {
      if (!isEmpty(round_classic_ids)) {
        const holes_played_classic = await this.holePlayedService.findHolesInRounds(round_classic_ids);
        isParThree = holes_played_classic.filter((hole) => hole.score > 0).every((hole) => hole.par == 3);

        const overall_classic = new SmartGolfStatsTypesOverall(
          [],
          [],
          holes_played_classic,
          this.holePlayedService,
          this.roundService,
          this.strokesPlayedService
        );
        classic_stats_fairways_hit = this.statsOverallService.getClassicStatsFairwaysHit(0, overall_classic);
        fw_missed_left_percent = overall_classic.classic_round_fairway_stats_percent('fw_missed_left');
        fw_missed_right_percent = overall_classic.classic_round_fairway_stats_percent('fw_missed_right');
        percent_fairway_hit = calculateAverageValues(percent_fairway, +classic_stats_fairways_hit);
        percent_right = calculateAverageValues(percent_right, +fw_missed_right_percent);
        percent_left = calculateAverageValues(percent_left, +fw_missed_left_percent);
      }
    }
    let c_percent_left = null;
    let c_percent_fairway_hit = null;
    let c_percent_right = null;
    if (percent_left != null || percent_fairway_hit != null || percent_right != null) {
      [c_percent_left, c_percent_fairway_hit, c_percent_right] = percentRound(
        [percent_left, percent_fairway_hit, percent_right],
        0
      );
    }
    if (isParThree && c_percent_left == 0 && c_percent_fairway_hit == 0 && c_percent_right == 0) {
      c_percent_left = null;
      c_percent_fairway_hit = null;
      c_percent_right = null;
    }
    let statsRes = {
      round_ids: [...round_ids, ...round_classic_ids],
      units: statsHelper.determine_units(query.units),
      average: statsHelper.calculate_with_units(query.units, 'yards', statsHelper.calculate_mean(strokes)),
      percentage_missed_left: c_percent_left,
      percentage_fairway_hit: c_percent_fairway_hit,
      percentage_missed_right: c_percent_right,
    };
    const strokeRes = [];
    for (const data of strokes) {
      let location = data.left ? 'left' : 'right';
      if (location == 'right') {
        location = data.right ? 'right' : 'center';
      }

      const shot = {
        x: statsHelper.calculate_with_units(
          query.units,
          'meters',
          this.strokesPlayedService.get_fairway_x_coordinate(data, location)
        ),
        y: statsHelper.calculate_with_units(query.units, 'meters', data.shot_distance),
        color: statsHelper.lie_color(data.ending_lie),
        bullet_border_color: statsHelper.lie_color(data.ending_lie),
        stroke_id: data.stroke_played_id,
        long_tee: data.long_tee,
        start_lie: data.starting_lie,
        end_lie: data.ending_lie,
        hole_name: data.hole_name,
        ordinal: data.stroke_ordinal,
        round_id: data.round_id,
        location: location,
      };
      strokeRes.push(shot);
    }
    statsRes = { ...statsRes, ...{ strokes: strokeRes } };
    return { data: statsRes };
  }
  check_driving_stat_complete(round_ids) {
    let completed = true;
    for (const round of round_ids) {
      if (!round.driving_stat_complete) {
        completed = false;
        break;
      }
    }
    return completed;
  }
  async get_stats_for_club_type(queryStrokes, club) {
    switch (club) {
      case 'driver':
        return await this.strokeStatsService.driver(queryStrokes);
      case '3 wood':
        return await this.strokeStatsService.three_wood(queryStrokes);
      case 'other':
        return await this.strokeStatsService.no_woods(queryStrokes);
      default:
        return await queryStrokes.getRawMany();
    }
  }

  dispersionClubType() {
    return;
  }
  dispersionByUnit() {
    return;
  }
}
