import { InjectQueue } from '@nestjs/bull';
import { BadRequestException, Inject, Injectable, Logger, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Queue } from 'bull';
import { IsNull, Repository } from 'typeorm';
import { OPTIONS_JOB_DEFAULT, ROUND } from 'src/rounds/round.const';
import { RoundService } from 'src/rounds/rounds.service';
import { throwBadRequestError, throwServerError } from 'src/utils/exception';
import { PROCESSORS, PROCESS_QUEUE_NAMES } from 'src/workers/jobs/job.constant';
import { RoundAuditImportMobileService } from '../round-audit-import-mobile/round-audit-import-mobile.service';
import { RoundAudit } from './entities/round-audit.entity';

@Injectable()
export class RoundAuditService {
  private readonly logger = new Logger(RoundAuditService.name);
  constructor(
    @InjectRepository(RoundAudit)
    private roundAuditRepository: Repository<RoundAudit>,
    private roundAuditImportMobileService: RoundAuditImportMobileService,
    @Inject(forwardRef(() => RoundService))
    private roundService: RoundService,
    @InjectQueue(PROCESSORS.RoundAuditImportJob) private roundAuditImportJobQueue: Queue,
    @InjectQueue(PROCESSORS.SimpleScoreToParJob) private simpleScoreToParJobQueue: Queue,
    @InjectQueue(PROCESSORS.PostScoreToGHIN) private postScoreToGHINJobQueue: Queue,
    @InjectQueue(PROCESSORS.MyTmEventsJob) private myTMEventsJobQueue: Queue
  ) {}

  async processImportRoundAudit(data: any) {
    return this.roundAuditImportMobileService.processImportRound(data);
  }
  async postRoundAudit(data: any, forceMapCourse = null) {
    const roundData = data['round'];
    const roundAuditData = {
      data: JSON.stringify(roundData),
      created_at: new Date(),
      updated_at: new Date(),
    };
    if (roundData?.arccos_round_id) {
      const isExistedArccosRound = await this.roundService.findOne({
        where: {
          arccos_round_id: roundData?.arccos_round_id,
          deleted_at: IsNull(),
        },
      });
      if (isExistedArccosRound) {
        throwBadRequestError('This Arccos round has already been posted');
      }
    }
    try {
      if (data?.post_score_ghin == true) {
        this.validateGHINCourseData(roundData);
        if (!roundData?.igolf_course_id) {
          try {
            if (roundData?.round_mode != ROUND.ROUND_MODE_SIMPLE) {
              throw new BadRequestException({
                message: 'Only accept round mode Simple',
                errorCode: 'INVALID_ROUND_MODE',
              });
            }
            return await this.roundService.forcePostScoreToGHIN(roundData);
          } catch (error) {
            console.error(error);
            return {
              message: error.message,
              errorCode: error?.response?.errorCode,
              ghinCourseId: error?.response?.ghinCourseId || error?.response?.ghin_course_id,
            };
          }
        }
      } else if (data?.post_score_golf_net == true) {
        this.validateGolfNetCourseData(roundData);
        if (!roundData?.igolf_course_id) {
          try {
            if (roundData?.round_mode != ROUND.ROUND_MODE_SIMPLE) {
              throw new BadRequestException({
                message: 'Only accept round mode Simple',
                errorCode: 'INVALID_ROUND_MODE',
              });
            }
            return await this.roundService.forcePostScoreToGolfNet(roundData);
          } catch (error) {
            console.error(error);
            return {
              message: error.message,
              errorCode: error?.response?.errorCode,
              golfNetCourseId: roundData?.golf_net_course_id,
            };
          }
        }
      }
      const roundAudit = await this.roundAuditRepository.save(this.roundAuditRepository.create(roundAuditData));
      this.logger.log(`ROUND_AUDIT: ${JSON.stringify(roundAudit)}`);
      if (roundAudit) {
        if (data.round_submit) {
          const round = await this.roundService.processCreateRound(roundAudit.id, roundAudit.data, forceMapCourse);
          if (!round) {
            return {};
          }
          if (round.round_mode == ROUND.ROUND_MODE_SIMPLE) {
            try {
              await this.simpleScoreToParJobQueue.add(
                PROCESS_QUEUE_NAMES.SIMPLE_SCORE_TO_PAR,
                {
                  roundId: round.id,
                },
                OPTIONS_JOB_DEFAULT
              );
            } catch (error) {
              console.log(error);
            }

            if (data.post_score_ghin == true) {
              try {
                const ghinScore = await this.roundService.postScoreToGHIN(round.id);
                return { round_ghin_id: ghinScore.score.id, round_id: round.id };
              } catch (error) {
                console.log(error);
                return {
                  round_id: round.id,
                  round_ghin_id: null,
                  message: error.message,
                  errorCode: error?.response?.errorCode,
                  ghinCourseId: error?.response?.ghinCourseId || error?.response?.ghin_course_id,
                };
              }
            }
          }
          //  # Call MyTM
          else {
            try {
              await this.myTMEventsJobQueue.add(
                PROCESS_QUEUE_NAMES.MY_TM_EVENTS,
                {
                  roundId: round.id,
                },
                OPTIONS_JOB_DEFAULT
              );
            } catch (error) {
              console.log(error);
            }
          }

          return { round_id: round.id };
        } else {
          const data = {
            auditId: roundAudit.id,
          };
          await this.roundAuditImportJobQueue.add(PROCESS_QUEUE_NAMES.ROUND_AUDIT_IMPORT, data, OPTIONS_JOB_DEFAULT);
          return { msg: 'ok' };
        }
      }
    } catch (error) {
      throwServerError(error.message);
    }
  }

  async postRoundAuditUSGA(data: any) {
    const roundData = data['round'];
    const roundAuditData = {
      data: JSON.stringify(roundData),
      created_at: new Date(),
      updated_at: new Date(),
    };
    try {
      this.validateGHINCourseData(roundData);
      const round = await this.roundService.validateRoundUSGA(roundData);
      if (round) {
        const scoreUsga = await this.roundService.forcePostScoreToGHINHaveRoundTM(roundData, false);
        return { ...scoreUsga, round_id: round.id };
      }
      const roundAudit = await this.roundAuditRepository.save(this.roundAuditRepository.create(roundAuditData));
      this.logger.log(`ROUND_AUDIT: ${JSON.stringify(roundAudit)}`);
      if (roundAudit) {
        const round = await this.roundService.processCreateRound(roundAudit.id, roundAudit.data);
        if (!round) {
          return {};
        }
        if (round.round_mode == ROUND.ROUND_MODE_SIMPLE) {
          try {
            await this.simpleScoreToParJobQueue.add(
              PROCESS_QUEUE_NAMES.SIMPLE_SCORE_TO_PAR,
              {
                roundId: round.id,
              },
              OPTIONS_JOB_DEFAULT
            );
          } catch (error) {
            console.log(error);
          }
        }
        //  # Call MyTM
        else {
          try {
            await this.myTMEventsJobQueue.add(
              PROCESS_QUEUE_NAMES.MY_TM_EVENTS,
              {
                roundId: round.id,
              },
              OPTIONS_JOB_DEFAULT
            );
          } catch (error) {
            console.log(error);
          }
        }

        try {
          const score = await this.roundService.forcePostScoreToGHINHaveRoundTM(round, true);
          return { ...score, round_id: round.id };
        } catch (error) {
          this.roundAuditImportMobileService.forceDeleteRound(round.id).catch((err) => err);
          this.roundAuditRepository.delete({ round_id: round.id }).catch((err) => err);
          console.log(error);
          return {
            round_id: round.id,
            round_ghin_id: null,
            message: error.message,
            errorCode: error?.response?.errorCode,
            ghinCourseId: error?.response?.ghinCourseId || error?.response?.ghin_course_id,
          };
        }

        return false;
      }
    } catch (error) {
      throwServerError(error.message);
    }
  }

  private validateGHINCourseData(roundData: any) {
    if (!roundData.ghin_course_id) {
      throwBadRequestError(`GHIN course id is required`);
    }
    if (!roundData.ghin_tee_set_id) {
      throwBadRequestError(`GHIN tee set id is required`);
    }
  }

  private validateGolfNetCourseData(roundData: any) {
    if (!roundData.golf_net_course_id) {
      throwBadRequestError(`Golf Net course id is required`);
    }
    if (!roundData.golf_net_tee_id) {
      throwBadRequestError(`Golf Net tee set id is required`);
    }
  }
}
