import { ApiProperty } from '@nestjs/swagger';
import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

@Entity({ name: 'round_audits' })
export class RoundAudit {
  @PrimaryGeneratedColumn()
  @ApiProperty({ example: 1 })
  id: number;

  @Column({ type: 'jsonb' })
  @ApiProperty({ example: 'string' })
  data: string;

  @Column({ type: 'text' })
  @ApiProperty({ example: 'string' })
  error: string;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  code: number;

  @CreateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  created_at: Date;

  @UpdateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  updated_at: Date;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  round_id: number;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  audit_type: number;

  @Column({ type: 'text' })
  @ApiProperty({ example: 'string' })
  process_error: string;
}
