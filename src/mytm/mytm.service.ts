import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import axios from 'axios';
import { Repository } from 'typeorm';
import { HolePlayed } from 'src/holes-played/entities/hole-played.entity';
import { HolesPlayedService } from 'src/holes-played/holes-played.service';
import { Round } from 'src/rounds/entities/round.entity';
import { User } from 'src/users/entities/user.entity';

@Injectable()
export class MytmService {
  private readonly logger = new Logger(MytmService.name);
  apiNewRound: any;
  apiTriggerUserCreateKlaviyo: any;
  apiTriggerAddPointSyncUSGA: any;
  apiTriggerEndRound: any;
  apiTriggerCollectSaleForceData: any;
  apiTriggerSendEmailCheckJobsDaily: any;
  constructor(
    private readonly configService: ConfigService,
    @InjectRepository(Round)
    private roundRepo: Repository<Round>,
    @InjectRepository(HolePlayed)
    private holePlayerRepo: Repository<HolePlayed>,
    @InjectRepository(User)
    private userRepo: Repository<User>,
    private holePlayerService: HolesPlayedService
  ) {
    const mytmEndpoint = configService.get('mytm.endpoint');
    this.apiNewRound = mytmEndpoint + '/events/mrp/start-round';
    this.apiTriggerUserCreateKlaviyo = mytmEndpoint + '/auth/trigger-user-success-klaviyo';
    this.apiTriggerEndRound = mytmEndpoint + '/auth/trigger-end-round-klaviyo';
    this.apiTriggerCollectSaleForceData = mytmEndpoint + '/user/trigger-collect-sale-force';
    this.apiTriggerAddPointSyncUSGA = mytmEndpoint + '/loyalty/point/usga';
    this.apiTriggerSendEmailCheckJobsDaily = mytmEndpoint + '/bull-dashboard/send-email-list';
  }

  getRequestHeaderConfigs() {
    return {
      headers: { clientId: this.configService.get('mytm.clientId') },
    };
  }
  private async initialize(round_id) {
    if (!round_id) {
      return null;
    }
    const round = await this.roundRepo.findOne({
      where: { id: round_id },
      select: ['id', 'user_id', 'igolf_course_id'],
      loadEagerRelations: false,
    });
    if (round) {
      const user = await this.userRepo.findOne({
        where: { id: round.user_id },
        select: ['cdm_id', 'email'],
        loadEagerRelations: false,
      });
      return { user, round };
    }
    return null;
  }

  async sendEventNewRound(round_id) {
    this.logger.debug(`SEND_EVENT_NEW_ROUND: ${round_id}`);
    const data = await this.initialize(round_id);
    this.logger.debug(data);
    if (!data) {
      return;
    }
    const { user, round } = data;
    if (!user.cdm_id) {
      return `${user.email} missing CDM_ID`;
    }
    const res = await axios.post(
      this.apiNewRound,
      {
        roundId: round.id,
        courseId: round.igolf_course_id,
        cdmUID: user.cdm_id,
      },
      this.getRequestHeaderConfigs()
    );
    this.logger.debug(res.data);
  }

  triggerUserCreateKlaviyo(email) {
    this.logger.log(`TRIGGER_USER_CREATE_KLAVIYO: ${email}`);
    if (email) {
      axios.post(
        this.apiTriggerUserCreateKlaviyo,
        {
          email,
        },
        this.getRequestHeaderConfigs()
      );
    }
  }
  async triggerAddPointUSGA(userId) {
    this.logger.log(`TRIGGER_ADD_POINT_USGA: ${userId}`);
    if (userId) {
      try {
        const response = await axios.get(
          this.apiTriggerAddPointSyncUSGA + `/${userId}`,
          this.getRequestHeaderConfigs()
        );
        console.log(response?.data);
      } catch (error) {
        console.log(error);

        console.error(`ERROR ADD POINT ${userId}: ` + error.message);
      }
    }
  }
  async triggerCollectSaleForceData(email) {
    this.logger.log(`TRIGGER_USER_COLLECT_SALE_FORCE_DATA: ${email}`);
    if (email) {
      try {
        await axios.post(
          this.apiTriggerCollectSaleForceData,
          {
            email,
          },
          this.getRequestHeaderConfigs()
        );
      } catch (error) {
        console.log(error.message);
      }
    }
  }

  async triggerEndPlayRoundKlaviyo(round, email) {
    const holeQueryBuilder = this.holePlayerRepo.createQueryBuilder();
    holeQueryBuilder.where({ round_id: round.id });
    holeQueryBuilder.select(['par', 'score', 'name', 'stroke_index']);
    const holes = await holeQueryBuilder.getRawMany();
    if (!holes) {
      return;
    }
    const holesScore: any = await this.holePlayerService.getScoreListHole(round.round_mode, holes, round.id);
    for (const hole of holes) {
      hole['score'] = holesScore.find((h) => h.id == hole.id)?.score | 0;
    }
    round.holes_completed = holes;
    try {
      const res = await axios.post(
        this.apiTriggerEndRound,
        {
          round,
          email,
        },
        this.getRequestHeaderConfigs()
      );
      this.logger.debug(`RESPONSE triggerEndPlayRoundKlaviyo`);
      this.logger.debug(JSON.stringify(res.data));
    } catch (error) {
      this.logger.error(error.message);
    }
  }

  async triggerSendEmailCheckJobsDaily(jobs, isFromOC = true) {
    this.logger.log(`TRIGGER_SEND_EMAIL_CHECK_JOBS_DAILY`);
    try {
      await axios.post(
        this.apiTriggerSendEmailCheckJobsDaily,
        {
          jobs,
          isFromOC,
        },
        this.getRequestHeaderConfigs()
      );
    } catch (error) {
      console.log(error.message);
    }
  }
}
