import { Controller, Get, HttpCode, HttpStatus, Param, Post, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { AuthGuard } from 'src/guards/auth.guard';
import { CACHE_KEYS } from './cdm.constant';
import { CdmService } from './cdm.service';

@ApiBearerAuth()
@UseGuards(AuthGuard)
@ApiTags('CDM')
@Controller({
  path: 'cdm',
})
export class CdmController {
  constructor(private readonly cdmService: CdmService) {}

  @Post('reset-caches')
  @HttpCode(HttpStatus.OK)
  async resetCaches() {
    await this.cdmService.delCDMCache(CACHE_KEYS.CDM_AUTH_TOKEN_CACHE_KEY);
    await this.cdmService.delCDMCache(CACHE_KEYS.CDM_CLUB_LOFTS_CACHE_KEY);
    await this.cdmService.delCDMCache(CACHE_KEYS.CDM_CLUB_MODELS_CACHE_KEY);
    await this.cdmService.delCDMCache(CACHE_KEYS.CDM_CLUB_HANDS_CACHE_KEY);
    await this.cdmService.delCDMCache(CACHE_KEYS.CDM_CLUB_LOFTS_CACHE_KEY);
    await this.cdmService.delCDMCache(CACHE_KEYS.CDM_CLUB_SHAFT_FLEX_CACHE_KEY);
    await this.cdmService.delCDMCache(CACHE_KEYS.CDM_CLUB_LOFT_ADJUSTMENT_CACHE_KEY);
    await this.cdmService.delCDMCache(CACHE_KEYS.CDM_CLUB_SHAFT_LENGTH_CACHE_KEY);
    await this.cdmService.delCDMCache(CACHE_KEYS.CDM_CLUB_LIES_CACHE_KEY);
    await this.cdmService.delCDMCache(CACHE_KEYS.CDM_CLUB_BRANDS_CACHE_KEY);
    await this.cdmService.delCDMCache(CACHE_KEYS.CDM_CLUB_CATEGORIES_CACHE_KEY);
    await this.cdmService.delCDMCache(CACHE_KEYS.CDM_CLUB_CATEGORIES_TYPES_CACHE_KEY);
    await this.cdmService.delCDMCache(CACHE_KEYS.CDM_REGIONS_CACHE_KEY);
    return { success: true };
  }
  @Get('/calc-handicap-index/:userId')
  @HttpCode(HttpStatus.OK)
  calcHandicapIndex(@Param('userId') userId) {
    this.cdmService.calculatePlayerHandicapIndex(userId);
    return { success: true };
  }
}
