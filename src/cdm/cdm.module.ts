import { Module } from '@nestjs/common';
import { SharedModule } from 'src/shared/shared.module';
import { IsExist } from 'src/utils/validators/is-exists.validator';
import { IsNotExist } from 'src/utils/validators/is-not-exists.validator';
import { CdmController } from './cdm.controller';
import { CdmService } from './cdm.service';

@Module({
  imports: [SharedModule],
  controllers: [CdmController],
  providers: [IsExist, IsNotExist, CdmService],
  exports: [CdmService],
})
export class CdmModule {}
