import { CACHE_MANAGER, Inject, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Cache } from 'cache-manager';
import ms from 'ms';
import { CACHE_KEYS } from './cdm.constant';

@Injectable()
export class CdmCacheService {
  private readonly logger = new Logger(CdmCacheService.name);
  caches = {
    clubModels: [],
    clubHands: [],
    clubLofts: [],
    clubShaftFlex: [],
    clubLoftAdjustment: [],
    clubShaftLength: [],
    clubLies: [],
    clubBrands: [],
    clubCategories: [],
    clubCategoriesTypes: [],
    regions: [],
  };
  constructor(@Inject(CACHE_MANAGER) private cacheManager: Cache, private readonly config: ConfigService) {}

  async initialCaches() {
    this.logger.log('CDM cached data initializing...');
    const restoreCacheKeys = [
      CACHE_KEYS.CDM_CLUB_MODELS_CACHE_KEY,
      CACHE_KEYS.CDM_CLUB_HANDS_CACHE_KEY,
      CACHE_KEYS.CDM_CLUB_LOFTS_CACHE_KEY,
      CACHE_KEYS.CDM_CLUB_SHAFT_FLEX_CACHE_KEY,
      CACHE_KEYS.CDM_CLUB_LOFT_ADJUSTMENT_CACHE_KEY,
      CACHE_KEYS.CDM_CLUB_SHAFT_LENGTH_CACHE_KEY,
      CACHE_KEYS.CDM_CLUB_LIES_CACHE_KEY,
      CACHE_KEYS.CDM_CLUB_BRANDS_CACHE_KEY,
      CACHE_KEYS.CDM_CLUB_CATEGORIES_CACHE_KEY,
      CACHE_KEYS.CDM_CLUB_CATEGORIES_TYPES_CACHE_KEY,
      CACHE_KEYS.CDM_REGIONS_CACHE_KEY,
    ];
    const handleRestores = [];
    restoreCacheKeys.forEach((key) => {
      handleRestores.push(this.cacheManager.get(key));
    });
    const [
      cachedModels,
      cachedHands,
      cachedLofts,
      cachedShaftFlex,
      cachedLoftAdjustment,
      cacheShaftLength,
      cachedLies,
      cachedBrands,
      cachedCategories,
      cachedCategoriesTypes,
      cachedRegions,
    ] = await Promise.all(handleRestores);
    if (cachedModels) {
      this.caches.clubModels = JSON.parse(cachedModels);
    }
    if (cachedHands) {
      this.caches.clubHands = JSON.parse(cachedHands);
    }
    if (cachedLofts) {
      this.caches.clubLofts = JSON.parse(cachedLofts);
    }
    if (cachedShaftFlex) {
      this.caches.clubShaftFlex = JSON.parse(cachedShaftFlex);
    }
    if (cachedLoftAdjustment) {
      this.caches.clubLoftAdjustment = JSON.parse(cachedLoftAdjustment);
    }
    if (cacheShaftLength) {
      this.caches.clubShaftLength = JSON.parse(cacheShaftLength);
    }
    if (cachedLies) {
      this.caches.clubLies = JSON.parse(cachedLies);
    }
    if (cachedBrands) {
      this.caches.clubBrands = JSON.parse(cachedBrands);
    }
    if (cachedCategories) {
      this.caches.clubCategories = JSON.parse(cachedCategories);
    }
    if (cachedCategoriesTypes) {
      this.caches.clubCategoriesTypes = JSON.parse(cachedCategoriesTypes);
    }
    if (cachedRegions) {
      this.caches.regions = JSON.parse(cachedRegions);
    }
    this.logger.log('CDM cached data initialized!');
  }

  async getAuthToken() {
    return this.cacheManager.get(CACHE_KEYS.CDM_AUTH_TOKEN_CACHE_KEY);
  }
  async delCDMCache(key) {
    return this.cacheManager.del(key);
  }

  async cacheAuthToken(token: string) {
    await this.cacheManager.set(CACHE_KEYS.CDM_AUTH_TOKEN_CACHE_KEY, token, CACHE_KEYS.CDM_AUTH_TOKEN_CACHE_TTL);
  }

  async cacheClubModels(models: any[]) {
    this.logger.log('Cached all models!');
    this.caches.clubModels = models;
    return this.cacheManager.set(
      CACHE_KEYS.CDM_CLUB_MODELS_CACHE_KEY,
      JSON.stringify(models),
      ms(`${this.config.get('app.cacheTTL')}`)
    );
  }

  async cacheClubHands(clubHands: any[]) {
    this.logger.log('Cached all club hands!');
    this.caches.clubHands = clubHands;
    return this.cacheManager.set(
      CACHE_KEYS.CDM_CLUB_HANDS_CACHE_KEY,
      JSON.stringify(clubHands),
      ms(`${this.config.get('app.cacheTTL')}`)
    );
  }

  async cacheClubLofts(clubLofts: any[]) {
    this.logger.log('Cached all club lofts!');
    this.caches.clubLofts = clubLofts;
    return this.cacheManager.set(
      CACHE_KEYS.CDM_CLUB_LOFTS_CACHE_KEY,
      JSON.stringify(clubLofts),
      ms(`${this.config.get('app.cacheTTL')}`)
    );
  }

  async cacheClubShaftFlex(clubShaftFlex: any[]) {
    this.logger.log('Cached all club shaft flex!');
    this.caches.clubShaftFlex = clubShaftFlex;
    return this.cacheManager.set(
      CACHE_KEYS.CDM_CLUB_SHAFT_FLEX_CACHE_KEY,
      JSON.stringify(clubShaftFlex),
      ms(`${this.config.get('app.cacheTTL')}`)
    );
  }

  async cacheClubLoftAdjustment(clubLoftAdjustment: any[]) {
    this.logger.log('Cached all club loft adjustment!');
    this.caches.clubLoftAdjustment = clubLoftAdjustment;
    return this.cacheManager.set(
      CACHE_KEYS.CDM_CLUB_LOFT_ADJUSTMENT_CACHE_KEY,
      JSON.stringify(clubLoftAdjustment),
      ms(`${this.config.get('app.cacheTTL')}`)
    );
  }

  async cacheClubShaftLength(clubShaftLength: any[]) {
    this.logger.log('Cached all club shaft length!');
    this.caches.clubShaftLength = clubShaftLength;
    return this.cacheManager.set(
      CACHE_KEYS.CDM_CLUB_SHAFT_LENGTH_CACHE_KEY,
      JSON.stringify(clubShaftLength),
      ms(`${this.config.get('app.cacheTTL')}`)
    );
  }

  async cacheClubLies(clubLies: any[]) {
    this.logger.log('Cached all club shaft length!');
    this.caches.clubLies = clubLies;
    return this.cacheManager.set(
      CACHE_KEYS.CDM_CLUB_LIES_CACHE_KEY,
      JSON.stringify(clubLies),
      ms(`${this.config.get('app.cacheTTL')}`)
    );
  }

  async cacheClubBrands(clubBrands: any[]) {
    this.logger.log('Cached all club brands!');
    this.caches.clubBrands = clubBrands;
    return this.cacheManager.set(
      CACHE_KEYS.CDM_CLUB_BRANDS_CACHE_KEY,
      JSON.stringify(clubBrands),
      ms(`${this.config.get('app.cacheTTL')}`)
    );
  }

  async cacheClubCategories(clubCategories: any[]) {
    this.logger.log('Cached all club categories!');
    this.caches.clubCategories = clubCategories;
    return this.cacheManager.set(
      CACHE_KEYS.CDM_CLUB_CATEGORIES_CACHE_KEY,
      JSON.stringify(clubCategories),
      ms(`${this.config.get('app.cacheTTL')}`)
    );
  }

  async cacheClubCategoriesTypes(clubCategoriesTypes: any[]) {
    this.logger.log('Cached all club categories!');
    this.caches.clubCategoriesTypes = clubCategoriesTypes;
    return this.cacheManager.set(
      CACHE_KEYS.CDM_CLUB_CATEGORIES_TYPES_CACHE_KEY,
      JSON.stringify(clubCategoriesTypes),
      ms(`${this.config.get('app.cacheTTL')}`)
    );
  }

  async cacheRegions(regions: any[]) {
    this.logger.log('Cached all regions!');
    this.caches.regions = regions;
    return this.cacheManager.set(
      CACHE_KEYS.CDM_REGIONS_CACHE_KEY,
      JSON.stringify(regions),
      ms(`${this.config.get('app.cacheTTL')}`)
    );
  }
}
