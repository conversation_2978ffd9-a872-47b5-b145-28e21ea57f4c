import { InjectQueue } from '@nestjs/bull';
import { CACHE_MANAGER, Inject, Injectable, Logger, forwardRef } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import { Queue } from 'bull';
import { Cache } from 'cache-manager';
import { isEmpty } from 'lodash';
import roundNumber from 'lodash/round';
import sortBy from 'lodash/sortBy';
import moment from 'moment';
import { HolesPlayedService } from 'src/holes-played/holes-played.service';
import { IGolfService } from 'src/igolf/igolf.service';
import { RoundService } from 'src/rounds/rounds.service';
import { UserCountry } from 'src/users/dto/create-user.dto';
import { WitbDTO } from 'src/users/dto/update-witbs.dto';
import { User } from 'src/users/entities/user.entity';
import { UsersService } from 'src/users/users.service';
import { isNumeric } from 'src/utils/helpers';
import { detectBaseline } from 'src/utils/utils';
import { CDM_CONSUMER_SYNCING_PROCESS_NAME, CDM_CONSUMER_SYNCING_QUEUE_NAME } from 'src/workers/cdm/cdm.constant';
import { UserGender } from '../users/dto/update-user.dto';
import { CdmCacheService } from './cdm.cache.service';
import { HANDICAP_CONSTANT } from './cdm.constant';

@Injectable()
export class CdmService {
  private readonly logger = new Logger(CdmService.name);

  constructor(
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    private readonly config: ConfigService,
    private cdmCacheService: CdmCacheService,
    @InjectQueue(CDM_CONSUMER_SYNCING_QUEUE_NAME) private cdmConsumerSyncingQueue: Queue,
    private usersService: UsersService,
    private roundService: RoundService,
    @Inject(forwardRef(() => HolesPlayedService)) private holePlayedService: HolesPlayedService,
    @Inject(forwardRef(() => IGolfService)) private iGolfService: IGolfService
  ) {}

  async getRequestHeaderConfigs() {
    let token = await this.cdmCacheService.getAuthToken();
    if (!token) {
      token = await this.handleRefreshCdmAuthToken();
    }
    return {
      headers: { Authorization: `bearer ${token}` },
    };
  }

  async getConsumer(consumerEmail: string, omitWITB = true, getDeletedWITB = false) {
    try {
      const res = await axios.get(
        `${this.config.get('app.cdmEndpoint')}/api/consumer/query?email=${encodeURIComponent(
          consumerEmail
        )}&omitWITB=${omitWITB}&getDeletedWITB=${getDeletedWITB}`,
        await this.getRequestHeaderConfigs()
      );
      return res?.data;
    } catch (error) {
      if (error?.response?.status == 401) {
        await this.handleRefreshCdmAuthToken();
      }
      this.logger.error(error);
      return null;
    }
  }
  async getWitbDetailV2(payload: WitbDTO, golferProfileId: string) {
    try {
      const url = `${this.config.get('app.cdmEndpoint')}/api/WhatsInTheBag/detail?id=${
        payload.witbId
      }&golferProfileId=${golferProfileId}`;
      const res = await axios.get(url, await this.getRequestHeaderConfigs());
      return res?.data;
    } catch (error) {
      this.logger.error(error);
      return null;
    }
  }

  async getWITBs(consumerEmail: string) {
    try {
      const res = await axios.get(
        `${this.config.get('app.cdmEndpoint')}/api/consumer/WITBs?email=${encodeURIComponent(consumerEmail)}`,
        await this.getRequestHeaderConfigs()
      );
      return res?.data;
    } catch (error) {
      this.logger.error(error);
      return null;
    }
  }
  async getWitbsV2(consumerEmail: string, getDeletedWITB?: boolean) {
    try {
      let url = `${this.config.get('app.cdmEndpoint')}/api/consumer/v2/WITBs?email=${encodeURIComponent(
        consumerEmail
      )}`;
      if (getDeletedWITB) {
        url += `&getDeletedWITB=true`;
      }
      const res = await axios.get(url, await this.getRequestHeaderConfigs());
      return res?.data;
    } catch (error) {
      this.logger.error(error);
      return null;
    }
  }

  async updateConsumer(user: User) {
    const region = await this.getRegionFromUserCountry(user.country);
    const country = this.getCountryFromUserCountry(user.country);
    const measurementUnits = this.getMeasurementUnitFromUserCountry(user.country);
    const params = {
      email: user.email,
      firstName: user.first_name,
      lastName: user?.name,
      dob: '',
      regionId: region?.id,
      handed: user.handed,
      country,
      userCountry: country,
      systemId: this.config.get('app.cdmMRPSystemId'),
      measurementUnits,
      postalCode: user.postal_code,
      gender: user.gender,
      homeCourse: user.home_course_name,
      iGolfCourseId: user.iGolf_home_course_id,
      acceptedTermsOn: user.accepted_terms_on,
      acceptedPrivacyOn: user.accepted_terms_on,
      strokesGainedBaseline: user.strokes_gained_baseline,
      // handicapPreference: 0,
      userInputHandicap: user.handicap,
      // tmCalculatedHandicap: '',
      mRP: user.id,
    };
    if (!user.first_name?.trim()) {
      params.firstName = user.name.split(' ').slice(0, -1).join(' ').trim();
      params.lastName = user.name.split(' ').slice(-1)?.join(' ')?.trim() || '';
    }
    if (user.birthday) {
      params.dob = moment(user.birthday).toISOString();
    }
    if (user.handicap && user.handicap.toLowerCase() === 'pro') {
      params.userInputHandicap = '-10';
    }
    if (!isNumeric(user.handicap)) {
      params.userInputHandicap = '';
    }
    if (user.gender?.length > 10) {
      params.gender = 'male';
    }
    if (user.handed?.length > 10) {
      params.handed = 'right';
    }
    const res = await this.addOrUpdateAccount(params);
    return res?.data;
  }

  async updateConsumerHandicapIndex(user, handicapIndex) {
    if (handicapIndex == null) {
      this.logger.debug('CONSUMER HANDICAP INDEX NULL');
      return;
    }
    const region = await this.getRegionFromUserCountry(user.country);
    this.logger.debug({ region });
    const params = {
      email: user.email,
      regionId: region?.id,
      systemId: this.config.get('app.cdmMRPSystemId'),
      tmCalculatedHandicap: handicapIndex,
    };
    this.logger.debug('UPDATE CONSUMER HANDICAP INDEX');
    this.logger.debug({ params });
    const res = await this.addOrUpdateAccount(params);
    return res?.data;
  }
  async updateConsumerGHINHandicapIndex(user, GhinHandicapIndex) {
    if (GhinHandicapIndex == null) {
      this.logger.debug('CONSUMER HANDICAP INDEX NULL');
      return;
    }
    const region = await this.getRegionFromUserCountry(user.country);
    const params = {
      email: user.email,
      regionId: region?.id,
      systemId: this.config.get('app.cdmMRPSystemId'),
      GhinHandicapIndex: GhinHandicapIndex,
    };
    this.logger.debug('UPDATE CONSUMER GHIN HANDICAP INDEX');
    this.logger.debug({ params });
    const res = await this.addOrUpdateAccount(params);
    return res?.data;
  }

  async updateConsumerGHINNumber(user) {
    const region = await this.getRegionFromUserCountry(user.country);
    const params = {
      email: user.email,
      regionId: region?.id,
      systemId: this.config.get('app.cdmMRPSystemId'),
      GHINNumber: user.ghin_id,
    };
    const res = await this.addOrUpdateAccount(params);
    return res?.data;
  }

  async updateConsumerMRPUserId(user) {
    const region = await this.getRegionFromUserCountry(user.country);
    const params = {
      email: user.email,
      regionId: region?.id,
      systemId: this.config.get('app.cdmMRPSystemId'),
      mRP: user.id,
    };
    const res = await this.addOrUpdateAccount(params);
    return res?.data;
  }

  syncConsumerByUserId(userId: number) {
    return this.cdmConsumerSyncingQueue.add(CDM_CONSUMER_SYNCING_PROCESS_NAME, {
      userId,
    });
  }

  async addOrUpdateAccount(params) {
    try {
      return axios.post(
        `${this.config.get('app.cdmEndpoint')}/api/consumer/addUpdate?omitWITB=true`,
        params,
        await this.getRequestHeaderConfigs()
      );
    } catch (error) {
      this.logger.error(error);
      return null;
    }
  }

  async addOrUpdateAccountOptIn(params) {
    try {
      return axios.post(
        `${this.config.get('app.cdmEndpoint')}/api/consumerOptIn/addUpdate`,
        params,
        await this.getRequestHeaderConfigs()
      );
    } catch (error) {
      this.logger.error(error);
      return null;
    }
  }

  async createWITB(data) {
    try {
      const res = await axios.post(
        `${this.config.get('app.cdmEndpoint')}/api/whatsInTheBag/create`,
        data,
        await this.getRequestHeaderConfigs()
      );
      return res.data;
    } catch (error) {
      this.logger.error(`createWITB`);
      this.logger.error(error);
      return null;
    }
  }

  async updateWITB(witbId, data) {
    try {
      this.logger.debug(`UPDATE WITB CDM: ${this.config.get('app.cdmEndpoint')}/api/whatsInTheBag/update`);
      this.logger.debug(
        `PAYLOAD: ${JSON.stringify({
          ...data,
          id: witbId,
        })}`
      );
      const res = await axios.post(
        `${this.config.get('app.cdmEndpoint')}/api/whatsInTheBag/update`,
        {
          ...data,
          id: witbId,
        },
        await this.getRequestHeaderConfigs()
      );
      return res.data;
    } catch (error) {
      this.logger.error(error);
      return null;
    }
  }
  async recoverWITB(witbId) {
    try {
      const res = await axios.post(
        `${this.config.get('app.cdmEndpoint')}/api/whatsInTheBag/recover?id=${witbId}`,
        {},
        await this.getRequestHeaderConfigs()
      );
      return res.data;
    } catch (error) {
      this.logger.error(error);
      return null;
    }
  }

  async cacheClubModels() {
    this.logger.log('Getting all club models...');
    const models = await this.getClubModels(15000, 0);

    return this.cdmCacheService.cacheClubModels(models);
  }
  delCDMCache(keyCache) {
    return this.cdmCacheService.delCDMCache(keyCache);
  }
  async getClubModels(take: number, skip: number) {
    try {
      const response = await axios.get(
        `${this.config.get('app.cdmEndpoint')}/api/model?take=${take}&skip=${skip}`,
        await this.getRequestHeaderConfigs()
      );
      return response.data.map((item) => ({
        id: item.id,
        name: item.name,
        imageThumb: item.imageThumb,
        imageSmall: item.imageSmall,
        imageReg: item.imageReg,
        imageLarge: item.imageLarge,
        brandId: item.brandId,
        thirdPartyId: item.thirdPartyId,
      }));
    } catch (error) {
      this.logger.error(`ERROR GET CLUB MODELS`);
      this.logger.error(error.message);
    }
  }

  async cacheClubHands() {
    try {
      this.logger.log('Getting all club hands...');
      const clubHands = await this.getClubHands(9999, 0);
      return this.cdmCacheService.cacheClubHands(clubHands);
    } catch (e) {
      this.logger.error(`Getting all club hands error: ${e.message}`);
    }
  }

  async getClubHands(take: number, skip: number) {
    const response = await axios.get(
      `${this.config.get('app.cdmEndpoint')}/api/clubHand?take=${take}&skip=${skip}`,
      await this.getRequestHeaderConfigs()
    );
    return response.data.map((item) => ({
      id: item.id,
      description: item.description,
    }));
  }

  async cacheClubLofts() {
    try {
      this.logger.log('Getting all club lofts...');
      const clubLofts = await this.getClubLofts(9999, 0);
      return this.cdmCacheService.cacheClubLofts(clubLofts);
    } catch (e) {
      this.logger.log(`Getting all club lofts error: ${e.message}`);
    }
  }

  async getClubLofts(take: number, skip: number) {
    const response = await axios.get(
      `${this.config.get('app.cdmEndpoint')}/api/ClubLoft?take=${take}&skip=${skip}`,
      await this.getRequestHeaderConfigs()
    );
    return response.data.map((item) => ({
      id: item.id,
      value: item.value,
      clubCategoryId: item.clubCategoryId,
      sortOrder: item.sortOrder,
      inUse: item.inUse,
    }));
  }

  async cacheClubShaftFlex() {
    try {
      this.logger.log('Getting all club shaft flex...');
      const clubShaftFlex = await this.getClubShaftFlex(9999, 0);
      return this.cdmCacheService.cacheClubShaftFlex(clubShaftFlex);
    } catch (e) {
      this.logger.error(`Getting all club shaft flex error: ${e.message}`);
    }
  }

  async getClubShaftFlex(take: number, skip: number) {
    const response = await axios.get(
      `${this.config.get('app.cdmEndpoint')}/api/ClubShaftFlex?take=${take}&skip=${skip}`,
      await this.getRequestHeaderConfigs()
    );
    return response.data.map((item) => ({
      id: item.id,
      value: item.value,
      sortOrder: item.sortOrder,
      inUse: item.inUse,
    }));
  }

  async cacheClubLoftAdjustment() {
    try {
      this.logger.log('Getting all club loft adjustment...');
      const clubLoftAdjustment = await this.getClubLoftAdjustment(9999, 0);
      return this.cdmCacheService.cacheClubLoftAdjustment(clubLoftAdjustment);
    } catch (e) {
      this.logger.error(`Getting all club loft adjustment error: ${e.message}`);
    }
  }

  async getClubLoftAdjustment(take: number, skip: number) {
    const response = await axios.get(
      `${this.config.get('app.cdmEndpoint')}/api/clubLoftAdjustment?take=${take}&skip=${skip}`,
      await this.getRequestHeaderConfigs()
    );
    return response.data.map((item) => ({
      id: item.id,
      value: item.value,
      sortOrder: item.sortOrder,
      inUse: item.inUse,
    }));
  }

  async cacheClubShaftLength() {
    try {
      this.logger.log('Getting all club shaft length...');
      const clubShaftLength = await this.getClubShaftLength(9999, 0);
      return this.cdmCacheService.cacheClubShaftLength(clubShaftLength);
    } catch (e) {
      this.logger.error(`Getting all club shaft length error: ${e.message}`);
    }
  }

  async getClubShaftLength(take: number, skip: number) {
    const response = await axios.get(
      `${this.config.get('app.cdmEndpoint')}/api/ClubShaftLength?take=${take}&skip=${skip}`,
      await this.getRequestHeaderConfigs()
    );
    return response.data.map((item) => ({
      id: item.id,
      value: item.value,
      isMinorAdjustment: item.isMinorAdjustment,
      sortOrder: item.sortOrder,
      clubCategoryId: item.clubCategoryId,
      inUse: item.inUse,
    }));
  }

  async cacheClubLies() {
    try {
      this.logger.log('Getting all club lies...');
      const clubLies = await this.getClubLies(9999, 0);
      return this.cdmCacheService.cacheClubLies(clubLies);
    } catch (e) {
      this.logger.error(`Getting all club lies error: ${e.message}`);
    }
  }

  async getClubLies(take: number, skip: number) {
    const response = await axios.get(
      `${this.config.get('app.cdmEndpoint')}/api/ClubLie?take=${take}&skip=${skip}`,
      await this.getRequestHeaderConfigs()
    );
    return response.data.map((item) => ({
      id: item.id,
      value: item.value,
      sortOrder: item.sortOrder,
      inUse: item.inUse,
    }));
  }

  async cacheClubBrands() {
    try {
      this.logger.log('Getting all club brands...');
      const clubBrands = await this.getClubBrands(9999, 0);
      return this.cdmCacheService.cacheClubBrands(clubBrands);
    } catch (e) {
      this.logger.log(`Getting all club brands error: ${e.message}`);
    }
  }

  async getClubBrands(take: number, skip: number) {
    const response = await axios.get(
      `${this.config.get('app.cdmEndpoint')}/api/brand?take=${take}&skip=${skip}`,
      await this.getRequestHeaderConfigs()
    );
    return response.data.map((item) => ({
      id: item.id,
      clubCategoryId: item.clubCategoryId,
      name: item.name,
    }));
  }

  async cacheClubCategories() {
    try {
      this.logger.log('Getting all club categories...');
      const clubCategories = await this.getClubCategories(9999, 0);
      return this.cdmCacheService.cacheClubCategories(clubCategories);
    } catch (e) {
      this.logger.log(`Getting all club categories error: ${e.message}`);
    }
  }

  async getClubCategories(take: number, skip: number) {
    const response = await axios.get(
      `${this.config.get('app.cdmEndpoint')}/api/clubCategory?take=${take}&skip=${skip}`,
      await this.getRequestHeaderConfigs()
    );
    return response.data.map((item) => ({
      id: item.id,
      name: item.name,
    }));
  }

  async cacheClubCategoriesTypes() {
    try {
      this.logger.log('Getting all club categories types...');
      const clubCategoriesTypes = await this.getClubCategoriesTypes(9999, 0);
      return this.cdmCacheService.cacheClubCategoriesTypes(clubCategoriesTypes);
    } catch (e) {
      this.logger.log(`Getting all club categories types error: ${e.message}`);
    }
  }

  async getClubCategoriesTypes(take: number, skip: number) {
    const response = await axios.get(
      `${this.config.get('app.cdmEndpoint')}/api/clubCategoryType?take=${take}&skip=${skip}`,
      await this.getRequestHeaderConfigs()
    );
    return response.data.map((item) => ({
      id: item.id,
      clubCategoryId: item.clubCategoryId,
      type: item.type,
      sortOrder: item.sortOrder,
    }));
  }

  async cacheRegions() {
    try {
      this.logger.log('Getting all regions...');
      const clubCategoriesTypes = await this.getRegions(9999, 0);
      return this.cdmCacheService.cacheRegions(clubCategoriesTypes);
    } catch (e) {
      this.logger.log(`Getting all club categories types error: ${e.message}`);
    }
  }

  async getRegions(take: number, skip: number) {
    const response = await axios.get(
      `${this.config.get('app.cdmEndpoint')}/api/region?take=${take}&skip=${skip}`,
      await this.getRequestHeaderConfigs()
    );
    return response.data;
  }

  async calculatePlayerHandicapIndex(userId: number) {
    this.logger.log(`CALCULATE PLAYER HANDICAP INDEX USER: ${userId}`);
    const roundScores = {};
    const user = await this.usersService.findOne({ id: userId });
    if (!user) {
      return null;
    }

    const rounds = await this.roundService.getRoundsForCalculatingHandicap(userId, 40);
    let validRounds = [];
    for (const round of rounds) {
      if (validRounds.length === 20) {
        continue;
      }
      const holesOfRound = await this.holePlayedService.findHolesInRounds([round.id]);
      const lstHoleScore = await this.holePlayedService.getScoreListHole(round.round_mode, holesOfRound, round.id);
      round['holes'] = lstHoleScore;
      const roundScore = this.roundService.getScore(round);
      roundScores[round.id] = roundScore;
      const isNineHolesPlayed = this.roundService.isNineHolesPlayed(round);
      const roundScoreValid = this.roundService.isRoundScoreValid(roundScore, isNineHolesPlayed);
      if (roundScoreValid) {
        validRounds.push(round);
      }
    }
    if (validRounds.length < 3) {
      this.logger.log(`VALID ROUNDS < 3: ${validRounds.length}`);
      return null;
    }
    validRounds = validRounds.reverse();

    let scoreDiffs = [];
    for (const round of validRounds) {
      if (!round.igolf_course_id) {
        this.logger.debug(`IGOLF_COURSE_ID EMPTY CONTINUE...`);
        continue;
      }
      try {
        let slope;
        let rating;
        const iGolfTee = await this.iGolfService.getCourseTeeDetail(round.igolf_course_id);
        const tee_info =
          iGolfTee?.teesList?.find((item) => item.teeName?.toLowerCase() === round.tee_name.toLowerCase()) || null;
        if (!tee_info) {
          this.logger.debug(`TEE_INFO EMPTY CONTINUE...`);
          continue;
        }
        let gender = user.gender?.toLowerCase() || UserGender.MALE;
        if (gender && gender.length > 10) {
          gender = UserGender.MALE;
        }
        if (gender === 'men') {
          gender = UserGender.MALE;
        }
        if (gender === UserGender.MALE) {
          slope = tee_info.slopeMen;
          rating = tee_info.ratingMen;
        } else {
          slope = tee_info.slopeWomen;
          rating = tee_info.ratingWomen;
        }
        const roundScore = roundScores[round.id];
        const isNineHolesPlayed = this.roundService.isNineHolesPlayed(round);
        if (isNineHolesPlayed) {
          rating = parseFloat(rating) / 2;
        }
        if (parseFloat(slope) > 0) {
          const calculateScoreDiff = roundNumber(
            (HANDICAP_CONSTANT / parseFloat(slope)) * (roundScore - parseFloat(rating)),
            2
          );
          scoreDiffs.push(calculateScoreDiff);
        }
      } catch (error) {
        this.logger.error(error);
        continue;
      }
    }
    scoreDiffs = sortBy(scoreDiffs.slice(0, 20));
    if (scoreDiffs.length < 3) {
      this.logger.debug(`SCORE DIFFS LENGTH INVALID`);
      this.logger.debug({ scoreDiffs });
      return null;
    }
    const handicapConfigs = CdmService.getHandicapConfigsByTotalRounds(scoreDiffs.length);
    this.logger.log({ handicapConfigs });
    let totalHandicap = 0;
    for (let i = 0; i < handicapConfigs.scoresToCount; i++) {
      totalHandicap += scoreDiffs[i] + handicapConfigs.handicapAdjustment;
    }
    const handicap = CdmService.getHandicap(roundNumber(totalHandicap / handicapConfigs.scoresToCount, 1));
    this.logger.debug({ handicap });
    return handicap;
  }

  async handleRefreshCdmAuthToken() {
    this.logger.debug(`HANDLE REFRESH CDM AUTH TOKEN`);
    this.logger.debug(`${this.config.get('app.cdmEndpoint')}/api/AuthUser`);
    const response = await axios.post(`${this.config.get('app.cdmEndpoint')}/api/AuthUser`, {
      username: this.config.get('app.cdmUsername'),
      password: this.config.get('app.cdmPassword'),
    });
    this.logger.debug(`CDM TOKEN: ${response.data?.token}`);
    await this.cdmCacheService.cacheAuthToken(response.data?.token);

    return response.data?.token;
  }

  async getRegionFromUserCountry(country: UserCountry | string) {
    let regionCode = 'USA';
    if (['gb', 'gbr', 'de', 'deu', 'fr', 'fra', 'se', 'swe'].includes(country.toLowerCase())) {
      regionCode = 'UK';
    }
    if (['ca', 'can'].includes(country.toLowerCase())) {
      regionCode = 'CAN';
    }
    if (['au', 'aus'].includes(country.toLowerCase())) {
      regionCode = 'AU';
    }
    this.logger.log({ regionCode });
    const regions = await this.getCDMRegions();

    return regions.find((item) => item.code.toLowerCase() === regionCode.toLowerCase());
  }
  private async getCDMRegions() {
    let cdmRegions: any = this.cdmCacheService.caches.regions;
    if (!cdmRegions || isEmpty(cdmRegions)) {
      cdmRegions = await this.cacheRegions();
      cdmRegions = this.cdmCacheService.caches.regions;
    }
    return cdmRegions;
  }
  getCountryFromUserCountry(country: UserCountry | string) {
    if (['ca', 'can'].includes(country.toLowerCase())) {
      return 'Canada';
    }
    if (['uk', 'gb', 'united kingdom'].includes(country.toLowerCase())) {
      return 'UK';
    }
    if (['au', 'aus'].includes(country.toLowerCase())) {
      return 'Australia';
    }
    return 'USA';
  }

  getMeasurementUnitFromUserCountry(country: UserCountry | string) {
    if (['uk', 'united kingdom', 'gb', 'us', 'ca', 'usa', 'can'].includes(country.toLowerCase())) {
      return 'yards';
    }
    return 'meters';
  }

  static convertWITBItemToClubParams(witb) {
    const params = {
      shaft_length: CdmService.mapCdmShaftLength(witb?.clubShaftLength?.value || ''),
      shaft_flex: CdmService.mapCdmShaftFlex(witb?.clubShaftFlex?.value || ''),
      face_lie_adjustment: CdmService.mapCdmFaceLieAdjustment(witb?.faceLieAdjustment?.value || ''),
      face_loft_adjustment: CdmService.mapCdmFaceLoftAdjustment(witb?.faceLoftAdjustment?.value || ''),
      loft: CdmService.mapCdmLoft(witb?.clubLoft?.value || ''),
      manufacturer: witb?.brand?.name || '',
      modelname: witb?.model?.name || '',
      club_family: '',
      club_type: '',
      in_bag: witb.inBag,
      cdm_witb_id: witb.id,
      disabled: !!witb.deleted,
    };
    const clubType = witb?.clubCategoryType?.type || '';
    params.club_family = CdmService.mapCdmClubFamily(witb?.clubCategory?.name || '');
    params.club_type = CdmService.mapCdmClubType(params.club_family, clubType);
    if (params.club_family.toLowerCase() == 'ball' && isEmpty(params.club_type)) {
      params.club_type = 'ball';
    }
    return params;
  }

  static mapCdmShaftLength(shaftLength) {
    if (!shaftLength) return '';
    if (shaftLength.toLowerCase() === 'standard') {
      return 'Std';
    }
    if (shaftLength.toLowerCase()?.includes('-')) {
      return `${shaftLength.toLowerCase()?.replace('-', '').trim()}" Short`;
    }
    if (shaftLength.toLowerCase()?.includes('+')) {
      return `${shaftLength.toLowerCase()?.replace('+', '').trim()}" Long`;
    }
    return shaftLength;
  }

  static mapCdmShaftFlex(shaftFlex) {
    return shaftFlex;
  }

  static mapCdmLoft(loft) {
    return loft ? `${loft}°` : '';
  }

  static mapCdmFaceLieAdjustment(faceLieAdjustment) {
    if (!faceLieAdjustment) return '';
    if (faceLieAdjustment?.toLowerCase() === 'standard') return 'Std';
    if (faceLieAdjustment?.toLowerCase()?.includes('upright')) {
      faceLieAdjustment = faceLieAdjustment?.toLowerCase()?.replace('upright', 'Up');
    }
    if (faceLieAdjustment?.toLowerCase()?.includes('*')) {
      return faceLieAdjustment?.replace('*', '°');
    }
    return faceLieAdjustment;
  }

  static mapCdmFaceLoftAdjustment(faceLoftAdjustment) {
    if (faceLoftAdjustment?.toLowerCase() === 'standard') return 'Std';
    return faceLoftAdjustment || '';
  }

  static mapCdmClubType(family, defaultValue) {
    if (!family) return '';
    if (family?.toLowerCase() === 'driver') return 'Driver';
    if (family?.toLowerCase() === 'putter') return 'Putter';
    let clubType = defaultValue.toLowerCase();
    if (clubType.length === 1 && family === 'Iron') {
      clubType = `${clubType}i`;
    } else if (clubType.includes('rescue')) {
      clubType = clubType.replace(' rescue', 'h');
    }
    return clubType;
  }

  static mapCdmClubFamily(family) {
    if (family.toLowerCase() === 'rescue') return 'Hybrid';
    if (family.toLowerCase() === 'irons') return 'Iron';
    return family;
  }

  static getHandicap(playerHandicap: number) {
    if (playerHandicap <= -10) {
      return -10;
    }
    if (playerHandicap >= 54) {
      return 54;
    }
    return playerHandicap;
  }

  static getHandicapConfigsByTotalRounds(totalRounds: number) {
    let handicapAdjustment = 0;
    let scoresToCount;
    switch (true) {
      case totalRounds === 3:
        handicapAdjustment = -2;
        break;
      case totalRounds === 4:
        handicapAdjustment = -1;
        break;
      case totalRounds === 5:
        handicapAdjustment = 0;
        break;
      case totalRounds === 6:
        handicapAdjustment = -1;
        break;
      case totalRounds === 7:
        handicapAdjustment = -1;
        break;
    }
    if (totalRounds >= 3 && totalRounds <= 5) {
      scoresToCount = 1;
    }
    if (totalRounds >= 6 && totalRounds <= 10) {
      scoresToCount = 2;
    }
    if (totalRounds >= 11 && totalRounds <= 13) {
      scoresToCount = 3;
    }
    if (totalRounds >= 14 && totalRounds <= 15) {
      scoresToCount = 4;
    }
    if (totalRounds >= 16 && totalRounds <= 17) {
      scoresToCount = 5;
    }
    if (totalRounds === 18) {
      scoresToCount = 6;
    }
    if (totalRounds === 19) {
      scoresToCount = 7;
    }
    if (totalRounds === 20) {
      scoresToCount = 8;
    }
    return { handicapAdjustment, scoresToCount };
  }

  static mapConsumerToUser(consumer) {
    const golferProfile = consumer.golferProfile;
    const consumerProfiles = consumer.consumerProfiles;
    const sgBaseLine = detectBaseline(golferProfile.strokesGainedBaseline);
    return {
      email: consumer.primaryEmail,
      first_name: consumer.firstName,
      name: consumer.lastName,
      birthday: consumer.dob,
      handicap: golferProfile?.newHandicap?.userInputHandicap || 0,
      handed: golferProfile.handed,
      strokes_gained_baseline: sgBaseLine,
      country: consumer.userCountry || UserCountry.US,
      gender: consumer.gender || UserGender.MALE,
      postal_code: consumerProfiles[0]?.address?.zipCode || '',
      iGolf_home_course_id: golferProfile.iGolfCourseId,
      home_course_name: golferProfile.homeCourse,
      accepted_terms_on: consumer.acceptedTermsOn,
      accepted_privacy_on: consumer.acceptedPrivacyOn,
      // accepted_both: 1,
    };
  }
}
