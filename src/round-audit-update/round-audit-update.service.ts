import { Inject, Injectable, Logger, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { isArray } from 'lodash';
import { In, Repository } from 'typeorm';
import { HolePlayed } from 'src/holes-played/entities/hole-played.entity';
import { HolesPlayedService } from 'src/holes-played/holes-played.service';
import { PlayerScore } from 'src/players/entities/player-score.entity';
import { Player } from 'src/players/entities/player.entity';
import { PlayersService } from 'src/players/players.service';
import { RoundAuditUtilsService } from 'src/round-audit-util/round-audit-util.service';
import { Round } from 'src/rounds/entities/round.entity';
import { ROUND } from 'src/rounds/round.const';
import { RoundService } from 'src/rounds/rounds.service';
import { StrokePlayed } from 'src/strokes-played/entities/stroke-played.entity';
import { StrokesPlayedService } from 'src/strokes-played/strokes-played.service';
import { StrokeStat } from 'src/strokes-stats/entities/stroke-stat.entity';
import { User } from 'src/users/entities/user.entity';
import { coordPutts, includeStr, isGear } from 'src/utils/utils';
import { ThreePartyCourseService } from '../igolf/threePartyCourse.service';

@Injectable()
export class RoundAuditUpdateService {
  public holesPlayedModify = [];
  public round = null;
  private logger = new Logger(RoundAuditUpdateService.name);
  constructor(
    @InjectRepository(Round)
    private roundRepo: Repository<Round>,
    @InjectRepository(HolePlayed)
    private holePlayedRepo: Repository<HolePlayed>,
    @InjectRepository(User)
    private userRepo: Repository<User>,
    @InjectRepository(StrokePlayed)
    private strokePlayedRepo: Repository<StrokePlayed>,
    @InjectRepository(StrokeStat)
    private strokeStatRepo: Repository<StrokeStat>,
    @InjectRepository(PlayerScore)
    private playerScoreRepo: Repository<PlayerScore>,
    @InjectRepository(Player)
    private playerRepo: Repository<Player>,
    private roundAuditUtilService: RoundAuditUtilsService,
    private playerService: PlayersService,
    @Inject(forwardRef(() => ThreePartyCourseService)) private threePartyCourseService: ThreePartyCourseService,
    @Inject(forwardRef(() => RoundService)) private roundService: RoundService,
    @Inject(forwardRef(() => HolesPlayedService)) private holePlayedService: HolesPlayedService,
    private strokesPlayerService: StrokesPlayedService,
    private holePlayerService: HolesPlayedService
  ) {}

  async updateRound(round, holeData, forceMapCourse = null) {
    const holeNumber = [];
    // if (holeData['inprogress'] != null) {
    holeData['holes']?.forEach((hole) => holeNumber.push(hole['number'].toString()));
    // }

    const data = holeData.holes;
    const roundAttrs = holeData;
    let holes = null;

    const { conditions, players } = await this.buildQueryRoundConditions(round, holeNumber);
    holes = await this.holePlayerService.find({
      ...conditions,
    });
    const holeIdsUpdate = holes?.map((h) => h.id);
    this.logger.log(`LIST HOLES UPDATE: ${holeIdsUpdate}`);
    await this.processUpdateRound(holes, roundAttrs, players, round, data, forceMapCourse);
    this.logger.log(`PROCESS UPDATE DONE...`);
  }
  private async buildQueryRoundConditions(round: any, holeNumber: any[]) {
    const conditions = {
      where: { round_id: round.id },
    };
    let players = null;
    if (round.round_mode == ROUND.ROUND_MODE_MULTIPLAYER) {
      players = await this.playerService.findBy({ where: { round_id: round.id }, select: ['id'] });
      conditions['relations'] = {
        playerScores: true,
        stats: true,
      };
    }
    if (holeNumber.length > 0) {
      conditions['where']['name'] = In(holeNumber);
    }
    return { conditions, players };
  }

  async processUpdateRound(holes, roundAttrs, players, round, data, forceMapCourse = null) {
    await this.clearPlayers(players);
    const { hole_score } = await this.transformScoresAndClearOldData(holes, round);

    await this.updateInfoRoundMultiplayer(round, roundAttrs);
    let stroke_delete = false;

    // # for samsung gear
    const generated_by = roundAttrs['generated_by'].toString().toLowerCase();
    const isSamSungGear = isGear(generated_by) && !forceMapCourse;
    const holesPlayedModify = [];
    let totalStrokes = 0;
    await Promise.all(
      data.map(async (hole) => {
        let iGolfPinLocation = [0, 0];
        if (!hole.pin_location) {
          if (round.round_mode === ROUND.ROUND_MODE_ADVANCED || round.round_mode === ROUND.ROUND_MODE_MULTIPLAYER) {
            iGolfPinLocation = (await this.threePartyCourseService.pinLocation(
              +hole.number,
              round.igolf_course_id,
              forceMapCourse
            )) || [0, 0];
            hole.pin_location = iGolfPinLocation[0]; //lon, lat
          } else {
            hole.pin_location = null;
          }
        } else {
          iGolfPinLocation = isArray(hole.pin_location)
            ? [hole.pin_location[1], hole.pin_location[0]]
            : hole.pin_location.coordinates;
          hole.pin_location = iGolfPinLocation;
        }

        const newHolePlayer = await this.createHole(hole, round, forceMapCourse);
        holesPlayedModify.push(newHolePlayer.id);
        let greenFront = null;
        const pinLocation = hole.pin_location; //[iGolfPinLocation[1], iGolfPinLocation[0]];
        let putts = null;

        if (isSamSungGear) {
          greenFront = await this.threePartyCourseService.greenFrontFor(newHolePlayer.name, round.igolf_course_id);
        }
        this.logger.log(`pinLocation: ${pinLocation}`);
        putts = this.listPutts(hole['strokes']);

        const holeScore = hole_score.find((score) => +score.name == +newHolePlayer.name);

        if (holeScore && holeScore.score != newHolePlayer.score && stroke_delete == false) {
          if (round.round_mode == ROUND.ROUND_MODE_ADVANCED) {
            stroke_delete = true;
            this.logger.debug(`START DELETE ALL STROKE IN HOLE: ${holeScore.hole_id}`);
            this.strokeStatRepo.delete({ hole_played_id: holeScore.hole_id });
          }
        }
        if (round.round_mode == ROUND.ROUND_MODE_MULTIPLAYER && hole['player_score']) {
          await Promise.all(
            hole.player_score.map(async (ps) => {
              try {
                await this.createPlayerScore(newHolePlayer, ps, round.id);
              } catch (e) {
                console.error(`ERROR CREATE PLAYER SCORE - ROUND ${round.id}`);
                console.error(e);
              }
            })
          );

          // for (const ps of hole.player_score) {
          //   await this.createPlayerScore(newHolePlayer, ps, round.id);
          // }
        }
        // if (!hole.strokes || isEmpty(hole.strokes)) {
        //   continue;
        // }
        if (hole.strokes && hole.strokes.length > 0) {
          await Promise.all(
            hole.strokes.map(async (stroke) => {
              if (isSamSungGear) {
                stroke['coords'] = coordPutts(pinLocation, greenFront, stroke, putts);
              }
              try {
                await this.createStroke(stroke, round.id, newHolePlayer.id, pinLocation);
                totalStrokes++;
              } catch (e) {
                console.error(`ERROR CREATE STROKE ROUND ${round.id}`);
                console.error(e);
              }
            })
          );
        }

        // for (const stroke of hole.strokes) {
        //   if (isSamSungGear) {
        //     stroke['coords'] = coordPutts(pinLocation, greenFront, stroke, putts);
        //   }
        //   totalStrokes++;
        //   await this.createStroke(stroke, round.id, newHolePlayer.id, pinLocation);
        // }
      })
    );
    if (roundAttrs['players'] && round.round_mode == ROUND.ROUND_MODE_MULTIPLAYER) {
      await Promise.all(
        roundAttrs.players.map(async (player) => {
          try {
            await this.createPlayer(player, round.id);
          } catch (e) {
            console.error(`ERROR CREATE PLAYERS ROUND ${round.id}`);
            console.error(e);
          }
        })
      );
      // for (const player of roundAttrs.players) {
      //   await this.createPlayer(player, round.id);
      // }
      delete roundAttrs.players;
    }
    // round.update(round_attrs.except(:round_mode)) and round.reload
    if (round.round_mode == ROUND.ROUND_MODE_CLASSIC && roundAttrs['round_mode'] == ROUND.ROUND_MODE_BASIC) {
      this.roundRepo.update({ id: round.id }, { round_mode: ROUND.ROUND_MODE_BASIC });
      round.round_mode = ROUND.ROUND_MODE_BASIC;
    }

    await this.switchToModeAdvanced(round);
    if (holesPlayedModify.length == 0) {
      return;
    }
    // TODO: pending for account Admin
    // this.roundService.updateScoreToGHIN(round);
    this.logger.debug(`============ ADD QUEUE COMPLETE JOB ROUND ID: ${round.id} ============`);
    this.roundAuditUtilService.queueCompleteRound(round, holesPlayedModify, totalStrokes);
  }
  private async switchToModeAdvanced(round: any) {
    const canSwitch = await this.canSwitchAdvancedMode(round);
    this.logger.log(`canSwitch Advanced: ${canSwitch}`);
    if (round.round_mode == ROUND.ROUND_MODE_BASIC && canSwitch) {
      await this.roundRepo.update({ id: round.id }, { round_mode: ROUND.ROUND_MODE_ADVANCED });
      round.round_mode = ROUND.ROUND_MODE_ADVANCED;
    }
  }

  private async updateInfoRoundMultiplayer(round: any, roundAttrs: any) {
    if (round.round_mode == ROUND.ROUND_MODE_MULTIPLAYER) {
      await this.roundRepo.update(
        { id: round.id },
        {
          team: roundAttrs['team'],
          multiplayer_game_type: roundAttrs['multiplayer_game_type'],
        }
      );
    }
  }

  private async transformScoresAndClearOldData(holes: any, round: any) {
    const hole_score = [];
    const holeIds = [];
    if (holes && holes.length > 0) {
      await Promise.all(
        holes.map(async (hole) => {
          let strokePlayedIds = null;
          let strokeStatsIds = null;
          let playerScoreIds = null;

          console.log(`HOLE UPDATE: ${hole.id} - HOLE NUMBER: ${hole.name} `);

          hole_score.push({ score: hole.score, hole_id: hole.id, name: +hole.name });
          holeIds.push(hole.id);

          if (round.round_mode == ROUND.ROUND_MODE_MULTIPLAYER) {
            playerScoreIds = hole?.playerScores?.map((playerScore: any) => playerScore.id);
          }
          if (round.round_mode == ROUND.ROUND_MODE_ADVANCED) {
            const strokePlayedIdsOld = await this.strokePlayedRepo.find({
              where: { hole_played_id: hole.id },
              select: ['id'],
            });
            const strokeStatsIdsOld = await this.strokeStatRepo.find({
              where: { hole_played_id: hole.id },
              select: ['id'],
            });
            if (strokePlayedIdsOld && strokePlayedIdsOld.length > 0) {
              strokePlayedIds = strokePlayedIdsOld.map((s) => s.id);
            }
            if (strokeStatsIdsOld && strokeStatsIdsOld.length > 0) {
              strokeStatsIds = strokeStatsIdsOld.map((s) => s.id);
            }
          }
          console.log(`============================================`);
          console.log(`HOLE NUMBER: ${hole.name}`);
          console.log({ strokePlayedIds });
          console.log({ strokeStatsIds });
          console.log({ playerScoreIds });
          console.log(`============================================`);
          await this.clearOldData(strokePlayedIds, strokeStatsIds, playerScoreIds, round.id);
        })
      );
      // for (const hole of holes) {
      //   console.log(`HOLE UPDATE: ${hole.id}`);

      //   hole_score.push({ score: hole.score, hole_id: hole.id, name: +hole.name });
      //   holeIds.push(hole.id);

      //   if (round.round_mode == ROUND.ROUND_MODE_MULTIPLAYER) {
      //     playerScoreIds = hole?.playerScores?.map((playerScore: any) => playerScore.id);
      //   }
      //   if (round.round_mode == ROUND.ROUND_MODE_ADVANCED) {
      //     strokePlayedIds = await this.strokePlayedRepo.find({ where: { hole_played_id: hole.id }, select: ['id'] });
      //     strokeStatsIds = await this.strokeStatRepo.find({ where: { hole_played_id: hole.id }, select: ['id'] });
      //     if (strokePlayedIds) {
      //       strokePlayedIds = strokePlayedIds.map((s) => s.id);
      //     }
      //     if (strokeStatsIds) {
      //       strokeStatsIds = strokeStatsIds.map((s) => s.id);
      //     }
      //   }
      //   await this.clearOldData(strokePlayedIds, strokeStatsIds, playerScoreIds);
      // }
    }
    if (holeIds.length > 0) {
      await this.holePlayedRepo.delete({ id: In(holeIds) });
    }
    return { holeIds, hole_score };
  }

  private async clearPlayers(players: any) {
    if (players) {
      const playersId = players.map((p) => p.id);
      await this.playerRepo.delete({ id: In(playersId) });
    }
  }

  private async clearOldData(strokePlayedIds: any, strokeStatsIds: any, playerScoreIds: any, roundId: any) {
    if (strokePlayedIds && strokePlayedIds.length > 0) {
      this.logger.log(`CLEAR STROKE PLAYED IN ${strokePlayedIds}`);
      await this.strokePlayedRepo.delete({ id: In(strokePlayedIds), round_id: roundId });
    }
    if (strokeStatsIds && strokeStatsIds.length > 0) {
      this.logger.log(`CLEAR STROKE STATS IN ${strokeStatsIds}`);
      await this.strokeStatRepo.delete({ id: In(strokeStatsIds), round_id: roundId });
    }
    if (playerScoreIds && playerScoreIds.length > 0) {
      this.logger.log(`CLEAR PLAYER SCORE IN ${playerScoreIds}`);
      await this.playerScoreRepo.delete({ id: In(playerScoreIds), round_id: roundId });
    }
  }

  async canSwitchAdvancedMode(round) {
    if (round.round_mode != ROUND.ROUND_MODE_BASIC) {
      return false;
    }
    let totalHolesPlayedScore = 0;
    const holes = await this.holePlayedService.findHolesInRounds([round.id]);
    for (const hole of holes) {
      const hole_score = await this.holePlayedService.score(ROUND.ROUND_MODE_BASIC, hole);
      if (hole_score > 0) {
        totalHolesPlayedScore += hole_score;
      }
    }
    let totalStrokes: any = await this.strokesPlayerService.findBy({ where: { round_id: round.id }, select: ['id'] });
    totalStrokes = totalStrokes?.length || 0;
    this.logger.log(
      `canSwitchAdvancedMode: totalStrokes: ${totalStrokes}, totalHolesPlayedScore: ${totalHolesPlayedScore}`
    );
    if (totalHolesPlayedScore == 0 || totalStrokes == 0) {
      return false;
    }

    return totalStrokes >= totalHolesPlayedScore;
  }

  listPutts(strokes) {
    const putts = strokes?.filter((stroke: any) => includeStr(stroke['starting_lie'], 'green'));
    return putts;
  }

  // create a hole for the round
  //
  async createHole(hole: any, round, forceMapCourse = null) {
    // round.holes_played.create!( create_hole_attributes(hole) )
    this.logger.log(`CREATE HOLE ROUND: ${round.id}`);
    const holeAttr = await this.roundAuditUtilService.createHoleAttributes(hole, round, forceMapCourse);
    return await this.holePlayerService.create(holeAttr);
  }

  //
  // creates a new stroke for the given hole
  //
  async createStroke(stroke, roundId, holePlayerId, pinLocation) {
    // new_hole.strokes_played.create!( create_stroke_attributes(stroke) )
    this.logger.log(`CREATE STROKE FOR HOLE ${holePlayerId}: ROUND: ${roundId}`);
    const strokeAttr = await this.roundAuditUtilService.createStrokeAttributes(
      stroke,
      roundId,
      holePlayerId,
      pinLocation
    );
    return await this.strokesPlayerService.create(strokeAttr);
  }

  //
  // create a player_score for the round
  //
  async createPlayerScore(hole, player_score, roundId) {
    // hole.player_score.create!(
    //   this.roundAuditUtilService.create_player_score_attributes(hole, player_score)
    // );
    this.logger.log(`CREATE PLAYER SCORE: ROUND: ${roundId}`);
    return await this.playerService.createPlayerScore(
      this.roundAuditUtilService.createPlayerScoreAttributes(hole, player_score, roundId)
    );
  }

  //
  // create a player  for the round
  //
  async createPlayer(player, roundId) {
    // round.player.create!(this.roundAuditUtilService.create_player_attributes(player));
    this.logger.log(`CREATE PLAYER: ROUND: ${roundId}`);
    return await this.playerService.createPlayer(this.roundAuditUtilService.createPlayerAttributes(player, roundId));
  }
}
