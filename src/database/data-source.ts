import 'reflect-metadata';
import { DataSource, DataSourceOptions } from 'typeorm';

const databaseCredentials = {
  port: parseInt(process.env.DATABASE_PORT, 10) || 5432,
  username: process.env.DATABASE_USERNAME,
  password: process.env.DATABASE_PASSWORD,
  database: process.env.DATABASE_NAME,
};

export const AppDataSource = new DataSource({
  type: process.env.DATABASE_TYPE,
  synchronize: process.env.DATABASE_SYNCHRONIZE === 'true',
  dropSchema: false,
  keepConnectionAlive: true,
  host: process.env.DATABASE_HOST,
  ...databaseCredentials,
  migrationsRun: process.env.NODE_ENV === 'production',
  logging: process.env.NODE_ENV !== 'production',
  logger: 'file',
  entities: [__dirname + '/../**/*.entity{.ts,.js}'],
  migrations: [__dirname + '/migrations/**/*{.ts,.js}'],
  cli: {
    entitiesDir: 'src',
    migrationsDir: 'src/database/migrations',
    subscribersDir: 'subscriber',
  },
  replication: {
    master: {
      host: process.env.DATABASE_HOST,
      ...databaseCredentials,
    },
    slaves: process.env.DATABASE_SLAVE_HOSTS.split(',').map((host, i) => ({
      host,
      ...databaseCredentials,
      username: process.env.DATABASE_SLAVE_USERNAMES.split(',')[i],
    })),
  },
  extra: {
    max: parseInt(process.env.DATABASE_MAX_CONNECTIONS, 10) || 100,
    ssl:
      process.env.DATABASE_SSL_ENABLED === 'true'
        ? {
            rejectUnauthorized: process.env.DATABASE_REJECT_UNAUTHORIZED === 'true',
            ca: process.env.DATABASE_CA ?? undefined,
            key: process.env.DATABASE_KEY ?? undefined,
            cert: process.env.DATABASE_CERT ?? undefined,
          }
        : undefined,
  },
} as DataSourceOptions);
