import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateCourseErrorTable1743153608128 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE SEQUENCE course_error_id_seq INCREMENT 1 START 1;
    `);
    await queryRunner.query(
      `CREATE TABLE "course_error" (
            "id" int4 NOT NULL DEFAULT nextval('course_error_id_seq' :: regclass),
            "course_id" character varying(30), 
            "course_name" character varying(255),
            "type" character varying(30),
            "is_not_course_detail" bool DEFAULT false,
            "is_not_ghin_id" bool DEFAULT false,
            "is_not_tees" bool DEFAULT false,
            "is_not_gps_detail" bool DEFAULT false,
            "is_not_gps_vector_detail" bool DEFAULT false,
            "is_not_score_card_detail" bool DEFAULT false,
            "created_at" TIMESTAMP NOT NULL DEFAULT now(),
            "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_PRIMARY_COURSE_ERROR_ID" PRIMARY KEY ("id")
        )`
    );
    await queryRunner.query(`CREATE INDEX "IDX_COURSE_ID" ON "course_error" ("course_id")`);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-empty-function
  public async down(queryRunner: QueryRunner): Promise<void> {}
}
