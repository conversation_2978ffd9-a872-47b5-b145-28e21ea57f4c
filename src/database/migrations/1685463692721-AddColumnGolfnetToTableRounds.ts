import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnGolfnetToTableRounds1685463692721 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `
                ALTER TABLE rounds
                ADD COLUMN golfnet_round_id character varying(30), 
                ADD COLUMN golfnet_course_id character varying(30), 
                ADD COLUMN golfnet_course_name character varying(100),
                ADD COLUMN golfnet_tee_set_id character varying(30),
                ADD COLUMN golfnet_tee_set_name character varying(100)
                 ;
          `
    );
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-empty-function
  public async down(queryRunner: QueryRunner): Promise<void> {}
}
