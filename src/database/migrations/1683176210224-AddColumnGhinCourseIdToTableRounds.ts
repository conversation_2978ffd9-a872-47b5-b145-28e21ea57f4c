import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnGhinCourseIdToTableRounds1683176210224 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `
            ALTER TABLE rounds
            ADD COLUMN ghin_course_id character varying(30), 
            ADD COLUMN ghin_course_name character varying(100),
            ADD COLUMN ghin_tee_set_id character varying(30),
            ADD COLUMN ghin_tee_set_name character varying(100)
             ;
      `
    );
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-empty-function
  public async down(queryRunner: QueryRunner): Promise<void> {}
}
