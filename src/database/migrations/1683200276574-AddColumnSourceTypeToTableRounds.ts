import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnSourceTypeToTableRounds1683200276574 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `
        ALTER TABLE rounds
        ADD COLUMN source_type character varying(10);
     `
    );
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-empty-function
  public async down(queryRunner: QueryRunner): Promise<void> {}
}
