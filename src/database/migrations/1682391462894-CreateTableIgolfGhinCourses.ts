import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTableIgolfGhinCourses1682391462894 implements MigrationInterface {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-empty-function
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE SEQUENCE igolf_ghin_course_id_seq INCREMENT 1 START 1;
    `);
    await queryRunner.query(
      `CREATE TABLE "igolf_ghin_courses" (
            "id" int4 NOT NULL DEFAULT nextval('igolf_ghin_course_id_seq' :: regclass),
            "igolf_course_id" character varying(60) NOT NULL, 
            "ghin_course_id" character varying(30) NOT NULL, 
            "ghin_course_name" character varying(255) NOT NULL, 
            "created_at" TIMESTAMP NOT NULL DEFAULT now(), 
            "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_PRIMARY_IGOLF_GHIN_COURSE" PRIMARY KEY ("id")
        )`
    );
    await queryRunner.query(`CREATE INDEX "IDX_IGOLF_GHIN_COURSE_ID" ON "igolf_ghin_courses" ("id")`);

    await queryRunner.query(
      `CREATE INDEX "IDX_IGOLF_GHIN_COURSE_IGOLF_COURSE_ID" ON "igolf_ghin_courses" ("igolf_course_id")`
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_IGOLF_GHIN_COURSE_GHIN_COURSE_ID" ON "igolf_ghin_courses" ("ghin_course_id")`
    );
  }
  // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-empty-function
  public async down(queryRunner: QueryRunner): Promise<void> {}
}
