import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnsTimeUtcToTableUserLogCourse1721036841347 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `
                ALTER TABLE golf_net_rounds
                ADD COLUMN user_timezone int4,
                ADD COLUMN played_on_utc TIMESTAMP;
            `
    );
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-empty-function
  public async down(queryRunner: QueryRunner): Promise<void> {}
}
