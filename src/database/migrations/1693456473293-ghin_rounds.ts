import { MigrationInterface, QueryRunner } from 'typeorm';

export class ghinRounds1693456473293 implements MigrationInterface {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-empty-function
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE SEQUENCE ghin_rounds_id_seq INCREMENT 1 START 1;
    `);
    await queryRunner.query(
      `CREATE TABLE "ghin_rounds" (
            "id" int4 NOT NULL DEFAULT nextval('ghin_rounds_id_seq' :: regclass),
            "ghin_id" int4 NOT NULL, 
            "ghin_round_id" int4 NOT NULL, 
            "ghin_parent_round_id" int4, 
            "ghin_course_id" character varying(30) NOT NULL, 
            "ghin_course_name" character varying(255) NOT NULL, 
            "ghin_tee_set_id" character varying(30),
            "ghin_tee_set_name" character varying(100),
            "ghin_tee_set_side" character varying(10),
            "course_rating" float,
            "score" integer,
            "slope_rating" integer,
            "score_type" character varying(10),
            "created_at" TIMESTAMP NOT NULL DEFAULT now(), 
            "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_PRIMARY_GHIN_ROUNDS" PRIMARY KEY ("id")
        )`
    );
    await queryRunner.query(`CREATE INDEX "IDX_GHIN_ROUNDS_ID" ON "ghin_rounds" ("id")`);

    await queryRunner.query(`CREATE INDEX "IDX_GHIN_ROUNDS_GHIN_ID" ON "ghin_rounds" ("ghin_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_GHIN_ROUNDS_GHIN_ROUND_ID" ON "ghin_rounds" ("ghin_round_id")`);
  }
  // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-empty-function
  public async down(queryRunner: QueryRunner): Promise<void> {}
}
