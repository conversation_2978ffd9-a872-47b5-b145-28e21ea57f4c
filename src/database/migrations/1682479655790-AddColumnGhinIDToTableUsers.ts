import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnGhinIDToTableUsers1682479655790 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `
        ALTER TABLE users
        ADD ghin_id int4;
      `
    );
  }

  // eslint-disable-next-line @typescript-eslint/no-empty-function, @typescript-eslint/no-unused-vars
  public async down(queryRunner: QueryRunner): Promise<void> {}
}
