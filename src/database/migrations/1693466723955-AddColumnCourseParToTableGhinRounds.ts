import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnCourseParToTableGhinRounds1693466723955 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `
            ALTER TABLE ghin_rounds
            ADD COLUMN course_par int4,
            ADD COLUMN course_yards int4,
            ADD COLUMN generated_by character varying(30),
            ADD COLUMN score_to_par int4;
        `
    );
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-empty-function
  public async down(queryRunner: QueryRunner): Promise<void> {}
}
