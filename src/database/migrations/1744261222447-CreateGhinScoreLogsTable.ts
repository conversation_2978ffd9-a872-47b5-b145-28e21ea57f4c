import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateGhinScoreLogsTable1744261222447 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE SEQUENCE ghin_score_log_id_seq INCREMENT 1 START 1;
    `);

    await queryRunner.query(`
      CREATE TABLE "ghin_score_logs" (
          "id" int4 NOT NULL DEFAULT nextval('ghin_score_log_id_seq' :: regclass),
          "round_id" INTEGER,
          "golfer_id" INTEGER,
          "score_detail" JSONB,
          "score_with_hole_detail" JSONB,
          "is_post_hole_by_hole" BOOLEAN,
          "response" JSONB,
          "created_at" TIMESTAMP DEFAULT now(),
          "updated_at" TIMESTAMP DEFAULT now(),
          CONSTRAINT "PK_PRIMARY_GHIN_SCORE_LOG_ID" PRIMARY KEY ("id")
      );
  `);
  }

  public async down(queryRunner: Query<PERSON>unner): Promise<void> {
    await queryRunner.query(`DROP TABLE "ghin_score_logs";`);
  }
}
