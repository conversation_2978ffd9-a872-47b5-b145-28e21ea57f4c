import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnPlayedOnToTaleGhinRounds1694059269564 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `
                ALTER TABLE ghin_rounds
                ADD COLUMN played_on TIMESTAMP;
            `
    );
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-empty-function
  public async down(queryRunner: QueryRunner): Promise<void> {}
}
