import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnGhinEmailToTableUser1684744586980 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `
      ALTER TABLE users
      ADD COLUMN ghin_email character varying(60);
    `
    );
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-empty-function
  public async down(queryRunner: QueryRunner): Promise<void> {}
}
