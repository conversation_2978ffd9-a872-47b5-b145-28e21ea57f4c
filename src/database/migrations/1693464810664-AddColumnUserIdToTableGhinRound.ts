import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnUserIdToTableGhinRound1693464810664 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `
            ALTER TABLE ghin_rounds
            ADD COLUMN user_id int4,
            ADD COLUMN number_of_holes_played int4;
        `
    );
    await queryRunner.query(`CREATE INDEX "IDX_GHIN_ROUNDS_BY_USER_ID" ON "ghin_rounds" ("user_id")`);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-empty-function
  public async down(queryRunner: QueryRunner): Promise<void> {}
}
