import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnCanadaCardIdToTableUsers1685377176579 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `
            ALTER TABLE users
            ADD canada_card_id character varying(20);
          `
    );
  }

  // eslint-disable-next-line @typescript-eslint/no-empty-function, @typescript-eslint/no-unused-vars
  public async down(queryRunner: QueryRunner): Promise<void> {}
}
