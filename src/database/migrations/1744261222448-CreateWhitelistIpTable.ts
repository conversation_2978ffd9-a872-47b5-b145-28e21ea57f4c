import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateWhitelistIpTable1744261222448 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE SEQUENCE whitelist_ip_id_seq INCREMENT 1 START 1;
    `);

    await queryRunner.query(`
      CREATE TABLE "whitelist_ips" (
          "id" int4 NOT NULL DEFAULT nextval('whitelist_ip_id_seq' :: regclass),
          "partner_name" character varying(255) NOT NULL,
          "ip_address" character varying(45) NOT NULL,
          "created_at" TIMESTAMP NOT NULL DEFAULT now(),
          "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
          CONSTRAINT "PK_PRIMARY_WHITELIST_IP_ID" PRIMARY KEY ("id")
      );
    `);

    await queryRunner.query(`CREATE INDEX "IDX_WHITELIST_IP_ADDRESS" ON "whitelist_ips" ("ip_address")`);
    await queryRunner.query(
      `CREATE INDEX "IDX_WHITELIST_IP_PARTNER" ON "whitelist_ips" ("ip_address", "partner_name")`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "whitelist_ips";`);
    await queryRunner.query(`DROP SEQUENCE whitelist_ip_id_seq;`);
  }
}
