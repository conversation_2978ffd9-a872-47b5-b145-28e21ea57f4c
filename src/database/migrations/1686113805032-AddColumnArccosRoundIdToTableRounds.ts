import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnArccosRoundIdToTableRounds1686113805032 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `
            ALTER TABLE rounds
            ADD COLUMN arccos_round_id character varying(20);
         `
    );
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-empty-function
  public async down(queryRunner: QueryRunner): Promise<void> {}
}
