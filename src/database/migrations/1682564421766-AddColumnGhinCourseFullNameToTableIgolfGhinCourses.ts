import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnGhinCourseFullNameToTableIgolfGhinCourses1682564421766 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `
          ALTER TABLE igolf_ghin_courses
          ADD ghin_course_full_name character varying(255);
        `
    );
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-empty-function
  public async down(queryRunner: QueryRunner): Promise<void> {}
}
