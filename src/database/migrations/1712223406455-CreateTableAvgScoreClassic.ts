import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTableAvgScoreClassic1712223406455 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE SEQUENCE average_scores_classic_id_seq INCREMENT 1 START 1;
        `);
    await queryRunner.query(
      `CREATE TABLE "average_scores_classic" (
                "id" int4 NOT NULL DEFAULT nextval('average_scores_classic_id_seq' :: regclass),
                "user_id" int4 NOT NULL, 
                "average_score" float NULL, 
                "lowest_score" float NULL, 
                "highest_score" float NULL, 
                "total_rounds" int4 NULL, 
                "created_at" TIMESTAMP NOT NULL DEFAULT now(), 
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
            CONSTRAINT "PK_PRIMARY_AVERAGE_SCORES_CLASSIC" PRIMARY KEY ("id")
            )`
    );
    await queryRunner.query(`CREATE INDEX "IDX_AVERAGE_SCORES_CLASSIC_ID" ON "average_scores_classic" ("id")`);
    await queryRunner.query(
      `CREATE INDEX "IDX_AVERAGE_SCORES_CLASSIC_USER_ID" ON "average_scores_classic" ("user_id")`
    );
  }
  // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-empty-function
  public async down(queryRunner: QueryRunner): Promise<void> {}
}
