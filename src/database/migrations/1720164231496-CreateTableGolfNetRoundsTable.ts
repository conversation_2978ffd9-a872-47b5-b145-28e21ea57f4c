import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTableGolfNetRoundsTable1720164231496 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE SEQUENCE golf_net_rounds_id_seq INCREMENT 1 START 1;
    `);
    await queryRunner.query(
      `CREATE TABLE "golf_net_rounds" (
            "id" int4 NOT NULL DEFAULT nextval('golf_net_rounds_id_seq' :: regclass),
            "user_id" int4 NOT NULL, 
            "canada_card_id" character varying(30), 
            "golfnet_round_id" character varying(30), 
            "golfnet_course_id" character varying(30), 
            "golfnet_course_name" character varying(255), 
            "golfnet_tee_set_id" character varying(30),
            "golfnet_tee_set_name" character varying(100),
            "score" integer,
            "score_to_par" integer,
            "generated_by" character varying(30),
            "course_rating" float,
            "slope_rating" integer,
            "course_yards" integer,
            "course_par" integer,
            "number_of_holes_played" integer,
            "score_type" character varying(10),
            "played_on" TIMESTAMP,
            "created_at" TIMESTAMP NOT NULL DEFAULT now(),
            "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_PRIMARY_GOLF_NET_ROUNDS" PRIMARY KEY ("id")
        )`
    );
    await queryRunner.query(`CREATE INDEX "IDX_GOLF_NET_ROUNDS_BY_USER_ID" ON "golf_net_rounds" ("user_id")`);
    await queryRunner.query(
      `CREATE INDEX "IDX_GOLF_NET_ROUNDS_CANADA_CARD_ID" ON "golf_net_rounds" ("canada_card_id")`
    );
    await queryRunner.query(`CREATE INDEX "IDX_GOLF_NET_ROUND_ID" ON "golf_net_rounds" ("golfnet_round_id")`);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-empty-function
  public async down(queryRunner: QueryRunner): Promise<void> {}
}
