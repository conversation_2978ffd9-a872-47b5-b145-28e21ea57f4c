import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTableIGolfGolfNetCourses1685377994462 implements MigrationInterface {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-empty-function
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE SEQUENCE igolf_golfnet_course_id_seq INCREMENT 1 START 1;
    `);
    await queryRunner.query(
      `CREATE TABLE "igolf_golfnet_courses" (
            "id" int4 NOT NULL DEFAULT nextval('igolf_golfnet_course_id_seq' :: regclass),
            "igolf_course_id" character varying(60) NOT NULL, 
            "golfnet_course_id" character varying(30) NOT NULL, 
            "golfnet_course_name" character varying(255) NOT NULL, 
            "created_at" TIMESTAMP NOT NULL DEFAULT now(), 
            "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_PRIMARY_IGOLF_GOLF_NET_COURSE" PRIMARY KEY ("id")
        )`
    );
    await queryRunner.query(`CREATE INDEX "IDX_IGOLF_GOLF_NET_COURSE_ID" ON "igolf_golfnet_courses" ("id")`);

    await queryRunner.query(
      `CREATE INDEX "IDX_IGOLF_GOLF_NET_COURSE_IGOLF_COURSE_ID" ON "igolf_golfnet_courses" ("igolf_course_id")`
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_IGOLF_GOLF_NET_COURSE_GHIN_COURSE_ID" ON "igolf_golfnet_courses" ("golfnet_course_id")`
    );
  }
  // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-empty-function
  public async down(queryRunner: QueryRunner): Promise<void> {}
}
