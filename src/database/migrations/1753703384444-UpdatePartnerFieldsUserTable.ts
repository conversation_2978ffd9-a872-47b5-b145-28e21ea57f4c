import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdatePartnerFieldsUserTable1753703384444 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `
                ALTER TABLE users
                ADD COLUMN partner_name character varying(50),
                ADD COLUMN partner_user_id character varying(255),
                ADD COLUMN partner_user_email character varying(255);
            `
    );
  }
  // eslint-disable-next-line @typescript-eslint/no-unused-vars,@typescript-eslint/no-empty-function
  public async down(queryRunner: QueryRunner): Promise<void> {}
}
