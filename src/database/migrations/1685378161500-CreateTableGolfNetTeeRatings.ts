import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTableGolfNetTeeRatings1685378161500 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
                CREATE SEQUENCE golfnet_tee_ratings_id_seq INCREMENT 1 START 1;
            `);
    await queryRunner.query(
      `CREATE TABLE "golfnet_tee_ratings" (
                    "id" int4 NOT NULL DEFAULT nextval('golfnet_tee_ratings_id_seq' :: regclass),
                    "golfnet_course_id" character varying(30) NOT NULL, 
                    "tee_set_rating_name" character varying(255) NOT NULL, 
                    "golfnet_tee_rating_id" character varying(30) NOT NULL, 
                    "gender" character varying(10), 
                    "holes_number" int4, 
                    "total_yard_age" int4, 
                    "total_par" int4, 
                    "tee_rating_detail" jsonb, 
                    "created_at" TIMESTAMP NOT NULL DEFAULT now(), 
                    "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_PRIMARY_GOLF_NET_TEE_RATINGS" PRIMARY KEY ("id")
                )`
    );
    await queryRunner.query(`CREATE INDEX "IDX_GOLF_NET_TEE_RATINGS_ID" ON "golfnet_tee_ratings" ("id")`);
    await queryRunner.query(
      `CREATE INDEX "IDX_GOLF_NET_TEE_RATINGS_GOLF_NET_COURSE_ID" ON "golfnet_tee_ratings" ("golfnet_course_id")`
    );
  }
  // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-empty-function
  public async down(queryRunner: QueryRunner): Promise<void> {}
}
