import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTableUserLogCourse1715400763371 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE SEQUENCE user_log_courses_id_seq INCREMENT 1 START 1;
        `);
    await queryRunner.query(
      `CREATE TABLE "user_log_courses" (
                "id" int4 NOT NULL DEFAULT nextval('user_log_courses_id_seq' :: regclass),
                "user_id" int4 NULL, 
                "request_time" TIMESTAMP NULL, 
                "params" jsonb NULL, 
                "client_id" character varying(255) NULL,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(), 
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
            CONSTRAINT "PK_PRIMARY_USER_LOG_COURSES" PRIMARY KEY ("id")
            )`
    );
    await queryRunner.query(`CREATE INDEX "IDX_USER_ID" ON "user_log_courses" ("user_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_TIME_REQUEST" ON "user_log_courses" ("request_time")`);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars,@typescript-eslint/no-empty-function
  public async down(queryRunner: QueryRunner): Promise<void> {}
}
