import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddGhinScoreColumnToTableRounds1690423009854 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `
              ALTER TABLE rounds
              ADD COLUMN ghin_score int4;
           `
    );
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-empty-function
  public async down(queryRunner: QueryRunner): Promise<void> {}
}
