import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTableIGolfLogRequest1710501155106 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE SEQUENCE igolf_log_request_id_seq INCREMENT 1 START 1;
        `);
    await queryRunner.query(
      `CREATE TABLE "igolf_log_requests" (
                "id" int4 NOT NULL DEFAULT nextval('igolf_log_request_id_seq' :: regclass),
                "igolf_course_id" character varying(60) NOT NULL, 
                "request_type" character varying(255) NOT NULL, 
                "client" character varying(20) NULL, 
                "count" integer NULL, 
                "created_at" TIMESTAMP NOT NULL DEFAULT now(), 
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
            CONSTRAINT "PK_IGOLF_LOG_REQUESTS" PRIMARY KEY ("id")
            )`
    );
    await queryRunner.query(`CREATE INDEX "IDX_IGOLF_LOG_REQUESTS_ID" ON "igolf_log_requests" ("id")`);
  }
  // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-empty-function
  public async down(queryRunner: QueryRunner): Promise<void> {}
}
