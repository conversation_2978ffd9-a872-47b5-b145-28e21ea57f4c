import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTableGhinTeeRatings1682394864676 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE SEQUENCE ghin_tee_ratings_id_seq INCREMENT 1 START 1;
        `);
    await queryRunner.query(
      `CREATE TABLE "ghin_tee_ratings" (
                "id" int4 NOT NULL DEFAULT nextval('ghin_tee_ratings_id_seq' :: regclass),
                "ghin_course_id" character varying(30) NOT NULL, 
                "tee_set_rating_name" character varying(255) NOT NULL, 
                "ghin_tee_rating_id" character varying(30) NOT NULL, 
                "gender" character varying(10), 
                "holes_number" int4, 
                "total_yard_age" int4, 
                "total_par" int4, 
                "tee_rating_detail" jsonb, 
                "created_at" TIMESTAMP NOT NULL DEFAULT now(), 
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
            CONSTRAINT "PK_PRIMARY_GHIN_TEE_RATINGS" PRIMARY KEY ("id")
            )`
    );
    await queryRunner.query(`CREATE INDEX "IDX_GHIN_TEE_RATINGS_ID" ON "ghin_tee_ratings" ("id")`);
    await queryRunner.query(
      `CREATE INDEX "IDX_GHIN_TEE_RATINGS_GHIN_COURSE_ID" ON "ghin_tee_ratings" ("ghin_course_id")`
    );
  }
  // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-empty-function
  public async down(queryRunner: QueryRunner): Promise<void> {}
}
