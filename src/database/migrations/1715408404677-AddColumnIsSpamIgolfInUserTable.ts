import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnIsSpamIgolfInUserTable1715408404677 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `
                ALTER TABLE users
                ADD is_spam_igolf bool DEFAULT false;
            `
    );
  }
  // eslint-disable-next-line @typescript-eslint/no-unused-vars,@typescript-eslint/no-empty-function
  public async down(queryRunner: QueryRunner): Promise<void> {}
}
