import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnIsUserIgolfBlockedInUserTable1715421984228 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `
                ALTER TABLE users
                ADD is_user_igolf_blocked bool DEFAULT false;
            `
    );
  }
  // eslint-disable-next-line @typescript-eslint/no-unused-vars,@typescript-eslint/no-empty-function
  public async down(queryRunner: QueryRunner): Promise<void> {}
}
