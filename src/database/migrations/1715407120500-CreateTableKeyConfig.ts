import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTableKeyConfig1715407120500 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE SEQUENCE key_config_id_seq INCREMENT 1 START 1;
        `);
    await queryRunner.query(
      `CREATE TABLE "key_config" (
                "id" int4 NOT NULL DEFAULT nextval('key_config_id_seq' :: regclass),
                "key" character varying(255) NOT NULL, 
                "value" character varying(255) NOT NULL, 
                "created_at" TIMESTAMP NOT NULL DEFAULT now(), 
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
            CONSTRAINT "PK_PRIMARY_KEY_CONFIG" PRIMARY KEY ("id")
            )`
    );
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars,@typescript-eslint/no-empty-function
  public async down(queryRunner: QueryRunner): Promise<void> {}
}
