/* eslint-disable @typescript-eslint/no-unused-vars */
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreateGhinTeeRatingDto } from './dto/create-ghin-tee-rating.dto';
import { UpdateGhinTeeRatingDto } from './dto/update-ghin-tee-rating.dto';
import { GhinTeeRating } from './entities/ghin-tee-rating.entity';

@Injectable()
export class GhinTeeRatingService {
  constructor(
    @InjectRepository(GhinTeeRating)
    private ghinTeeRating: Repository<GhinTeeRating>
  ) {}
  create(createGhinTeeRatingDto: CreateGhinTeeRatingDto) {
    return this.ghinTeeRating.save(this.ghinTeeRating.create(createGhinTeeRatingDto));
  }

  findOne(field: any) {
    return this.ghinTeeRating.findOneBy(field);
  }
  findBy(field: any) {
    return this.ghinTeeRating.findBy(field);
  }

  remove(field: any) {
    return this.ghinTeeRating.delete(field);
  }
}
