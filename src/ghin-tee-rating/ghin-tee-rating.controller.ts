import { Body, Controller, Delete, Get, Param, Post } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { CreateGhinTeeRatingDto } from './dto/create-ghin-tee-rating.dto';
import { GhinTeeRatingService } from './ghin-tee-rating.service';

@Controller('ghin-tee-rating')
@ApiTags('GHIN Tee Rating')
export class GhinTeeRatingController {
  constructor(private readonly ghinTeeRatingService: GhinTeeRatingService) {}

  @Post()
  create(@Body() createGhinTeeRatingDto: CreateGhinTeeRatingDto) {
    return this.ghinTeeRatingService.create(createGhinTeeRatingDto);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.ghinTeeRatingService.findOne(+id);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.ghinTeeRatingService.remove(+id);
  }
}
