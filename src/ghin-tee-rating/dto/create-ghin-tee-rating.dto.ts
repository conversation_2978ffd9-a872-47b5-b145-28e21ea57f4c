import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

export class CreateGhinTeeRatingDto {
  @ApiProperty({ example: 'string' })
  @IsNotEmpty()
  ghin_course_id: string;

  @ApiProperty({ example: 'string' })
  @IsNotEmpty()
  tee_set_rating_name: string;

  @ApiProperty({ example: 'string' })
  @IsNotEmpty()
  ghin_tee_rating_id: string;

  @ApiProperty({ example: 'string' })
  @IsNotEmpty()
  gender: string;

  @ApiProperty({ example: 1 })
  @IsNotEmpty()
  holes_number: number;

  @ApiProperty({ example: 1 })
  @IsNotEmpty()
  total_yard_age: number;

  @ApiProperty({ example: 1 })
  @IsNotEmpty()
  total_par: number;

  @ApiProperty({ example: 'string' })
  @IsNotEmpty()
  tee_rating_detail: string;
}
