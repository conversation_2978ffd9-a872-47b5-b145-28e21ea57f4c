import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PlayerScoreDto } from './dto/player-score.dto';
import { PlayerDto } from './dto/player.dto';
import { PlayerScore } from './entities/player-score.entity';
import { Player } from './entities/player.entity';

@Injectable()
export class PlayersService {
  constructor(
    @InjectRepository(Player)
    private playerRepository: Repository<Player>,
    @InjectRepository(PlayerScore)
    private playerScoreRepository: Repository<PlayerScore>
  ) {}
  async createPlayer(createPlayerDto: PlayerDto) {
    return await this.playerRepository.save(this.playerRepository.create(createPlayerDto));
  }

  async createPlayerScore(playerScoreDto: PlayerScoreDto) {
    return await this.playerScoreRepository.save(this.playerScoreRepository.create(playerScoreDto));
  }
  findPlayedScoreByHole(holeId) {
    return this.playerScoreRepository.find({ where: { hole_played_id: holeId }, select: ['hole_score', 'player_id'] });
  }
  findAll() {
    return `This action returns all players`;
  }

  findOne(id: number) {
    return `This action returns a #${id} player`;
  }
  async findBy(options: any) {
    return await this.playerRepository.find(options);
  }

  remove(id: number) {
    return `This action removes a #${id} player`;
  }
}
