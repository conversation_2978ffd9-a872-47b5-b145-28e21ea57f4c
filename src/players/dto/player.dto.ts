import { ApiProperty } from '@nestjs/swagger';
import { IsOptional } from 'class-validator';

export class PlayerDto {
  @IsOptional()
  @ApiProperty({ example: 1 })
  id: number;

  @IsOptional()
  @ApiProperty({ example: 1 })
  round_id: number;

  @IsOptional()
  @ApiProperty({ example: 1 })
  player_id: number;

  @IsOptional()
  @ApiProperty({ example: 'string' })
  player_name: string;

  @IsOptional()
  @ApiProperty({ example: 'string' })
  team: string;

  @IsOptional()
  @ApiProperty({ example: 1 })
  user_id: number;

  @IsOptional()
  @ApiProperty({ example: 'string' })
  player_email: string;

  @IsOptional()
  @ApiProperty({ example: 'string' })
  player_initials: string;

  @IsOptional()
  @ApiProperty({ example: 1 })
  stroke_received: number;

  @ApiProperty({ example: new Date().toISOString() })
  created_at: Date;

  @ApiProperty({ example: new Date().toISOString() })
  updated_at: Date;
}
