import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional } from 'class-validator';

export class PlayerScoreDto {
  @IsOptional()
  @ApiProperty({ example: 1 })
  id: number;

  @IsNotEmpty()
  @ApiProperty({ example: 1 })
  round_id: number;

  @IsOptional()
  @ApiProperty({ example: 1 })
  hole_played_id: number;

  @IsNotEmpty()
  @ApiProperty({ example: 1 })
  player_id: number;

  @IsNotEmpty()
  @ApiProperty({ example: 1 })
  hole_number: number;

  @IsNotEmpty()
  @ApiProperty({ example: 1 })
  hole_score: number;

  @ApiProperty({ example: new Date().toISOString() })
  created_at: Date;

  @ApiProperty({ example: new Date().toISOString() })
  updated_at: Date;
}
