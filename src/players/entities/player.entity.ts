import { ApiProperty } from '@nestjs/swagger';
import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

@Entity({ name: 'players' })
export class Player {
  @PrimaryGeneratedColumn()
  @ApiProperty({ example: 1 })
  id: number;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  round_id: number;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  player_id: number;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  player_name: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  team: string;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  user_id: number;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  player_email: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  player_initials: string;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  stroke_received: number;

  @CreateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  created_at: Date;

  @UpdateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  updated_at: Date;
}
