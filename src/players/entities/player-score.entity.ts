import { ApiProperty } from '@nestjs/swagger';
import {
  <PERSON>umn,
  CreateDateColumn,
  Entity,
  JoinColumn,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { HolePlayed } from 'src/holes-played/entities/hole-played.entity';
import { Player } from './player.entity';

@Entity({ name: 'player_scores' })
export class PlayerScore {
  @PrimaryGeneratedColumn()
  @ApiProperty({ example: 1 })
  id: number;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  round_id: number;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  hole_played_id: number;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  player_id: number;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  hole_number: number;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  hole_score: number;

  @CreateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  created_at: Date;

  @UpdateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  updated_at: Date;

  @OneToOne(() => HolePlayed, (holePlayed) => holePlayed.id, {
    eager: false,
  })
  @JoinColumn({ name: 'hole_played_id', referencedColumnName: 'id' })
  hole_played?: HolePlayed;

  @OneToOne(() => Player, (player) => player.id, {
    eager: false,
  })
  @JoinColumn({ name: 'player_id', referencedColumnName: 'id' })
  player?: Player;
}
