import { Module } from '@nestjs/common';
import { SharedModule } from 'src/shared/shared.module';
import { IsExist } from 'src/utils/validators/is-exists.validator';
import { IsNotExist } from 'src/utils/validators/is-not-exists.validator';
import { UserCronService } from './user.cron.service';
import { UsersController } from './users.controller';

@Module({
  imports: [SharedModule],
  controllers: [UsersController],
  providers: [IsExist, IsNotExist, UserCronService],
  exports: [],
})
export class UsersModule {}
