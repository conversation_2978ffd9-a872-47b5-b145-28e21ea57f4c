import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsDateString, IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export enum StrokesGainedBaseline {
  PRO = 'pro',
  SCRATCH = 'scratch',
  FIVE = '5',
  TEN = '10',
  FIFTEEN = '15',
  TWENTY = '20',
}

export class StrokesGainedQueryDto {
  @ApiProperty({
    example: StrokesGainedBaseline.SCRATCH,
  })
  @IsNotEmpty()
  @IsEnum(StrokesGainedBaseline)
  strokes_gained_baseline: string;

  @ApiProperty({
    example: true,
  })
  @IsOptional()
  @IsString()
  last_round?: string;

  @ApiProperty({
    example: '100,102',
  })
  @IsOptional()
  @IsString()
  round_ids?: string;

  @ApiProperty({
    example: '2025-05-01',
  })
  @IsOptional()
  @IsDateString()
  @Transform(({ value }) => value?.trim())
  start_date?: string;

  @ApiProperty({
    example: '2025-05-01',
  })
  @IsOptional()
  @IsDateString()
  @Transform(({ value }) => value?.trim())
  end_date?: string;
}
