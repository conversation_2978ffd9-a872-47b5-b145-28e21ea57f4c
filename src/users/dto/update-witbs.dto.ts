import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

export class UpdateWitbsDTO {
  @ApiProperty({ example: [] })
  @IsNotEmpty()
  witbs: WitbDTO[];

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ example: 'uid' })
  golferProfileId: string;
}
export class WitbDTO {
  @ApiProperty({ example: 'uid' })
  @IsNotEmpty()
  witbId: string;

  @ApiProperty({ example: 'false' })
  @IsNotEmpty()
  isDelete: boolean;
}
