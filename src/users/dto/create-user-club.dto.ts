import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { IsDefined, IsNotEmptyObject, IsNumber, IsObject, IsOptional, ValidateNested } from 'class-validator';

export class UserClubDto {
  @ApiProperty({ example: 'PW' })
  @Transform(({ value }) => value?.trim())
  @IsOptional()
  club_type: string;

  @ApiProperty({ example: '2\\" Long' })
  @Transform(({ value }) => value?.trim())
  @IsOptional()
  shaft_length: string;

  @ApiProperty({ example: 'X-Stiff' })
  @Transform(({ value }) => value?.trim())
  @IsOptional()
  shaft_flex: string;

  @ApiProperty({ example: '4° Up' })
  @Transform(({ value }) => value?.trim())
  @IsOptional()
  face_lie_adjustment: string;

  @ApiProperty({ example: '2° Strong' })
  @Transform(({ value }) => value?.trim())
  @IsOptional()
  face_loft_adjustment: string;

  @ApiProperty({ example: 'string' })
  @Transform(({ value }) => value?.trim())
  @IsOptional()
  loft: string;

  @ApiProperty({ example: 'TaylorMade' })
  @Transform(({ value }) => value?.trim())
  @IsOptional()
  manufacturer: string;

  @ApiProperty({ example: 'P790 2019' })
  @Transform(({ value }) => value?.trim())
  @IsOptional()
  modelname: string;

  @ApiProperty({ example: 'iron' })
  @Transform(({ value }) => value?.trim())
  @IsOptional()
  club_family: string;

  @ApiProperty({ example: true })
  @IsOptional()
  in_bag: boolean;

  @ApiProperty({ example: true })
  @IsOptional()
  disabled: boolean;

  @IsOptional()
  user_id: number;
}

export class CreateUserClubDto {
  @IsOptional()
  @IsDefined()
  @IsNotEmptyObject()
  @IsObject()
  @ValidateNested()
  @Type(() => UserClubDto)
  club: UserClubDto;
}

export class ActivateUserClubDto {
  @IsNumber({}, { each: true })
  club_ids: number[];
  @IsOptional()
  inactive?: any;
}
