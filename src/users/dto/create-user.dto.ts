import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
  IsBoolean,
  IsDefined,
  IsEmail,
  IsEnum,
  IsNotEmpty,
  IsNotEmptyObject,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  Matches,
  MinLength,
  ValidateNested,
} from 'class-validator';

export enum UserCountry {
  US = 'us',
  USA = 'usa',
  GB = 'gb',
  CA = 'ca',
  CANADA = 'canada',
  CAN = 'can',
  AU = 'au',
}

export enum SignUpBy {
  IOS = 'iOS Device',
  ANDROID = 'Android Device',
  WEB = 'Webapp',
}

export class CreateUserUserDto {
  @ApiProperty({ example: '<EMAIL>' })
  @Transform(({ value }) => value?.toLowerCase().trim())
  @IsNotEmpty()
  @IsEmail()
  email: string | null;

  @ApiProperty()
  @IsNotEmpty()
  @MinLength(8, { message: 'is too short (minimum is 8 characters)' })
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[\s\S]{8,}$/, {
    message: 'must include at least 1 lowercase letter, 1 uppercase letter and 1 digit',
  })
  @IsString()
  password?: string;

  @ApiProperty()
  @IsNotEmpty()
  @MinLength(8, { message: 'is too short (minimum is 8 characters)' })
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[\s\S]{8,}$/, {
    message: 'must include at least 1 lowercase letter, 1 uppercase letter and 1 digit',
  })
  @IsString()
  password_confirmation?: string;

  @ApiProperty({ example: 'Doe' })
  @Transform(({ value }) => value?.trim())
  @IsNotEmpty()
  name: string;

  @ApiProperty({ example: 'Join' })
  @Transform(({ value }) => value?.trim())
  @IsNotEmpty()
  first_name: string;

  @IsNumber()
  @IsNotEmpty()
  @ApiProperty({ example: 1 })
  accepted_both?: number;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ example: '26/06/2017' })
  accepted_privacy_on?: string | Date;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ example: '26/06/2017' })
  accepted_terms_on?: string | Date;

  @IsOptional()
  encrypted_password?: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ example: '26/06/2017' })
  birthday?: string | Date;

  @IsString()
  @ApiProperty({ example: 'us' })
  @IsNotEmpty()
  @Transform(({ value }) => value?.toLowerCase()?.trim())
  // @IsEnum(UserCountry)
  country?: string;

  @IsString()
  @ApiProperty({ example: SignUpBy.IOS })
  @IsOptional()
  @IsEnum(SignUpBy)
  signup_by?: SignUpBy;

  @IsBoolean()
  @IsOptional()
  @ApiProperty({ example: true })
  third_party_email_opt_in?: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty({ example: true })
  tmag_email_opt_in?: boolean;

  @IsOptional()
  @ApiProperty({ example: 10000 })
  @Transform(({ value }) => `${value?.trim()}`)
  postal_code?: string;

  @IsString()
  @IsOptional()
  @ApiProperty({ example: 'igolf home' })
  @Transform(({ value }) => value?.trim())
  iGolf_home_course_id?: string;

  @IsString()
  @IsOptional()
  @ApiProperty({ example: 'home course' })
  @Transform(({ value }) => value?.trim())
  home_course_name?: string;

  @IsString()
  @IsOptional()
  @ApiProperty({ example: 'partner ID' })
  @Transform(({ value }) => value?.trim())
  partner_user_id?: string;

  @IsString()
  @IsOptional()
  @ApiProperty({ example: 'partner name' })
  @Transform(({ value }) => value?.trim())
  partner_name?: string;

  @IsString()
  @IsOptional()
  @ApiProperty({ example: 'partner email' })
  @Transform(({ value }) => value?.trim())
  partner_user_email?: string;

  @IsString()
  @IsOptional()
  @ApiProperty({ example: 'male or female' })
  @Transform(({ value }) => value?.trim())
  gender?: string;
}

export class CreateTagUserDto {
  @ApiProperty()
  @IsOptional()
  @IsString()
  tag_user_id: string;

  @ApiProperty({ example: '<EMAIL>' })
  @Transform(({ value }) => value?.toLowerCase().trim())
  @IsOptional()
  @IsEmail()
  tag_user_email: string | null;
}

export class CreateUserDto {
  @IsDefined()
  @IsNotEmptyObject()
  @IsObject()
  @ValidateNested()
  @Type(() => CreateUserUserDto)
  user!: CreateUserUserDto;

  @IsString()
  @IsOptional()
  @Transform(({ value }) => value?.trim())
  device_token: string;
}

export class CreatePartnerUserDto {
  @IsDefined()
  @IsNotEmptyObject()
  @IsObject()
  @ValidateNested()
  @Type(() => CreateTagUserDto)
  user!: CreateTagUserDto;
}
