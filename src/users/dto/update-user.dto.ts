import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
  IsBoolean,
  IsDefined,
  IsEmail,
  IsEnum,
  IsNotEmpty,
  IsObject,
  IsOptional,
  IsString,
  Matches,
  MinLength,
  ValidateNested,
} from 'class-validator';
import { SignUpBy } from './create-user.dto';

export enum UserGender {
  MALE = 'male',
  FEMALE = 'female',
}

export class UpdateUserUserDto {
  @ApiProperty({ example: 'Doe' })
  @Transform(({ value }) => value?.trim())
  @IsOptional()
  name: string;

  @ApiProperty({ example: 'John' })
  @Transform(({ value }) => value?.trim())
  @IsOptional()
  first_name: string;

  @IsString()
  @IsOptional()
  @ApiProperty({ example: '26/06/2017' })
  accepted_privacy_on?: string | Date;

  @IsString()
  @IsOptional()
  @ApiProperty({ example: '26/06/2017' })
  accepted_terms_on?: string | Date;

  @IsOptional()
  @Transform(({ value }) => value?.trim())
  @ApiProperty({ example: '14.9' })
  handicap?: string;

  @IsOptional()
  @Transform(({ value }) => value?.trim())
  @ApiProperty({ example: 'right' })
  handed?: string;

  @IsOptional()
  @Transform(({ value }) => value?.toLowerCase()?.trim())
  @IsEnum(UserGender)
  @ApiProperty({ example: 'male' })
  gender?: UserGender;

  @IsOptional()
  @Transform(({ value }) => value?.trim())
  @ApiProperty({ example: 'Pro' })
  strokes_gained_baseline?: string;

  @IsString()
  @IsOptional()
  @ApiProperty({ example: '26/06/2017' })
  birthday?: string | Date;

  @IsString()
  @ApiProperty({ example: 'us' })
  @IsOptional()
  @Transform(({ value }) => value?.toLowerCase()?.trim())
  // @IsEnum(UserCountry)
  country?: string;

  @IsString()
  @ApiProperty({ example: SignUpBy.IOS })
  @IsOptional()
  @IsEnum(SignUpBy)
  signup_by?: SignUpBy;

  @IsBoolean()
  @IsOptional()
  @ApiProperty({ example: true })
  third_party_email_opt_in?: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty({ example: true })
  tmag_email_opt_in?: boolean;

  @IsOptional()
  @ApiProperty({ example: 10000 })
  @Transform(({ value }) => `${value?.trim()}`)
  postal_code?: string;

  @IsString()
  @IsOptional()
  @ApiProperty({ example: 'igolf home' })
  @Transform(({ value }) => value?.trim())
  iGolf_home_course_id?: string;

  @IsString()
  @IsOptional()
  @ApiProperty({ example: 'home course' })
  @Transform(({ value }) => value?.trim())
  home_course_name?: string;

  @IsString()
  @IsOptional()
  @ApiProperty({ example: '1' })
  @Transform(({ value }) => value?.trim())
  ghin_id?: string;
}

export class UpdateUserDto {
  @IsOptional()
  @IsDefined()
  @IsObject()
  @ValidateNested()
  @Type(() => UpdateUserUserDto)
  user!: UpdateUserUserDto;
}
export class UpdateGHINIdDto {
  @IsNotEmpty()
  ghin_id: string;
}
export class UpdateCanadaCardIdDto {
  @IsNotEmpty()
  canada_card_id: string;
}
export class RequestAccessGHINDto {
  @IsNotEmpty()
  @IsEmail()
  email: string;
  @IsNotEmpty()
  ghin_id: string;
}
export class UpdateUserUserPasswordDto {
  @ApiProperty({ example: '' })
  @Transform(({ value }) => value?.trim())
  @IsNotEmpty()
  current_password: string;

  @ApiProperty()
  @IsNotEmpty()
  @MinLength(8, { message: 'is too short (minimum is 8 characters)' })
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[\s\S]{8,}$/, {
    message: 'must include at least 1 lowercase letter, 1 uppercase letter and 1 digit',
  })
  @IsString()
  password?: string;

  @ApiProperty()
  @IsNotEmpty()
  @MinLength(8, { message: 'is too short (minimum is 8 characters)' })
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[\s\S]{8,}$/, {
    message: 'must include at least 1 lowercase letter, 1 uppercase letter and 1 digit',
  })
  @IsString()
  password_confirmation?: string;
}

export class UpdateUserPasswordDto {
  @ApiProperty({ example: 1 })
  @IsNotEmpty()
  id: number;

  @IsDefined()
  @IsObject()
  @ValidateNested()
  @Type(() => UpdateUserUserPasswordDto)
  user!: UpdateUserUserPasswordDto;
}
