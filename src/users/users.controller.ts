import { InjectQueue } from '@nestjs/bull';
import {
  Body,
  Controller,
  DefaultValuePipe,
  Delete,
  Get,
  HttpCode,
  HttpException,
  HttpStatus,
  Param,
  ParseBoolPipe,
  ParseIntPipe,
  Patch,
  Post,
  Put,
  Query,
  Req,
  Request,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiParam, ApiQuery, ApiTags } from '@nestjs/swagger';
import { Queue } from 'bull';
import { plainToClass } from 'class-transformer';
import _ from 'lodash';
import { isEmpty } from 'lodash';
import moment from 'moment';
import { AuthService } from 'src/auth/auth.service';
import {
  AddSIEmailDto,
  AuthAccessTokenLoginDto,
  AuthAccessTokenRegisterDto,
  AuthEmailRegisterDto,
  UpdateSIEmailDto,
} from 'src/auth/dto/auth-access-token-login.dto';
import { LoginWithEmailPasswordDto } from 'src/auth/dto/auth-email-login.dto';
import { AverageScoresService } from 'src/average-scores/average-scores.service';
import { CdmService } from 'src/cdm/cdm.service';
import { ClubsService } from 'src/clubs/clubs.service';
import { Club } from 'src/clubs/entities/club.entity';
import { GetAllScoreDto } from 'src/ghin/dto/get-all-score.dto';
import { GhinService } from 'src/ghin/ghin.service';
import { VerifyMemberDto } from 'src/golfnet/dto/verify-member.dto';
import { GolfNetService } from 'src/golfnet/golfnet.service';
import { AuthGuard } from 'src/guards/auth.guard';
import { OAuthGuard } from 'src/guards/oauth.guard';
import { KlaviyoService } from 'src/klaviyo/klaviyo.service';
import { MytmService } from 'src/mytm/mytm.service';
import { CreateRoundDto } from 'src/rounds/dto/create-round.dto';
import { OPTIONS_JOB_DEFAULT } from 'src/rounds/round.const';
import { RoundService } from 'src/rounds/rounds.service';
import { StatsApproachService } from 'src/stats/stats-approarch.service';
import { StatsDrivingService } from 'src/stats/stats-driving.service';
import { StatsOverallService } from 'src/stats/stats-overall.service';
import { StatsPuttingService } from 'src/stats/stats-putting.service';
import { StatsShortService } from 'src/stats/stats-short.service';
import {
  throwBadRequestError,
  throwNotFoundError,
  throwUnauthorizedError,
  throwUnprocessableEntityError,
} from 'src/utils/exception';
import { StatsHelper } from 'src/utils/helper/stats_helper';
import { getOptionsPaging } from 'src/utils/infinity-pagination';
import { BaseRequest } from 'src/utils/types/request';
import { deleteValueBlank, detectBaseline, isValidId, parseStrDate } from 'src/utils/utils';
import { PROCESSORS, PROCESS_QUEUE_NAMES } from 'src/workers/jobs/job.constant';
import { ClientsService } from '../clients/clients.service';
import { GolfNetQueryDto } from '../golfnet/dto/golf-net.query.dto';
import { ActivateUserClubDto, CreateUserClubDto, UserClubDto } from './dto/create-user-club.dto';
import { CreatePartnerUserDto, CreateUserDto, UserCountry } from './dto/create-user.dto';
import { StrokesGainedQueryDto } from './dto/strokes-gained-query.dto';
import {
  RequestAccessGHINDto,
  UpdateCanadaCardIdDto,
  UpdateGHINIdDto,
  UpdateUserDto,
  UpdateUserPasswordDto,
  UpdateUserUserDto,
  UserGender,
} from './dto/update-user.dto';
import { UpdateWitbsDTO } from './dto/update-witbs.dto';
import { SG_BASELINES } from './entities/user.entity';
import { UsersService } from './users.service';

@UseGuards(OAuthGuard)
@ApiTags('Users')
@Controller({
  path: '',
})
export class UsersController {
  constructor(
    private readonly usersService: UsersService,
    private clubService: ClubsService,
    private authService: AuthService,
    private cdmService: CdmService,
    private klaviyoService: KlaviyoService,
    private averageScoresService: AverageScoresService,
    private statsOverallService: StatsOverallService,
    private startDrivingService: StatsDrivingService,
    private statsApproachService: StatsApproachService,
    private statsShortService: StatsShortService,
    private statsPuttingService: StatsPuttingService,
    private ghinService: GhinService,
    private mytmService: MytmService,
    private golfNetService: GolfNetService,
    private clientsService: ClientsService,
    @InjectQueue(PROCESSORS.CalcHandicapJob) private calcHandicapJobQueue: Queue,
    @InjectQueue(PROCESSORS.SyncUpdateUserFromCdmJob) private syncUpdateUserFromCDMJobQueue: Queue,
    private readonly roundService: RoundService
  ) {}

  @Post(['tag-user', 'tag-user.json'])
  @HttpCode(HttpStatus.OK)
  async createPartnerUser(@Body() partnerUserDto: CreatePartnerUserDto, @Req() req: any) {
    const company = req?.client?.company;
    try {
      const user = await this.usersService.createPartnerUser(partnerUserDto, company);
      return {
        ...this.usersService.transformCreateUser(user),
      };
    } catch (error) {
      if (error.message === 'Email has already been taken') {
        return UsersController.throwUserCreateError({ email: ['has already been taken'] });
      }
      if (error.message.startsWith('Auth0 error:')) {
        return UsersController.throwUserCreateError({ auth0: [error.message.replace('Auth0 error: ', '')] });
      }
      throw error;
    }
  }

  @Post(['users', 'users.json'])
  @HttpCode(HttpStatus.OK)
  async createUser(@Body() userDto: CreateUserDto) {
    userDto.user.birthday = parseStrDate(userDto.user.birthday);
    userDto.user.accepted_privacy_on = parseStrDate(userDto.user.accepted_privacy_on);
    userDto.user.accepted_terms_on = parseStrDate(userDto.user.accepted_terms_on);
    userDto.user.encrypted_password = userDto.user.password;
    const isEmailExists = await this.authService.isEmailAlreadyExists(userDto.user.email);
    if (isEmailExists) {
      return UsersController.throwUserCreateError({ email: ['has already been taken'] });
    }
    const [error] = await this.authService.createAuth0User(userDto.user.email, userDto.user.password);
    if (error) {
      return UsersController.throwUserCreateError({ auth0: [error.message] });
    }
    const [accessTokenError, auth0AccessToken] = await this.authService.getAuth0AccessToken(
      userDto.user.email,
      userDto.user.password
    );
    if (accessTokenError) {
      return UsersController.throwUserCreateError({ auth0: [accessTokenError.message] });
    }
    const user = await this.usersService.create(userDto);
    if (userDto.device_token) {
      await this.usersService.saveDeviceToken(user.id, userDto.device_token);
    }
    // await this.klaviyoService.trackWithMyTM('/auth/trigger-user-success-klaviyo', {
    //   email: user.email,
    // });
    try {
      await this.cdmService.syncConsumerByUserId(user.id);
    } catch (error) {
      console.log(error);
    }

    // let oauthAccessToken;
    // if (request.isOAuthRequest) {
    // oauthAccessToken = await this.authService.createOAuthAccessToken(user.id);
    // }
    return {
      ...this.usersService.transformCreateUser(user),
      auth0_token: auth0AccessToken,
    };
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Put('users/:id')
  @ApiParam({ name: 'id', example: 41428 })
  @HttpCode(HttpStatus.OK)
  async putUpdateUser(@Param('id') id: number, @Body() userDto: UpdateUserDto | UpdateUserUserDto) {
    return this.handleUpdateUser(id, userDto);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Put(['user/update_password', 'user/update_password.json'])
  @HttpCode(HttpStatus.OK)
  async putUpdateUserPassword(@Body() userPasswordDto: UpdateUserPasswordDto) {
    const [e, user] = await this.authService.changeUserPassword(
      userPasswordDto.id,
      userPasswordDto.user.current_password,
      userPasswordDto.user.password
    );
    if (e) {
      return {
        msg: e.message,
        status: 'bad_request',
        password_history: "Password cannot be the same as any of the last five Passwords you've used",
      };

      // return UsersController.throwUserChangePasswordError(
      //   e.message === 'is invalid' ? { current_password: [e.message] } : { auth0: [e.message] }
      // );
    }
    return user;
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Patch('users/:id')
  @ApiParam({ name: 'id', example: 41428 })
  @HttpCode(HttpStatus.OK)
  async patchUpdateUser(@Param('id') id: number, @Body() userDto: UpdateUserDto) {
    return this.handleUpdateUser(id, userDto);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Patch('users/:id/ghin')
  @ApiParam({ name: 'id', example: 41428 })
  @HttpCode(HttpStatus.OK)
  async patchUpdateGHINIdUser(@Param('id') id: number, @Body() userDto: UpdateGHINIdDto) {
    const user = await this.usersService.updateGHINId(id, userDto.ghin_id);
    let userData = {
      ...this.usersService.transformUser(user),
    };
    userData = deleteValueBlank(userData, false);
    return {
      data: { ...userData },
    };
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Patch('users/:id/canada_card_id')
  @ApiParam({ name: 'id', example: 41428 })
  @HttpCode(HttpStatus.OK)
  async patchUpdateCardIdIdUser(@Param('id') id: number, @Body() userDto: UpdateCanadaCardIdDto) {
    const user = await this.usersService.updateCanadaCardId(id, userDto.canada_card_id);
    let userData = {
      ...this.usersService.transformUser(user),
    };
    userData = deleteValueBlank(userData, false);
    return {
      data: { ...userData },
    };
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Delete('users/:id/canada_card_id')
  @ApiParam({ name: 'id', example: 41428 })
  @HttpCode(HttpStatus.OK)
  async deleteCardIdIdUser(@Param('id') id: number) {
    const user = await this.usersService.updateCanadaCardId(id, null);
    let userData = {
      ...this.usersService.transformUser(user),
    };
    userData = deleteValueBlank(userData, false);
    return {
      data: { ...userData },
    };
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Post('users/:id/request_access_ghin')
  @ApiParam({ name: 'id', example: 41428 })
  @HttpCode(HttpStatus.OK)
  postRequestAccessGHIN(@Param('id') id: number, @Body() payload: RequestAccessGHINDto) {
    return this.ghinService.requestAccessGHIN(id, payload);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Delete('users/:id/revoke_access_ghin')
  @ApiParam({ name: 'id', example: 41428 })
  @HttpCode(HttpStatus.NO_CONTENT)
  revokeAccessGHIN(@Param('id') id: number) {
    return this.ghinService.revokeAccessToGHIN(id);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Post('users/:id/request_access_whs')
  @ApiParam({ name: 'id', example: 41428 })
  @HttpCode(HttpStatus.OK)
  postRequestAccessWHS(@Param('id') id: number, @Body() payload: VerifyMemberDto) {
    return this.golfNetService.requestAccessWHS(id, payload);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Delete('users/:id/revoke_access_whs')
  @ApiParam({ name: 'id', example: 41428 })
  @HttpCode(HttpStatus.NO_CONTENT)
  revokeAccessWHS(@Param('id') id: number) {
    return this.golfNetService.revokeAccessToWHS(id);
  }

  async handleUpdateUser(id: number, userDto: UpdateUserDto | UpdateUserUserDto) {
    const existUser = await this.usersService.findOne({ id });
    if (!existUser) {
      return throwNotFoundError('User not found!');
    }

    const params: any = { ...userDto };
    const payload = params.user != undefined ? params : { user: params };
    if (payload.user?.birthday) {
      payload.user.birthday = parseStrDate(payload.user.birthday);
    }
    if (payload.user?.accepted_privacy_on) {
      payload.user.accepted_privacy_on = parseStrDate(payload.user.accepted_privacy_on);
    }
    if (payload.user?.accepted_terms_on) {
      payload.user.accepted_terms_on = parseStrDate(payload.user.accepted_terms_on);
    }
    if (payload.user?.strokes_gained_baseline) {
      payload.user.strokes_gained_baseline = detectBaseline(payload.user?.strokes_gained_baseline);
    }
    delete payload?.user?.accepted_both;
    const user = await this.usersService.update(id, payload);

    this.mytmService.triggerCollectSaleForceData(user.email);

    await this.cdmService.syncConsumerByUserId(user.id);
    const clubs = await this.clubService.getUserClubs(id, 1, 50, false, false);

    let userData = {
      ...this.usersService.transformUser(user),
    };
    userData = deleteValueBlank(userData, false);
    userData['strokes_gained_baseline'] = StatsHelper.to_baseline_number(userData['strokes_gained_baseline']);
    if (clubs?.data) {
      clubs.data = clubs.data.map((club) => _.omit(club, ['image_large', 'image_reg', 'image_small', 'image_thumb']));
    }
    return {
      data: {
        ...userData,
        clubs: clubs.data || [],
      },
    };
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('users/:id')
  @ApiParam({ name: 'id', example: 41428 })
  @HttpCode(HttpStatus.OK)
  async getUser(@Param('id') id: number) {
    const user = await this.usersService.findOne({ id });
    if (!user) {
      return throwNotFoundError('User not found!');
    }
    const clubs = await this.clubService.getUserClubs(id, 1, 50, false, false);
    let userData = {
      ...this.usersService.transformUser(user),
    };
    userData = deleteValueBlank(userData, false);
    userData['strokes_gained_baseline'] = `${SG_BASELINES[userData['strokes_gained_baseline']] || ''}`;
    if (clubs?.data) {
      clubs.data = clubs.data.map((club) => _.omit(club, ['image_large', 'image_reg', 'image_small', 'image_thumb']));
    }
    return {
      data: {
        ...userData,
        clubs: clubs.data || [],
      },
    };
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Delete('users/:id')
  @ApiParam({ name: 'id', example: 41428 })
  @HttpCode(HttpStatus.NO_CONTENT)
  async deleteUser(@Param('id') id: number) {
    await this.usersService.softDelete(id);
    return {
      success: true,
    };
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Delete('users/force-delete/:id')
  @ApiParam({ name: 'id', example: 41428 })
  @HttpCode(HttpStatus.OK)
  async forceDeleteUser(@Param('id') id: number) {
    return this.usersService.forceDelete(id);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('users/:id/cdm')
  @ApiParam({ name: 'id', example: 41428 })
  @HttpCode(HttpStatus.OK)
  async getUserCdm(@Param('id') id: number) {
    const user = await this.usersService.findOne({ id });
    if (!user) {
      return throwNotFoundError('User not found!');
    }
    const consumer = await this.cdmService.getConsumer(user.email, false);
    if (!consumer) {
      return throwNotFoundError('User not found!');
    }
    const golferProfile = consumer.golferProfile;
    let handicap = golferProfile?.newHandicap?.userInputHandicap || 0;
    if (parseFloat(handicap) < 0) {
      handicap = `+${handicap * -1}`;
    }
    const witb = golferProfile?.whatsInTheBags || [];
    await this.usersService.syncCDMClubs(user);
    return {
      data: {
        id: user.id,
        email: user.email,
        handicap,
        handed: golferProfile.handed,
        birthday: consumer.dob,
        strokes_gained_baseline: SG_BASELINES[golferProfile.strokesGainedBaseline] || '',
        name: consumer.lastName,
        first_name: consumer.firstName,
        country: consumer.userCountry,
        gender: consumer.gender || UserGender.MALE,
        third_party_email_opt_in: user.third_party_email_opt_in,
        tmag_email_opt_in: user.tmag_email_opt_in,
        accepted_terms_on: user.accepted_terms_on,
        accepted_privacy_on: user.accepted_privacy_on,
        avatar: user.avatar || '/images/original/missing.png',
        token: user.token,
        guardian_email: user.guardian_email,
        postal_code: user.postal_code,
        iGolf_home_course_id: user.iGolf_home_course_id,
        home_course_name: user.home_course_name,
        cdm_id: user.cdm_id,
        start_mytm: user.start_mytm,
        clubs: witb
          .filter((item) => !!item.inBag)
          .map((item) => ({
            in_bag: item.inBag,
            disabled: !!item.deleted,
            shaft_length: item.clubShaftLength?.value || '',
            shaft_flex: item.clubShaftFlex?.value || '',
            face_lie_adjustment: item.faceLieAdjustment?.value || '',
            face_loft_adjustment: item.faceLoftAdjustment?.value || '',
            loft: item.clubLoft?.value || '',
            manufacturer: item.brand?.name || '',
            modelname: item.model?.name || '',
            club_type: item.clubCategoryType?.type || '',
            club_family: item.clubCategory?.name || '',
            cdm_witb_id: item.id,
            image_thumb: item.model?.imageThumb || '',
            image_small: item.model?.imageSmall || '',
            image_reg: item.model?.imageReg || '',
            image_large: item.model?.imageLarge || '',
          })),
      },
    };
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('users/update/:cdmId')
  @ApiParam({ name: 'cdmId', example: 'uid' })
  @HttpCode(HttpStatus.OK)
  async forceUpdateUserFromCDM(@Param('cdmId') cdmId: string) {
    await this.syncUpdateUserFromCDMJobQueue.add(
      PROCESS_QUEUE_NAMES.SYNC_UPDATE_USER_FROM_CDM,
      { cdmId },
      OPTIONS_JOB_DEFAULT
    );
    return { msg: `Start update user: ${cdmId}` };
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('users/:id/clubs')
  @HttpCode(HttpStatus.OK)
  @ApiParam({ name: 'id', example: 41428 })
  @ApiQuery({ name: 'page', example: 1, required: false })
  @ApiQuery({ name: 'per', example: 25, required: false })
  @ApiQuery({ name: 'excludeRegistedClubs', example: true, required: false })
  @ApiQuery({ name: 'inactive', example: true, required: false })
  userClubs(
    @Param('id') id: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page = 1,
    @Query('per', new DefaultValuePipe(25), ParseIntPipe) limit = 25,
    @Query('excludeRegistedClubs', new DefaultValuePipe(false), ParseBoolPipe)
    excludeRegistedClubs = false,
    @Query('inactive', new DefaultValuePipe(false), ParseBoolPipe)
    inactive = false
  ) {
    return this.clubService.getUserClubs(id, page, limit, excludeRegistedClubs, inactive);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Post('users/:id/clubs')
  @HttpCode(HttpStatus.CREATED)
  @ApiParam({ name: 'id', example: 41428 })
  async createUserClubs(@Param('id') id: number, @Body() clubDto: CreateUserClubDto | UserClubDto) {
    const [club, user] = await Promise.all([
      this.clubService.createUserClub(id, clubDto),
      this.usersService.findOne({ id }),
    ]);
    this.mytmService.triggerCollectSaleForceData(user.email);
    const witb = await this.cdmService.getWITBs(user.email);
    if (!club) {
      return throwBadRequestError();
    }
    return {
      data: this.clubService.transformClubData(club, id, witb),
    };
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Post('users/:id/clubs/activate')
  @HttpCode(HttpStatus.OK)
  @ApiParam({ name: 'id', example: 41428 })
  // Delete club
  async activateUserClubs(
    @Param('id') id: number,
    @Body() activateClubDto: ActivateUserClubDto,
    @Query('deactivate', new DefaultValuePipe(false), ParseBoolPipe) deactivate = false
  ) {
    return this.clubService.activateUserClubs(id, activateClubDto.club_ids, deactivate);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Post('users/:id/clubs/active')
  @HttpCode(HttpStatus.OK)
  @ApiParam({ name: 'id', example: 41428 })
  async activeUserClubs(@Param('id') id: number, @Body() activateClubDto: ActivateUserClubDto, @Query() params: any) {
    let inactive: any = false;
    if (!isEmpty(params.inactive)) {
      inactive = params.inactive;
    }
    if (!isEmpty(activateClubDto.inactive)) {
      inactive = activateClubDto.inactive;
    }
    return this.clubService.activeUserClubs(id, activateClubDto.club_ids, inactive);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('users/:id/clubs/deactivate')
  @HttpCode(HttpStatus.OK)
  @ApiParam({ name: 'id', example: 41428 })
  async deactivateUserClubs(@Param('id') id: number) {
    const [deactivatedClubs, user] = await Promise.all([
      this.clubService.getUserDeactivatedClubs(id),
      this.usersService.findOne({ id }),
    ]);
    this.mytmService.triggerCollectSaleForceData(user.email);
    const witb = await this.cdmService.getWitbsV2(user.email, true);
    return {
      msg: 'Fetch clubs successfully',
      data: deactivatedClubs.map((club) => this.clubService.transformClubData(club, id, witb)),
    };
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('users/:id/clubs/trigger_update/:cdm_witb_id')
  @HttpCode(HttpStatus.OK)
  @ApiParam({ name: 'id', example: 41428 })
  @ApiParam({ name: 'cdm_witb_id', example: 'uuid' })
  async triggerUpdateCdmWITBToClub(@Param('id') id: number, @Param('cdm_witb_id') cdmWitbId: string) {
    if (!id || !cdmWitbId) {
      return throwBadRequestError();
    }
    const club = await this.clubService.findClubWithCdmWITBId(cdmWitbId);
    if (!club) {
      return throwBadRequestError();
    }
    const user = await this.usersService.findOne({ id });
    if (!user) {
      return throwBadRequestError();
    }
    this.mytmService.triggerCollectSaleForceData(user.email);
    const consumer = await this.cdmService.getConsumer(user.email, false, false);
    const golferProfile = consumer.golferProfile;
    // TODO:V2
    // const witbDetail = await this.cdmService.getWitbDetailV2({ witbId: cdmWitbId, isDelete: null }, golferProfile.id);
    // if (witbDetail) {
    //   await this.clubService.updateClub(club.id, CdmService.convertWITBItemToClubParams(witbDetail));
    // }

    // V1
    const witb = golferProfile?.whatsInTheBags || [];
    const matchWitb = witb.find((item) => item.id === cdmWitbId);
    if (!matchWitb || witb.length === 0) {
      await this.clubService.markClubAsDisabled(club.id);
    } else {
      await this.clubService.updateClub(club.id, CdmService.convertWITBItemToClubParams(matchWitb));
    }

    return {
      msg: `Update club ${cdmWitbId}`,
    };
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Post('users/:id/clubs/trigger_update')
  @HttpCode(HttpStatus.OK)
  @ApiParam({ name: 'id', example: 41428 })
  async triggerUpdateCdmWITBsToClub(@Param('id') id: number, @Body() payload: UpdateWitbsDTO) {
    if (!id) {
      return throwBadRequestError();
    }
    if (payload.witbs.length == 0 || !payload.witbs) {
      return throwBadRequestError();
    }
    // const clubs = await this.clubService.findClubWithCdmWITBIds(payload.witbs);
    // if (!clubs) {
    //   return throwBadRequestError();
    // }
    const user = await this.usersService.findOne({ id });
    if (!user) {
      return throwBadRequestError();
    }
    this.mytmService.triggerCollectSaleForceData(user.email);
    // TODO: need optimize update club
    for (const witb of payload.witbs) {
      if (witb.isDelete) {
        await this.clubService.markClubCDMAsDisabled(witb.witbId);
      } else {
        const witbCDM = await this.cdmService.getWitbDetailV2(witb, payload.golferProfileId);
        if (!witbCDM) {
          continue;
        }
        const club = await this.clubService.findCdmWitbId(witb.witbId);
        if (!club) {
          // create new witb
          const newClub: Club = plainToClass(Club, {
            ...CdmService.convertWITBItemToClubParams(witbCDM),
            user_id: user.id,
          });
          newClub['created_at'] = new Date();
          newClub['updated_at'] = new Date();
          await this.clubService.create(newClub);
        } else {
          //update witb
          await this.clubService.updateClubByCdmWitbId(witb.witbId, CdmService.convertWITBItemToClubParams(witbCDM));
        }
      }
    }

    return {
      msg: `Update club ${payload.witbs.map((w) => w.witbId).join(',')}`,
    };
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('users/:id/show_advance')
  @ApiParam({ name: 'id', example: 41428 })
  async showAdvance(@Param('id') id: number) {
    if (!isValidId(id)) {
      console.log(`INVALID USER_ID ${id}- SHOW: FALSE`);
      return { show: false };
    }
    const user = await this.usersService.findOne({ id });
    if (!user) {
      console.log('USER NOT FOUND - SHOW: FALSE');
      return { show: false };
    }

    if (![UserCountry.US, UserCountry.USA].includes(user.country.toLowerCase() as any)) {
      console.log(`COUNTRY DIFF US ${user.country} - SHOW: TRUE`);
      return { show: true };
    }

    const consumer = await this.cdmService.getConsumer(user.email, true);
    if (!consumer) {
      console.log(`NOT FOUND CONSUMER ${user.email} - SHOW: FALSE`);
      return { show: false };
    }
    if (consumer?.myTMSubscriptionLevel > 0) {
      console.log(`myTMSubscriptionLevel ${consumer?.myTMSubscriptionLevel} - SHOW: TRUE`);
      return { show: true };
    }
    const totalAdvancedRoundsLastYear = await this.roundService.countAdvancedRoundsWithDates(
      moment().subtract(1, 'year').startOf('year').toDate(),
      moment().subtract(1, 'year').endOf('year').toDate(),
      id
    );
    console.log(`totalAdvancedRoundsLastYear SHOW:  ${totalAdvancedRoundsLastYear >= 2}`);
    return { show: totalAdvancedRoundsLastYear >= 2 };
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('users/:id/average_scores')
  @ApiParam({ name: 'id', example: 41428 })
  async showAverageScores(@Param('id') id: number) {
    if (!isValidId(id)) {
      return {};
    }
    return this.averageScoresService.findOneByUserId(id);
  }
  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('users/:id/average_scores_classic')
  @ApiParam({ name: 'id', example: 41428 })
  async showAverageScoresClassic(@Param('id') id: number) {
    if (!isValidId(id)) {
      return {};
    }
    return this.averageScoresService.findOneAvgClassicByUserId(id);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('users/:id/update_avg_total_rounds')
  @ApiParam({ name: 'id', example: 41428 })
  async updateAVGTotalRounds(@Param('id') id: number) {
    await this.averageScoresService.updateTotalRounds(id);
    return {
      success: true,
    };
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('users/:id/update_handicap_index')
  @ApiParam({ name: 'id', example: 41428 })
  async handicapIndexUpdate(@Param('id') id: number) {
    const user = await this.usersService.findOne({ id });
    if (!user) {
      return throwBadRequestError();
    }
    return {
      success: true,
    };
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('users/:id/privacy_policy')
  @ApiParam({ name: 'id', example: 41428 })
  async privacyPolicy(@Param('id') id: number) {
    const user = await this.usersService.findOne({ id });
    return {
      privacy_policy: !!user?.accepted_privacy_on,
    };
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('users/:id/terms')
  @ApiParam({ name: 'id', example: 41428 })
  async terms(@Param('id') id: number) {
    const user = await this.usersService.findOne({ id });
    return {
      terms_of_service: !!user?.accepted_terms_on,
    };
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('users/:id/policies')
  @ApiParam({ name: 'id', example: 41428 })
  async policies(@Param('id') id: number) {
    const user = await this.usersService.findOne({ id });
    return {
      privacy_policy: !!user?.accepted_privacy_on,
      terms_of_service: !!user?.accepted_terms_on,
    };
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('users/:id/rounds')
  @ApiParam({ name: 'id', example: 41428 })
  @ApiQuery({ name: 'page', example: 1, required: false })
  @ApiQuery({ name: 'per', example: 10, required: false })
  @ApiQuery({ name: 'usga', example: false, required: false })
  @ApiQuery({ name: 'golfnet', example: false, required: false })
  findAll(@Param('id') id: number, @Query() query?: any) {
    if (!isValidId(id)) {
      return { data: [] };
    }
    let { light, completed, usga, golfnet } = query;
    if (!completed) {
      completed = true;
    }
    if (light == undefined) {
      light = true;
    }
    if (!usga || ![true, 'true'].includes(usga)) {
      usga = false;
    } else {
      usga = true;
    }
    if (!golfnet || ![true, 'true'].includes(golfnet)) {
      golfnet = false;
    } else {
      golfnet = true;
    }
    if (!query.per) {
      query['per'] = 30;
    }
    const { page, limit } = getOptionsPaging(query.page, query.per);
    const pagingOption = { limit, page };

    return this.usersService.findRounds(id, pagingOption, light, completed, usga, golfnet);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('users/:id/round-usga')
  @ApiParam({ name: 'id', example: 41428 })
  @ApiQuery({ name: 'page', example: 1, required: false })
  @ApiQuery({ name: 'per', example: 10, required: false })
  findRoundTMAndUSGA(@Param('id') id: number, @Query() query?: any) {
    if (!isValidId(id)) {
      return { data: [] };
    }
    let { light, completed } = query;
    if (!completed) {
      completed = true;
    }
    if (light == undefined) {
      light = true;
    }

    if (!query.per) {
      query['per'] = 30;
    }
    const { page, limit } = getOptionsPaging(query.page, query.per);
    const pagingOption = { limit, page };

    return this.usersService.findRoundTMAndUSGA(id, pagingOption, light, completed);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Post('users/:id/rounds')
  @ApiParam({ name: 'id', example: 41428 })
  postRound(@Param('id') id: number, @Body() roundDto: CreateRoundDto) {
    return this.roundService.postRound(id, roundDto);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Post('users/:id/round-usga')
  @ApiParam({ name: 'id', example: 41428 })
  postRoundUSGA(@Param('id') id: number, @Body() roundDto: CreateRoundDto) {
    return this.roundService.postRoundUSGA(id, roundDto);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('users/:id/handicap_index')
  @ApiParam({ name: 'id', example: 41428 })
  getHandicapIndex(@Param('id') id: number) {
    return this.ghinService.getHandicapIndex(id);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('users/:id/ghin_course_handicap/:course_id')
  @HttpCode(HttpStatus.OK)
  getCourseHandicap(@Param('id') id: string, @Param('course_id') course_id: string) {
    return this.ghinService.getGHINCourseHandicap(id, course_id);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('users/:id/canada_handicap_index')
  @ApiParam({ name: 'id', example: 41428 })
  getCanadaHandicapIndex(@Param('id') id: number) {
    return this.golfNetService.getHandicapIndex(id);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('users/:id/golfer_profile')
  @ApiParam({ name: 'id', example: 41428 })
  getGHINGolferProfile(@Param('id') id: number) {
    return this.ghinService.getGolferProfile(id);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('users/:id/ghin_scores')
  @ApiParam({ name: 'id', example: 41428 })
  getGHINScores(@Param('id') id: number) {
    return this.ghinService.getGHINScores(id);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('users/:id/ghin_all_scores')
  @ApiParam({ name: 'id', example: 41428 })
  getGHINAllScores(@Param('id') id: number, @Query() query: GetAllScoreDto) {
    return this.ghinService.getGHINAllScores(id, query);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('users/:id/golf_net_scores')
  @ApiParam({ name: 'id', example: 41428 })
  getGolfNetScores(@Param('id') id: number, @Query() query: GolfNetQueryDto) {
    return this.golfNetService.getScoreHistory(id, query);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @HttpCode(200)
  @Post(['users/revoke_token.json', 'users/revoke_token'])
  async revokeToken(@Request() request: BaseRequest) {
    await this.authService.revokeOAuthAccessToken(request.token, request.user.id);
    const deviceToken = request.headers['HTTP_DEVICE_TOKEN'.toLowerCase()];
    if (deviceToken) {
      await this.usersService.deleteDeviceToken(request.user.id, deviceToken);
    }
    return;
  }

  @Post(['users/login_by_access_token.json', 'users/login_by_access_token'])
  async loginByAccessToken(@Body() loginDto: AuthAccessTokenLoginDto, @Request() request: BaseRequest) {
    const [error, data] = await this.authService.loginWithAuth0AccessToken(loginDto, request.isOAuthRequest);
    if (error) {
      return throwUnauthorizedError(error.message);
    }
    return data;
  }

  @Post(['users/by_access_token.json', 'users/by_access_token'])
  async registerByAccessToken(@Body() loginDto: AuthAccessTokenRegisterDto) {
    const [error, data, statusCode] = await this.authService.registerWithAuth0AccessToken(loginDto);
    if (statusCode === 422) {
      return throwUnprocessableEntityError(error.message);
    }
    if (error) {
      return throwUnauthorizedError(error.message);
    }
    return data;
  }

  @Post(['users/by_email.json', 'users/by_email'])
  async registerByEmail(@Body() registerDto: AuthEmailRegisterDto) {
    const [error, data, statusCode] = await this.authService.registerWithEmail(registerDto);
    if (statusCode === 422) {
      return throwUnprocessableEntityError(error.message);
    }
    if (error) {
      return throwUnauthorizedError(error.message);
    }
    return data;
  }
  @Post('users/by_si_email')
  async registerBySIEmail(@Body() registerDto: AddSIEmailDto) {
    const [error, data, statusCode] = await this.authService.registerWithSIEmail(registerDto);
    if (statusCode === 422) {
      return throwUnprocessableEntityError(error.message);
    }
    if (error) {
      return throwUnauthorizedError(error.message);
    }
    return data;
  }
  @Post('users/update_si_email')
  async updateSIEmail(@Body() payloadUpdate: UpdateSIEmailDto) {
    const [error, data, statusCode] = await this.authService.updateSIAccount(payloadUpdate);
    if (statusCode === 422) {
      return throwUnprocessableEntityError(error.message);
    }
    if (error) {
      return throwUnauthorizedError(error.message);
    }
    return data;
  }

  @Post(['users/sign_in.json', 'users/sign_in'])
  async loginByEmailPassword(@Body() loginDto: LoginWithEmailPasswordDto, @Request() request: BaseRequest) {
    const [error, data] = await this.authService.validateLogin(loginDto, request.isOAuthRequest);
    if (error) {
      return throwUnauthorizedError(error.message);
    }
    return data;
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('users/tag/:email/strokes-gained/overall')
  @HttpCode(HttpStatus.OK)
  @ApiParam({ name: 'id', example: 41428 })
  async tagStrokesGainedOverall(@Param('email') email: string, @Query() query: StrokesGainedQueryDto) {
    return await this.statsOverallService.tagStrokesGainedOverall(email, query);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('users/:id/stats/overall')
  @HttpCode(HttpStatus.OK)
  @ApiParam({ name: 'id', example: 41428 })
  async statsOverall(@Param('id') userId: number, @Query() query: any, @Request() request: BaseRequest) {
    return await this.statsOverallService.overalls(userId, query, request.user);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('users/:id/stats/club')
  @HttpCode(HttpStatus.OK)
  @ApiParam({ name: 'id', example: 41428 })
  async statsClub(@Param('id') userId: number, @Query() query: any, @Request() request: BaseRequest) {
    return await this.statsOverallService.overallByClub(userId, query, request.user);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('users/:id/stats/club/:clubId')
  @HttpCode(HttpStatus.OK)
  @ApiParam({ name: 'id', example: 25514 })
  @ApiParam({ name: 'clubId', example: 645678 })
  async statsOneClub(
    @Param('id') userId: number,
    @Param('clubId') clubId: any,
    @Query() query: any,
    @Request() request: BaseRequest
  ) {
    return await this.statsOverallService.overallByOneClub(userId, query, request.user, clubId);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('users/:id/stats/club/:clubId/putter')
  @HttpCode(HttpStatus.OK)
  @ApiParam({ name: 'id', example: 25514 })
  @ApiParam({ name: 'clubId', example: 645678 })
  async statsOneClubPutter(
    @Param('id') userId: number,
    @Param('clubId') clubId: any,
    @Query() query: any,
    @Request() request: BaseRequest
  ) {
    return await this.statsOverallService.overallByOneClubPutter(userId, query, request.user, clubId);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('users/:id/stats/driving')
  @HttpCode(HttpStatus.OK)
  @ApiParam({ name: 'id', example: 25514 })
  async statsDriving(@Param('id') userId: number, @Query() query: any, @Request() request: BaseRequest) {
    return await this.startDrivingService.driving(userId, query, request.user);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('users/:id/stats/driving/dispersion')
  @HttpCode(HttpStatus.OK)
  @ApiParam({ name: 'id', example: 25514 })
  async statsDrivingDispersion(@Param('id') userId: number, @Query() query: any, @Request() request: BaseRequest) {
    return await this.startDrivingService.distance_and_dispersion(userId, query, request.user);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('users/:id/stats/approach')
  @HttpCode(HttpStatus.OK)
  @ApiParam({ name: 'id', example: 25514 })
  async statsApproach(@Param('id') userId: number, @Query() query: any, @Request() request: BaseRequest) {
    return await this.statsApproachService.approach(userId, query, request.user);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('users/:id/stats/approach/proximity')
  @HttpCode(HttpStatus.OK)
  @ApiParam({ name: 'id', example: 25514 })
  async statsApproachProximity(@Param('id') userId: number, @Query() query: any, @Request() request: BaseRequest) {
    return await this.statsApproachService.proximity_to_hole(userId, query, request.user);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('users/:id/stats/short')
  @HttpCode(HttpStatus.OK)
  @ApiParam({ name: 'id', example: 25514 })
  async statsShort(@Param('id') userId: number, @Query() query: any, @Request() request: BaseRequest) {
    return await this.statsShortService.short(userId, query, request.user);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('users/:id/stats/short/proximity')
  @HttpCode(HttpStatus.OK)
  @ApiParam({ name: 'id', example: 25514 })
  async statsShortProximity(@Param('id') userId: number, @Query() query: any, @Request() request: BaseRequest) {
    return await this.statsShortService.proximity_to_hole(userId, query, request.user);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('users/:id/stats/putting')
  @HttpCode(HttpStatus.OK)
  @ApiParam({ name: 'id', example: 25514 })
  async statsPutting(@Param('id') userId: number, @Query() query: any, @Request() request: BaseRequest) {
    return await this.statsPuttingService.putting(userId, query, request.user);
  }
  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('users/:id/stats/putting/proximity')
  @HttpCode(HttpStatus.OK)
  @ApiParam({ name: 'id', example: 25514 })
  async statsPuttingProximity(@Param('id') userId: number, @Query() query: any, @Request() request: BaseRequest) {
    return await this.statsPuttingService.proximity_to_hole(userId, query, request.user);
  }
  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('users/:id/rounds/played')
  @HttpCode(HttpStatus.OK)
  @ApiParam({ name: 'id', example: 25514 })
  async roundsPlayed(@Param('id') userId: number) {
    return await this.roundService.roundsPlayed(userId);
  }

  static throwUserCreateError(errors) {
    throw new HttpException(
      {
        errors,
      },
      HttpStatus.UNPROCESSABLE_ENTITY
    );
  }

  static throwUserChangePasswordError(errors) {
    throw new HttpException(errors, HttpStatus.UNPROCESSABLE_ENTITY);
  }
}
