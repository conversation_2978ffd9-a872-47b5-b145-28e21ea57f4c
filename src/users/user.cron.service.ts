import { InjectQueue } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { Queue } from 'bull';
import { isEmpty } from 'lodash';
import { Repository } from 'typeorm';
import { Club } from 'src/clubs/entities/club.entity';
import { OPTIONS_JOB_DEFAULT } from 'src/rounds/round.const';
import { isCronJobHandlersEnabled, isProduction } from 'src/utils/cron';
import { PROCESSORS, PROCESS_QUEUE_NAMES } from 'src/workers/jobs/job.constant';

@Injectable()
export class UserCronService {
  private readonly logger = new Logger(UserCronService.name);
  constructor(
    @InjectRepository(Club)
    private clubRepo: Repository<Club>,
    @InjectQueue(PROCESSORS.SyncClubsToCDM) private syncClubsToCDMJobQueue: Queue
  ) {}

  @Cron(isProduction ? CronExpression.EVERY_30_MINUTES : CronExpression.EVERY_10_MINUTES)
  async checkUserNotSyncClubs() {
    this.logger.debug(`CHECK USER NOT SYNC CLUBS....`);
    if (!isCronJobHandlersEnabled()) {
      return;
    }
    this.logger.debug(`START SCAN LIST USER NOT SYNC CLUB TO CDM`);
    const sql = ` 
        SELECT id, email, cdm_id
        FROM users WHERE id IN (
          SELECT DISTINCT (user_id) 
          FROM clubs 
          WHERE 
           ( cdm_witb_id IS NULL OR cdm_witb_id ='' )
            AND 
              user_id IS NOT NULL 
            ORDER BY user_id DESC
        )
        AND 
          cdm_id IS NOT NULL 
        LIMIT 20;
    `;
    const users = await this.clubRepo.query(sql);
    if (!users || isEmpty(users)) {
      this.logger.debug(`ALL CLUBS HAD SYNC...`);
      return;
    }
    this.logger.debug(`HAVE ${users.length} USER NOT SYNC CLUBS`);
    const userIds = users.map((user) => user.id);
    console.log({ userIds });

    for (const userId of userIds) {
      try {
        await this.syncClubsToCDMJobQueue.add(PROCESS_QUEUE_NAMES.SYNC_CLUBS_TO_CDM, { userId }, OPTIONS_JOB_DEFAULT);
      } catch (error) {
        console.log(`CRONJOB ADD SYNC_CLUBS_TO_CDM FAIL`);
        console.log(error);
      }
    }
  }
}
