import { ApiProperty } from '@nestjs/swagger';
import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';
import { EntityHelper } from 'src/utils/entity-helper';

@Entity({ name: 'device_tokens' })
export class DeviceToken extends EntityHelper {
  @PrimaryGeneratedColumn()
  @ApiProperty({ example: 1 })
  id: number;

  @Column({ nullable: false })
  @ApiProperty({ example: 180947 })
  user_id: number;

  @Column({ nullable: false })
  @ApiProperty({ example: 'eDp9LxqNTi-zvPkh4eQE8X:APA91bGlPWXOKGr' })
  device_token: string;

  @CreateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  created_at: Date;

  @UpdateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  updated_at: Date;
}
