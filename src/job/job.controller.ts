import { InjectQueue } from '@nestjs/bull';
import { Body, Controller, Post } from '@nestjs/common';
import { Queue } from 'bull';
import { PROCESSORS } from 'src/workers/jobs/job.constant';
import { JobDto } from './job.dto';
import { JobService } from './job.service';

@Controller('job')
export class JobController {
  constructor(
    private readonly jobService: JobService,
    @InjectQueue(PROCESSORS.IGolfRoundCompleteJob) private iGolfRoundCompleteJobQueue: Queue,
    @InjectQueue(PROCESSORS.ForceRoundCompleteJob) private forceRoundCompleteJobQueue: Queue,
    @InjectQueue(PROCESSORS.IGolfRoundDrivingDispersionJob) private iGolfRoundDrivingDispersionJobQueue: Queue,
    @InjectQueue(PROCESSORS.CalcHandicapJob) private calcHandicapJobQueue: Queue,
    @InjectQueue(PROCESSORS.SimpleScoreToParJob) private simpleScoreToParQueue: Queue,
    @InjectQueue(PROCESSORS.CalculateAverageScoreJob) private averageScoreQueue: Queue
  ) {}

  @Post()
  clearJob(@Body() body: JobDto) {
    console.log(body);
    return body;
  }
}
