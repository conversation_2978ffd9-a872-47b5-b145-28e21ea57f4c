export enum ROUND {
  ROUND_MODE_BASIC = 'Basic',
  ROUND_MODE_ADVANCED = 'Advanced',
  ROUND_MODE_MULTIPLAYER = 'Multiplayer',
  ROUND_MODE_SIMPLE = 'Simple',
  ROUND_MODE_CLASSIC = 'Classic',
}
export enum MAP_TYPE {
  MAP_TYPE_MRP = 'MRP',
  MAP_TYPE_IGOLF = 'iGolf',
  MAP_TYPE_GLX = 'glx',
  MAP_TYPE_TAG = 'tag',
}
export enum TYPES {
  Qualification = 'Qualification',
  Practice = 'Practice',
  Tournament = 'Tournament',
}
export const OPTIONS_JOB_COMPLETED_ROUND = {
  delay: 0,
  priority: 1,
  attempts: 10,
  backoff: {
    type: 'fixed',
    delay: 1 * 60 * 1000,
  },
  removeOnComplete: {
    age: 24 * 60 * 60,
  },
};
export const OPTIONS_JOB_ROUND_DRIVING_DISPERSION_ROUND = {
  delay: 0,
  priority: 2,
  attempts: 10,
  backoff: {
    type: 'fixed',
    delay: 3 * 60 * 1000,
  },
  removeOnComplete: {
    age: 24 * 60 * 60,
  },
};
export const OPTIONS_JOB_DEFAULT = {
  delay: 0,
  attempts: 10,
  backoff: {
    type: 'fixed',
    delay: 3 * 60 * 1000,
  },
  removeOnComplete: {
    age: 24 * 60 * 60,
  },
};
export const DATE_CAN_POST_SCORE_TO_USGA = '2023-09-06';
