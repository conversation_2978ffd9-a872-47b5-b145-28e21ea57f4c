import { InjectQueue } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { Queue } from 'bull';
import { isEmpty } from 'lodash';
import moment from 'moment';
import { Repository } from 'typeorm';
import { isCronJobHandlersEnabled } from 'src/utils/cron';
import { PROCESSORS, PROCESS_QUEUE_NAMES } from 'src/workers/jobs/job.constant';
import { RoundIdsDto } from './dto/round-ids.dto';
import { Round } from './entities/round.entity';
import { OPTIONS_JOB_ROUND_DRIVING_DISPERSION_ROUND, ROUND } from './round.const';

@Injectable()
export class RoundCronService {
  private readonly logger = new Logger(RoundCronService.name);
  constructor(
    @InjectRepository(Round)
    private roundRepo: Repository<Round>,
    @InjectQueue(PROCESSORS.ForceRoundCompleteJob) private forceRoundCompleteJobQueue: Queue,
    @InjectQueue(PROCESSORS.IGolfRoundDrivingDispersionJob) private iGolfRoundDrivingDispersionJobQueue: Queue
  ) {}

  @Cron(CronExpression.EVERY_DAY_AT_1AM)
  async regenerateRoundMissingStats(roundIdsDto?: RoundIdsDto) {
    this.logger.log(`REGENERATE ROUND MISSING STATS....`);
    if (roundIdsDto && roundIdsDto?.roundIds?.length > 0) {
      for (const roundId of roundIdsDto.roundIds) {
        try {
          await this.forceRoundCompleteJobQueue.add(
            PROCESS_QUEUE_NAMES.FORCE_ROUND_COMPLETE,
            { roundId },
            OPTIONS_JOB_ROUND_DRIVING_DISPERSION_ROUND
          );
        } catch (error) {
          console.log(`CRONJOB ADD FORCE_ROUND_COMPLETE FAIL`);
          console.log(error);
        }
      }
      return;
    }
    if (!isCronJobHandlersEnabled()) {
      return;
    }
    this.logger.log(`START SCAN LIST ROUND MISSING STATS`);
    // Scan list round completed 7 day before.
    const dateScan = moment().subtract(7, 'days').format('YYYY-MM-DD');
    const roundQuery = this.roundRepo.createQueryBuilder();
    roundQuery.where(
      ` round_mode = :roundMode  AND 
        map_id = 'iGolf' AND 
        (created_at >= :dateScan OR updated_at >= :dateScan) AND 
        completed = true AND 
        stats_completed <> true 
        AND deleted_at is null
        
      `,
      { dateScan, roundMode: ROUND.ROUND_MODE_ADVANCED }
    );
    roundQuery.select(['id']);
    const rounds = await roundQuery.getRawMany();
    if (!rounds || isEmpty(rounds)) {
      this.logger.log(`ALL ROUNDS HAVE STATS...`);
      return;
    }
    this.logger.log(`HAVE ${rounds.length} ROUND MISSING STATS`);
    this.logger.log(rounds);
    const roundIds = rounds.map((r) => r.id);
    for (const roundId of roundIds) {
      try {
        await this.forceRoundCompleteJobQueue.add(
          PROCESS_QUEUE_NAMES.FORCE_ROUND_COMPLETE,
          { roundId },
          OPTIONS_JOB_ROUND_DRIVING_DISPERSION_ROUND
        );
      } catch (error) {
        console.log(`CRONJOB ADD QUEUE FORCE_ROUND_COMPLETE FAIL`);
        console.log(error);
      }
    }
  }
  @Cron(CronExpression.EVERY_DAY_AT_2AM)
  async recalculateDrivingDispersionStats() {
    this.logger.log(`RECALCULATE DRIVING DISPERSION STATS....`);
    if (!isCronJobHandlersEnabled()) {
      return;
    }
    this.logger.log(`START SCAN LIST ROUND NOT GENERATE DISPERSION STATS`);
    // Scan list round completed 7 day before.
    const dateScan = moment().subtract(7, 'days').format('YYYY-MM-DD');
    const roundQuery = this.roundRepo.createQueryBuilder();
    roundQuery.where(
      `
       round_mode = :roundMode AND 
       map_id = 'iGolf' AND 
       (created_at >= :dateScan OR updated_at >= :dateScan) AND 
       stats_completed = true  AND 
       driving_stat_complete <> true 
       AND deleted_at is null
       
       `,
      { dateScan, roundMode: ROUND.ROUND_MODE_ADVANCED }
    );
    roundQuery.select(['id']);
    const rounds = await roundQuery.getRawMany();
    if (!rounds || isEmpty(rounds)) {
      this.logger.log(`ALL ROUNDS GENERATED STATS...`);
      return;
    }
    this.logger.log(`HAVE ${rounds.length} ROUND MISSING DRIVING DISPERSION STATS`);
    this.logger.log(rounds);
    const roundIds = rounds.map((r) => r.id);
    for (const roundId of roundIds) {
      try {
        await this.iGolfRoundDrivingDispersionJobQueue.add(
          PROCESS_QUEUE_NAMES.IGOLF_ROUND_DRIVING_DISPERSION,
          { roundId },
          OPTIONS_JOB_ROUND_DRIVING_DISPERSION_ROUND
        );
      } catch (error) {
        console.log(error);
      }
    }
  }
}
