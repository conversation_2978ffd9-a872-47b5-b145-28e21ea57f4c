import { IsNotEmpty } from 'class-validator';

export class GhinScoreUpdateDto {
  @IsNotEmpty()
  golfer_id: string;

  @IsNotEmpty()
  course_id: string;

  @IsNotEmpty()
  tee_set_id: string;

  @IsNotEmpty()
  tee_set_side: string;

  @IsNotEmpty()
  played_at: string;

  @IsNotEmpty()
  score_type: string;

  @IsNotEmpty()
  number_of_holes: number;

  @IsNotEmpty()
  number_of_played_holes: number;

  @IsNotEmpty()
  gender: string;

  @IsNotEmpty()
  source: string;

  @IsNotEmpty()
  slope_rating: string;

  @IsNotEmpty()
  adjusted_gross_score?: number;

  @IsNotEmpty()
  course_rating?: number;

  @IsNotEmpty()
  course_name?: string;
}
