import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional } from 'class-validator';
import { PlayerDto } from 'src/players/dto/player.dto';
import { HoleDto } from './hole.dto';

export class RoundDto {
  @ApiProperty({ example: 14128 })
  @IsNotEmpty()
  user_id: number;

  @IsOptional()
  @ApiProperty({ example: 1 })
  id: number;

  @IsOptional()
  @ApiProperty({ example: 34 })
  duration?: number;

  @IsOptional()
  @ApiProperty({ example: '2' })
  event_id?: string;

  @IsOptional()
  @ApiProperty({ example: 'Professional' })
  tee_name: string;

  @IsOptional()
  @ApiProperty({ example: false })
  completed: boolean;

  @IsOptional()
  @ApiProperty({ example: '' })
  course_id?: string;

  @IsOptional()
  @ApiProperty({ example: '45cNQvmektK5' })
  igolf_course_id: string;

  @IsOptional()
  @ApiProperty({ example: 'La costa Resort & Spa - Champions' })
  course_name: string;

  @IsOptional()
  @ApiProperty({ example: 7220 })
  course_yards: number;

  @IsOptional()
  @ApiProperty({ example: 20 })
  course_par: number;

  @IsOptional()
  @ApiProperty({ example: 'iGolf' })
  map_id: string;

  @IsOptional()
  @ApiProperty({ example: new Date().toISOString() })
  played_on: Date;

  @IsOptional()
  @ApiProperty({ example: new Date().toISOString() })
  played_on_utc: Date;

  @IsOptional()
  @ApiProperty({ example: 'Practice' })
  round_type: string;

  @IsOptional()
  @ApiProperty({ example: 'MyTM+' })
  play_service: string;

  @IsOptional()
  @ApiProperty({ example: 'iOS' })
  play_client: string;

  @IsOptional()
  @ApiProperty({ example: 0 })
  total_score: number;

  @IsOptional()
  @ApiProperty({ example: 'iOS' })
  generated_by: string;

  @IsOptional()
  @ApiProperty({ example: '+7' })
  user_timezone: string;

  @IsOptional()
  @ApiProperty({ example: true })
  inprogress: boolean;

  @IsOptional()
  @ApiProperty({ type: () => HoleDto })
  holes: HoleDto[];

  @IsOptional()
  @ApiProperty({ type: () => PlayerDto })
  players: PlayerDto[];

  @IsOptional()
  @ApiProperty({ example: 'H' })
  source_type: string;

  @IsOptional()
  @ApiProperty({ example: 'ghin_course_id' })
  ghin_course_id: string;

  @IsOptional()
  @ApiProperty({ example: 'ghin_course_name' })
  ghin_course_name: string;

  @IsOptional()
  @ApiProperty({ example: 'ghin_tee_set_id' })
  ghin_tee_set_id: string;

  @IsOptional()
  @ApiProperty({ example: 'ghin_tee_set_name' })
  ghin_tee_set_name: string;

  @IsOptional()
  @ApiProperty({ example: 'golf_net_course_id' })
  golf_net_course_id: string;

  @IsOptional()
  @ApiProperty({ example: 'golf_net_course_name' })
  golf_net_course_name: string;

  @IsOptional()
  @ApiProperty({ example: 'golf_net_tee_set_id' })
  golf_net_tee_id: string;

  @IsOptional()
  @ApiProperty({ example: 'golf_net_tee_set_name' })
  golf_net_tee_name: string;

  @IsOptional()
  @ApiProperty({ example: 'Simple' })
  round_mode: string;
}
