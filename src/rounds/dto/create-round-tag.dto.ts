import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsDefined,
  IsEnum,
  IsNotEmptyObject,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';

export enum MapCourseType {
  TAG = 'TAG',
  IGOLF = 'IGOLF',
}

export enum GeneratedByType {
  TAG_IOS = 'TAG iOS',
  TAG_ANDROID = 'TAG Android',
  TAG_WATCH_OS = 'TAG Watch OS',
  TAG_CONNECTED = 'TAG Connected',
}

export class HoleDto {
  @ApiProperty({ example: 1, description: 'Hole number' })
  @IsNumber()
  number: number;

  @ApiProperty({ example: 4, description: 'Par for the hole' })
  @IsNumber()
  par: number;

  @ApiProperty({ example: 402, description: 'Distance in yards' })
  @IsNumber()
  distance: number;

  @ApiProperty({ example: 11, description: 'Hole handicap' })
  @IsNumber()
  handicap: number;

  @ApiProperty({ example: [33.09246147206435, -117.2636182978749] })
  @IsArray()
  coords: number[];
}

export class RoundTagDto {
  @ApiProperty({ example: 'tag_user_id' })
  @IsOptional()
  @IsString()
  user_id: string;

  @ApiProperty({ example: 'tag_user_email' })
  @IsString()
  email: string;

  @ApiProperty({ example: 'Professional' })
  @IsString()
  tee_name: string;

  @ApiProperty({ example: 'E16FF621' })
  @IsString()
  course_id: string;

  @ApiProperty({ example: 'La Costa Resort & Spa - North' })
  @IsString()
  course_name: string;

  @ApiProperty({ example: 'TAG', enum: MapCourseType })
  @IsEnum(MapCourseType)
  map_course: MapCourseType;

  @ApiProperty({ example: 'TAG iOS', enum: GeneratedByType })
  @IsEnum(GeneratedByType)
  generated_by: GeneratedByType;

  @ApiProperty({ example: '2025-07-23T09:58:54.205Z' })
  @IsString()
  played_on_utc: string;

  @ApiProperty({ example: '7' })
  @IsString()
  user_timezone: string;

  @ApiProperty({
    example: [
      {
        number: 1,
        par: 4,
        distance: 402,
        handicap: 11,
        pin_location: [33.095353, -117.263791],
      },
    ],
  })
  @IsOptional()
  @IsArray()
  @Type(() => HoleDto)
  holes?: HoleDto[];
}

export class CreateRoundTagDto {
  @ApiProperty({ type: RoundTagDto })
  @IsDefined()
  @IsNotEmptyObject()
  @IsObject()
  @ValidateNested()
  @Type(() => RoundTagDto)
  round: RoundTagDto;
}
