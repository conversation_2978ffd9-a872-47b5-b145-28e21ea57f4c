import { IsNotEmpty, IsOptional } from 'class-validator';

export class GhinScoreHoleDto {
  @IsNotEmpty()
  hole_number: number;

  @IsOptional()
  raw_score?: number;

  @IsOptional()
  total_score?: number;

  @IsOptional()
  putts?: number;

  @IsOptional()
  fairway_hit?: boolean;

  @IsOptional()
  gir_flag?: boolean;

  @IsOptional()
  drive_accuracy?: number;

  @IsOptional()
  par?: number;

  @IsOptional()
  approach_shot_accuracy?: number;

  @IsOptional()
  most_likely_score?: number;

  @IsOptional()
  x_hole?: boolean;
}
