import { ApiProperty } from '@nestjs/swagger';
import { IsOptional } from 'class-validator';
import { StrokeDto } from './stroke.dto';

export class HoleDto {
  @IsOptional()
  @ApiProperty({ example: 4 })
  par: number;

  @IsOptional()
  @ApiProperty({ example: 6 })
  score: number;

  @IsOptional()
  @ApiProperty({ example: 1 })
  number: number;

  @IsOptional()
  @ApiProperty({ type: () => StrokeDto })
  strokes: StrokeDto[];

  @IsOptional()
  @ApiProperty({ example: [33.09534714849834, -117.2637851448075] })
  pin_location: [];

  @IsOptional()
  @ApiProperty({ example: 1 })
  stroke_index: string;
}
