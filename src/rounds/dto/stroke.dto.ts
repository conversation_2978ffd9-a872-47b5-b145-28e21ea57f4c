import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';
import { Timestamp } from 'typeorm';
import { StartingLie } from '../enums/starting-lie.enum';

export class StrokeDto {
  @IsOptional()
  @ApiProperty({ example: [33.09246147206435, -117.2636182978749] })
  coords: [];

  @IsOptional()
  @ApiProperty({ example: 199487 })
  club_id: number;

  @IsOptional()
  @ApiProperty({ example: 1 })
  ordinal: number;

  @IsOptional()
  @ApiProperty({ example: false })
  penalty: boolean;

  @IsOptional()
  @ApiProperty({ example: false })
  recovery: boolean;

  @IsOptional()
  @ApiProperty({ example: false })
  difficult: boolean;

  @IsOptional()
  @ApiProperty({ example: '1495419787.546189' })
  timestamp: Timestamp;

  @IsOptional()
  @ApiProperty({
    example: StartingLie.TEE,
    enum: StartingLie,
    description: 'Starting lie position for the stroke',
  })
  @IsEnum(StartingLie)
  starting_lie: StartingLie;
}
