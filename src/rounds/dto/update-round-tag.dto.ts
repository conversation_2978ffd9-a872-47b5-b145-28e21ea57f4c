import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsBoolean, IsEnum, IsNotEmptyObject, IsNumber, IsOptional, ValidateNested } from 'class-validator';
import { StartingLie } from '../enums/starting-lie.enum';

export class UpdateStrokeTagDto {
  @ApiProperty({ example: [33.09246147206435, -117.2636182978749] })
  @IsArray()
  coords: number[];

  @ApiProperty({ example: 1 })
  @IsNumber()
  ordinal: number;

  @ApiProperty({ example: false })
  @IsBoolean()
  penalty: boolean;

  @ApiProperty({
    example: StartingLie.TEE,
    enum: StartingLie,
    description: 'Starting lie position for the stroke',
  })
  @IsEnum(StartingLie)
  starting_lie: StartingLie;
}

export class UpdateHoleTagDto {
  @ApiProperty({ example: 1 })
  @IsNumber()
  number: number;

  @ApiProperty({ example: 4 })
  @IsNumber()
  par: number;

  @ApiProperty({ example: 6 })
  @IsNumber()
  score: number;

  @ApiProperty({ example: 9 })
  @IsOptional()
  @IsNumber()
  handicap?: number;

  @ApiProperty({ example: 350 })
  @IsOptional()
  @IsNumber()
  distance?: number;

  @ApiProperty({ example: [33.09534714849834, -117.2637851448075] })
  @IsOptional()
  @IsArray()
  pin_location?: number[];

  @ApiProperty({ type: [UpdateStrokeTagDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpdateStrokeTagDto)
  strokes: UpdateStrokeTagDto[];
}

export class UpdateRoundTagDataDto {
  @ApiProperty({ type: [UpdateHoleTagDto] })
  @IsOptional()
  @IsArray()
  @Type(() => UpdateHoleTagDto)
  holes: UpdateHoleTagDto[];

  @ApiProperty({ example: 6 })
  @IsOptional()
  @IsNumber()
  total_score: number;

  @ApiProperty({ example: false })
  @IsBoolean()
  completed: boolean;
}

export class UpdateRoundTagDto {
  @ApiProperty({ type: UpdateRoundTagDataDto })
  @IsNotEmptyObject()
  @Type(() => UpdateRoundTagDataDto)
  round: UpdateRoundTagDataDto;
}
