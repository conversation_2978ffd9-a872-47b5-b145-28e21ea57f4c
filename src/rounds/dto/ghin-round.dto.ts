import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional } from 'class-validator';

export class GhinRoundDto {
  @ApiProperty({ example: 1 })
  @IsOptional()
  id?: number;

  @ApiProperty({ example: 1 })
  @IsNotEmpty()
  user_id: number;

  @ApiProperty({ example: 1 })
  @IsNotEmpty()
  ghin_id: number;

  @ApiProperty({ example: 1 })
  @IsNotEmpty()
  ghin_round_id: number;

  @ApiProperty({ example: 1 })
  @IsOptional()
  ghin_parent_round_id: number;

  @ApiProperty({ example: 'string' })
  @IsNotEmpty()
  ghin_course_id: string;

  @ApiProperty({ example: 'string' })
  @IsNotEmpty()
  ghin_course_name: string;

  @ApiProperty({ example: 'string' })
  @IsNotEmpty()
  ghin_tee_set_id: string;

  @ApiProperty({ example: 'string' })
  @IsNotEmpty()
  ghin_tee_set_name: string;

  @ApiProperty({ example: 'string' })
  @IsNotEmpty()
  ghin_tee_set_side: string;

  @ApiProperty({ example: 1.0 })
  @IsNotEmpty()
  course_rating: number;

  @ApiProperty({ example: 9 })
  @IsOptional()
  number_of_holes_played: number;

  @ApiProperty({ example: 1 })
  @IsNotEmpty()
  score: number;

  @ApiProperty({ example: 1 })
  @IsOptional()
  score_to_par?: number;

  @ApiProperty({ example: 1 })
  @IsOptional()
  course_par?: number;

  @ApiProperty({ example: 1 })
  @IsOptional()
  course_yards?: number;

  @ApiProperty({ example: 1 })
  @IsOptional()
  slope_rating?: number;

  @ApiProperty({ example: 'string' })
  @IsOptional()
  score_type?: string;

  @ApiProperty({ example: 'iOS' })
  @IsOptional()
  generated_by?: string;

  @ApiProperty({ example: new Date().toISOString() })
  @IsOptional()
  created_at?: Date;

  @ApiProperty({ example: new Date().toISOString() })
  @IsOptional()
  played_on?: Date;

  @ApiProperty({ example: new Date().toISOString() })
  @IsOptional()
  updated_at?: Date;
}
