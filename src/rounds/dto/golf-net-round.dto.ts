import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional } from 'class-validator';

export class GolfNetRoundDto {
  @ApiProperty({ example: 14128 })
  @IsNotEmpty()
  user_id: number;

  @IsOptional()
  @ApiProperty({ example: '1' })
  canada_card_id: string;

  @IsOptional()
  @ApiProperty({ example: '200' })
  golfnet_round_id: string;

  @IsOptional()
  @ApiProperty({ example: 'golf_net_course_id' })
  golfnet_course_id: string;

  @IsOptional()
  @ApiProperty({ example: 'golf_net_course_name' })
  golfnet_course_name: string;

  @IsOptional()
  @ApiProperty({ example: 'golf_net_tee_set_id' })
  golfnet_tee_set_id: string;

  @IsOptional()
  @ApiProperty({ example: 'golf_net_tee_set_name' })
  golfnet_tee_set_name: string;

  @IsOptional()
  @ApiProperty({ example: 0 })
  score: number;

  @IsOptional()
  @ApiProperty({ example: 0 })
  score_to_par: number;

  @IsOptional()
  @ApiProperty({ example: 'iOS' })
  generated_by: string;

  @IsOptional()
  @ApiProperty({ example: 1.0 })
  course_rating: number;

  @IsOptional()
  @ApiProperty({ example: 1 })
  slope_rating: number;

  @IsOptional()
  @ApiProperty({ example: 1 })
  course_yards: number;

  @IsOptional()
  @ApiProperty({ example: 1 })
  course_par: number;

  @IsOptional()
  @ApiProperty({ example: 1 })
  number_of_holes_played: number;

  @IsOptional()
  @ApiProperty({ example: 1 })
  user_timezone: number;

  @IsOptional()
  @ApiProperty({ example: 'string' })
  score_type: string;

  @IsOptional()
  @ApiProperty({ example: new Date().toString() })
  played_on: Date;

  @IsOptional()
  @ApiProperty({ example: new Date().toISOString() })
  played_on_utc: Date;

  @ApiProperty({ example: new Date().toISOString() })
  @IsOptional()
  created_at?: Date;

  @ApiProperty({ example: new Date().toISOString() })
  @IsOptional()
  updated_at?: Date;
}
