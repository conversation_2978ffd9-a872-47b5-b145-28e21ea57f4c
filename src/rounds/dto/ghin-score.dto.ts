import { IsNotEmpty, IsOptional } from 'class-validator';
import { GhinScoreHoleDto } from './ghin-score.hole.dto';

/*
drive_accuracy: 0 - Missed Left, 1 - Missed Right, 2 - Missed Long, 3 - Missed Short 
approach_shot_accuracy: 0 - Missed Left, 1 - Missed Right, 2 - Miss<PERSON> Long, 3 - Missed Short
The score can have holes that are not played, but not more than 4 for 18-hole scores and 2 for 9-hole scores,
 or holes that are most likely scores. x_hole: true - hole not played, 
 most_likely_score: number - the value of the most likely score
*/
export class GhinScoreDto {
  @IsNotEmpty()
  golfer_id: string;
  @IsNotEmpty()
  course_id: string;
  @IsNotEmpty()
  tee_set_id: string;
  @IsNotEmpty()
  tee_set_side: string;
  @IsNotEmpty()
  played_at: string;
  @IsNotEmpty()
  score_type: string;
  @IsNotEmpty()
  number_of_holes: number;
  @IsNotEmpty()
  gender: string;
  @IsNotEmpty()
  override_confirmation: boolean;
  @IsNotEmpty()
  is_manual: boolean;
  @IsNotEmpty()
  source: string;

  @IsOptional()
  with_gps?: boolean;
  @IsOptional()
  with_watch?: boolean;

  @IsOptional()
  game_score?: boolean;

  @IsOptional()
  game_type?: string;

  @IsOptional()
  transferred?: boolean;

  @IsOptional()
  hole_details?: GhinScoreHoleDto[];

  @IsOptional()
  allow_duplicates?: boolean;

  @IsOptional()
  adjusted_gross_score?: number;

  // data post score 9 holes
  @IsOptional()
  front9_adjusted?: number;

  @IsOptional()
  back9_adjusted?: number;

  @IsOptional()
  number_of_played_holes: number;
}
