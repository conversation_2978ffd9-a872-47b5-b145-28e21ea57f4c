import { Module } from '@nestjs/common';
import { SharedModule } from 'src/shared/shared.module';
import { RoundCronService } from './round.cron.service';
import { RoundController } from './rounds.controller';
import { RoundService } from './rounds.service';

@Module({
  imports: [SharedModule],
  controllers: [RoundController],
  providers: [RoundService, RoundCronService],
  exports: [RoundService, RoundCronService],
})
export class RoundModule {}
