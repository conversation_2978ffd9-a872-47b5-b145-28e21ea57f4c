import { InjectQueue } from '@nestjs/bull';
import {
  Body,
  ClassSerializerInterceptor,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Patch,
  Post,
  Put,
  Req,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { Api<PERSON><PERSON>erA<PERSON>, ApiParam, ApiTags } from '@nestjs/swagger';
import { Queue } from 'bull';
import { AuthGuard } from 'src/guards/auth.guard';
import { RoundAuditService } from 'src/round-audit/round-audit.service';
import { isValidId } from 'src/utils/utils';
import { PROCESSORS, PROCESS_QUEUE_NAMES } from 'src/workers/jobs/job.constant';
import { CreateRoundTagDto } from './dto/create-round-tag.dto';
import { CreateRoundDto } from './dto/create-round.dto';
import { PostScoreGolfNetDto } from './dto/post-score-golfnet.dto';
import { PostScoreDto } from './dto/post-score.dto';
import { RoundIdsDto } from './dto/round-ids.dto';
import { UpdateRoundTagDto } from './dto/update-round-tag.dto';
import { DATE_CAN_POST_SCORE_TO_USGA } from './round.const';
import { RoundService } from './rounds.service';

@ApiBearerAuth()
@UseGuards(AuthGuard)
@ApiTags('Round')
@Controller('rounds')
@UseInterceptors(ClassSerializerInterceptor)
export class RoundController {
  constructor(
    private readonly roundService: RoundService,
    private readonly roundAuditService: RoundAuditService,

    @InjectQueue(PROCESSORS.ClearShotNotInHole) private clearShotInHole: Queue
  ) {}

  @Post()
  create(@Body() createRoundDto: CreateRoundDto) {
    return this.roundAuditService.postRoundAudit(createRoundDto);
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Put('tag/:id')
  @ApiParam({ name: 'id', example: 957316 })
  updateRoundTag(@Param('id') id: string, @Body() updateRoundTagDto: UpdateRoundTagDto) {
    if (isValidId(id)) {
      return this.roundService.updateRoundTag(+id, updateRoundTagDto);
    }
    return {};
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Post('tag')
  createRoundTag(@Body() createRoundTagDto: CreateRoundTagDto, @Req() req: any) {
    const company = req?.client?.company;
    return this.roundService.createRoundTag(createRoundTagDto, company);
  }

  // @Get()
  // findAll(@Query('light') light?: boolean, @Query('per') per?: number) {
  //   return this.roundService.findAll();
  // }

  @Get(':id')
  @ApiParam({ name: 'id', example: 957316 })
  findOne(@Param('id') id: string) {
    if (isValidId(id)) {
      try {
        this.clearShotInHole.add(PROCESS_QUEUE_NAMES.CLEAR_SHOT_NOT_IN_HOLE, { roundId: id });
      } catch (error) {
        console.log(error);
      }

      return this.roundService.findOneBy(+id);
    }
    return {};
  }

  @Get(':id/stats_completed')
  @ApiParam({ name: 'id', example: 957316 })
  checkStatsCompleted(@Param('id') id: string) {
    if (isValidId(id)) {
      return this.roundService.checkStatsCompleted(+id);
    }
    return {};
  }

  // USGA
  @Get('/ghin/can_post_score_usga')
  canPostScoreUSGA() {
    return { date: DATE_CAN_POST_SCORE_TO_USGA };
  }

  @Get(':id/post_score')
  @ApiParam({ name: 'id', example: 957316 })
  postScore(@Param('id') id: string) {
    if (isValidId(id)) {
      return this.roundService.postScoreToGHIN(+id);
    }
    return {};
  }
  @Post(':id/post_score')
  @ApiParam({ name: 'id', example: 957316 })
  confirmPostScore(@Param('id') id: string, @Body() postScoreDto: PostScoreDto) {
    if (isValidId(id)) {
      return this.roundService.confirmPostScoreToGHIN(+id, postScoreDto);
    }
    return {};
  }
  // Canada
  @Get(':id/post_score_golf_net')
  @ApiParam({ name: 'id', example: 957316 })
  postScoreToGolfNet(@Param('id') id: string) {
    if (isValidId(id)) {
      return this.roundService.postScoreToGolfNet(+id);
    }
    return {};
  }
  @Post(':id/post_score_golf_net')
  @ApiParam({ name: 'id', example: 957316 })
  confirmPostScoreToGolfNet(@Param('id') id: string, @Body() postScoreDto: PostScoreGolfNetDto) {
    if (isValidId(id)) {
      return this.roundService.confirmPostScoreToGolfNet(+id, postScoreDto);
    }
    return {};
  }

  @Post('/trigger/update-stats')
  forceUpdateRoundStats(@Body() roundIds: RoundIdsDto) {
    this.roundService.forceUpdateRoundStats(roundIds);
    return { success: true };
  }

  @Patch(':id')
  @ApiParam({ name: 'id', example: 957316 })
  pathUpdate(@Param('id') id: string, @Body() roundDto: CreateRoundDto) {
    if (isValidId(id)) {
      return this.roundService.update(+id, roundDto);
    }
    return {};
  }
  @Put(':id')
  @ApiParam({ name: 'id', example: 957316 })
  putUpdateRound(@Param('id') id: string, @Body() roundDto: CreateRoundDto) {
    if (isValidId(id)) {
      return this.roundService.update(+id, roundDto);
    }
    return {};
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiParam({ name: 'id', example: 957316 })
  remove(@Param('id') id: string) {
    if (isValidId(id)) {
      return this.roundService.remove(+id);
    }
    return {};
  }
}
