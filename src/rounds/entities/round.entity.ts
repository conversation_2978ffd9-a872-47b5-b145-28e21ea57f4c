import { ApiProperty } from '@nestjs/swagger';
import { Exclude } from 'class-transformer';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { HolePlayed } from 'src/holes-played/entities/hole-played.entity';

@Entity({ name: 'rounds' })
export class Round {
  @PrimaryGeneratedColumn()
  @ApiProperty({ example: 1 })
  @Index()
  id: number;

  @Column({ nullable: true, type: 'integer' })
  @ApiProperty({ example: '1' })
  'client_id': number;

  @Column({ nullable: true, type: 'integer' })
  @ApiProperty({ example: '' })
  'facility_id': number;

  @Column({ nullable: true, type: 'integer' })
  @ApiProperty({ example: '' })
  'tee_id': number;

  @Column({ nullable: true, type: 'integer' })
  @ApiProperty({ example: '41428' })
  @Index()
  'user_id': number;

  @Column({ nullable: true, type: 'integer' })
  @ApiProperty({ example: '1' })
  'event_id': number;

  @Column({ nullable: true, type: 'integer' })
  @ApiProperty({ example: 104 })
  'total_score': number;

  @Column({ nullable: true, type: 'integer' })
  @ApiProperty({ example: 12 })
  'duration': number;

  @Column({ nullable: true, type: 'integer' })
  @ApiProperty({ example: '' })
  'lowest_heartrate': number;

  @Column({ nullable: true, type: 'integer' })
  @ApiProperty({ example: '' })
  'average_heartrate': number;

  @Column({ nullable: true, type: 'integer' })
  @ApiProperty({ example: '' })
  'peak_heartrate': number;

  @Column({ nullable: true, type: 'integer' })
  @ApiProperty({ example: 'calories_burned' })
  'calories_burned': number;

  @Column({ nullable: true })
  @ApiProperty({ example: '+7' })
  'user_timezone': string;

  @Column({ nullable: true })
  @ApiProperty({ example: '' })
  @Exclude()
  'postback_url': string;

  @DeleteDateColumn({ nullable: true })
  @ApiProperty({ example: null })
  'deleted_at': Date;

  @CreateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  'created_at': Date;

  @UpdateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  'updated_at': Date;

  @Column({ type: 'timestamp' })
  @ApiProperty({ example: new Date().toISOString() })
  'played_on': Date;

  @Column({ nullable: true })
  @ApiProperty({ example: 'Practice' })
  'round_type': string;

  @Column({ nullable: true })
  @ApiProperty({ example: '' })
  'temperature': string;

  @Column({ nullable: true })
  @ApiProperty({ example: '' })
  'ground_conditions': string;

  @Column({ nullable: true })
  @ApiProperty({ example: 'iOS' })
  'generated_by': string;

  @Column({ type: 'timestamp', nullable: true })
  @ApiProperty({ example: new Date().toISOString() })
  'time_stamp': Date;

  @Column({ nullable: true })
  @ApiProperty({ example: 32 })
  'course_par': number;

  @Column({ nullable: true, type: 'boolean' })
  @ApiProperty({ example: false })
  'completed': boolean;

  @Column({ nullable: true })
  @ApiProperty({ example: '' })
  'import': string;

  @Column({ nullable: true })
  @ApiProperty({ example: 'White' })
  'tee_name': string;

  @Column({ nullable: true })
  @ApiProperty({ example: '' })
  // @Exclude()
  'player_metadata': string;

  @Column({ nullable: true })
  @ApiProperty({ example: '' })
  'course_conditions': string;

  @Column({ nullable: true, select: false })
  @ApiProperty({ example: '' })
  'holes_completed': string;

  @Column({ nullable: true })
  @ApiProperty({ example: 0 })
  'course_id': number;

  @Column({ nullable: true })
  @ApiProperty({ example: 10 })
  'course_yards': number;

  @Column({ nullable: true })
  @ApiProperty({ example: 0 })
  @Exclude()
  'steps_count': number;

  @Column({ nullable: true, type: 'boolean' })
  @ApiProperty({ example: false })
  'updated_stroke': boolean;

  @Column({ nullable: true, type: 'boolean' })
  @ApiProperty({ example: false })
  'updated_missing_coords': boolean;

  @Column({ nullable: true, type: 'boolean' })
  @ApiProperty({ example: true })
  'inprogress': boolean;

  @Column({ nullable: true, type: 'boolean' })
  @ApiProperty({ example: false })
  'driving_stat_complete': boolean;

  @Column({ nullable: true, type: 'varchar' })
  @ApiProperty({ example: 'Advanced' })
  @Index()
  'round_mode': string;

  @Column({ nullable: true })
  @ApiProperty({ example: 'Part B - Long Bien Golf Course' })
  'course_name': string;

  @Column({ nullable: true })
  @ApiProperty({ example: 'iGolf' })
  'map_id': string;

  @Column({ nullable: true })
  @ApiProperty({ example: 'XQiSFtiwAw0B' })
  @Index()
  'igolf_course_id': string;

  @Column({ nullable: true, type: 'boolean' })
  @ApiProperty({ example: false })
  'team': boolean;

  @Column({ nullable: true })
  @ApiProperty({ example: '' })
  'multiplayer_game_type': string;

  @Column({ nullable: true })
  @ApiProperty({ example: '' })
  'device_token': string;

  @Column({ nullable: true, type: 'integer' })
  @ApiProperty({ example: 0 })
  'front_9_score': number;

  @Column({ nullable: true })
  @ApiProperty({ example: 0 })
  'number_of_holes_played': number;

  @Column({ nullable: true })
  @ApiProperty({ example: 0 })
  'back_9_score': number;

  @Column({ nullable: true })
  @ApiProperty({ example: 0 })
  'eighteen_holes_score': number;

  @Column({ nullable: true })
  @ApiProperty({ example: 0 })
  'simple_score_to_par': number;

  @Column({ nullable: true })
  @ApiProperty({ example: 0 })
  'ghin_round_id': number;

  @Column({ nullable: true })
  @ApiProperty({ example: '' })
  'play_service': string;

  @Column({ nullable: true })
  @ApiProperty({ example: '' })
  'play_client': string;

  @Column({ nullable: true })
  @ApiProperty({ example: false })
  'stats_completed': boolean;

  @Column({ nullable: true })
  @ApiProperty({ example: '' })
  'ghin_course_id': string;

  @Column({ nullable: true })
  @ApiProperty({ example: '' })
  'ghin_course_name': string;

  @Column({ nullable: true })
  @ApiProperty({ example: '' })
  'ghin_tee_set_id': string;

  @Column({ nullable: true })
  @ApiProperty({ example: '' })
  'ghin_tee_set_name': string;

  @Column({ nullable: true })
  @ApiProperty({ example: 60 })
  'ghin_score': number;

  @Column({ nullable: true })
  @ApiProperty({ example: '' })
  'source_type': string;

  @Column({ nullable: true })
  @ApiProperty({ example: '' })
  'golfnet_course_id': string;

  @Column({ nullable: true })
  @ApiProperty({ example: '' })
  'golfnet_course_name': string;

  @Column({ nullable: true })
  @ApiProperty({ example: '' })
  'golfnet_tee_set_id': string;

  @Column({ nullable: true })
  @ApiProperty({ example: '' })
  'golfnet_tee_set_name': string;

  @Column({ nullable: true })
  @ApiProperty({ example: '' })
  'golfnet_round_id': string;

  @Column({ nullable: true })
  @ApiProperty({ example: '' })
  'arccos_round_id': string;

  @OneToMany(() => HolePlayed, (hole) => hole.round, {
    eager: true,
  })
  holes?: HolePlayed[];
}
