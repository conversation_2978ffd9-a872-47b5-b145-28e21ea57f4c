import { ApiProperty } from '@nestjs/swagger';
import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

@Entity({ name: 'course_error' })
export class CourseErrorEntity {
  @PrimaryGeneratedColumn()
  @ApiProperty({ example: 1 })
  id: number;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  course_id: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  course_name: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  type: string;

  @Column({ nullable: false })
  @ApiProperty({ example: false })
  is_not_course_detail: boolean;

  @Column({ nullable: false })
  @ApiProperty({ example: false })
  is_not_ghin_id: boolean;

  @Column({ nullable: false })
  @ApiProperty({ example: false })
  is_not_tees: boolean;

  @Column({ nullable: false })
  @ApiProperty({ example: false })
  is_not_gps_detail: boolean;

  @Column({ nullable: false })
  @ApiProperty({ example: false })
  is_not_gps_vector_detail: boolean;

  @Column({ nullable: false })
  @ApiProperty({ example: false })
  is_not_score_card_detail: boolean;

  @CreateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  created_at: Date;

  @UpdateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  updated_at: Date;
}
