import { Body, Controller, HttpCode, HttpStatus, Post, Req } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { CourseSearchDto } from './dto/course-search.dto';
import { CourseDto } from './dto/course.dto';
import { GlxService } from './glx.service';

@ApiTags('GLX')
@Controller('glx')
export class GlxController {
  constructor(private readonly glxService: GlxService) {}

  @Post('detail')
  @HttpCode(HttpStatus.OK)
  async getCourseDetail(@Body() courseDto: CourseDto, @Req() req: any) {
    const course = await this.glxService.getCourseDetail(courseDto.id_course, req?.headers?.client);
    return course;
  }

  @Post('search')
  @HttpCode(HttpStatus.OK)
  async searchCourse(@Body() courseSearchDto: CourseSearchDto, @Req() req: any) {
    return this.glxService.searchCourse(courseSearchDto, req?.headers?.client);
  }

  @Post('course_gps_detail')
  @HttpCode(HttpStatus.OK)
  async getCourseGPSDetail(@Body() courseDto: CourseDto, @Req() req: any) {
    return this.glxService.getCourseGPSDetail(courseDto.id_course, req?.headers?.client);
  }

  @Post('course_gps_vector_detail')
  @HttpCode(HttpStatus.OK)
  async getCourseGPSVectorDetail(@Body() courseDto: CourseDto, @Req() req: any) {
    return this.glxService.getCourseGPSVectorDetail(courseDto.id_course, req?.headers?.client);
  }

  @Post('course_scorecard_detail')
  @HttpCode(HttpStatus.OK)
  async getCourseScoreCardDetail(@Body() courseDto: CourseDto, @Req() req: any) {
    return this.glxService.getCourseScoreCardDetail(courseDto.id_course, req?.headers?.client);
  }

  @Post('course_tee_detail')
  @HttpCode(HttpStatus.OK)
  async getCourseTeeDetail(@Body() courseDto: CourseDto, @Req() req: any) {
    return this.glxService.getCourseTeeDetail(courseDto.id_course, req?.headers?.client);
  }

  @Post('course_elevation_data_detail')
  @HttpCode(HttpStatus.OK)
  async getCourseElevationDataDetail(@Body() courseDto: CourseDto, @Req() req: any) {
    return this.glxService.getCourseElevationDataDetail(courseDto.id_course, req?.headers?.client);
  }
}
