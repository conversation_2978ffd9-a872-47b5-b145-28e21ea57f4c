export enum GlxConst {
  API_VERSION = '1.1',
  SIGNATURE_VERSION = '2.0',
  SIGNATURE_METHOD = 'HmacSHA256',
  RESPONSE_FORMAT = 'JSON',

  ACTION_COURSE_DETAIL = 'CourseDetails',
  ACTION_COURSE_GPS_DETAIL = 'CourseGPSDetails',
  ACTION_COURSE_GPS_VECTOR_DETAIL = 'CourseGPSVectorDetails',
  ACTION_COURSE_SCORECARD = 'CourseScorecardDetails',
  ACTION_COURSE_TEE_DETAIL = 'CourseTeeDetails',
  ACTION_COURSE_ELEVATION_DATA_DETAIL = 'CourseElevationDataDetails',
  ACTION_GLX_COURSE_LIST_NAME = '/list/search/',
  ACTION_GLX_COURSE_LIST_GPS = '/list/nearby/',
  ACTION_GLX_VALIDATE = '/validate',
  ACTION_GLX_COURSE_DETAIL = '/detail',
  ACTION_GLX_COURSE_SCORECARD = '/scorecard',
  ACTION_GLX_GPS_DATA = '/gpsdata',
  ACTION_GLX_GPS_VECTOR = '/gpsvector',
  ACTION_COURSE_LIST = 'CourseList',

  CACHE_COURSE_PREFIX = 'COURSE',
  CACHE_COURSE_LIST_GPS = 'COURSE_GLX:GPS_LIST',
  CACHE_GLX_COURSE_DETAIL = 'COURSE_GLX:DETAIL',
  CACHE_GLX_COURSE_GPS_DETAIL = 'COURSE_GLX:GPS_DETAIL',
  CACHE_GLX_COURSE_GPS_VECTOR_DETAIL = 'COURSE_GLX:GPS_VECTOR_DETAIL',
  CACHE_GLX_COURSE_SCORECARD = 'COURSE_GLX:SCORECARD',
  CACHE_GLX_COURSE_TEE_DETAIL = 'COURSE_GLX:TEE_DETAIL',
  CACHE_GLX_COURSE_ELEVATION_DATA_DETAIL = 'COURSE_GLX:ELEVATION_DATA_DETAIL',
}
