import { InjectQueue } from '@nestjs/bull';
import { Cache } from '@nestjs/cache-manager';
import { CACHE_MANAGER, Inject, Injectable, Logger, forwardRef } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm';
import * as turf from '@turf/turf';
import axios from 'axios';
import { Queue } from 'bull';
import crypto from 'crypto';
import CryptoJS from 'crypto-js';
import { convertDistance, getBounds, getDistance } from 'geolib';
import _, { compact, isEmpty } from 'lodash';
import moment from 'moment';
import ms from 'ms';
import { EntityManager, Repository } from 'typeorm';
import { Round } from 'src/rounds/entities/round.entity';
import { OPTIONS_JOB_DEFAULT } from 'src/rounds/round.const';
import { centroid } from 'src/utils/utils';
import { PROCESSORS, PROCESS_QUEUE_NAMES } from 'src/workers/jobs/job.constant';
import { GhinGLXService } from '../ghin/ghinGLX.service';
import { UserLogCourseEntity } from '../igolf/entity/user-log-course.entity';
import { KeyConfigEntity } from '../key-config/entities/key-config.entity';
import { User } from '../users/entities/user.entity';
import { formatGLXCourseDetail, formatGLXCourseListData } from '../utils/format-glx-course';
import { CourseSearchDto } from './dto/course-search.dto';
import { UserLogCourseDto } from './dto/user-log-course.dto';
import { CourseErrorEntity } from './entity/course-error.entity';
import { GlxConst } from './glx.const';
import { CLIENTS } from './glx.types';

const LIMIT_TIME_REQUEST_COURSES = 'LIMIT_TIME_REQUEST_COURSES';

@Injectable()
export class GlxService {
  private readonly logger = new Logger(GlxService.name);
  apiTriggerKlaviyoSpamIgolf: any;
  constructor(
    private configService: ConfigService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    @InjectRepository(Round)
    private roundRepository: Repository<Round>,
    @InjectRepository(User)
    private userRepo: Repository<User>,
    @InjectRepository(UserLogCourseEntity)
    private userLogCourseRepo: Repository<UserLogCourseEntity>,
    @InjectRepository(CourseErrorEntity)
    private courseErrorRepo: Repository<CourseErrorEntity>,
    @InjectRepository(KeyConfigEntity)
    private keyConfigRepo: Repository<KeyConfigEntity>,
    @Inject(forwardRef(() => GhinGLXService)) private ghinGLXService: GhinGLXService,
    @InjectQueue(PROCESSORS.LogRequestIGolfSI) private logRequestIGolf: Queue,
    @InjectEntityManager()
    private readonly entityManager: EntityManager
  ) {
    const mytmEndpoint = configService.get('mytm.endpoint');
    this.apiTriggerKlaviyoSpamIgolf = mytmEndpoint + '/play/spam';
  }

  getRequestHeaderConfigs() {
    return {
      headers: { clientId: this.configService.get('mytm.clientId') },
    };
  }

  base64UrlEncode(input) {
    return input
      .replace(/\+/g, '-') // Replace + with -
      .replace(/\//g, ',') // Replace / with ,
      .replace(/=/g, '_'); // Replace = with _
  }

  generateSignature(timestamp, token, uid, method, path, queryParams, clientSecret) {
    const message = `${timestamp}${token}${uid}${method}${path}${queryParams}`;

    // Create HMAC signature
    const hmac = crypto.createHmac('sha256', clientSecret);
    hmac.update(message);
    const signature = hmac.digest('base64');

    const encodedSignature = this.base64UrlEncode(signature);

    // URL encode signature
    return `${timestamp}-${encodedSignature}`;
  }

  async getCourseDetail(idCourse: string, client?: string) {
    const payload = {
      id_course: idCourse,
      detailLevel: 2,
      stateFormat: 3,
      countryFormat: 3,
      client,
    };
    const cacheDetail = await this.getCacheCourse(GlxConst.CACHE_GLX_COURSE_DETAIL, payload);
    if (cacheDetail) {
      return cacheDetail;
    }

    const data = await this.callGLXService(GlxConst.ACTION_GLX_COURSE_DETAIL, payload);
    return this.formatCourseDetail(data, idCourse);
  }

  async searchCourse(courseDto: CourseSearchDto, client?: string) {
    let isSearchGPS = false;
    const user = await this.userRepo.findOne({
      where: { id: courseDto?.mrpId },
    });
    if (!user) {
      return { msg: 'User not found!' };
    }
    // fake gps acc: <EMAIL>
    if (courseDto?.referenceLatitude && courseDto?.referenceLongitude && user?.email === '<EMAIL>') {
      courseDto.referenceLatitude = 33.168415;
      courseDto.referenceLongitude = -117.227295;
    }

    if (courseDto?.referenceLatitude && courseDto?.referenceLongitude) {
      isSearchGPS = true;
      const cacheSearch = await this.getCacheSearchCourse(courseDto);
      if (cacheSearch) {
        return cacheSearch;
      }
    }

    const { courseName } = courseDto;
    if (courseName && courseName.length < 4) {
      return null;
    } else if (!isSearchGPS && !courseName) {
      return {
        ErrorDetails: ['At least one of following parameters must be not null: courseName'],
        Status: 401,
        ErrorMessage: 'Invalid Custom Parameters',
      };
    }

    const isUserSpamCourse = await this.checkUserSpamCourse(courseDto, client);

    if (isUserSpamCourse) {
      return { isLimitRequest: true };
    }

    if (courseDto.mrpId) {
      delete courseDto.mrpId;
    }
    let data = null;
    let action = null;
    if (courseDto.courseName) {
      action = GlxConst.ACTION_GLX_COURSE_LIST_NAME;
    } else {
      action = GlxConst.ACTION_GLX_COURSE_LIST_GPS;
    }

    data = await this.callGLXService(action, { ...courseDto, client });

    return this.formatCourseListData(data?.CourseList, courseDto);
  }

  async checkUserSpamCourse(courseDto: CourseSearchDto, client?: string) {
    try {
      const { mrpId } = courseDto;
      let user = null;
      if (mrpId) {
        user = await this.userRepo.findOne({
          where: { id: courseDto?.mrpId },
        });
      }
      if (!mrpId || !user) {
        this.saveLogCourseSearch(courseDto, client).catch((e) => e);
        return false;
      }

      if (user.is_spam_igolf) {
        return this.checkUserUnBlockRequest(user, courseDto, client);
      }

      const keyConfig = await this.keyConfigRepo.findOne({
        where: {
          key: LIMIT_TIME_REQUEST_COURSES,
        },
      });
      const limit = keyConfig.value ? Number(keyConfig.value) : 60;
      const sqlQuery = `SELECT COUNT(*) AS requestcount
            FROM user_log_courses
            WHERE user_id = '${user.id}'
                AND created_at >= NOW() - INTERVAL '1 minutes';`;
      const result = await this.entityManager.query(sqlQuery);
      if (!result || !result?.length) return false;
      const requestCount = parseInt(result[0]?.requestcount);
      if (requestCount >= limit) {
        const currentTime: any = new Date();
        this.userRepo.update({ id: user?.id }, { is_spam_igolf: true, is_user_igolf_blocked: true }).catch((e) => e);
        // send email to admin
        this.triggerKlaviyoSpamIgolf(user?.email, limit, currentTime).catch((e) => e);
        return true;
      } else {
        this.saveLogCourseSearch(courseDto, client).catch((e) => e);
      }
      return false;
    } catch (err) {
      this.logger.error(`ERROR CHECK SPAM IGOLF: ${JSON.stringify(err)}`);
      return false;
    }
  }

  async checkUserUnBlockRequest(user, courseDto, client) {
    const sqlQuery = `SELECT MAX(request_time) AS timerq
            FROM user_log_courses
            WHERE user_id = '${user.id}';`;
    const result = await this.entityManager.query(sqlQuery);
    if (!result || !result?.length) return false;
    const lastRequestTime: any = new Date(result[0]?.timerq);
    const currentTime: any = new Date();
    const timeSinceLastRequest: any = currentTime - lastRequestTime;
    if (timeSinceLastRequest < 5 * 60 * 1000) {
      return true;
    }
    await this.userRepo.update({ id: user.id }, { is_spam_igolf: false });
    this.saveLogCourseSearch(courseDto, client).catch((e) => e);
    return false;
  }

  async saveLogCourseSearch(courseDto: CourseSearchDto, client?: string) {
    let user = null;
    if (courseDto.mrpId) {
      user = await this.userRepo.findOne({
        where: {
          id: courseDto.mrpId,
        },
      });
    }

    const newUserLogCourse: UserLogCourseDto = {
      request_time: new Date(),
      params: JSON.stringify(courseDto),
      client_id: client,
    };

    if (user && user.id) {
      newUserLogCourse.user_id = user.id;
    }

    return this.userLogCourseRepo.save(newUserLogCourse);
  }

  private setMaxRadiusSearch(courseDto: CourseSearchDto) {
    if (courseDto?.radius) {
      const radius = parseInt(`${courseDto?.radius}`);
      if (radius > 16) {
        courseDto.radius = 16;
      } else {
        courseDto.radius = 16;
      }
    } else {
      courseDto.radius = 16;
    }
  }

  async getCourseGPSDetail(idCourse: string, client?: string) {
    const actionGpsData = GlxConst.ACTION_GLX_GPS_DATA;
    const cacheGLXGPSDetail = GlxConst.CACHE_GLX_COURSE_GPS_DETAIL;
    const payload = {
      id_course: idCourse,
      client,
    };
    const cacheGPSDetail = await this.getCacheCourse(cacheGLXGPSDetail, payload);
    if (cacheGPSDetail) {
      return cacheGPSDetail;
    }
    const glxGpsDetail = await this.callGLXService(actionGpsData, payload);
    const glxHls = _.result(glxGpsDetail, 'data.hls', null);
    if (isEmpty(glxHls)) {
      this.saveCourseErrorLog(idCourse, { is_not_gps_detail: true }).catch((e) => e);
      return this.getMsgErrorGlx(idCourse, 'Not Found');
    }
    const glxFormatGpsDetail = this.formatCourseGpsDetail(idCourse, glxHls);
    if (glxFormatGpsDetail && !isEmpty(glxFormatGpsDetail.GPSList)) {
      await this.setCacheCourse(cacheGLXGPSDetail, glxFormatGpsDetail, idCourse);
    }
    return glxFormatGpsDetail;
  }

  async getCourseGPSVectorDetail(idCourse: string, client?: string) {
    const actionGpsVectorData = GlxConst.ACTION_GLX_GPS_VECTOR;
    const actionGpsData = GlxConst.ACTION_GLX_GPS_DATA;
    const cacheGLXGPSVectorDetail = GlxConst.CACHE_GLX_COURSE_GPS_VECTOR_DETAIL;
    const payload = {
      id_course: idCourse,
      client,
    };
    const cacheGPSVectorDetail = await this.getCacheCourse(cacheGLXGPSVectorDetail, payload);
    if (cacheGPSVectorDetail) {
      return cacheGPSVectorDetail;
    }
    const [glxGpsVectorDetail, glxGpsData] = await Promise.all([
      this.callGLXService(actionGpsVectorData, payload),
      this.callGLXService(actionGpsData, payload),
    ]);
    const glxVector = _.result(glxGpsVectorDetail, 'data.hls', null);
    const glxGps = _.result(glxGpsData, 'data.hls', null);
    if (isEmpty(glxVector) || isEmpty(glxGps)) {
      this.saveCourseErrorLog(idCourse, { is_not_gps_vector_detail: true }).catch((e) => e);
      return this.getMsgErrorGlx(idCourse, 'Not Found');
    }
    const glxFormatGpsVectorDetail = this.formatCourseGpsVectorDetail(idCourse, glxVector, glxGps);
    if (glxFormatGpsVectorDetail && !isEmpty(glxFormatGpsVectorDetail.vectorGPSObject)) {
      await this.setCacheCourse(cacheGLXGPSVectorDetail, glxFormatGpsVectorDetail, idCourse);
    }
    return glxFormatGpsVectorDetail;
  }

  async getCourseScoreCardDetail(idCourse: string, client?: string) {
    const actionScorecard = GlxConst.ACTION_GLX_COURSE_SCORECARD;
    const cacheGLXScoreCard = GlxConst.CACHE_GLX_COURSE_SCORECARD;
    const payload = {
      id_course: idCourse,
      client,
    };

    const cacheDetail = await this.getCacheCourse(cacheGLXScoreCard, payload);
    if (cacheDetail) {
      return cacheDetail;
    }

    const glxScoreCard = await this.callGLXService(actionScorecard, payload);
    const glxHls = _.result(glxScoreCard, 'data.hls', null);
    if (isEmpty(glxHls)) {
      this.saveCourseErrorLog(idCourse, { is_not_score_card_detail: true }).catch((e) => e);
      return this.getMsgErrorGlx(idCourse, 'Not Found');
    }
    const glxFormatScoreCard = this.formatCourseScoreCard(idCourse, glxHls);
    if (glxFormatScoreCard && !isEmpty(glxFormatScoreCard.menScorecardList)) {
      await this.setCacheCourse(cacheGLXScoreCard, glxFormatScoreCard, idCourse);
    }
    return glxFormatScoreCard;
  }

  async getCourseTeeDetail(idCourse: string, client?: string) {
    const actionScorecard = GlxConst.ACTION_GLX_COURSE_SCORECARD;
    const cacheCourseTee = GlxConst.CACHE_GLX_COURSE_TEE_DETAIL;
    const payload = {
      id_course: idCourse,
      detailLevel: 2,
      client,
    };
    const cacheDetail = await this.getCacheCourse(cacheCourseTee, payload);
    if (cacheDetail) {
      return cacheDetail;
    }

    const glxDetail = await this.callGLXService(GlxConst.ACTION_GLX_COURSE_DETAIL, {
      id_course: idCourse,
      client,
    });
    const holes = glxDetail?.holes;
    const glxScoreCard = await this.callGLXService(actionScorecard, payload);

    const teeBoxGlx = _.result(glxScoreCard, 'data.TeeBox', []);
    if (isEmpty(teeBoxGlx)) {
      this.saveCourseErrorLog(idCourse, { is_not_tees: true }).catch((e) => e);
      return this.getMsgErrorGlx(idCourse, 'Can not find Tee!');
    }
    const glxTees = this.formatCourseTee(teeBoxGlx, holes);
    if (glxTees && !isEmpty(glxTees.teesList)) {
      await this.setCacheCourse(cacheCourseTee, glxTees, idCourse);
    }

    return glxTees;
  }
  async getCourseElevationDataDetail(idCourse: string, client?: string) {
    return await this.callGLXService(GlxConst.ACTION_COURSE_ELEVATION_DATA_DETAIL, {
      id_course: idCourse,
      detailLevel: 2,
      client,
    });
  }

  async getUserCourseRecent(userId: number) {
    const queryBuilder = this.roundRepository.createQueryBuilder();
    queryBuilder.select(['igolf_course_id', 'course_name', 'map_id', 'ghin_course_id', 'ghin_course_name']);
    queryBuilder
      .where(
        `user_id = :userId AND completed = true AND (map_id = 'iGolf' OR map_id = 'glx') AND (igolf_course_id IS NOT NULL AND igolf_course_id != '') `,
        { userId }
      )
      .take(100)
      .orderBy({ played_on: 'DESC', created_at: 'DESC' });
    const rounds = await queryBuilder.getRawMany();

    let courseList = [];
    if (rounds) {
      courseList = _.uniqBy(rounds, (round) => round.igolf_course_id);
      courseList = courseList.map((c) => {
        return {
          id_course: c.igolf_course_id,
          courseName: c.course_name,
          ghin_course_id: c.ghin_course_id,
          ghin_course_name: c.ghin_course_name,
        };
      });
      if (courseList.length >= 10) {
        courseList.length = 10;
      }
    }
    return {
      totalCourses: courseList.length,
      courseList,
    };
  }

  async totalYardage(teeName: string, idCourse: string, isSimpleRound = false, isParOut = false, isParIn = false) {
    const teeDetail = await this.getCourseTeeDetail(idCourse);
    if (teeDetail.Status == 1) {
      const teeSelected = this.hasTeesList(teeDetail)
        ? teeDetail?.teesList.find(
            (tee: any) => tee?.teeName?.toString().trim().toLowerCase() == teeName?.trim().toLowerCase()
          )
        : 0;
      if (isSimpleRound) {
        if (isParOut) {
          return teeSelected?.yds1to9 || 0;
        } else if (isParIn) {
          return teeSelected?.yds10to18 || 0;
        }
      }
      return teeSelected?.ydsTotal || 0;
    }
    return 0;
  }

  async totalPar(teeName: string, idCourse: string, isSimpleRound = false, isParOut = false, isParIn = false) {
    const [teeDetail, scoreCard] = await Promise.all([
      this.getCourseTeeDetail(idCourse),
      this.getCourseScoreCardDetail(idCourse),
    ]);

    const gender = this.hasTeesList(teeDetail)
      ? teeDetail?.teesList?.find((tee: any) => tee.teeName?.toLowerCase().trim() == teeName?.trim().toLowerCase())
          ?.gender || 'men'
      : 'men';

    const scoreCardPerson = gender == 'men' ? scoreCard.menScorecardList : scoreCard.wmnScorecardList;

    if (isSimpleRound) {
      if (isParOut) {
        return +scoreCardPerson[0]?.parOut;
      } else if (isParIn) {
        return +scoreCardPerson[0]?.parIn;
      }
    }

    return +scoreCardPerson[0]?.parOut + +scoreCardPerson[0]?.parIn;
  }

  private async callGLXService(action: GlxConst, payload: any) {
    try {
      payload['active'] = 1;
      if (payload?.client) {
        if ([CLIENTS.SWING_INDEX, CLIENTS.SWING_INDEX_APP].includes(payload?.client)) {
          await this.logRequestIGolf.add(
            PROCESS_QUEUE_NAMES.LOG_REQUEST_IGOLF_SI,
            {
              client: payload.client,
              actionType: action,
              igolfCourseId: payload?.id_course ?? action,
            },
            OPTIONS_JOB_DEFAULT
          );
        }

        delete payload.client;
      }
      // this.setMaxRadiusSearch(payload);
      let params = '';
      if (!isEmpty(payload.courseName)) {
        params = payload.courseName;
      } else if (payload.referenceLatitude && payload.referenceLongitude) {
        params = `${payload.referenceLatitude}/${payload.referenceLongitude}/16`;
      } else if (!isEmpty(payload.id_course)) {
        params = `?cid=${payload.id_course}`;
      }
      const headers = await this.getRequestHeaderGLX(action, params);
      const { data } = await axios.get(this.buildUrlActionGLX(action, params), { headers });
      return data;
    } catch (error) {
      this.logger.error(`ERROR CALL GLX SERVICE ACTION ${action}`);
      this.logger.error(error.message);
      return null;
    }
  }

  private async getTokenGLX() {
    const isLocal = process.env.NODE_ENV === 'LOCAL' ? true : false;
    let cacheKey = `${GlxConst.CACHE_COURSE_PREFIX}:GLX_TOKEN`;
    if (isLocal) {
      cacheKey = `${GlxConst.CACHE_COURSE_PREFIX}:GLX_TOKEN_LOCAL`;
    }
    const cachedToken = await this.cacheManager.get(cacheKey);

    if (cachedToken) {
      return cachedToken;
    }

    const timestamp = Math.floor(Date.now() / 1000);
    const partnerToken = this.configService.get('glx.partnerToken');
    const secretKey = this.configService.get('glx.secretKey');
    const uid = this.configService.get('glx.UID');
    const xApiKey = this.configService.get('glx.xApiKey');
    const path = GlxConst.ACTION_GLX_VALIDATE;
    const signature = this.generateSignature(timestamp, partnerToken, uid, 'GET', path, '', secretKey);

    const headers = {
      'x-api-key': xApiKey,
      token: partnerToken,
      signature: signature,
      uid: uid,
    };

    try {
      const response = await axios.get(`${this.configService.get('glx.urlBase')}${GlxConst.ACTION_GLX_VALIDATE}`, {
        headers,
      });

      const token = response.data.token;
      await this.cacheManager.set(cacheKey, token, { ttl: 8 * 24 * 60 * 60 }); // 8 days in seconds
      return token;
    } catch (error) {
      this.logger.error('Error fetching GLX token', error);
      throw new Error('Unable to fetch GLX token');
    }
  }

  private async getRequestHeaderGLX(action, params = '') {
    const timestamp = Math.floor(Date.now() / 1000);
    const secretKey = this.configService.get('glx.secretKey');
    const uid = this.configService.get('glx.UID');
    const xApiKey = this.configService.get('glx.xApiKey');

    const token = await this.getTokenGLX();
    const signature = this.generateSignature(timestamp, token, uid, 'GET', action, params, secretKey);

    return {
      'x-api-key': xApiKey,
      token: token,
      signature: signature,
      uid: uid,
    };
  }

  private isActionNeedCache(action: GlxConst) {
    const actionsCache = [
      GlxConst.ACTION_COURSE_DETAIL,
      GlxConst.ACTION_COURSE_GPS_DETAIL,
      GlxConst.ACTION_COURSE_GPS_VECTOR_DETAIL,
      GlxConst.ACTION_COURSE_SCORECARD,
      GlxConst.ACTION_COURSE_TEE_DETAIL,
      GlxConst.ACTION_COURSE_ELEVATION_DATA_DETAIL,
      GlxConst.ACTION_COURSE_LIST,
    ];
    return actionsCache.includes(action);
  }

  private buildUrlActionGLX(action: string, params: string) {
    return `${this.configService.get('glx.urlBase')}${action}${params}`;
  }

  private calculateSignature(url1: string, url2: string) {
    const url = `${url1}${url2}`;
    const sha256 = CryptoJS.HmacSHA256(url, this.iGolfApiSecretKey());
    return CryptoJS.enc.Base64.stringify(sha256).replace(/\+/g, '-').replace(/\//g, '_').replace(/\=+$/, '');
  }

  private timeStamp() {
    return moment().format('YYMMDDHHmmss') + this.timeZone();
  }

  private timeZone() {
    return moment().toString().split('GMT').pop();
  }

  private iGolfApiSecretKey() {
    return this.configService.get('igolf.apiSecretKey');
  }

  private iGolfApiBase() {
    return this.configService.get('igolf.apiBase');
  }

  private async setCacheCourse(key: string, payload: any, idCourse: string) {
    const ttl = ms(this.configService.get('glx.cacheTTL'));
    console.log(`SET CACHE ${key}: COURSE: ${idCourse}`);
    await this.cacheManager.set(`${GlxConst.CACHE_COURSE_PREFIX}:${key}:${idCourse}`, JSON.stringify(payload), {
      ttl: parseInt(ttl, 10) / 1000,
    });
  }
  private async getCacheCourse(action: any, payload: any) {
    let course: any;
    try {
      course = await this.cacheManager.get(`${GlxConst.CACHE_COURSE_PREFIX}:${action}:${payload.id_course}`);
      return JSON.parse(course);
    } catch (error) {
      console.log(`ERROR GET CACHE COURSE`);
      course = null;
    }
  }

  private async getCacheSearchCourse(payload: CourseSearchDto): Promise<any> {
    const lat = Number(payload?.referenceLatitude).toFixed(4);
    const long = Number(payload?.referenceLongitude).toFixed(4);
    const key = `${GlxConst.CACHE_COURSE_PREFIX}:${GlxConst.CACHE_COURSE_LIST_GPS}:LOCATION_GLX_${lat}_${long}`;

    try {
      const cacheSearch: any = await this.getCacheValueSearchCourse(key);
      if (cacheSearch) {
        return JSON.parse(cacheSearch);
      }
      return null;
    } catch (error) {
      console.log(`ERROR GET CACHE COURSE`, error);
      return null;
    }
  }
  private async getCacheValueSearchCourse(key) {
    return await this.cacheManager.get(key);
  }

  async setCacheSearchCourse(payload: CourseSearchDto, dateSearch) {
    const lat = Number(payload?.referenceLatitude).toFixed(4);
    const long = Number(payload?.referenceLongitude).toFixed(4);
    const key = `${GlxConst.CACHE_COURSE_PREFIX}:${GlxConst.CACHE_COURSE_LIST_GPS}:LOCATION_GLX_${lat}_${long}`;
    if (lat && long) {
      // save course in 30 minutes
      return await this.cacheManager.set(key, JSON.stringify(dateSearch), {
        ttl: ms('30m') / 1000,
      });
    }
    return false;
  }

  // async totalYardage(teeName: string, idCourse: string) {
  //   const teeDetail = await this.getCourseTeeDetail(idCourse);
  //   if (teeDetail.Status == 1) {
  //     return (
  //       teeDetail?.teesList.find(
  //         (tee: any) => tee?.teeName?.toString().trim().toLowerCase() == teeName?.trim().toLowerCase()
  //       )?.ydsTotal || 0
  //     );
  //   }
  //   return 0;
  // }

  fairway_center_path(holeNumber, idCourse) {
    this.logger.debug({ holeNumber, idCourse });
  }

  async coordinatesFor(holeNumber, lie, idCourse, client?: string) {
    const geo = await this.getGeoJsonFor(holeNumber, lie, idCourse, client);
    if (!geo) {
      return [];
    }
    const shape = geo?.Shapes?.Shape;
    if (!shape) {
      return [];
    }
    const points = shape?.Points;
    if (points == '0 0') {
      return [];
    }
    return this.convertStringPoints(shape);
  }

  async getGeoJsonFor(holeNumber, lie, idCourse, client?: string) {
    const hole = await this.getGeojson(holeNumber, idCourse, client);
    if (!hole) {
      return {};
    }
    lie = lie.toLowerCase();
    switch (lie) {
      case 'tee':
        return hole?.Teebox || null;
      case 'fairway':
        return hole?.Fairway || null;
      case 'green':
        return hole?.Green || null;
      case 'fairway_center_path':
      case 'tee-boundary':
        return hole?.Centralpath || null;
      default:
        return null;
    }
  }

  async getGeojson(holeNumber, idCourse, client?: string) {
    const courseGPSVectorDetail = await this.getCourseGPSVectorDetail(idCourse, client);

    if (courseGPSVectorDetail.Status == 1) {
      const holes = _.result(courseGPSVectorDetail, 'vectorGPSObject.Holes.Hole', null);
      if (!holes) {
        return null;
      }
      return holes.find((h: any) => +h.HoleNumber == +holeNumber);
    }
    return null;
  }

  // async totalPar(teeName: string, idCourse: string) {
  //   const [teeDetail, scoreCard] = await Promise.all([
  //     this.getCourseTeeDetail(idCourse),
  //     this.getCourseScoreCardDetail(idCourse),
  //   ]);
  //   if (teeDetail.Status != 1) {
  //     return 0;
  //   }
  //   const gender =
  //     teeDetail?.teesList?.find((tee: any) => tee.teeName?.toLowerCase().trim() == teeName?.trim().toLowerCase())
  //       ?.gender || 'men';
  //
  //   const scoreCardPerson = gender == 'men' ? scoreCard.menScorecardList : scoreCard.wmnScorecardList;
  //   return +scoreCardPerson[0]?.parOut + +scoreCardPerson[0]?.parIn;
  // }

  async pinLocation(holeNumber, idCourse) {
    const hole = await this.getGeojson(holeNumber, idCourse);
    if (!hole) {
      return null;
    }
    const greenCenter = hole?.Greencenter?.Shapes?.Shape;
    const pinLocation = this.convertStringPoints(greenCenter);
    if (pinLocation != '') {
      return pinLocation[0];
    }
    return null;
  }

  async greenFrontFor(holeNumber: any, idCourse: string) {
    const courseGPSDetail = await this.getCourseGPSDetail(idCourse);
    if (courseGPSDetail.Status == 1) {
      const front = courseGPSDetail['GPSList'].find((hole: any) => +hole.holeNumber == +holeNumber);
      if (!front) {
        this.logger.log(`NOTFOUND GREEN FRONT FOR HOLE NUMBER ${holeNumber} - COURSE: ${idCourse}`);
        return null;
      }
      return [front['frontLon'], front['frontLat']];
    }
    return null;
  }

  convertStringPoints(points) {
    if (points) {
      return points.map((p: any) => {
        return p['Points'].split(',').map((point: any) => point.split(' ').map((coord: any) => parseFloat(coord)));
      });
    }
    return '';
  }
  async get_random_point_within_shape(lie, holeNumber, idCourse) {
    const geo = await this.coordinatesFor(holeNumber, lie, idCourse);
    if (geo) {
      try {
        const points = geo[0];
        const coordCentroid = centroid(points);
        const { maxLat, minLat, maxLng, minLng } = getBounds(points);
        const arrRing: any = [minLng, minLat, maxLng, maxLat];
        const pointRandom = turf.randomPoint(1, { bbox: arrRing });
        const centroidPoint = turf.point(coordCentroid);
        const midPoint = turf.midpoint(
          pointRandom.features[0].geometry.coordinates,
          centroidPoint.geometry.coordinates
        );
        const midCoords = midPoint?.geometry.coordinates;
        if (midCoords) {
          return midCoords;
        } else {
          return null;
        }
      } catch (error) {
        this.logger.log(error.message);
        return null;
      }
    }
  }

  async triggerKlaviyoSpamIgolf(email, limit, timeBlock) {
    this.logger.log(`TRIGGER_triggerKlaviyoSpamIgolf: ${email}`);
    if (email) {
      try {
        await axios.get(
          this.apiTriggerKlaviyoSpamIgolf + `/${email}?limit=${limit}&timeBlock=${timeBlock}`,
          this.getRequestHeaderConfigs()
        );
        return true;
      } catch (error) {
        console.error(`ERROR_triggerKlaviyoSpamIgolf ${email}: ` + error.message);
        this.logger.log(`ERROR_triggerKlaviyoSpamIgolf: ${JSON.stringify(error)}`);
      }
    }
  }

  private formatCourseGpsVectorDetail(courseId, glxVectorArray, glxGpsArray) {
    const formatPoints = (points) => {
      if (!points || !points.length) return [];
      return points.map((point) => {
        if (!point) return {};
        if (Array.isArray(point[0])) {
          return { Points: point.map((coord) => coord.join(' ')).join(',') };
        }
        return { Points: point.join(' ') };
      });
    };

    const formatShape = (shape) => ({
      ShapeCount: shape ? shape.length : 0,
      Shapes: {
        Shape: shape ? formatPoints(shape) : [],
      },
    });

    const formatHoleObject = (glxVector, glxGps) => {
      const teeboxShape = formatShape(glxVector.tPts);
      const teeboxLength = teeboxShape.Shapes.Shape.length;

      return {
        Greencenter: formatShape(
          glxGps.pl ? glxGps.pl.filter((point) => point.n === 'g').map((point) => point.pt) : []
        ),
        Perimeter: formatShape(glxVector.prPts),
        Teebox: teeboxShape,
        Bunker: formatShape(glxVector.bPts),
        Fairway: formatShape(glxVector.fPts),
        Centralpath: [],
        Teeboxcenter: formatShape(
          glxGps.pl
            ? glxGps.pl
                .filter((point) => {
                  for (let i = 1; i <= teeboxLength; i++) {
                    if (point.n === `t${i}`) {
                      return true;
                    }
                  }
                  return false;
                })
                .map((point) => point.pt)
            : []
        ),
        Green: formatShape(glxGps.gPts),
        HoleNumber: glxGps.hl,
      };
    };

    const holes = glxVectorArray.map((glxVector, index) => formatHoleObject(glxVector, glxGpsArray[index]));

    const waterPoints = compact(glxVectorArray.flatMap((glxVector) => glxVector.wPts));

    const vectorGPSObject = {
      Water: formatShape(waterPoints),
      Holes: {
        Hole: holes,
      },
      HoleCount: glxVectorArray.length,
    };

    return {
      vectorGPSObject,
      lastModified: moment().format('YYYY-MM-DD HH:mm:ss'),
      id_course: courseId,
      Status: 1,
      ErrorMessage: 'Action successful',
    };
  }

  private formatCourseGpsDetail(courseId, glxHls) {
    const gpsList = glxHls.map((hole) => {
      const frontPoint = hole.pl.find((point) => point.n === 'gF');
      const centerPoint = hole.pl.find((point) => point.n === 'g');
      const backPoint = hole.pl.find((point) => point.n === 'gB');
      const teePoints = hole.pl.filter((point) => point.n.startsWith('t'));
      const customPoints = hole.pl.filter((point) => !['gF', 'g', 'gB'].includes(point.n) && !point.n.startsWith('t'));

      const formattedHole = {
        holeNumber: hole.hl,
        frontLat: frontPoint ? frontPoint.pt[1] : null,
        frontLon: frontPoint ? frontPoint.pt[0] : null,
        centerLat: centerPoint ? centerPoint.pt[1] : null,
        centerLon: centerPoint ? centerPoint.pt[0] : null,
        backLat: backPoint ? backPoint.pt[1] : null,
        backLon: backPoint ? backPoint.pt[0] : null,
      };

      teePoints.forEach((point, index) => {
        formattedHole[`teeLat${index + 1}`] = point.pt[1];
        formattedHole[`teeLon${index + 1}`] = point.pt[0];
      });

      customPoints.forEach((point, index) => {
        formattedHole[`customLat${index + 1}`] = 0;
        formattedHole[`customLon${index + 1}`] = 0;
        formattedHole[`customName${index + 1}`] = null;
        formattedHole[`customDesc${index + 1}`] = null;
      });

      return formattedHole;
    });

    return {
      GPSList: gpsList,
      lastModified: moment().format('YYYY-MM-DD HH:mm:ss'),
      id_course: courseId,
      Status: 1,
      ErrorMessage: 'Action successful',
    };
  }

  private formatCourseScoreCard(courseId, glxHls) {
    const hcpHole = glxHls.map((hole) => hole.h);
    const parHole = glxHls.map((hole) => hole.p);
    const parOut = parHole.slice(0, 9).reduce((acc, par) => acc + par, 0);
    const parIn = parHole.slice(9).reduce((acc, par) => acc + par, 0);
    const parTotal = parOut + parIn;

    const scorecard = {
      hcpHole,
      parHole,
      parOut,
      parIn,
      parTotal,
    };

    return {
      menScorecardList: [scorecard],
      wmnScorecardList: [scorecard],
      id_course: courseId,
      lastModified: moment().format('YYYY-MM-DD HH:mm:ss'),
      Status: 1,
      ErrorMessage: 'Action successful',
    };
  }

  private formatCourseTee(glxTees, holes) {
    const formattedData = {
      teesList: [],
      courseNumHoles: holes,
      Status: 1,
      ErrorMessage: 'Action successful',
    };
    let displayOrder = 0;

    glxTees.forEach((tee) => {
      const genders = [
        {
          rating: tee.rt,
          slope: tee.sl,
          gender: 'male',
          ratingMen: tee.rt,
          ratingWomen: 0,
          slopeMen: tee.sl,
          slopeWomen: 0,
          ydsTotal: tee.totalDist || 0,
          yds1to9: tee.fDist || 0,
          yds10to18: tee.bDist || 0,
          yds1to18: tee.totalDist || 0,
        },
        {
          rating: tee.wrt,
          slope: tee.wsl,
          gender: 'female',
          ratingMen: 0,
          ratingWomen: tee.wrt,
          slopeMen: 0,
          slopeWomen: tee.wsl,
          ydsTotal: tee.totalDist || 0,
          yds1to9: tee.fDist || 0,
          yds10to18: tee.bDist || 0,
          yds1to18: tee.totalDist || 0,
        },
      ];

      genders.forEach(
        ({ rating, gender, ratingMen, ratingWomen, slopeMen, slopeWomen, yds1to9, yds10to18, yds1to18, ydsTotal }) => {
          if (rating) {
            let teeName = tee.nm;
            if (gender === 'female') {
              teeName = `${teeName} (Female)`;
            } else {
              teeName = `${teeName} (Male)`;
            }
            formattedData.teesList.push({
              ratingMen,
              ratingWomen,
              slopeMen,
              slopeWomen,
              teeName,
              id_courseTeeType: 0,
              gender: gender === 'male' ? 'men' : 'wmn',
              id_courseTeeColor: 0,
              teeColorName: '',
              teeColorValue: '',
              ydsTotal,
              yds1to9,
              yds10to18,
              yds1to18,
              ydsHole: null,
              displayOrder: displayOrder++,
            });
          }
        }
      );
    });

    return formattedData;
  }

  private async formatCourseListData(apiData = [], courseDto: CourseSearchDto) {
    const courseList = formatGLXCourseListData(apiData);
    if (courseDto.referenceLatitude && courseDto.referenceLongitude) {
      const locationUser = { latitude: courseDto.referenceLatitude, longitude: courseDto.referenceLongitude };
      courseList.forEach((course) => {
        const locationCourse = { latitude: course.latitude, longitude: course.longitude };
        const distanceInMeters = getDistance(locationUser, locationCourse);
        const distanceInMiles = convertDistance(distanceInMeters, 'mi');
        course.distance = parseFloat(distanceInMiles.toFixed(2));
      });

      // Sort courseList by distance in ascending order
      courseList.sort((a, b) => a.distance - b.distance);
    }

    const result = {
      page: 1,
      totalpages: courseList.length ? 1 : 0,
      totalCourses: courseList.length,
      courseList,
      Status: 1,
      ErrorMessage: 'Action successful',
    };

    if (!isEmpty(courseList) && courseDto.referenceLatitude && courseDto.referenceLongitude) {
      await this.setCacheSearchCourse(courseDto, result);
    }

    return result;
  }

  private async formatCourseDetail(input, courseId) {
    const cacheCourseDetail = GlxConst.CACHE_GLX_COURSE_DETAIL;
    if (!input || !input?.id) {
      // this.saveCourseErrorLog(courseId, { is_not_course_detail: true }).catch((e) => e);
      return {
        ErrorDetails: {
          name: 'Course',
          propertyName: 'id',
          value: courseId,
        },
        Status: 501,
        ErrorMessage: 'Not Found',
      };
    }
    const courseDetail = formatGLXCourseDetail(input);
    if (courseDetail && (isEmpty(courseDetail?.ghinId) || courseDetail?.ghinId === '0')) {
      this.saveCourseErrorLog(courseId, { is_not_ghin_id: true }).catch((e) => e);
    }
    await this.setCacheCourse(cacheCourseDetail, courseDetail, courseId);
    return courseDetail;
  }

  private async saveCourseErrorLog(courseId, data) {
    try {
      if (!courseId) return;
      let courseError: CourseErrorEntity = await this.courseErrorRepo.findOne({
        where: {
          course_id: courseId,
        },
      });

      if (courseError) {
        // Update existing course error
        courseError = { ...courseError, ...data, updated_at: new Date() };
        await this.courseErrorRepo.save(courseError);
      } else {
        const payload = {
          id_course: courseId,
        };
        const courseDetail = await this.callGLXService(GlxConst.ACTION_GLX_COURSE_DETAIL, payload);
        if (isEmpty(courseDetail) || isEmpty(courseDetail?.name)) {
          return;
        }
        // Create new course error
        const courseErrorCreate = this.courseErrorRepo.create({
          course_id: courseId,
          course_name: courseDetail?.name || '',
          type: 'glx',
          ...data,
        });
        await this.courseErrorRepo.save(courseErrorCreate);
      }
    } catch (error) {
      console.log('error', error?.message || '');
    }
  }

  private hasTeesList(obj: any): obj is { teesList: any[] } {
    return obj && 'teesList' in obj;
  }

  private getMsgErrorGlx = (idCourse, msg, code = 501) => {
    return {
      ErrorDetails: {
        name: 'Course',
        propertyName: 'id',
        value: idCourse,
      },
      Status: code,
      ErrorMessage: msg,
    };
  };
}
