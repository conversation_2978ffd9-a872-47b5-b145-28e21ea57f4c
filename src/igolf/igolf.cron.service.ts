import { InjectQueue } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { Queue } from 'bull';
import { chunk, isEmpty, map } from 'lodash';
import { Repository } from 'typeorm';
import { isCronJobHandlersEnabled, isProduction } from 'src/utils/cron';
import { PROCESSORS, PROCESS_QUEUE_NAMES } from 'src/workers/jobs/job.constant';
import { OPTIONS_JOB_DEFAULT } from '../rounds/round.const';
import { UserLogCourseEntity } from './entity/user-log-course.entity';

@Injectable()
export class IgolfCronService {
  private readonly logger = new Logger(IgolfCronService.name);
  constructor(
    @InjectRepository(UserLogCourseEntity)
    private userLogCourseRepo: Repository<UserLogCourseEntity>,
    @InjectQueue(PROCESSORS.RemoveUserLogCourse) private removeUserLogCourseQueue: Queue
  ) {}

  @Cron(isProduction ? CronExpression.EVERY_DAY_AT_5AM : CronExpression.EVERY_30_MINUTES)
  async removeUserLogCourse() {
    this.logger.log(`REMOVE USER LOG COURSE START....`);
    if (!isCronJobHandlersEnabled()) {
      return;
    }
    // Get request after 7 days.
    const userLogCourseBuilder = this.userLogCourseRepo.createQueryBuilder();
    userLogCourseBuilder.where(`created_at < NOW() - INTERVAL '7 days'`);
    userLogCourseBuilder.select(['id']);
    let userLogCourses = await userLogCourseBuilder.getRawMany();
    if (isEmpty(userLogCourses)) {
      return null;
    }
    userLogCourses = chunk(map(userLogCourses, 'id'), 80);
    try {
      for (const ids of userLogCourses) {
        await this.removeUserLogCourseQueue.add(
          PROCESS_QUEUE_NAMES.REMOVE_USER_LOG_COURSE,
          {
            ids,
          },
          OPTIONS_JOB_DEFAULT
        );
      }
    } catch (err) {
      console.log(err);
      this.logger.log(`ERROR REMOVE USER LOG COURSE END: ${JSON.stringify(err)}`);
    }
  }
}
