export enum IGolfConst {
  API_VERSION = '1.1',
  SIGNATURE_VERSION = '2.0',
  SIGNATURE_METHOD = 'HmacSHA256',
  RESPONSE_FORMAT = 'JSON',

  ACTION_COURSE_DETAIL = 'CourseDetails',
  ACTION_COURSE_GPS_DETAIL = 'CourseGPSDetails',
  ACTION_COURSE_GPS_VECTOR_DETAIL = 'CourseGPSVectorDetails',
  ACTION_COURSE_SCORECARD = 'CourseScorecardDetails',
  ACTION_COURSE_TEE_DETAIL = 'CourseTeeDetails',
  ACTION_COURSE_ELEVATION_DATA_DETAIL = 'CourseElevationDataDetails',

  ACTION_COURSE_LIST = 'CourseList',

  CACHE_COURSE_PREFIX = 'COURSE',
  GLX = 'GLX',
  IGOLF = 'IGOLF',
  TAG = 'TAG',
  CACHE_COURSE_DETAIL = 'COURSE_DETAIL',
  CACHE_COURSE_GPS_DETAIL = 'COURSE_GPS_DETAIL',
  CACHE_COURSE_GPS_VECTOR_DETAIL = 'COURSE_GPS_VECTOR_DETAIL',
  CACHE_COURSE_SCORECARD = 'COURSE_SCORECARD',
  CACHE_COURSE_TEE_DETAIL = 'COURSE_TEE_DETAIL',
  CACHE_COURSE_ELEVATION_DATA_DETAIL = 'COURSE_ELEVATION_DATA_DETAIL',
}
