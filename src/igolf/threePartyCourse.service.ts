import { InjectQueue } from '@nestjs/bull';
import { Cache } from '@nestjs/cache-manager';
import { CACHE_MANAGER, Inject, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm';
import { Queue } from 'bull';
import { EntityManager, Repository } from 'typeorm';
import { Round } from 'src/rounds/entities/round.entity';
import { PROCESSORS } from 'src/workers/jobs/job.constant';
import { GlxConst } from '../glx/glx.const';
import { GlxService } from '../glx/glx.service';
import { KeyConfigEntity } from '../key-config/entities/key-config.entity';
import { User } from '../users/entities/user.entity';
import { CourseSearchDto, CourseSearchGhinDto } from './dto/course-search.dto';
import { LogRequestDto } from './dto/log.request.dto';
import { IgolfLogRequests } from './entity/ghin.log.request.entity';
import { UserLogCourseEntity } from './entity/user-log-course.entity';
import { IGolfConst } from './igolf.const';
import { IGolfService } from './igolf.service';

@Injectable()
export class ThreePartyCourseService {
  private readonly logger = new Logger(ThreePartyCourseService.name);
  apiTriggerKlaviyoSpamIgolf: any;

  constructor(
    private configService: ConfigService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    @InjectRepository(Round) private roundRepository: Repository<Round>,
    @InjectRepository(IgolfLogRequests) private igolfLogRequestRepo: Repository<IgolfLogRequests>,
    @InjectRepository(User) private userRepo: Repository<User>,
    @InjectRepository(UserLogCourseEntity) private userLogCourseRepo: Repository<UserLogCourseEntity>,
    @InjectRepository(KeyConfigEntity) private keyConfigRepo: Repository<KeyConfigEntity>,
    @InjectQueue(PROCESSORS.LogRequestIGolfSI) private logRequestIGolf: Queue,
    @InjectEntityManager() private readonly entityManager: EntityManager,
    private readonly iGolfService: IGolfService,
    private readonly glxService: GlxService
  ) {
    const mytmEndpoint = configService.get('mytm.endpoint');
    this.apiTriggerKlaviyoSpamIgolf = mytmEndpoint + '/play/spam';
  }

  async getCacheService() {
    const cacheKey = `${GlxConst.CACHE_COURSE_PREFIX}:SERVICE`;
    let service = await this.cacheManager.get(cacheKey);

    if (!service) {
      service = IGolfConst.IGOLF;
      await this.cacheManager.set(cacheKey, service);
    }

    return service;
  }

  async getCourseDetail(idCourse: string, client?: string, forceService?: string) {
    let service = null;
    if (forceService) {
      service = forceService;
    } else {
      service = await this.getCacheService();
    }
    if (service === IGolfConst.IGOLF) {
      return this.iGolfService.getCourseDetail(idCourse, client);
    } else {
      return this.glxService.getCourseDetail(idCourse, client);
    }
  }

  async searchCourse(courseDto: CourseSearchDto, client?: string, forceService?: string) {
    let service = null;
    if (forceService) {
      service = forceService;
    } else {
      service = await this.getCacheService();
    }
    if (service === IGolfConst.IGOLF) {
      return this.iGolfService.searchCourse(courseDto, client);
    } else {
      return this.glxService.searchCourse(courseDto, client);
    }
  }

  async searchCourseFromGhin(courseDto: CourseSearchGhinDto, client?: string, forceService?: string) {
    let service = null;
    if (forceService) {
      service = forceService;
    } else {
      service = await this.getCacheService();
    }
    if (service === IGolfConst.IGOLF) {
      return this.iGolfService.searchCourseFromGhin(courseDto, client);
    } else {
      return {};
    }
  }

  async checkUserUnBlockRequest(user, courseDto, client?: string, forceService?: string) {
    let service = null;
    if (forceService) {
      service = forceService;
    } else {
      service = await this.getCacheService();
    }
    if (service === IGolfConst.IGOLF) {
      return this.iGolfService.checkUserUnBlockRequest(user, courseDto, client);
    } else {
      return this.glxService.checkUserUnBlockRequest(user, courseDto, client);
    }
  }

  async saveLogCourseSearch(courseDto: CourseSearchDto, client?: string, forceService?: string) {
    let service = null;
    if (forceService) {
      service = forceService;
    } else {
      service = await this.getCacheService();
    }
    if (service === IGolfConst.IGOLF) {
      return this.iGolfService.saveLogCourseSearch(courseDto, client);
    } else {
      return this.glxService.saveLogCourseSearch(courseDto, client);
    }
  }

  async getCourseGPSDetail(idCourse: string, client?: string, forceService?: string) {
    let service = null;
    if (forceService) {
      service = forceService;
    } else {
      service = await this.getCacheService();
    }

    if (service === IGolfConst.IGOLF) {
      return this.iGolfService.getCourseGPSDetail(idCourse, client);
    } else {
      return this.glxService.getCourseGPSDetail(idCourse, client);
    }
  }

  async getCourseGPSVectorDetail(idCourse: string, client?: string, forceService?: string) {
    let service = null;
    if (forceService) {
      service = forceService;
    } else {
      service = await this.getCacheService();
    }
    if (service === IGolfConst.IGOLF) {
      return this.iGolfService.getCourseGPSVectorDetail(idCourse, client);
    } else {
      return this.glxService.getCourseGPSVectorDetail(idCourse, client);
    }
  }

  async getCourseScoreCardDetail(idCourse: string, client?: string, forceService?: string) {
    let service = null;
    if (forceService) {
      service = forceService;
    } else {
      service = await this.getCacheService();
    }

    if (service === IGolfConst.IGOLF) {
      return this.iGolfService.getCourseScoreCardDetail(idCourse, client);
    } else {
      return this.glxService.getCourseScoreCardDetail(idCourse, client);
    }
  }

  async getCourseTeeDetail(idCourse: string, client?: string, forceService?: string) {
    let service = null;
    if (forceService) {
      service = forceService;
    } else {
      service = await this.getCacheService();
    }

    if (service === IGolfConst.IGOLF) {
      return this.iGolfService.getCourseTeeDetail(idCourse, client);
    } else {
      return this.glxService.getCourseTeeDetail(idCourse, client);
    }
  }

  async getCourseElevationDataDetail(idCourse: string, client?: string, forceService?: string) {
    let service = null;
    if (forceService) {
      service = forceService;
    } else {
      service = await this.getCacheService();
    }
    if (service === IGolfConst.IGOLF) {
      return this.iGolfService.getCourseElevationDataDetail(idCourse, client);
    } else {
      return this.glxService.getCourseElevationDataDetail(idCourse, client);
    }
  }

  async getUserCourseRecent(userId: number, forceService?: string) {
    let service = null;
    if (forceService) {
      service = forceService;
    } else {
      service = await this.getCacheService();
    }
    if (service === IGolfConst.IGOLF) {
      return this.iGolfService.getUserCourseRecent(userId);
    } else {
      return this.glxService.getUserCourseRecent(userId);
    }
  }

  async totalYardage(
    teeName: string,
    idCourse: string,
    isSimpleRound = false,
    isParOut = false,
    isParIn = false,
    forceService?: string
  ) {
    let service = null;
    if (forceService) {
      service = forceService;
    } else {
      service = await this.getCacheService();
    }
    if (service === IGolfConst.IGOLF) {
      return this.iGolfService.totalYardage(teeName, idCourse, isSimpleRound, isParOut, isParIn);
    } else {
      return this.glxService.totalYardage(teeName, idCourse, isSimpleRound, isParOut, isParIn);
    }
  }

  async totalPar(
    teeName: string,
    idCourse: string,
    isSimpleRound = false,
    isParOut = false,
    isParIn = false,
    forceService?: string
  ) {
    let service = null;
    if (forceService) {
      service = forceService;
    } else {
      service = await this.getCacheService();
    }
    if (service === IGolfConst.IGOLF) {
      return this.iGolfService.totalPar(teeName, idCourse, isSimpleRound, isParOut, isParIn);
    } else {
      return this.glxService.totalPar(teeName, idCourse, isSimpleRound, isParOut, isParIn);
    }
  }

  async pinLocation(holeNumber, idCourse, forceService?: string) {
    let service = null;
    if (forceService) {
      service = forceService;
    } else {
      service = await this.getCacheService();
    }
    if (service === IGolfConst.IGOLF) {
      return this.iGolfService.pinLocation(holeNumber, idCourse);
    } else {
      return this.glxService.pinLocation(holeNumber, idCourse);
    }
  }

  async greenFrontFor(holeNumber: any, idCourse: string, forceService?: string) {
    let service = null;
    if (forceService) {
      service = forceService;
    } else {
      service = await this.getCacheService();
    }
    if (service === IGolfConst.IGOLF) {
      return this.iGolfService.greenFrontFor(holeNumber, idCourse);
    } else {
      return this.glxService.greenFrontFor(holeNumber, idCourse);
    }
  }

  async get_random_point_within_shape(lie, holeNumber, idCourse, forceService?: string) {
    let service = null;
    if (forceService) {
      service = forceService;
    } else {
      service = await this.getCacheService();
    }
    if (service === IGolfConst.IGOLF) {
      return this.iGolfService.get_random_point_within_shape(lie, holeNumber, idCourse);
    } else {
      return this.glxService.get_random_point_within_shape(lie, holeNumber, idCourse);
    }
  }

  async coordinatesFor(holeNumber, lie, idCourse, client?: string, forceService?: string) {
    let service = null;
    if (forceService) {
      service = forceService;
    } else {
      service = await this.getCacheService();
    }
    if (service === IGolfConst.IGOLF) {
      return this.iGolfService.coordinatesFor(holeNumber, lie, idCourse, client);
    } else {
      return this.glxService.coordinatesFor(holeNumber, lie, idCourse, client);
    }
  }

  async addLogRequest(jobDataLogRequest: any) {
    return this.iGolfService.addLogRequest(jobDataLogRequest);
  }

  async getLogRequest(payload: LogRequestDto) {
    return this.iGolfService.getLogRequest(payload);
  }

  async removeUserLogCourse(ids: any[]) {
    return this.iGolfService.removeUserLogCourse(ids);
  }
}
