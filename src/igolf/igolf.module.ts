import { Module } from '@nestjs/common';
import { SharedModule } from 'src/shared/shared.module';
import { IGolfController } from './igolf.controller';
import { IgolfCronService } from './igolf.cron.service';
import { IGolfService } from './igolf.service';
import { ThreePartyCourseService } from './threePartyCourse.service';

@Module({
  imports: [SharedModule],
  controllers: [IGolfController],
  providers: [IGolfService, IgolfCronService, ThreePartyCourseService],
  exports: [],
})
export class IGolfModule {}
