import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional } from 'class-validator';

export class CourseSearchDto {
  @IsOptional()
  id_course?: string;

  @ApiProperty({ example: 'Blue Moon Crossing', required: false })
  @IsOptional()
  courseName?: string;
  @ApiProperty({ example: 'Phoenix', required: false })
  @IsOptional()
  city?: string;

  @IsOptional()
  @ApiProperty({ example: 25, required: false })
  radius?: number;

  @IsOptional()
  @ApiProperty({ example: 33.091159, required: false })
  referenceLatitude?: number;

  @IsOptional()
  @ApiProperty({ example: -117.266714, required: false })
  referenceLongitude?: number;

  @IsOptional()
  @ApiProperty({ example: 840, required: false })
  id_country?: number;

  @IsOptional()
  @ApiProperty({ example: 85016, required: false })
  zipcode?: number;

  @IsOptional()
  @ApiProperty({ example: 2, required: false })
  id_state?: number;

  @IsOptional()
  @ApiProperty({ example: 1, required: false })
  page?: number;

  @IsOptional()
  @ApiProperty({ example: 99, required: false })
  resultsPerPage?: number;

  @IsNotEmpty()
  @ApiProperty({ example: 12345, required: true })
  mrpId?: number;
}

export class CourseSearchGhinDto {
  @IsOptional()
  id_course?: string;

  @ApiProperty({ example: 'Blue Moon Crossing', required: false })
  @IsOptional()
  courseName?: string;
  @ApiProperty({ example: 'Phoenix', required: false })
  @IsOptional()
  city?: string;

  @IsOptional()
  @ApiProperty({ example: 25, required: false })
  radius?: number;

  @IsOptional()
  @ApiProperty({ example: 33.091159, required: false })
  referenceLatitude?: number;

  @IsOptional()
  @ApiProperty({ example: -117.266714, required: false })
  referenceLongitude?: number;

  @IsOptional()
  @ApiProperty({ example: 840, required: false })
  id_country?: number;

  @IsOptional()
  @ApiProperty({ example: 85016, required: false })
  zipcode?: number;

  @IsOptional()
  @ApiProperty({ example: 2, required: false })
  id_state?: number;

  @IsOptional()
  @ApiProperty({ example: 1, required: false })
  page?: number;

  @IsOptional()
  @ApiProperty({ example: 99, required: false })
  resultsPerPage?: number;

  @IsOptional()
  @ApiProperty({ example: 12345, required: false })
  mrpId?: number;
}
