import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsNotEmpty } from 'class-validator';

// enum TypeRequests {
//   day,
//   month,
//   year,
//   range,
// }
export class LogRequestDto {
  @IsNotEmpty()
  @IsDateString()
  @ApiProperty({ example: '2024-03-04' })
  from: Date;

  @IsNotEmpty()
  @IsDateString()
  @ApiProperty({ example: '2024-03-10' })
  to: Date;

  // @IsNotEmpty()
  // @IsEnum(TypeRequests, { message: 'Type must be value in: day, month, year, range' })
  // @ApiProperty({ enum: TypeRequests, example: TypeRequests.day })
  // type: TypeRequests;
}
