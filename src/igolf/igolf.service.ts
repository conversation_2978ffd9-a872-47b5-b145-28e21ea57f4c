import { InjectQueue } from '@nestjs/bull';
import { Cache } from '@nestjs/cache-manager';
import { BadRequestException, CACHE_MANAGER, Inject, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm';
import * as turf from '@turf/turf';
import axios from 'axios';
import { Queue } from 'bull';
import CryptoJS from 'crypto-js';
import * as geolib from 'geolib';
import _, { isEmpty } from 'lodash';
import moment from 'moment';
import ms from 'ms';
import { Between, EntityManager, In, Repository } from 'typeorm';
import { Round } from 'src/rounds/entities/round.entity';
import { OPTIONS_JOB_DEFAULT } from 'src/rounds/round.const';
import { centroid } from 'src/utils/utils';
import { PROCESSORS, PROCESS_QUEUE_NAMES } from 'src/workers/jobs/job.constant';
import { KeyConfigEntity } from '../key-config/entities/key-config.entity';
import { User } from '../users/entities/user.entity';
import { CourseSearchDto, CourseSearchGhinDto } from './dto/course-search.dto';
import { LogRequestDto } from './dto/log.request.dto';
import { UserLogCourseDto } from './dto/user-log-course.dto';
import { IgolfLogRequests } from './entity/ghin.log.request.entity';
import { UserLogCourseEntity } from './entity/user-log-course.entity';
import { IGolfConst } from './igolf.const';
import { CLIENTS } from './igolf.types';

const LIMIT_TIME_REQUEST_COURSES = 'LIMIT_TIME_REQUEST_COURSES';

@Injectable()
export class IGolfService {
  private readonly logger = new Logger(IGolfService.name);
  apiTriggerKlaviyoSpamIgolf: any;
  constructor(
    private configService: ConfigService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    @InjectRepository(Round)
    private roundRepository: Repository<Round>,
    @InjectRepository(IgolfLogRequests)
    private igolfLogRequestRepo: Repository<IgolfLogRequests>,
    @InjectRepository(User)
    private userRepo: Repository<User>,
    @InjectRepository(UserLogCourseEntity)
    private userLogCourseRepo: Repository<UserLogCourseEntity>,
    @InjectRepository(KeyConfigEntity)
    private keyConfigRepo: Repository<KeyConfigEntity>,
    // @Inject(forwardRef(() => GlxService)) private glxService: GlxService,
    @InjectQueue(PROCESSORS.LogRequestIGolfSI) private logRequestIGolf: Queue,
    @InjectEntityManager()
    private readonly entityManager: EntityManager
  ) {
    const mytmEndpoint = configService.get('mytm.endpoint');
    this.apiTriggerKlaviyoSpamIgolf = mytmEndpoint + '/play/spam';
  }

  getRequestHeaderConfigs() {
    return {
      headers: { clientId: this.configService.get('mytm.clientId') },
    };
  }

  async getCourseDetail(idCourse: string, client?: string) {
    return await this.callIGolfService(IGolfConst.ACTION_COURSE_DETAIL, {
      id_course: idCourse,
      detailLevel: 2,
      stateFormat: 3,
      countryFormat: 3,
      client,
    });
  }

  async searchCourse(courseDto: CourseSearchDto, client?: string) {
    if (courseDto?.referenceLatitude && courseDto?.referenceLongitude) {
      const cacheSearch = await this.getCacheSearchCourse(courseDto);
      if (cacheSearch) {
        return cacheSearch;
      }
    }

    const { courseName } = courseDto;
    if (courseName && courseName.length < 4) {
      return null;
    }

    const user = await this.userRepo.findOne({
      where: { id: courseDto?.mrpId },
    });

    if (!user) {
      return { msg: 'User not found!' };
    }

    const isUserSpamCourse = await this.checkUserSpamCourse(courseDto, client);

    if (isUserSpamCourse) {
      return { isLimitRequest: true };
    }

    if (courseDto.mrpId) {
      delete courseDto.mrpId;
    }

    return await this.callIGolfService(IGolfConst.ACTION_COURSE_LIST, { ...courseDto, client }, true);
  }

  async checkUserSpamCourse(courseDto: CourseSearchDto, client?: string) {
    try {
      const { mrpId } = courseDto;
      let user = null;
      if (mrpId) {
        user = await this.userRepo.findOne({
          where: { id: courseDto?.mrpId },
        });
      }
      if (!mrpId || !user) {
        this.saveLogCourseSearch(courseDto, client).catch((e) => e);
        return false;
      }

      if (user.is_spam_igolf) {
        return this.checkUserUnBlockRequest(user, courseDto, client);
      }

      const keyConfig = await this.keyConfigRepo.findOne({
        where: {
          key: LIMIT_TIME_REQUEST_COURSES,
        },
      });
      const limit = keyConfig.value ? Number(keyConfig.value) : 60;
      const sqlQuery = `SELECT COUNT(*) AS requestcount
            FROM user_log_courses
            WHERE user_id = '${user.id}'
                AND created_at >= NOW() - INTERVAL '1 minutes';`;
      const result = await this.entityManager.query(sqlQuery);
      if (!result || !result?.length) return false;
      const requestCount = parseInt(result[0]?.requestcount);
      if (requestCount >= limit) {
        const currentTime: any = new Date();
        this.userRepo.update({ id: user?.id }, { is_spam_igolf: true, is_user_igolf_blocked: true }).catch((e) => e);
        // send email to admin
        this.triggerKlaviyoSpamIgolf(user?.email, limit, currentTime).catch((e) => e);
        return true;
      } else {
        this.saveLogCourseSearch(courseDto, client).catch((e) => e);
      }
      return false;
    } catch (err) {
      this.logger.error(`ERROR CHECK SPAM IGOLF: ${JSON.stringify(err)}`);
      return false;
    }
  }

  async searchCourseFromGhin(courseDto: CourseSearchGhinDto, client?: string) {
    if (courseDto?.referenceLatitude && courseDto?.referenceLongitude) {
      const cacheSearch = await this.getCacheSearchCourse(courseDto);
      if (cacheSearch) {
        return cacheSearch;
      }
    }

    const { courseName } = courseDto;
    if (courseName && courseName.length < 4) {
      return null;
    }

    this.saveLogCourseSearch(courseDto, client).catch((e) => e);

    return await this.callIGolfService(IGolfConst.ACTION_COURSE_LIST, { ...courseDto, client }, true);
  }

  async checkUserUnBlockRequest(user, courseDto, client) {
    const sqlQuery = `SELECT MAX(request_time) AS timerq
            FROM user_log_courses
            WHERE user_id = '${user.id}';`;
    const result = await this.entityManager.query(sqlQuery);
    if (!result || !result?.length) return false;
    const lastRequestTime: any = new Date(result[0]?.timerq);
    const currentTime: any = new Date();
    const timeSinceLastRequest: any = currentTime - lastRequestTime;
    if (timeSinceLastRequest < 5 * 60 * 1000) {
      return true;
    }
    await this.userRepo.update({ id: user.id }, { is_spam_igolf: false });
    this.saveLogCourseSearch(courseDto, client).catch((e) => e);
    return false;
  }

  async saveLogCourseSearch(courseDto: CourseSearchDto, client?: string) {
    let user = null;
    if (courseDto.mrpId) {
      user = await this.userRepo.findOne({
        where: {
          id: courseDto.mrpId,
        },
      });
    }

    const newUserLogCourse: UserLogCourseDto = {
      request_time: new Date(),
      params: JSON.stringify(courseDto),
      client_id: client,
    };

    if (user && user.id) {
      newUserLogCourse.user_id = user.id;
    }

    return this.userLogCourseRepo.save(newUserLogCourse);
  }

  private setMaxRadiusSearch(courseDto: CourseSearchDto) {
    if (courseDto?.radius) {
      const radius = parseInt(`${courseDto?.radius}`);
      if (radius > 10) {
        courseDto.radius = 10;
      }
    }
  }

  async getCourseGPSDetail(idCourse: string, client?: string) {
    return await this.callIGolfService(IGolfConst.ACTION_COURSE_GPS_DETAIL, {
      id_course: idCourse,
      client,
    });
  }

  async getCourseGPSVectorDetail(idCourse: string, client?: string) {
    return await this.callIGolfService(IGolfConst.ACTION_COURSE_GPS_VECTOR_DETAIL, {
      id_course: idCourse,
      client,
    });
  }

  async getCourseScoreCardDetail(idCourse: string, client?: string) {
    return await this.callIGolfService(IGolfConst.ACTION_COURSE_SCORECARD, {
      id_course: idCourse,
      client,
    });
  }

  async getCourseTeeDetail(idCourse: string, client?: string) {
    return await this.callIGolfService(IGolfConst.ACTION_COURSE_TEE_DETAIL, {
      id_course: idCourse,
      detailLevel: 2,
      client,
    });
  }
  async getCourseElevationDataDetail(idCourse: string, client?: string) {
    return await this.callIGolfService(IGolfConst.ACTION_COURSE_ELEVATION_DATA_DETAIL, {
      id_course: idCourse,
      detailLevel: 2,
      client,
    });
  }

  async getUserCourseRecent(userId: number) {
    const queryBuilder = this.roundRepository.createQueryBuilder();
    queryBuilder.select(['igolf_course_id', 'course_name', 'map_id', 'ghin_course_id', 'ghin_course_name']);
    queryBuilder
      .where(
        `user_id = :userId AND completed = true AND map_id = 'iGolf' AND (igolf_course_id IS NOT NULL AND igolf_course_id  != '') `,
        { userId }
      )
      .take(100)
      .orderBy({ played_on: 'DESC', created_at: 'DESC' });
    const rounds = await queryBuilder.getRawMany();

    let courseList = [];
    if (rounds) {
      courseList = _.uniqBy(rounds, (round) => round.igolf_course_id);
      courseList = courseList.map((c) => {
        return {
          id_course: c.igolf_course_id,
          courseName: c.course_name,
          ghin_course_id: c.ghin_course_id,
          ghin_course_name: c.ghin_course_name,
        };
      });
      if (courseList.length >= 10) {
        courseList.length = 10;
      }
    }
    return {
      totalCourses: courseList.length,
      courseList,
    };
  }

  private async callIGolfService(action: IGolfConst, payload: any, forceCallService = false) {
    try {
      payload['active'] = 1;
      if (payload?.client) {
        if ([CLIENTS.SWING_INDEX, CLIENTS.SWING_INDEX_APP].includes(payload?.client)) {
          await this.logRequestIGolf.add(
            PROCESS_QUEUE_NAMES.LOG_REQUEST_IGOLF_SI,
            {
              client: payload.client,
              actionType: action,
              igolfCourseId: payload?.id_course ?? action,
            },
            OPTIONS_JOB_DEFAULT
          );
        }

        delete payload.client;
      }
      this.setMaxRadiusSearch(payload);
      if (this.isActionNeedCache(action) && !forceCallService) {
        return await this.getCacheCourse(action, payload);
      }
      this.logger.log(`CALL IGOLF ACTION: ${action}, payload: ${JSON.stringify(payload)}`);
      const { data } = await axios.post(this.buildUrlActionIGolf(action), payload);
      if (data?.Status == 1 && this.isActionNeedCache(action)) {
        if (action === IGolfConst.ACTION_COURSE_LIST) {
          await this.setCacheSearchCourse(payload, data);
        } else {
          await this.setCacheCourse(action, data, payload.id_course);
        }
      }
      return data;
    } catch (error) {
      this.logger.error(`ERROR CALL IGOLF SERVICE ACTION ${action}`);
      this.logger.error(error.message);
      return null;
    }
  }

  private isActionNeedCache(action: IGolfConst) {
    const actionsCache = [
      IGolfConst.ACTION_COURSE_DETAIL,
      IGolfConst.ACTION_COURSE_GPS_DETAIL,
      IGolfConst.ACTION_COURSE_GPS_VECTOR_DETAIL,
      IGolfConst.ACTION_COURSE_SCORECARD,
      IGolfConst.ACTION_COURSE_TEE_DETAIL,
      IGolfConst.ACTION_COURSE_ELEVATION_DATA_DETAIL,
      IGolfConst.ACTION_COURSE_LIST,
    ];
    return actionsCache.includes(action);
  }

  private buildUrlActionIGolf(action: string) {
    const url1 = `${action}/${this.iGolfApiKey()}/${IGolfConst.API_VERSION}/${IGolfConst.SIGNATURE_VERSION}/${
      IGolfConst.SIGNATURE_METHOD
    }/`;
    const url2 = `${this.timeStamp()}/${IGolfConst.RESPONSE_FORMAT}`;
    const signature = this.calculateSignature(url1, url2);
    return `${this.iGolfApiBase()}${url1}${signature}/${url2}`;
  }

  private calculateSignature(url1: string, url2: string) {
    const url = `${url1}${url2}`;
    const sha256 = CryptoJS.HmacSHA256(url, this.iGolfApiSecretKey());
    return CryptoJS.enc.Base64.stringify(sha256).replace(/\+/g, '-').replace(/\//g, '_').replace(/\=+$/, '');
  }

  private timeStamp() {
    return moment().format('YYMMDDHHmmss') + this.timeZone();
  }

  private timeZone() {
    return moment().toString().split('GMT').pop();
  }

  private iGolfApiKey() {
    return this.configService.get('igolf.apiKey');
  }

  private iGolfApiSecretKey() {
    return this.configService.get('igolf.apiSecretKey');
  }

  private iGolfApiBase() {
    return this.configService.get('igolf.apiBase');
  }

  private async setCacheCourse(key: string, payload: any, idCourse: string) {
    const ttl = ms(this.configService.get('app.cacheTTL'));
    console.log(`SET CACHE ${key}: COURSE: ${idCourse}`);
    await this.cacheManager.set(`${IGolfConst.CACHE_COURSE_PREFIX}:${key}:${idCourse}`, JSON.stringify(payload), {
      ttl: parseInt(ttl, 10) / 1000,
    });
  }
  private async getCacheCourse(action: any, payload: any) {
    let course: any;
    try {
      course = await this.cacheManager.get(`${IGolfConst.CACHE_COURSE_PREFIX}:${action}:${payload.id_course}`);
    } catch (error) {
      console.log(`ERROR GET CACHE COURSE`);
      course = null;
    }
    if (course) {
      const dataCourse = JSON.parse(course);
      if (dataCourse.Status != 1) {
        this.logger.debug(`RECALL GET COURSE  ${action}: COURSE: ${payload.id_course}`);
        return await this.callIGolfService(action, payload, true);
      } else {
        return JSON.parse(course);
      }
    }
    return await this.callIGolfService(action, payload, true);
  }
  private async getCacheSearchCourse(payload: CourseSearchDto): Promise<any> {
    const lat = Number(payload?.referenceLatitude).toFixed(4);
    const long = Number(payload?.referenceLongitude).toFixed(4);
    const key = `${IGolfConst.CACHE_COURSE_PREFIX}:${IGolfConst.ACTION_COURSE_LIST}:LOCATION_${lat}_${long}`;

    try {
      const cacheSearch: any = await this.getCacheValueSearchCourse(key);
      if (cacheSearch) {
        return JSON.parse(cacheSearch);
      }
      return null;
    } catch (error) {
      console.log(`ERROR GET CACHE COURSE`, error);
      return null;
    }
  }
  private async getCacheValueSearchCourse(key) {
    return await this.cacheManager.get(key);
  }

  async setCacheSearchCourse(payload: CourseSearchDto, dateSearch) {
    const lat = Number(payload?.referenceLatitude).toFixed(4);
    const long = Number(payload?.referenceLongitude).toFixed(4);
    const key = `${IGolfConst.CACHE_COURSE_PREFIX}:${IGolfConst.ACTION_COURSE_LIST}:LOCATION_${lat}_${long}`;
    if (lat && long) {
      // save course in 30 minutes
      return await this.cacheManager.set(key, JSON.stringify(dateSearch), {
        ttl: ms('30m') / 1000,
      });
    }
    return false;
  }

  async totalYardage(teeName: string, idCourse: string, isSimpleRound = false, isParOut = false, isParIn = false) {
    const teeDetail = await this.getCourseTeeDetail(idCourse);
    if (teeDetail.Status == 1) {
      const teeSelected = teeDetail?.teesList.find(
        (tee: any) => tee?.teeName?.toString().trim().toLowerCase() == teeName?.trim().toLowerCase()
      );
      if (isSimpleRound) {
        if (isParOut) {
          return teeSelected?.yds1to9 || 0;
        } else if (isParIn) {
          return teeSelected?.yds10to18 || 0;
        }
      }
      return teeSelected?.ydsTotal || 0;
    }
    return 0;
  }

  fairway_center_path(holeNumber, idCourse) {
    this.logger.debug({ holeNumber, idCourse });
  }

  async coordinatesFor(holeNumber, lie, idCourse, client?: string) {
    const geo = await this.getGeoJsonFor(holeNumber, lie, idCourse, client);
    if (!geo) {
      return [];
    }
    const shape = geo?.Shapes?.Shape;
    if (!shape) {
      return [];
    }
    const points = shape?.Points;
    if (points == '0 0') {
      return [];
    }
    return this.convertStringPoints(shape);
  }

  async getGeoJsonFor(holeNumber, lie, idCourse, client?: string) {
    const hole = await this.getGeojson(holeNumber, idCourse, client);
    if (!hole) {
      return {};
    }
    lie = lie.toLowerCase();
    switch (lie) {
      case 'tee':
        return hole?.Teebox || null;
      case 'fairway':
        return hole?.Fairway || null;
      case 'green':
        return hole?.Green || null;
      case 'fairway_center_path':
      case 'tee-boundary':
        return hole?.Centralpath || null;
      default:
        return null;
    }
  }

  async getGeojson(holeNumber, idCourse, client?: string) {
    const courseGPSVectorDetail = await this.getCourseGPSVectorDetail(idCourse, client);

    if (courseGPSVectorDetail.Status == 1) {
      const holes = courseGPSVectorDetail?.vectorGPSObject?.Holes?.Hole;
      if (!holes) {
        return null;
      }
      return holes.find((h: any) => +h.HoleNumber == +holeNumber);
    }
    return null;
  }

  async totalPar(teeName: string, idCourse: string, isSimpleRound = false, isParOut = false, isParIn = false) {
    const [teeDetail, scoreCard] = await Promise.all([
      this.getCourseTeeDetail(idCourse),
      this.getCourseScoreCardDetail(idCourse),
    ]);
    if (teeDetail.Status != 1) {
      return 0;
    }
    const gender =
      teeDetail?.teesList?.find((tee: any) => tee.teeName?.toLowerCase().trim() == teeName?.trim().toLowerCase())
        ?.gender || 'men';

    const scoreCardPerson = gender == 'men' ? scoreCard.menScorecardList : scoreCard.wmnScorecardList;
    if (isSimpleRound) {
      if (isParOut) {
        return +scoreCardPerson[0]?.parOut;
      } else if (isParIn) {
        return +scoreCardPerson[0]?.parIn;
      }
    }

    return +scoreCardPerson[0]?.parOut + +scoreCardPerson[0]?.parIn;
  }

  async pinLocation(holeNumber, idCourse) {
    const hole = await this.getGeojson(holeNumber, idCourse);
    if (!hole) {
      return null;
    }
    const greenCenter = hole?.Greencenter?.Shapes?.Shape;
    const pinLocation = this.convertStringPoints(greenCenter);
    if (pinLocation != '') {
      return pinLocation[0];
    }
    return null;
  }

  async greenFrontFor(holeNumber: any, idCourse: string) {
    const courseGPSDetail = await this.getCourseGPSDetail(idCourse);
    if (courseGPSDetail.Status == 1) {
      const front = courseGPSDetail['GPSList'].find((hole: any) => +hole.holeNumber == +holeNumber);
      if (!front) {
        this.logger.log(`NOTFOUND GREEN FRONT FOR HOLE NUMBER ${holeNumber} - COURSE: ${idCourse}`);
        return null;
      }
      return [front['frontLon'], front['frontLat']];
    }
    return null;
  }

  convertStringPoints(points) {
    if (points) {
      return points.map((p: any) => {
        return p['Points'].split(',').map((point: any) => point.split(' ').map((coord: any) => parseFloat(coord)));
      });
    }
    return '';
  }
  async get_random_point_within_shape(lie, holeNumber, idCourse) {
    const geo = await this.coordinatesFor(holeNumber, lie, idCourse);
    if (geo) {
      try {
        const points = geo[0];
        const coordCentroid = centroid(points);
        const { maxLat, minLat, maxLng, minLng } = geolib.getBounds(points);
        const arrRing: any = [minLng, minLat, maxLng, maxLat];
        const pointRandom = turf.randomPoint(1, { bbox: arrRing });
        const centroidPoint = turf.point(coordCentroid);
        const midPoint = turf.midpoint(
          pointRandom.features[0].geometry.coordinates,
          centroidPoint.geometry.coordinates
        );
        const midCoords = midPoint?.geometry.coordinates;
        if (midCoords) {
          return midCoords;
        } else {
          return null;
        }
      } catch (error) {
        this.logger.log(error.message);
        return null;
      }
    }
  }
  async addLogRequest(jobDataLogRequest: any) {
    const { client, actionType, igolfCourseId } = jobDataLogRequest;
    if (![CLIENTS.SWING_INDEX, CLIENTS.SWING_INDEX_APP].includes(client)) {
      console.log(`No need to log ${client}`);
      return;
    }
    console.log(`ADDING LOG REQUEST IGOLF: ${actionType} - COURSE: ${igolfCourseId}`);

    const currentDate = moment().format('YYYY-MM-DD');
    const logRequest = await this.igolfLogRequestRepo.findOne({
      where: { igolf_course_id: igolfCourseId, request_type: actionType, created_at: moment(currentDate).toDate() },
    });
    if (logRequest) {
      try {
        await this.igolfLogRequestRepo.update({ id: logRequest.id }, { count: logRequest.count + 1 });
        return true;
      } catch (err) {
        console.log(err);
        return false;
      }
    }

    const payloadSave = {
      igolf_course_id: igolfCourseId,
      request_type: actionType,
      client,
      count: 1,
      created_at: moment(currentDate).toDate(),
    };
    try {
      const dataSave = this.igolfLogRequestRepo.create(payloadSave);
      await this.igolfLogRequestRepo.save(dataSave);
      return true;
    } catch (err) {
      console.log(err);
      return false;
    }
  }
  async getLogRequest(payload: LogRequestDto) {
    // validate limit request 1 year
    const dateFrom = moment(payload.from);
    const dateTo = moment(payload.to);

    const diffYear = dateTo.diff(dateFrom, 'year');
    const diffDay = dateTo.diff(dateFrom, 'day');
    if (diffDay < 0) {
      throw new BadRequestException('Date To must be greater than or equal Date From!');
    }
    if (diffYear > 1) {
      throw new BadRequestException('Maximum log range is 1 year!');
    }
    const query = this.igolfLogRequestRepo.createQueryBuilder('');
    query
      .where({
        created_at: Between(payload.from, payload.to),
      })
      .orderBy({ created_at: 'DESC' });
    // const dateName = (item) => moment(item.created_at).format('YYYY-MM-DD');

    const logs = await query.getMany();
    if (logs.length > 0) {
      const totalRequest = logs.reduce((accumulator, currentValue) => {
        return accumulator + currentValue.count;
      }, 0);
      const transformLogs = logs.map((item) => {
        return {
          igolf_course_id: item.igolf_course_id,
          request_type: item.request_type,
          client: item.client,
          count: item.count,
          created_at: moment(item.created_at).format('YYYY-MM-DD'),
        };
      });
      const requests = _.groupBy(transformLogs, 'created_at');

      this.transformLogDetail(requests);

      return { count: totalRequest, requests };
    }
    return [];
  }

  async triggerKlaviyoSpamIgolf(email, limit, timeBlock) {
    this.logger.log(`TRIGGER_triggerKlaviyoSpamIgolf: ${email}`);
    if (email) {
      try {
        await axios.get(
          this.apiTriggerKlaviyoSpamIgolf + `/${email}?limit=${limit}&timeBlock=${timeBlock}`,
          this.getRequestHeaderConfigs()
        );
        return true;
      } catch (error) {
        console.error(`ERROR_triggerKlaviyoSpamIgolf ${email}: ` + error.message);
        this.logger.log(`ERROR_triggerKlaviyoSpamIgolf: ${JSON.stringify(error)}`);
      }
    }
  }

  async removeUserLogCourse(ids: any[]) {
    if (isEmpty(ids)) return;
    return this.userLogCourseRepo.delete({ id: In(ids) });
  }

  private transformLogDetail(requests) {
    const keys = Object.keys(requests);
    for (const key of keys) {
      const totalRequestByDate = requests[key].reduce((accumulator, currentValue) => {
        return accumulator + currentValue.count;
      }, 0);
      requests[key] = { data: _.groupBy(requests[key], 'request_type'), count: totalRequestByDate } as any;
    }
  }
}
