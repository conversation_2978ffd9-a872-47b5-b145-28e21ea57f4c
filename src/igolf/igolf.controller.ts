import { Body, Controller, Get, HttpCode, HttpStatus, Param, Post, Req, UseGuards } from '@nestjs/common';
import { ApiBody, ApiParam, ApiTags } from '@nestjs/swagger';
import { AuthGuard } from 'src/guards/auth.guard';
import { CourseSearchDto } from './dto/course-search.dto';
import { CourseDto } from './dto/course.dto';
import { LogRequestDto } from './dto/log.request.dto';
import { ThreePartyCourseService } from './threePartyCourse.service';

@ApiTags('IGolf')
@Controller('igolf')
export class IGolfController {
  constructor(private readonly threePartyCourseService: ThreePartyCourseService) {}

  @Post('detail')
  @HttpCode(HttpStatus.OK)
  async getCourseDetail(@Body() courseDto: CourseDto, @Req() req: any) {
    const course = await this.threePartyCourseService.getCourseDetail(courseDto.id_course, req?.headers?.client);
    return course;
  }

  @Post('search')
  @HttpCode(HttpStatus.OK)
  async searchCourse(@Body() courseSearchDto: CourseSearchDto, @Req() req: any) {
    return this.threePartyCourseService.searchCourse(courseSearchDto, req?.headers?.client);
  }

  @Post('course_gps_detail')
  @HttpCode(HttpStatus.OK)
  async getCourseGPSDetail(@Body() courseDto: CourseDto, @Req() req: any) {
    return this.threePartyCourseService.getCourseGPSDetail(courseDto.id_course, req?.headers?.client);
  }

  @Post('course_gps_vector_detail')
  @HttpCode(HttpStatus.OK)
  async getCourseGPSVectorDetail(@Body() courseDto: CourseDto, @Req() req: any) {
    return this.threePartyCourseService.getCourseGPSVectorDetail(courseDto.id_course, req?.headers?.client);
  }

  @Post('course_scorecard_detail')
  @HttpCode(HttpStatus.OK)
  async getCourseScoreCardDetail(@Body() courseDto: CourseDto, @Req() req: any) {
    return this.threePartyCourseService.getCourseScoreCardDetail(courseDto.id_course, req?.headers?.client);
  }

  @Post('course_tee_detail')
  @HttpCode(HttpStatus.OK)
  async getCourseTeeDetail(@Body() courseDto: CourseDto, @Req() req: any) {
    return this.threePartyCourseService.getCourseTeeDetail(courseDto.id_course, req?.headers?.client);
  }

  @Post('course_elevation_data_detail')
  @HttpCode(HttpStatus.OK)
  async getCourseElevationDataDetail(@Body() courseDto: CourseDto, @Req() req: any) {
    return this.threePartyCourseService.getCourseElevationDataDetail(courseDto.id_course, req?.headers?.client);
  }

  @Get(':userId/course_recent')
  @ApiParam({ name: 'userId', example: '41428' })
  @HttpCode(HttpStatus.OK)
  async getUserCourseRecent(@Param('userId') userId: string) {
    return this.threePartyCourseService.getUserCourseRecent(+userId);
  }

  @Post('log_requests')
  @ApiBody({ type: LogRequestDto })
  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard)
  async getLogRequest(@Body() payload: LogRequestDto): Promise<any> {
    return await this.threePartyCourseService.getLogRequest(payload);
  }
}
