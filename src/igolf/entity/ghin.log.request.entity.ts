import { ApiProperty } from '@nestjs/swagger';
import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

@Entity({ name: 'igolf_log_requests' })
export class IgolfLogRequests {
  @PrimaryGeneratedColumn()
  @ApiProperty({ example: 1 })
  id: number;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  igolf_course_id: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  request_type: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  client: string;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  count: number;

  @CreateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  created_at: Date;

  @UpdateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  updated_at: Date;
}
