import { ApiProperty } from '@nestjs/swagger';
import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

@Entity({ name: 'user_log_courses' })
export class UserLogCourseEntity {
  @PrimaryGeneratedColumn()
  @ApiProperty({ example: 1 })
  id: number;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  user_id: number;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  client_id: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  params: string;

  @CreateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  request_time: Date;

  @CreateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  created_at: Date;

  @UpdateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  updated_at: Date;
}
