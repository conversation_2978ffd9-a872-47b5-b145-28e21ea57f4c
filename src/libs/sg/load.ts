import * as fs from 'fs';
import { isEmpty } from 'lodash';
import * as path from 'path';
import { SgHelper } from './helper';

export class SgLoad extends SgHelper {
  // module Load
  //   extend self
  //   extend Helper

  // #
  // # loads the YAML file for the given lie condition &
  // # skill level of the player
  // #
  // read_yaml_data(lie, skill_level: '') {
  //   // ::YAML::load( yaml_data(lie, skill_level).read )
  // }

  // #
  // # loads the JSON file for the given lie condition &
  // # skill level of the player
  // #
  // # def read_json_data(lie:, pga: false)
  read_json_data(lie, skill_level: '') {
    // ::JSON.parse( json_data(lie, skill_level).read, symbolize_names: true )
    const data = fs.readFileSync(this.json_data(lie, skill_level)).toString('utf-8');
    return JSON.parse(data);
  }
  // #
  // # location of raw text file data
  // #
  raw_data(lie, skill_level: '') {
    console.log(lie, skill_level);

    // pro = skill_level.to_s.empty? ? "" : "#{skill_level}_"
    // raw_data_files.join("#{pro}#{format_lie(lie)}ShotValue.txt")
  }

  // #
  // # location to a yaml file
  // #
  yaml_data(lie, skill_level) {
    console.log(lie, skill_level);
    // data_files.join("#{format_filename(lie, skill_level)}.yml")
  }

  // #
  // # location to a json file
  // #
  // # def json_data(lie, pga = false)
  json_data(lie, skill_level) {
    // data_files.join("#{format_filename(lie, skill_level)}.json")
    const fileName = this.format_filename(lie, skill_level) + '.json';
    return path.join(this.data_files(), fileName);
  }

  // #
  // # The location of the TXT files
  // #
  raw_data_files() {
    // ::Pathname.new(__dir__).join("../../import_data")
    return path.join(__dirname, './sg-data/import_data/');
  }

  // #
  // # location of the YAML files
  // #
  data_files() {
    // ::Pathname.new(__dir__).join("data")
    return path.join(__dirname, './sg-data/data/');
  }
  // #
  // # formats the filename
  // #
  format_filename(lie, skill_level) {
    if (skill_level == 'pro') {
      skill_level = 'pga';
    }
    return isEmpty(skill_level) ? `${this.format_lie(lie)}` : `${this.format_lie(lie)}_${skill_level}`;
  }
}
