{"lie": "recovery", "skill_level": "pga", "unit": "yards", "baselines": [{"distance": 1, "baseline": 3.3901, "skill_level": "pga", "lie": "recovery"}, {"distance": 2, "baseline": 3.3963, "skill_level": "pga", "lie": "recovery"}, {"distance": 3, "baseline": 3.4024, "skill_level": "pga", "lie": "recovery"}, {"distance": 4, "baseline": 3.4086, "skill_level": "pga", "lie": "recovery"}, {"distance": 5, "baseline": 3.4148, "skill_level": "pga", "lie": "recovery"}, {"distance": 6, "baseline": 3.421, "skill_level": "pga", "lie": "recovery"}, {"distance": 7, "baseline": 3.4271, "skill_level": "pga", "lie": "recovery"}, {"distance": 8, "baseline": 3.4333, "skill_level": "pga", "lie": "recovery"}, {"distance": 9, "baseline": 3.4395, "skill_level": "pga", "lie": "recovery"}, {"distance": 10, "baseline": 3.4456, "skill_level": "pga", "lie": "recovery"}, {"distance": 11, "baseline": 3.4518, "skill_level": "pga", "lie": "recovery"}, {"distance": 12, "baseline": 3.458, "skill_level": "pga", "lie": "recovery"}, {"distance": 13, "baseline": 3.4641, "skill_level": "pga", "lie": "recovery"}, {"distance": 14, "baseline": 3.4703, "skill_level": "pga", "lie": "recovery"}, {"distance": 15, "baseline": 3.4765, "skill_level": "pga", "lie": "recovery"}, {"distance": 16, "baseline": 3.4827, "skill_level": "pga", "lie": "recovery"}, {"distance": 17, "baseline": 3.4888, "skill_level": "pga", "lie": "recovery"}, {"distance": 18, "baseline": 3.495, "skill_level": "pga", "lie": "recovery"}, {"distance": 19, "baseline": 3.5012, "skill_level": "pga", "lie": "recovery"}, {"distance": 20, "baseline": 3.5073, "skill_level": "pga", "lie": "recovery"}, {"distance": 21, "baseline": 3.5135, "skill_level": "pga", "lie": "recovery"}, {"distance": 22, "baseline": 3.5197, "skill_level": "pga", "lie": "recovery"}, {"distance": 23, "baseline": 3.5258, "skill_level": "pga", "lie": "recovery"}, {"distance": 24, "baseline": 3.532, "skill_level": "pga", "lie": "recovery"}, {"distance": 25, "baseline": 3.5382, "skill_level": "pga", "lie": "recovery"}, {"distance": 26, "baseline": 3.5443, "skill_level": "pga", "lie": "recovery"}, {"distance": 27, "baseline": 3.5505, "skill_level": "pga", "lie": "recovery"}, {"distance": 28, "baseline": 3.5567, "skill_level": "pga", "lie": "recovery"}, {"distance": 29, "baseline": 3.5629, "skill_level": "pga", "lie": "recovery"}, {"distance": 30, "baseline": 3.569, "skill_level": "pga", "lie": "recovery"}, {"distance": 31, "baseline": 3.5752, "skill_level": "pga", "lie": "recovery"}, {"distance": 32, "baseline": 3.5825, "skill_level": "pga", "lie": "recovery"}, {"distance": 33, "baseline": 3.6017, "skill_level": "pga", "lie": "recovery"}, {"distance": 34, "baseline": 3.6197, "skill_level": "pga", "lie": "recovery"}, {"distance": 35, "baseline": 3.6365, "skill_level": "pga", "lie": "recovery"}, {"distance": 36, "baseline": 3.6522, "skill_level": "pga", "lie": "recovery"}, {"distance": 37, "baseline": 3.6669, "skill_level": "pga", "lie": "recovery"}, {"distance": 38, "baseline": 3.6807, "skill_level": "pga", "lie": "recovery"}, {"distance": 39, "baseline": 3.6935, "skill_level": "pga", "lie": "recovery"}, {"distance": 40, "baseline": 3.7055, "skill_level": "pga", "lie": "recovery"}, {"distance": 41, "baseline": 3.7167, "skill_level": "pga", "lie": "recovery"}, {"distance": 42, "baseline": 3.7272, "skill_level": "pga", "lie": "recovery"}, {"distance": 43, "baseline": 3.7369, "skill_level": "pga", "lie": "recovery"}, {"distance": 44, "baseline": 3.746, "skill_level": "pga", "lie": "recovery"}, {"distance": 45, "baseline": 3.7545, "skill_level": "pga", "lie": "recovery"}, {"distance": 46, "baseline": 3.7624, "skill_level": "pga", "lie": "recovery"}, {"distance": 47, "baseline": 3.7697, "skill_level": "pga", "lie": "recovery"}, {"distance": 48, "baseline": 3.7765, "skill_level": "pga", "lie": "recovery"}, {"distance": 49, "baseline": 3.7829, "skill_level": "pga", "lie": "recovery"}, {"distance": 50, "baseline": 3.7887, "skill_level": "pga", "lie": "recovery"}, {"distance": 51, "baseline": 3.7942, "skill_level": "pga", "lie": "recovery"}, {"distance": 52, "baseline": 3.7992, "skill_level": "pga", "lie": "recovery"}, {"distance": 53, "baseline": 3.8039, "skill_level": "pga", "lie": "recovery"}, {"distance": 54, "baseline": 3.8082, "skill_level": "pga", "lie": "recovery"}, {"distance": 55, "baseline": 3.8122, "skill_level": "pga", "lie": "recovery"}, {"distance": 56, "baseline": 3.8158, "skill_level": "pga", "lie": "recovery"}, {"distance": 57, "baseline": 3.8192, "skill_level": "pga", "lie": "recovery"}, {"distance": 58, "baseline": 3.8222, "skill_level": "pga", "lie": "recovery"}, {"distance": 59, "baseline": 3.825, "skill_level": "pga", "lie": "recovery"}, {"distance": 60, "baseline": 3.8275, "skill_level": "pga", "lie": "recovery"}, {"distance": 61, "baseline": 3.8298, "skill_level": "pga", "lie": "recovery"}, {"distance": 62, "baseline": 3.8318, "skill_level": "pga", "lie": "recovery"}, {"distance": 63, "baseline": 3.8336, "skill_level": "pga", "lie": "recovery"}, {"distance": 64, "baseline": 3.8352, "skill_level": "pga", "lie": "recovery"}, {"distance": 65, "baseline": 3.8366, "skill_level": "pga", "lie": "recovery"}, {"distance": 66, "baseline": 3.8377, "skill_level": "pga", "lie": "recovery"}, {"distance": 67, "baseline": 3.8387, "skill_level": "pga", "lie": "recovery"}, {"distance": 68, "baseline": 3.8395, "skill_level": "pga", "lie": "recovery"}, {"distance": 69, "baseline": 3.8401, "skill_level": "pga", "lie": "recovery"}, {"distance": 70, "baseline": 3.8406, "skill_level": "pga", "lie": "recovery"}, {"distance": 71, "baseline": 3.8409, "skill_level": "pga", "lie": "recovery"}, {"distance": 72, "baseline": 3.841, "skill_level": "pga", "lie": "recovery"}, {"distance": 73, "baseline": 3.841, "skill_level": "pga", "lie": "recovery"}, {"distance": 74, "baseline": 3.8409, "skill_level": "pga", "lie": "recovery"}, {"distance": 75, "baseline": 3.8406, "skill_level": "pga", "lie": "recovery"}, {"distance": 76, "baseline": 3.8401, "skill_level": "pga", "lie": "recovery"}, {"distance": 77, "baseline": 3.8395, "skill_level": "pga", "lie": "recovery"}, {"distance": 78, "baseline": 3.8388, "skill_level": "pga", "lie": "recovery"}, {"distance": 79, "baseline": 3.838, "skill_level": "pga", "lie": "recovery"}, {"distance": 80, "baseline": 3.8371, "skill_level": "pga", "lie": "recovery"}, {"distance": 81, "baseline": 3.836, "skill_level": "pga", "lie": "recovery"}, {"distance": 82, "baseline": 3.8349, "skill_level": "pga", "lie": "recovery"}, {"distance": 83, "baseline": 3.8336, "skill_level": "pga", "lie": "recovery"}, {"distance": 84, "baseline": 3.8323, "skill_level": "pga", "lie": "recovery"}, {"distance": 85, "baseline": 3.8308, "skill_level": "pga", "lie": "recovery"}, {"distance": 86, "baseline": 3.8293, "skill_level": "pga", "lie": "recovery"}, {"distance": 87, "baseline": 3.8277, "skill_level": "pga", "lie": "recovery"}, {"distance": 88, "baseline": 3.8261, "skill_level": "pga", "lie": "recovery"}, {"distance": 89, "baseline": 3.8243, "skill_level": "pga", "lie": "recovery"}, {"distance": 90, "baseline": 3.8225, "skill_level": "pga", "lie": "recovery"}, {"distance": 91, "baseline": 3.8207, "skill_level": "pga", "lie": "recovery"}, {"distance": 92, "baseline": 3.8188, "skill_level": "pga", "lie": "recovery"}, {"distance": 93, "baseline": 3.8169, "skill_level": "pga", "lie": "recovery"}, {"distance": 94, "baseline": 3.8149, "skill_level": "pga", "lie": "recovery"}, {"distance": 95, "baseline": 3.8129, "skill_level": "pga", "lie": "recovery"}, {"distance": 96, "baseline": 3.8109, "skill_level": "pga", "lie": "recovery"}, {"distance": 97, "baseline": 3.8089, "skill_level": "pga", "lie": "recovery"}, {"distance": 98, "baseline": 3.8069, "skill_level": "pga", "lie": "recovery"}, {"distance": 99, "baseline": 3.8049, "skill_level": "pga", "lie": "recovery"}, {"distance": 100, "baseline": 3.8029, "skill_level": "pga", "lie": "recovery"}, {"distance": 101, "baseline": 3.801, "skill_level": "pga", "lie": "recovery"}, {"distance": 102, "baseline": 3.7991, "skill_level": "pga", "lie": "recovery"}, {"distance": 103, "baseline": 3.7972, "skill_level": "pga", "lie": "recovery"}, {"distance": 104, "baseline": 3.7954, "skill_level": "pga", "lie": "recovery"}, {"distance": 105, "baseline": 3.7936, "skill_level": "pga", "lie": "recovery"}, {"distance": 106, "baseline": 3.7919, "skill_level": "pga", "lie": "recovery"}, {"distance": 107, "baseline": 3.7903, "skill_level": "pga", "lie": "recovery"}, {"distance": 108, "baseline": 3.7887, "skill_level": "pga", "lie": "recovery"}, {"distance": 109, "baseline": 3.7873, "skill_level": "pga", "lie": "recovery"}, {"distance": 110, "baseline": 3.7859, "skill_level": "pga", "lie": "recovery"}, {"distance": 111, "baseline": 3.7847, "skill_level": "pga", "lie": "recovery"}, {"distance": 112, "baseline": 3.7835, "skill_level": "pga", "lie": "recovery"}, {"distance": 113, "baseline": 3.7825, "skill_level": "pga", "lie": "recovery"}, {"distance": 114, "baseline": 3.7816, "skill_level": "pga", "lie": "recovery"}, {"distance": 115, "baseline": 3.7809, "skill_level": "pga", "lie": "recovery"}, {"distance": 116, "baseline": 3.7803, "skill_level": "pga", "lie": "recovery"}, {"distance": 117, "baseline": 3.7798, "skill_level": "pga", "lie": "recovery"}, {"distance": 118, "baseline": 3.7795, "skill_level": "pga", "lie": "recovery"}, {"distance": 119, "baseline": 3.7793, "skill_level": "pga", "lie": "recovery"}, {"distance": 120, "baseline": 3.7792, "skill_level": "pga", "lie": "recovery"}, {"distance": 121, "baseline": 3.7794, "skill_level": "pga", "lie": "recovery"}, {"distance": 122, "baseline": 3.7796, "skill_level": "pga", "lie": "recovery"}, {"distance": 123, "baseline": 3.7801, "skill_level": "pga", "lie": "recovery"}, {"distance": 124, "baseline": 3.7806, "skill_level": "pga", "lie": "recovery"}, {"distance": 125, "baseline": 3.7814, "skill_level": "pga", "lie": "recovery"}, {"distance": 126, "baseline": 3.7822, "skill_level": "pga", "lie": "recovery"}, {"distance": 127, "baseline": 3.7832, "skill_level": "pga", "lie": "recovery"}, {"distance": 128, "baseline": 3.7844, "skill_level": "pga", "lie": "recovery"}, {"distance": 129, "baseline": 3.7856, "skill_level": "pga", "lie": "recovery"}, {"distance": 130, "baseline": 3.787, "skill_level": "pga", "lie": "recovery"}, {"distance": 131, "baseline": 3.7884, "skill_level": "pga", "lie": "recovery"}, {"distance": 132, "baseline": 3.7899, "skill_level": "pga", "lie": "recovery"}, {"distance": 133, "baseline": 3.7915, "skill_level": "pga", "lie": "recovery"}, {"distance": 134, "baseline": 3.7932, "skill_level": "pga", "lie": "recovery"}, {"distance": 135, "baseline": 3.7949, "skill_level": "pga", "lie": "recovery"}, {"distance": 136, "baseline": 3.7965, "skill_level": "pga", "lie": "recovery"}, {"distance": 137, "baseline": 3.7982, "skill_level": "pga", "lie": "recovery"}, {"distance": 138, "baseline": 3.7998, "skill_level": "pga", "lie": "recovery"}, {"distance": 139, "baseline": 3.8013, "skill_level": "pga", "lie": "recovery"}, {"distance": 140, "baseline": 3.8027, "skill_level": "pga", "lie": "recovery"}, {"distance": 141, "baseline": 3.804, "skill_level": "pga", "lie": "recovery"}, {"distance": 142, "baseline": 3.805, "skill_level": "pga", "lie": "recovery"}, {"distance": 143, "baseline": 3.8058, "skill_level": "pga", "lie": "recovery"}, {"distance": 144, "baseline": 3.8064, "skill_level": "pga", "lie": "recovery"}, {"distance": 145, "baseline": 3.8066, "skill_level": "pga", "lie": "recovery"}, {"distance": 146, "baseline": 3.8064, "skill_level": "pga", "lie": "recovery"}, {"distance": 147, "baseline": 3.8058, "skill_level": "pga", "lie": "recovery"}, {"distance": 148, "baseline": 3.8046, "skill_level": "pga", "lie": "recovery"}, {"distance": 149, "baseline": 3.8029, "skill_level": "pga", "lie": "recovery"}, {"distance": 150, "baseline": 3.8006, "skill_level": "pga", "lie": "recovery"}, {"distance": 151, "baseline": 3.7985, "skill_level": "pga", "lie": "recovery"}, {"distance": 152, "baseline": 3.7993, "skill_level": "pga", "lie": "recovery"}, {"distance": 153, "baseline": 3.8001, "skill_level": "pga", "lie": "recovery"}, {"distance": 154, "baseline": 3.8009, "skill_level": "pga", "lie": "recovery"}, {"distance": 155, "baseline": 3.8016, "skill_level": "pga", "lie": "recovery"}, {"distance": 156, "baseline": 3.8024, "skill_level": "pga", "lie": "recovery"}, {"distance": 157, "baseline": 3.8032, "skill_level": "pga", "lie": "recovery"}, {"distance": 158, "baseline": 3.804, "skill_level": "pga", "lie": "recovery"}, {"distance": 159, "baseline": 3.8048, "skill_level": "pga", "lie": "recovery"}, {"distance": 160, "baseline": 3.8056, "skill_level": "pga", "lie": "recovery"}, {"distance": 161, "baseline": 3.8063, "skill_level": "pga", "lie": "recovery"}, {"distance": 162, "baseline": 3.8071, "skill_level": "pga", "lie": "recovery"}, {"distance": 163, "baseline": 3.8079, "skill_level": "pga", "lie": "recovery"}, {"distance": 164, "baseline": 3.8087, "skill_level": "pga", "lie": "recovery"}, {"distance": 165, "baseline": 3.8095, "skill_level": "pga", "lie": "recovery"}, {"distance": 166, "baseline": 3.8103, "skill_level": "pga", "lie": "recovery"}, {"distance": 167, "baseline": 3.811, "skill_level": "pga", "lie": "recovery"}, {"distance": 168, "baseline": 3.8118, "skill_level": "pga", "lie": "recovery"}, {"distance": 169, "baseline": 3.8126, "skill_level": "pga", "lie": "recovery"}, {"distance": 170, "baseline": 3.8134, "skill_level": "pga", "lie": "recovery"}, {"distance": 171, "baseline": 3.8139, "skill_level": "pga", "lie": "recovery"}, {"distance": 172, "baseline": 3.8144, "skill_level": "pga", "lie": "recovery"}, {"distance": 173, "baseline": 3.8152, "skill_level": "pga", "lie": "recovery"}, {"distance": 174, "baseline": 3.816, "skill_level": "pga", "lie": "recovery"}, {"distance": 175, "baseline": 3.8171, "skill_level": "pga", "lie": "recovery"}, {"distance": 176, "baseline": 3.8182, "skill_level": "pga", "lie": "recovery"}, {"distance": 177, "baseline": 3.8195, "skill_level": "pga", "lie": "recovery"}, {"distance": 178, "baseline": 3.8209, "skill_level": "pga", "lie": "recovery"}, {"distance": 179, "baseline": 3.8225, "skill_level": "pga", "lie": "recovery"}, {"distance": 180, "baseline": 3.8241, "skill_level": "pga", "lie": "recovery"}, {"distance": 181, "baseline": 3.8258, "skill_level": "pga", "lie": "recovery"}, {"distance": 182, "baseline": 3.8276, "skill_level": "pga", "lie": "recovery"}, {"distance": 183, "baseline": 3.8295, "skill_level": "pga", "lie": "recovery"}, {"distance": 184, "baseline": 3.8315, "skill_level": "pga", "lie": "recovery"}, {"distance": 185, "baseline": 3.8335, "skill_level": "pga", "lie": "recovery"}, {"distance": 186, "baseline": 3.8356, "skill_level": "pga", "lie": "recovery"}, {"distance": 187, "baseline": 3.8377, "skill_level": "pga", "lie": "recovery"}, {"distance": 188, "baseline": 3.8399, "skill_level": "pga", "lie": "recovery"}, {"distance": 189, "baseline": 3.8422, "skill_level": "pga", "lie": "recovery"}, {"distance": 190, "baseline": 3.8445, "skill_level": "pga", "lie": "recovery"}, {"distance": 191, "baseline": 3.8468, "skill_level": "pga", "lie": "recovery"}, {"distance": 192, "baseline": 3.8492, "skill_level": "pga", "lie": "recovery"}, {"distance": 193, "baseline": 3.8516, "skill_level": "pga", "lie": "recovery"}, {"distance": 194, "baseline": 3.854, "skill_level": "pga", "lie": "recovery"}, {"distance": 195, "baseline": 3.8565, "skill_level": "pga", "lie": "recovery"}, {"distance": 196, "baseline": 3.859, "skill_level": "pga", "lie": "recovery"}, {"distance": 197, "baseline": 3.8615, "skill_level": "pga", "lie": "recovery"}, {"distance": 198, "baseline": 3.864, "skill_level": "pga", "lie": "recovery"}, {"distance": 199, "baseline": 3.8665, "skill_level": "pga", "lie": "recovery"}, {"distance": 200, "baseline": 3.869, "skill_level": "pga", "lie": "recovery"}, {"distance": 201, "baseline": 3.8716, "skill_level": "pga", "lie": "recovery"}, {"distance": 202, "baseline": 3.8741, "skill_level": "pga", "lie": "recovery"}, {"distance": 203, "baseline": 3.8767, "skill_level": "pga", "lie": "recovery"}, {"distance": 204, "baseline": 3.8792, "skill_level": "pga", "lie": "recovery"}, {"distance": 205, "baseline": 3.8818, "skill_level": "pga", "lie": "recovery"}, {"distance": 206, "baseline": 3.8843, "skill_level": "pga", "lie": "recovery"}, {"distance": 207, "baseline": 3.8868, "skill_level": "pga", "lie": "recovery"}, {"distance": 208, "baseline": 3.8894, "skill_level": "pga", "lie": "recovery"}, {"distance": 209, "baseline": 3.8919, "skill_level": "pga", "lie": "recovery"}, {"distance": 210, "baseline": 3.8945, "skill_level": "pga", "lie": "recovery"}, {"distance": 211, "baseline": 3.897, "skill_level": "pga", "lie": "recovery"}, {"distance": 212, "baseline": 3.8995, "skill_level": "pga", "lie": "recovery"}, {"distance": 213, "baseline": 3.902, "skill_level": "pga", "lie": "recovery"}, {"distance": 214, "baseline": 3.9045, "skill_level": "pga", "lie": "recovery"}, {"distance": 215, "baseline": 3.907, "skill_level": "pga", "lie": "recovery"}, {"distance": 216, "baseline": 3.9095, "skill_level": "pga", "lie": "recovery"}, {"distance": 217, "baseline": 3.912, "skill_level": "pga", "lie": "recovery"}, {"distance": 218, "baseline": 3.9145, "skill_level": "pga", "lie": "recovery"}, {"distance": 219, "baseline": 3.917, "skill_level": "pga", "lie": "recovery"}, {"distance": 220, "baseline": 3.9195, "skill_level": "pga", "lie": "recovery"}, {"distance": 221, "baseline": 3.9219, "skill_level": "pga", "lie": "recovery"}, {"distance": 222, "baseline": 3.9244, "skill_level": "pga", "lie": "recovery"}, {"distance": 223, "baseline": 3.9269, "skill_level": "pga", "lie": "recovery"}, {"distance": 224, "baseline": 3.9293, "skill_level": "pga", "lie": "recovery"}, {"distance": 225, "baseline": 3.9318, "skill_level": "pga", "lie": "recovery"}, {"distance": 226, "baseline": 3.9342, "skill_level": "pga", "lie": "recovery"}, {"distance": 227, "baseline": 3.9367, "skill_level": "pga", "lie": "recovery"}, {"distance": 228, "baseline": 3.9391, "skill_level": "pga", "lie": "recovery"}, {"distance": 229, "baseline": 3.9416, "skill_level": "pga", "lie": "recovery"}, {"distance": 230, "baseline": 3.9441, "skill_level": "pga", "lie": "recovery"}, {"distance": 231, "baseline": 3.9465, "skill_level": "pga", "lie": "recovery"}, {"distance": 232, "baseline": 3.949, "skill_level": "pga", "lie": "recovery"}, {"distance": 233, "baseline": 3.9515, "skill_level": "pga", "lie": "recovery"}, {"distance": 234, "baseline": 3.954, "skill_level": "pga", "lie": "recovery"}, {"distance": 235, "baseline": 3.9564, "skill_level": "pga", "lie": "recovery"}, {"distance": 236, "baseline": 3.9589, "skill_level": "pga", "lie": "recovery"}, {"distance": 237, "baseline": 3.9615, "skill_level": "pga", "lie": "recovery"}, {"distance": 238, "baseline": 3.964, "skill_level": "pga", "lie": "recovery"}, {"distance": 239, "baseline": 3.9665, "skill_level": "pga", "lie": "recovery"}, {"distance": 240, "baseline": 3.9691, "skill_level": "pga", "lie": "recovery"}, {"distance": 241, "baseline": 3.9717, "skill_level": "pga", "lie": "recovery"}, {"distance": 242, "baseline": 3.9742, "skill_level": "pga", "lie": "recovery"}, {"distance": 243, "baseline": 3.9769, "skill_level": "pga", "lie": "recovery"}, {"distance": 244, "baseline": 3.9795, "skill_level": "pga", "lie": "recovery"}, {"distance": 245, "baseline": 3.9821, "skill_level": "pga", "lie": "recovery"}, {"distance": 246, "baseline": 3.9848, "skill_level": "pga", "lie": "recovery"}, {"distance": 247, "baseline": 3.9875, "skill_level": "pga", "lie": "recovery"}, {"distance": 248, "baseline": 3.9903, "skill_level": "pga", "lie": "recovery"}, {"distance": 249, "baseline": 3.993, "skill_level": "pga", "lie": "recovery"}, {"distance": 250, "baseline": 3.9958, "skill_level": "pga", "lie": "recovery"}, {"distance": 251, "baseline": 3.9986, "skill_level": "pga", "lie": "recovery"}, {"distance": 252, "baseline": 4.0015, "skill_level": "pga", "lie": "recovery"}, {"distance": 253, "baseline": 4.0044, "skill_level": "pga", "lie": "recovery"}, {"distance": 254, "baseline": 4.0073, "skill_level": "pga", "lie": "recovery"}, {"distance": 255, "baseline": 4.0103, "skill_level": "pga", "lie": "recovery"}, {"distance": 256, "baseline": 4.0133, "skill_level": "pga", "lie": "recovery"}, {"distance": 257, "baseline": 4.0163, "skill_level": "pga", "lie": "recovery"}, {"distance": 258, "baseline": 4.0194, "skill_level": "pga", "lie": "recovery"}, {"distance": 259, "baseline": 4.0225, "skill_level": "pga", "lie": "recovery"}, {"distance": 260, "baseline": 4.0257, "skill_level": "pga", "lie": "recovery"}, {"distance": 261, "baseline": 4.0289, "skill_level": "pga", "lie": "recovery"}, {"distance": 262, "baseline": 4.0322, "skill_level": "pga", "lie": "recovery"}, {"distance": 263, "baseline": 4.0355, "skill_level": "pga", "lie": "recovery"}, {"distance": 264, "baseline": 4.0388, "skill_level": "pga", "lie": "recovery"}, {"distance": 265, "baseline": 4.0422, "skill_level": "pga", "lie": "recovery"}, {"distance": 266, "baseline": 4.0457, "skill_level": "pga", "lie": "recovery"}, {"distance": 267, "baseline": 4.0492, "skill_level": "pga", "lie": "recovery"}, {"distance": 268, "baseline": 4.0527, "skill_level": "pga", "lie": "recovery"}, {"distance": 269, "baseline": 4.0563, "skill_level": "pga", "lie": "recovery"}, {"distance": 270, "baseline": 4.06, "skill_level": "pga", "lie": "recovery"}, {"distance": 271, "baseline": 4.0637, "skill_level": "pga", "lie": "recovery"}, {"distance": 272, "baseline": 4.0674, "skill_level": "pga", "lie": "recovery"}, {"distance": 273, "baseline": 4.0712, "skill_level": "pga", "lie": "recovery"}, {"distance": 274, "baseline": 4.0751, "skill_level": "pga", "lie": "recovery"}, {"distance": 275, "baseline": 4.079, "skill_level": "pga", "lie": "recovery"}, {"distance": 276, "baseline": 4.083, "skill_level": "pga", "lie": "recovery"}, {"distance": 277, "baseline": 4.0871, "skill_level": "pga", "lie": "recovery"}, {"distance": 278, "baseline": 4.0912, "skill_level": "pga", "lie": "recovery"}, {"distance": 279, "baseline": 4.0953, "skill_level": "pga", "lie": "recovery"}, {"distance": 280, "baseline": 4.0995, "skill_level": "pga", "lie": "recovery"}, {"distance": 281, "baseline": 4.1038, "skill_level": "pga", "lie": "recovery"}, {"distance": 282, "baseline": 4.1081, "skill_level": "pga", "lie": "recovery"}, {"distance": 283, "baseline": 4.1125, "skill_level": "pga", "lie": "recovery"}, {"distance": 284, "baseline": 4.1169, "skill_level": "pga", "lie": "recovery"}, {"distance": 285, "baseline": 4.1214, "skill_level": "pga", "lie": "recovery"}, {"distance": 286, "baseline": 4.126, "skill_level": "pga", "lie": "recovery"}, {"distance": 287, "baseline": 4.1306, "skill_level": "pga", "lie": "recovery"}, {"distance": 288, "baseline": 4.1353, "skill_level": "pga", "lie": "recovery"}, {"distance": 289, "baseline": 4.14, "skill_level": "pga", "lie": "recovery"}, {"distance": 290, "baseline": 4.1448, "skill_level": "pga", "lie": "recovery"}, {"distance": 291, "baseline": 4.1496, "skill_level": "pga", "lie": "recovery"}, {"distance": 292, "baseline": 4.1545, "skill_level": "pga", "lie": "recovery"}, {"distance": 293, "baseline": 4.1595, "skill_level": "pga", "lie": "recovery"}, {"distance": 294, "baseline": 4.1645, "skill_level": "pga", "lie": "recovery"}, {"distance": 295, "baseline": 4.1696, "skill_level": "pga", "lie": "recovery"}, {"distance": 296, "baseline": 4.1747, "skill_level": "pga", "lie": "recovery"}, {"distance": 297, "baseline": 4.1799, "skill_level": "pga", "lie": "recovery"}, {"distance": 298, "baseline": 4.1851, "skill_level": "pga", "lie": "recovery"}, {"distance": 299, "baseline": 4.1904, "skill_level": "pga", "lie": "recovery"}, {"distance": 300, "baseline": 4.1957, "skill_level": "pga", "lie": "recovery"}, {"distance": 301, "baseline": 4.2011, "skill_level": "pga", "lie": "recovery"}, {"distance": 302, "baseline": 4.2065, "skill_level": "pga", "lie": "recovery"}, {"distance": 303, "baseline": 4.212, "skill_level": "pga", "lie": "recovery"}, {"distance": 304, "baseline": 4.2175, "skill_level": "pga", "lie": "recovery"}, {"distance": 305, "baseline": 4.223, "skill_level": "pga", "lie": "recovery"}, {"distance": 306, "baseline": 4.2286, "skill_level": "pga", "lie": "recovery"}, {"distance": 307, "baseline": 4.2343, "skill_level": "pga", "lie": "recovery"}, {"distance": 308, "baseline": 4.24, "skill_level": "pga", "lie": "recovery"}, {"distance": 309, "baseline": 4.2457, "skill_level": "pga", "lie": "recovery"}, {"distance": 310, "baseline": 4.2515, "skill_level": "pga", "lie": "recovery"}, {"distance": 311, "baseline": 4.2573, "skill_level": "pga", "lie": "recovery"}, {"distance": 312, "baseline": 4.2632, "skill_level": "pga", "lie": "recovery"}, {"distance": 313, "baseline": 4.2691, "skill_level": "pga", "lie": "recovery"}, {"distance": 314, "baseline": 4.275, "skill_level": "pga", "lie": "recovery"}, {"distance": 315, "baseline": 4.2809, "skill_level": "pga", "lie": "recovery"}, {"distance": 316, "baseline": 4.2869, "skill_level": "pga", "lie": "recovery"}, {"distance": 317, "baseline": 4.2929, "skill_level": "pga", "lie": "recovery"}, {"distance": 318, "baseline": 4.299, "skill_level": "pga", "lie": "recovery"}, {"distance": 319, "baseline": 4.3051, "skill_level": "pga", "lie": "recovery"}, {"distance": 320, "baseline": 4.3112, "skill_level": "pga", "lie": "recovery"}, {"distance": 321, "baseline": 4.3173, "skill_level": "pga", "lie": "recovery"}, {"distance": 322, "baseline": 4.3234, "skill_level": "pga", "lie": "recovery"}, {"distance": 323, "baseline": 4.3296, "skill_level": "pga", "lie": "recovery"}, {"distance": 324, "baseline": 4.3358, "skill_level": "pga", "lie": "recovery"}, {"distance": 325, "baseline": 4.342, "skill_level": "pga", "lie": "recovery"}, {"distance": 326, "baseline": 4.3482, "skill_level": "pga", "lie": "recovery"}, {"distance": 327, "baseline": 4.3545, "skill_level": "pga", "lie": "recovery"}, {"distance": 328, "baseline": 4.3607, "skill_level": "pga", "lie": "recovery"}, {"distance": 329, "baseline": 4.367, "skill_level": "pga", "lie": "recovery"}, {"distance": 330, "baseline": 4.3733, "skill_level": "pga", "lie": "recovery"}, {"distance": 331, "baseline": 4.3795, "skill_level": "pga", "lie": "recovery"}, {"distance": 332, "baseline": 4.3858, "skill_level": "pga", "lie": "recovery"}, {"distance": 333, "baseline": 4.3921, "skill_level": "pga", "lie": "recovery"}, {"distance": 334, "baseline": 4.3984, "skill_level": "pga", "lie": "recovery"}, {"distance": 335, "baseline": 4.4047, "skill_level": "pga", "lie": "recovery"}, {"distance": 336, "baseline": 4.411, "skill_level": "pga", "lie": "recovery"}, {"distance": 337, "baseline": 4.4173, "skill_level": "pga", "lie": "recovery"}, {"distance": 338, "baseline": 4.4237, "skill_level": "pga", "lie": "recovery"}, {"distance": 339, "baseline": 4.43, "skill_level": "pga", "lie": "recovery"}, {"distance": 340, "baseline": 4.4363, "skill_level": "pga", "lie": "recovery"}, {"distance": 341, "baseline": 4.4426, "skill_level": "pga", "lie": "recovery"}, {"distance": 342, "baseline": 4.4488, "skill_level": "pga", "lie": "recovery"}, {"distance": 343, "baseline": 4.4551, "skill_level": "pga", "lie": "recovery"}, {"distance": 344, "baseline": 4.4614, "skill_level": "pga", "lie": "recovery"}, {"distance": 345, "baseline": 4.4677, "skill_level": "pga", "lie": "recovery"}, {"distance": 346, "baseline": 4.4739, "skill_level": "pga", "lie": "recovery"}, {"distance": 347, "baseline": 4.4802, "skill_level": "pga", "lie": "recovery"}, {"distance": 348, "baseline": 4.4864, "skill_level": "pga", "lie": "recovery"}, {"distance": 349, "baseline": 4.4926, "skill_level": "pga", "lie": "recovery"}, {"distance": 350, "baseline": 4.4989, "skill_level": "pga", "lie": "recovery"}, {"distance": 351, "baseline": 4.5051, "skill_level": "pga", "lie": "recovery"}, {"distance": 352, "baseline": 4.5112, "skill_level": "pga", "lie": "recovery"}, {"distance": 353, "baseline": 4.5174, "skill_level": "pga", "lie": "recovery"}, {"distance": 354, "baseline": 4.5236, "skill_level": "pga", "lie": "recovery"}, {"distance": 355, "baseline": 4.5297, "skill_level": "pga", "lie": "recovery"}, {"distance": 356, "baseline": 4.5359, "skill_level": "pga", "lie": "recovery"}, {"distance": 357, "baseline": 4.542, "skill_level": "pga", "lie": "recovery"}, {"distance": 358, "baseline": 4.5481, "skill_level": "pga", "lie": "recovery"}, {"distance": 359, "baseline": 4.5542, "skill_level": "pga", "lie": "recovery"}, {"distance": 360, "baseline": 4.5603, "skill_level": "pga", "lie": "recovery"}, {"distance": 361, "baseline": 4.5652, "skill_level": "pga", "lie": "recovery"}, {"distance": 362, "baseline": 4.57, "skill_level": "pga", "lie": "recovery"}, {"distance": 363, "baseline": 4.5747, "skill_level": "pga", "lie": "recovery"}, {"distance": 364, "baseline": 4.5794, "skill_level": "pga", "lie": "recovery"}, {"distance": 365, "baseline": 4.5841, "skill_level": "pga", "lie": "recovery"}, {"distance": 366, "baseline": 4.5889, "skill_level": "pga", "lie": "recovery"}, {"distance": 367, "baseline": 4.5936, "skill_level": "pga", "lie": "recovery"}, {"distance": 368, "baseline": 4.5983, "skill_level": "pga", "lie": "recovery"}, {"distance": 369, "baseline": 4.6031, "skill_level": "pga", "lie": "recovery"}, {"distance": 370, "baseline": 4.6078, "skill_level": "pga", "lie": "recovery"}, {"distance": 371, "baseline": 4.6125, "skill_level": "pga", "lie": "recovery"}, {"distance": 372, "baseline": 4.6172, "skill_level": "pga", "lie": "recovery"}, {"distance": 373, "baseline": 4.622, "skill_level": "pga", "lie": "recovery"}, {"distance": 374, "baseline": 4.6267, "skill_level": "pga", "lie": "recovery"}, {"distance": 375, "baseline": 4.6314, "skill_level": "pga", "lie": "recovery"}, {"distance": 376, "baseline": 4.6362, "skill_level": "pga", "lie": "recovery"}, {"distance": 377, "baseline": 4.6409, "skill_level": "pga", "lie": "recovery"}, {"distance": 378, "baseline": 4.6456, "skill_level": "pga", "lie": "recovery"}, {"distance": 379, "baseline": 4.6503, "skill_level": "pga", "lie": "recovery"}, {"distance": 380, "baseline": 4.6551, "skill_level": "pga", "lie": "recovery"}, {"distance": 381, "baseline": 4.6598, "skill_level": "pga", "lie": "recovery"}, {"distance": 382, "baseline": 4.6645, "skill_level": "pga", "lie": "recovery"}, {"distance": 383, "baseline": 4.6693, "skill_level": "pga", "lie": "recovery"}, {"distance": 384, "baseline": 4.674, "skill_level": "pga", "lie": "recovery"}, {"distance": 385, "baseline": 4.6787, "skill_level": "pga", "lie": "recovery"}, {"distance": 386, "baseline": 4.6834, "skill_level": "pga", "lie": "recovery"}, {"distance": 387, "baseline": 4.6882, "skill_level": "pga", "lie": "recovery"}, {"distance": 388, "baseline": 4.6929, "skill_level": "pga", "lie": "recovery"}, {"distance": 389, "baseline": 4.6976, "skill_level": "pga", "lie": "recovery"}, {"distance": 390, "baseline": 4.7024, "skill_level": "pga", "lie": "recovery"}, {"distance": 391, "baseline": 4.7071, "skill_level": "pga", "lie": "recovery"}, {"distance": 392, "baseline": 4.7118, "skill_level": "pga", "lie": "recovery"}, {"distance": 393, "baseline": 4.7165, "skill_level": "pga", "lie": "recovery"}, {"distance": 394, "baseline": 4.7213, "skill_level": "pga", "lie": "recovery"}, {"distance": 395, "baseline": 4.726, "skill_level": "pga", "lie": "recovery"}, {"distance": 396, "baseline": 4.7307, "skill_level": "pga", "lie": "recovery"}, {"distance": 397, "baseline": 4.7355, "skill_level": "pga", "lie": "recovery"}, {"distance": 398, "baseline": 4.7402, "skill_level": "pga", "lie": "recovery"}, {"distance": 399, "baseline": 4.7449, "skill_level": "pga", "lie": "recovery"}, {"distance": 400, "baseline": 4.7496, "skill_level": "pga", "lie": "recovery"}, {"distance": 401, "baseline": 4.7544, "skill_level": "pga", "lie": "recovery"}, {"distance": 402, "baseline": 4.7591, "skill_level": "pga", "lie": "recovery"}, {"distance": 403, "baseline": 4.7638, "skill_level": "pga", "lie": "recovery"}, {"distance": 404, "baseline": 4.7686, "skill_level": "pga", "lie": "recovery"}, {"distance": 405, "baseline": 4.7733, "skill_level": "pga", "lie": "recovery"}, {"distance": 406, "baseline": 4.778, "skill_level": "pga", "lie": "recovery"}, {"distance": 407, "baseline": 4.7827, "skill_level": "pga", "lie": "recovery"}, {"distance": 408, "baseline": 4.7875, "skill_level": "pga", "lie": "recovery"}, {"distance": 409, "baseline": 4.7922, "skill_level": "pga", "lie": "recovery"}, {"distance": 410, "baseline": 4.7969, "skill_level": "pga", "lie": "recovery"}, {"distance": 411, "baseline": 4.8017, "skill_level": "pga", "lie": "recovery"}, {"distance": 412, "baseline": 4.8064, "skill_level": "pga", "lie": "recovery"}, {"distance": 413, "baseline": 4.8111, "skill_level": "pga", "lie": "recovery"}, {"distance": 414, "baseline": 4.8158, "skill_level": "pga", "lie": "recovery"}, {"distance": 415, "baseline": 4.8206, "skill_level": "pga", "lie": "recovery"}, {"distance": 416, "baseline": 4.8253, "skill_level": "pga", "lie": "recovery"}, {"distance": 417, "baseline": 4.83, "skill_level": "pga", "lie": "recovery"}, {"distance": 418, "baseline": 4.8348, "skill_level": "pga", "lie": "recovery"}, {"distance": 419, "baseline": 4.8395, "skill_level": "pga", "lie": "recovery"}, {"distance": 420, "baseline": 4.8442, "skill_level": "pga", "lie": "recovery"}, {"distance": 421, "baseline": 4.8489, "skill_level": "pga", "lie": "recovery"}, {"distance": 422, "baseline": 4.8537, "skill_level": "pga", "lie": "recovery"}, {"distance": 423, "baseline": 4.8584, "skill_level": "pga", "lie": "recovery"}, {"distance": 424, "baseline": 4.8631, "skill_level": "pga", "lie": "recovery"}, {"distance": 425, "baseline": 4.8679, "skill_level": "pga", "lie": "recovery"}, {"distance": 426, "baseline": 4.8726, "skill_level": "pga", "lie": "recovery"}, {"distance": 427, "baseline": 4.8773, "skill_level": "pga", "lie": "recovery"}, {"distance": 428, "baseline": 4.882, "skill_level": "pga", "lie": "recovery"}, {"distance": 429, "baseline": 4.8868, "skill_level": "pga", "lie": "recovery"}, {"distance": 430, "baseline": 4.8915, "skill_level": "pga", "lie": "recovery"}, {"distance": 431, "baseline": 4.8962, "skill_level": "pga", "lie": "recovery"}, {"distance": 432, "baseline": 4.901, "skill_level": "pga", "lie": "recovery"}, {"distance": 433, "baseline": 4.9057, "skill_level": "pga", "lie": "recovery"}, {"distance": 434, "baseline": 4.9104, "skill_level": "pga", "lie": "recovery"}, {"distance": 435, "baseline": 4.9151, "skill_level": "pga", "lie": "recovery"}, {"distance": 436, "baseline": 4.9199, "skill_level": "pga", "lie": "recovery"}, {"distance": 437, "baseline": 4.9246, "skill_level": "pga", "lie": "recovery"}, {"distance": 438, "baseline": 4.9293, "skill_level": "pga", "lie": "recovery"}, {"distance": 439, "baseline": 4.9341, "skill_level": "pga", "lie": "recovery"}, {"distance": 440, "baseline": 4.9388, "skill_level": "pga", "lie": "recovery"}, {"distance": 441, "baseline": 4.9435, "skill_level": "pga", "lie": "recovery"}, {"distance": 442, "baseline": 4.9482, "skill_level": "pga", "lie": "recovery"}, {"distance": 443, "baseline": 4.953, "skill_level": "pga", "lie": "recovery"}, {"distance": 444, "baseline": 4.9577, "skill_level": "pga", "lie": "recovery"}, {"distance": 445, "baseline": 4.9624, "skill_level": "pga", "lie": "recovery"}, {"distance": 446, "baseline": 4.9672, "skill_level": "pga", "lie": "recovery"}, {"distance": 447, "baseline": 4.9719, "skill_level": "pga", "lie": "recovery"}, {"distance": 448, "baseline": 4.9766, "skill_level": "pga", "lie": "recovery"}, {"distance": 449, "baseline": 4.9813, "skill_level": "pga", "lie": "recovery"}, {"distance": 450, "baseline": 4.9861, "skill_level": "pga", "lie": "recovery"}, {"distance": 451, "baseline": 4.9908, "skill_level": "pga", "lie": "recovery"}, {"distance": 452, "baseline": 4.9955, "skill_level": "pga", "lie": "recovery"}, {"distance": 453, "baseline": 5.0003, "skill_level": "pga", "lie": "recovery"}, {"distance": 454, "baseline": 5.005, "skill_level": "pga", "lie": "recovery"}, {"distance": 455, "baseline": 5.0097, "skill_level": "pga", "lie": "recovery"}, {"distance": 456, "baseline": 5.0144, "skill_level": "pga", "lie": "recovery"}, {"distance": 457, "baseline": 5.0192, "skill_level": "pga", "lie": "recovery"}, {"distance": 458, "baseline": 5.0239, "skill_level": "pga", "lie": "recovery"}, {"distance": 459, "baseline": 5.0286, "skill_level": "pga", "lie": "recovery"}, {"distance": 460, "baseline": 5.0334, "skill_level": "pga", "lie": "recovery"}, {"distance": 461, "baseline": 5.0381, "skill_level": "pga", "lie": "recovery"}, {"distance": 462, "baseline": 5.0428, "skill_level": "pga", "lie": "recovery"}, {"distance": 463, "baseline": 5.0475, "skill_level": "pga", "lie": "recovery"}, {"distance": 464, "baseline": 5.0523, "skill_level": "pga", "lie": "recovery"}, {"distance": 465, "baseline": 5.057, "skill_level": "pga", "lie": "recovery"}, {"distance": 466, "baseline": 5.0617, "skill_level": "pga", "lie": "recovery"}, {"distance": 467, "baseline": 5.0665, "skill_level": "pga", "lie": "recovery"}, {"distance": 468, "baseline": 5.0712, "skill_level": "pga", "lie": "recovery"}, {"distance": 469, "baseline": 5.0759, "skill_level": "pga", "lie": "recovery"}, {"distance": 470, "baseline": 5.0806, "skill_level": "pga", "lie": "recovery"}, {"distance": 471, "baseline": 5.0854, "skill_level": "pga", "lie": "recovery"}, {"distance": 472, "baseline": 5.0901, "skill_level": "pga", "lie": "recovery"}, {"distance": 473, "baseline": 5.0948, "skill_level": "pga", "lie": "recovery"}, {"distance": 474, "baseline": 5.0996, "skill_level": "pga", "lie": "recovery"}, {"distance": 475, "baseline": 5.1043, "skill_level": "pga", "lie": "recovery"}, {"distance": 476, "baseline": 5.109, "skill_level": "pga", "lie": "recovery"}, {"distance": 477, "baseline": 5.1137, "skill_level": "pga", "lie": "recovery"}, {"distance": 478, "baseline": 5.1185, "skill_level": "pga", "lie": "recovery"}, {"distance": 479, "baseline": 5.1232, "skill_level": "pga", "lie": "recovery"}, {"distance": 480, "baseline": 5.1279, "skill_level": "pga", "lie": "recovery"}, {"distance": 481, "baseline": 5.1327, "skill_level": "pga", "lie": "recovery"}, {"distance": 482, "baseline": 5.1374, "skill_level": "pga", "lie": "recovery"}, {"distance": 483, "baseline": 5.1421, "skill_level": "pga", "lie": "recovery"}, {"distance": 484, "baseline": 5.1468, "skill_level": "pga", "lie": "recovery"}, {"distance": 485, "baseline": 5.1516, "skill_level": "pga", "lie": "recovery"}, {"distance": 486, "baseline": 5.1563, "skill_level": "pga", "lie": "recovery"}, {"distance": 487, "baseline": 5.161, "skill_level": "pga", "lie": "recovery"}, {"distance": 488, "baseline": 5.1658, "skill_level": "pga", "lie": "recovery"}, {"distance": 489, "baseline": 5.1705, "skill_level": "pga", "lie": "recovery"}, {"distance": 490, "baseline": 5.1752, "skill_level": "pga", "lie": "recovery"}, {"distance": 491, "baseline": 5.1799, "skill_level": "pga", "lie": "recovery"}, {"distance": 492, "baseline": 5.1847, "skill_level": "pga", "lie": "recovery"}, {"distance": 493, "baseline": 5.1894, "skill_level": "pga", "lie": "recovery"}, {"distance": 494, "baseline": 5.1941, "skill_level": "pga", "lie": "recovery"}, {"distance": 495, "baseline": 5.1989, "skill_level": "pga", "lie": "recovery"}, {"distance": 496, "baseline": 5.2036, "skill_level": "pga", "lie": "recovery"}, {"distance": 497, "baseline": 5.2083, "skill_level": "pga", "lie": "recovery"}, {"distance": 498, "baseline": 5.213, "skill_level": "pga", "lie": "recovery"}, {"distance": 499, "baseline": 5.2178, "skill_level": "pga", "lie": "recovery"}, {"distance": 500, "baseline": 5.2225, "skill_level": "pga", "lie": "recovery"}, {"distance": 501, "baseline": 5.2272, "skill_level": "pga", "lie": "recovery"}, {"distance": 502, "baseline": 5.232, "skill_level": "pga", "lie": "recovery"}, {"distance": 503, "baseline": 5.2367, "skill_level": "pga", "lie": "recovery"}, {"distance": 504, "baseline": 5.2414, "skill_level": "pga", "lie": "recovery"}, {"distance": 505, "baseline": 5.2461, "skill_level": "pga", "lie": "recovery"}, {"distance": 506, "baseline": 5.2509, "skill_level": "pga", "lie": "recovery"}, {"distance": 507, "baseline": 5.2556, "skill_level": "pga", "lie": "recovery"}, {"distance": 508, "baseline": 5.2603, "skill_level": "pga", "lie": "recovery"}, {"distance": 509, "baseline": 5.2651, "skill_level": "pga", "lie": "recovery"}, {"distance": 510, "baseline": 5.2698, "skill_level": "pga", "lie": "recovery"}, {"distance": 511, "baseline": 5.2745, "skill_level": "pga", "lie": "recovery"}, {"distance": 512, "baseline": 5.2792, "skill_level": "pga", "lie": "recovery"}, {"distance": 513, "baseline": 5.284, "skill_level": "pga", "lie": "recovery"}, {"distance": 514, "baseline": 5.2887, "skill_level": "pga", "lie": "recovery"}, {"distance": 515, "baseline": 5.2934, "skill_level": "pga", "lie": "recovery"}, {"distance": 516, "baseline": 5.2982, "skill_level": "pga", "lie": "recovery"}, {"distance": 517, "baseline": 5.3029, "skill_level": "pga", "lie": "recovery"}, {"distance": 518, "baseline": 5.3076, "skill_level": "pga", "lie": "recovery"}, {"distance": 519, "baseline": 5.3123, "skill_level": "pga", "lie": "recovery"}, {"distance": 520, "baseline": 5.3171, "skill_level": "pga", "lie": "recovery"}, {"distance": 521, "baseline": 5.3218, "skill_level": "pga", "lie": "recovery"}, {"distance": 522, "baseline": 5.3265, "skill_level": "pga", "lie": "recovery"}, {"distance": 523, "baseline": 5.3313, "skill_level": "pga", "lie": "recovery"}, {"distance": 524, "baseline": 5.336, "skill_level": "pga", "lie": "recovery"}, {"distance": 525, "baseline": 5.3407, "skill_level": "pga", "lie": "recovery"}, {"distance": 526, "baseline": 5.3454, "skill_level": "pga", "lie": "recovery"}, {"distance": 527, "baseline": 5.3502, "skill_level": "pga", "lie": "recovery"}, {"distance": 528, "baseline": 5.3549, "skill_level": "pga", "lie": "recovery"}, {"distance": 529, "baseline": 5.3596, "skill_level": "pga", "lie": "recovery"}, {"distance": 530, "baseline": 5.3644, "skill_level": "pga", "lie": "recovery"}, {"distance": 531, "baseline": 5.3691, "skill_level": "pga", "lie": "recovery"}, {"distance": 532, "baseline": 5.3738, "skill_level": "pga", "lie": "recovery"}, {"distance": 533, "baseline": 5.3785, "skill_level": "pga", "lie": "recovery"}, {"distance": 534, "baseline": 5.3833, "skill_level": "pga", "lie": "recovery"}, {"distance": 535, "baseline": 5.388, "skill_level": "pga", "lie": "recovery"}, {"distance": 536, "baseline": 5.3927, "skill_level": "pga", "lie": "recovery"}, {"distance": 537, "baseline": 5.3975, "skill_level": "pga", "lie": "recovery"}, {"distance": 538, "baseline": 5.4022, "skill_level": "pga", "lie": "recovery"}, {"distance": 539, "baseline": 5.4069, "skill_level": "pga", "lie": "recovery"}, {"distance": 540, "baseline": 5.4116, "skill_level": "pga", "lie": "recovery"}, {"distance": 541, "baseline": 5.4164, "skill_level": "pga", "lie": "recovery"}, {"distance": 542, "baseline": 5.4211, "skill_level": "pga", "lie": "recovery"}, {"distance": 543, "baseline": 5.4258, "skill_level": "pga", "lie": "recovery"}, {"distance": 544, "baseline": 5.4306, "skill_level": "pga", "lie": "recovery"}, {"distance": 545, "baseline": 5.4353, "skill_level": "pga", "lie": "recovery"}, {"distance": 546, "baseline": 5.44, "skill_level": "pga", "lie": "recovery"}, {"distance": 547, "baseline": 5.4447, "skill_level": "pga", "lie": "recovery"}, {"distance": 548, "baseline": 5.4495, "skill_level": "pga", "lie": "recovery"}, {"distance": 549, "baseline": 5.4542, "skill_level": "pga", "lie": "recovery"}, {"distance": 550, "baseline": 5.4589, "skill_level": "pga", "lie": "recovery"}, {"distance": 551, "baseline": 5.4637, "skill_level": "pga", "lie": "recovery"}, {"distance": 552, "baseline": 5.4684, "skill_level": "pga", "lie": "recovery"}, {"distance": 553, "baseline": 5.4731, "skill_level": "pga", "lie": "recovery"}, {"distance": 554, "baseline": 5.4778, "skill_level": "pga", "lie": "recovery"}, {"distance": 555, "baseline": 5.4826, "skill_level": "pga", "lie": "recovery"}, {"distance": 556, "baseline": 5.4873, "skill_level": "pga", "lie": "recovery"}, {"distance": 557, "baseline": 5.492, "skill_level": "pga", "lie": "recovery"}, {"distance": 558, "baseline": 5.4968, "skill_level": "pga", "lie": "recovery"}, {"distance": 559, "baseline": 5.5015, "skill_level": "pga", "lie": "recovery"}, {"distance": 560, "baseline": 5.5062, "skill_level": "pga", "lie": "recovery"}, {"distance": 561, "baseline": 5.5109, "skill_level": "pga", "lie": "recovery"}, {"distance": 562, "baseline": 5.5157, "skill_level": "pga", "lie": "recovery"}, {"distance": 563, "baseline": 5.5204, "skill_level": "pga", "lie": "recovery"}, {"distance": 564, "baseline": 5.5251, "skill_level": "pga", "lie": "recovery"}, {"distance": 565, "baseline": 5.5299, "skill_level": "pga", "lie": "recovery"}, {"distance": 566, "baseline": 5.5346, "skill_level": "pga", "lie": "recovery"}, {"distance": 567, "baseline": 5.5393, "skill_level": "pga", "lie": "recovery"}, {"distance": 568, "baseline": 5.544, "skill_level": "pga", "lie": "recovery"}, {"distance": 569, "baseline": 5.5488, "skill_level": "pga", "lie": "recovery"}, {"distance": 570, "baseline": 5.5535, "skill_level": "pga", "lie": "recovery"}, {"distance": 571, "baseline": 5.5582, "skill_level": "pga", "lie": "recovery"}, {"distance": 572, "baseline": 5.563, "skill_level": "pga", "lie": "recovery"}, {"distance": 573, "baseline": 5.5677, "skill_level": "pga", "lie": "recovery"}, {"distance": 574, "baseline": 5.5724, "skill_level": "pga", "lie": "recovery"}, {"distance": 575, "baseline": 5.5771, "skill_level": "pga", "lie": "recovery"}, {"distance": 576, "baseline": 5.5819, "skill_level": "pga", "lie": "recovery"}, {"distance": 577, "baseline": 5.5866, "skill_level": "pga", "lie": "recovery"}, {"distance": 578, "baseline": 5.5913, "skill_level": "pga", "lie": "recovery"}, {"distance": 579, "baseline": 5.5961, "skill_level": "pga", "lie": "recovery"}, {"distance": 580, "baseline": 5.6008, "skill_level": "pga", "lie": "recovery"}, {"distance": 581, "baseline": 5.6055, "skill_level": "pga", "lie": "recovery"}, {"distance": 582, "baseline": 5.6102, "skill_level": "pga", "lie": "recovery"}, {"distance": 583, "baseline": 5.615, "skill_level": "pga", "lie": "recovery"}, {"distance": 584, "baseline": 5.6197, "skill_level": "pga", "lie": "recovery"}, {"distance": 585, "baseline": 5.6244, "skill_level": "pga", "lie": "recovery"}, {"distance": 586, "baseline": 5.6292, "skill_level": "pga", "lie": "recovery"}, {"distance": 587, "baseline": 5.6339, "skill_level": "pga", "lie": "recovery"}, {"distance": 588, "baseline": 5.6386, "skill_level": "pga", "lie": "recovery"}, {"distance": 589, "baseline": 5.6433, "skill_level": "pga", "lie": "recovery"}, {"distance": 590, "baseline": 5.6481, "skill_level": "pga", "lie": "recovery"}, {"distance": 591, "baseline": 5.6528, "skill_level": "pga", "lie": "recovery"}, {"distance": 592, "baseline": 5.6575, "skill_level": "pga", "lie": "recovery"}, {"distance": 593, "baseline": 5.6623, "skill_level": "pga", "lie": "recovery"}, {"distance": 594, "baseline": 5.667, "skill_level": "pga", "lie": "recovery"}, {"distance": 595, "baseline": 5.6717, "skill_level": "pga", "lie": "recovery"}, {"distance": 596, "baseline": 5.6764, "skill_level": "pga", "lie": "recovery"}, {"distance": 597, "baseline": 5.6812, "skill_level": "pga", "lie": "recovery"}, {"distance": 598, "baseline": 5.6859, "skill_level": "pga", "lie": "recovery"}, {"distance": 599, "baseline": 5.6906, "skill_level": "pga", "lie": "recovery"}, {"distance": 600, "baseline": 5.6954, "skill_level": "pga", "lie": "recovery"}, {"distance": 601, "baseline": 5.7001, "skill_level": "pga", "lie": "recovery"}, {"distance": 602, "baseline": 5.7048, "skill_level": "pga", "lie": "recovery"}, {"distance": 603, "baseline": 5.7095, "skill_level": "pga", "lie": "recovery"}, {"distance": 604, "baseline": 5.7143, "skill_level": "pga", "lie": "recovery"}, {"distance": 605, "baseline": 5.719, "skill_level": "pga", "lie": "recovery"}, {"distance": 606, "baseline": 5.7237, "skill_level": "pga", "lie": "recovery"}, {"distance": 607, "baseline": 5.7285, "skill_level": "pga", "lie": "recovery"}, {"distance": 608, "baseline": 5.7332, "skill_level": "pga", "lie": "recovery"}, {"distance": 609, "baseline": 5.7379, "skill_level": "pga", "lie": "recovery"}, {"distance": 610, "baseline": 5.7426, "skill_level": "pga", "lie": "recovery"}, {"distance": 611, "baseline": 5.7474, "skill_level": "pga", "lie": "recovery"}, {"distance": 612, "baseline": 5.7521, "skill_level": "pga", "lie": "recovery"}, {"distance": 613, "baseline": 5.7568, "skill_level": "pga", "lie": "recovery"}, {"distance": 614, "baseline": 5.7616, "skill_level": "pga", "lie": "recovery"}, {"distance": 615, "baseline": 5.7663, "skill_level": "pga", "lie": "recovery"}, {"distance": 616, "baseline": 5.771, "skill_level": "pga", "lie": "recovery"}, {"distance": 617, "baseline": 5.7757, "skill_level": "pga", "lie": "recovery"}, {"distance": 618, "baseline": 5.7805, "skill_level": "pga", "lie": "recovery"}, {"distance": 619, "baseline": 5.7852, "skill_level": "pga", "lie": "recovery"}, {"distance": 620, "baseline": 5.7899, "skill_level": "pga", "lie": "recovery"}, {"distance": 621, "baseline": 5.7947, "skill_level": "pga", "lie": "recovery"}, {"distance": 622, "baseline": 5.7994, "skill_level": "pga", "lie": "recovery"}, {"distance": 623, "baseline": 5.8041, "skill_level": "pga", "lie": "recovery"}, {"distance": 624, "baseline": 5.8088, "skill_level": "pga", "lie": "recovery"}, {"distance": 625, "baseline": 5.8136, "skill_level": "pga", "lie": "recovery"}, {"distance": 626, "baseline": 5.8183, "skill_level": "pga", "lie": "recovery"}, {"distance": 627, "baseline": 5.823, "skill_level": "pga", "lie": "recovery"}, {"distance": 628, "baseline": 5.8278, "skill_level": "pga", "lie": "recovery"}, {"distance": 629, "baseline": 5.8325, "skill_level": "pga", "lie": "recovery"}, {"distance": 630, "baseline": 5.8372, "skill_level": "pga", "lie": "recovery"}, {"distance": 631, "baseline": 5.8419, "skill_level": "pga", "lie": "recovery"}, {"distance": 632, "baseline": 5.8467, "skill_level": "pga", "lie": "recovery"}, {"distance": 633, "baseline": 5.8514, "skill_level": "pga", "lie": "recovery"}, {"distance": 634, "baseline": 5.8561, "skill_level": "pga", "lie": "recovery"}, {"distance": 635, "baseline": 5.8609, "skill_level": "pga", "lie": "recovery"}, {"distance": 636, "baseline": 5.8656, "skill_level": "pga", "lie": "recovery"}, {"distance": 637, "baseline": 5.8703, "skill_level": "pga", "lie": "recovery"}, {"distance": 638, "baseline": 5.875, "skill_level": "pga", "lie": "recovery"}, {"distance": 639, "baseline": 5.8798, "skill_level": "pga", "lie": "recovery"}, {"distance": 640, "baseline": 5.8845, "skill_level": "pga", "lie": "recovery"}, {"distance": 641, "baseline": 5.8892, "skill_level": "pga", "lie": "recovery"}, {"distance": 642, "baseline": 5.894, "skill_level": "pga", "lie": "recovery"}, {"distance": 643, "baseline": 5.8987, "skill_level": "pga", "lie": "recovery"}, {"distance": 644, "baseline": 5.9034, "skill_level": "pga", "lie": "recovery"}, {"distance": 645, "baseline": 5.9081, "skill_level": "pga", "lie": "recovery"}, {"distance": 646, "baseline": 5.9129, "skill_level": "pga", "lie": "recovery"}, {"distance": 647, "baseline": 5.9176, "skill_level": "pga", "lie": "recovery"}, {"distance": 648, "baseline": 5.9223, "skill_level": "pga", "lie": "recovery"}, {"distance": 649, "baseline": 5.9271, "skill_level": "pga", "lie": "recovery"}, {"distance": 650, "baseline": 5.9318, "skill_level": "pga", "lie": "recovery"}, {"distance": 651, "baseline": 5.9365, "skill_level": "pga", "lie": "recovery"}, {"distance": 652, "baseline": 5.9412, "skill_level": "pga", "lie": "recovery"}, {"distance": 653, "baseline": 5.946, "skill_level": "pga", "lie": "recovery"}, {"distance": 654, "baseline": 5.9507, "skill_level": "pga", "lie": "recovery"}, {"distance": 655, "baseline": 5.9554, "skill_level": "pga", "lie": "recovery"}, {"distance": 656, "baseline": 5.9602, "skill_level": "pga", "lie": "recovery"}, {"distance": 657, "baseline": 5.9649, "skill_level": "pga", "lie": "recovery"}, {"distance": 658, "baseline": 5.9696, "skill_level": "pga", "lie": "recovery"}, {"distance": 659, "baseline": 5.9743, "skill_level": "pga", "lie": "recovery"}, {"distance": 660, "baseline": 5.9791, "skill_level": "pga", "lie": "recovery"}, {"distance": 661, "baseline": 5.9838, "skill_level": "pga", "lie": "recovery"}, {"distance": 662, "baseline": 5.9885, "skill_level": "pga", "lie": "recovery"}, {"distance": 663, "baseline": 5.9933, "skill_level": "pga", "lie": "recovery"}, {"distance": 664, "baseline": 5.998, "skill_level": "pga", "lie": "recovery"}, {"distance": 665, "baseline": 6.0027, "skill_level": "pga", "lie": "recovery"}, {"distance": 666, "baseline": 6.0074, "skill_level": "pga", "lie": "recovery"}, {"distance": 667, "baseline": 6.0122, "skill_level": "pga", "lie": "recovery"}, {"distance": 668, "baseline": 6.0169, "skill_level": "pga", "lie": "recovery"}, {"distance": 669, "baseline": 6.0216, "skill_level": "pga", "lie": "recovery"}, {"distance": 670, "baseline": 6.0264, "skill_level": "pga", "lie": "recovery"}, {"distance": 671, "baseline": 6.0311, "skill_level": "pga", "lie": "recovery"}, {"distance": 672, "baseline": 6.0358, "skill_level": "pga", "lie": "recovery"}, {"distance": 673, "baseline": 6.0405, "skill_level": "pga", "lie": "recovery"}, {"distance": 674, "baseline": 6.0453, "skill_level": "pga", "lie": "recovery"}, {"distance": 675, "baseline": 6.05, "skill_level": "pga", "lie": "recovery"}, {"distance": 676, "baseline": 6.0547, "skill_level": "pga", "lie": "recovery"}, {"distance": 677, "baseline": 6.0595, "skill_level": "pga", "lie": "recovery"}, {"distance": 678, "baseline": 6.0642, "skill_level": "pga", "lie": "recovery"}, {"distance": 679, "baseline": 6.0689, "skill_level": "pga", "lie": "recovery"}, {"distance": 680, "baseline": 6.0736, "skill_level": "pga", "lie": "recovery"}, {"distance": 681, "baseline": 6.0784, "skill_level": "pga", "lie": "recovery"}, {"distance": 682, "baseline": 6.0831, "skill_level": "pga", "lie": "recovery"}, {"distance": 683, "baseline": 6.0878, "skill_level": "pga", "lie": "recovery"}, {"distance": 684, "baseline": 6.0926, "skill_level": "pga", "lie": "recovery"}, {"distance": 685, "baseline": 6.0973, "skill_level": "pga", "lie": "recovery"}, {"distance": 686, "baseline": 6.102, "skill_level": "pga", "lie": "recovery"}, {"distance": 687, "baseline": 6.1067, "skill_level": "pga", "lie": "recovery"}, {"distance": 688, "baseline": 6.1115, "skill_level": "pga", "lie": "recovery"}, {"distance": 689, "baseline": 6.1162, "skill_level": "pga", "lie": "recovery"}, {"distance": 690, "baseline": 6.1209, "skill_level": "pga", "lie": "recovery"}, {"distance": 691, "baseline": 6.1257, "skill_level": "pga", "lie": "recovery"}, {"distance": 692, "baseline": 6.1304, "skill_level": "pga", "lie": "recovery"}, {"distance": 693, "baseline": 6.1351, "skill_level": "pga", "lie": "recovery"}, {"distance": 694, "baseline": 6.1398, "skill_level": "pga", "lie": "recovery"}, {"distance": 695, "baseline": 6.1446, "skill_level": "pga", "lie": "recovery"}, {"distance": 696, "baseline": 6.1493, "skill_level": "pga", "lie": "recovery"}, {"distance": 697, "baseline": 6.154, "skill_level": "pga", "lie": "recovery"}, {"distance": 698, "baseline": 6.1588, "skill_level": "pga", "lie": "recovery"}, {"distance": 699, "baseline": 6.1635, "skill_level": "pga", "lie": "recovery"}, {"distance": 700, "baseline": 6.1682, "skill_level": "pga", "lie": "recovery"}]}