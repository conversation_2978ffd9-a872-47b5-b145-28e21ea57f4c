// #
// # Import a TXT file to either YAML or JSON
// #
// # e.g.
// #
// #   * Via class methods:
// #
// #     Sg::Import.all :json
// #     Sg::Import.all :yaml
// #
// #   * Via instance methods
// #
// #     Sg::Import.new( lie: :fairway, pga: true ).write(:json)
// #     Sg::Import.new( lie: :fairway, pga: true ).write(:yaml)
// #
// #
export class SgImport {
  // extend  Helper
  // include Helper

  // class << self

  // #
  // # import all files to YAML/JSON
  // #
  // # e.g.
  // #
  // #   Sg::Import.all :json
  // #
  all(format = 'yaml') {
    console.log(format);

    //   Load.raw_data_files.children.each do |data|
    //   next if data.basename.to_s =~ /\.DS/i
    //   importer  = new( lie: data.basename.to_s )
    //   importer.write( format )
    // end
  }

  // #
  // # ::Sg::Import.queue(:json)
  // #
  queue(format = 'json') {
    console.log(format);
    //   Load.raw_data_files.children.map do |data|
    //   next if data.basename.to_s =~ /\.DS/i
    //   new( lie: data.basename.to_s )
    // end.compact
  }

  // #
  // # accessors
  // #
  // attr_reader :lie, :unit, :skill_level, :raw_file

  // #
  // # lie
  // #
  // #   * fairway
  // #   * putting
  // #   * recovery
  // #   * rough
  // #   * sand
  // #   * tee
  // #
  // # pga - user's skill level; false == scratch
  // #
  // #   * True / False
  // #
  // #
  initialize(lie) {
    console.log(lie);
    // @lie          = get_lie( lie )
    // @unit         = determine_unit(@lie)
    // @skill_level  = determine_skill_level(lie)
    // @raw_file     = Load.raw_data(lie: lie, skill_level: @skill_level)
  }

  // #
  // # Write baselines to a given format/file
  // #
  write(format = 'yaml') {
    console.log(format);
    // ::File.open( get_write_file(format), "wb" ) do |file|
    //   file.puts ( format.to_s =~/yaml/i ? to_yaml : to_json )
    // end
  }
  // #
  // # convert to hash
  // #
  to_h() {
    // {
    //   lie:          lie,
    //   skill_level:  @skill_level,
    //   unit:         unit,
    //   baselines:    baselines,
    // }
  }

  // alias :to_hash :to_h

  // #
  // # convert to JSON
  // #
  to_json() {
    // to_h.to_json
  }

  // #
  // # convert to YAML
  // #
  to_yaml() {
    // to_h.to_yaml
  }

  // #
  // # private methods
  // #
  // private

  // #
  // # strokes gained baselines
  // #
  baselines() {
    // @baselines ||= ::File.open(@raw_file, 'r').map do |line|
    //   dis, base = line.split(',')
    //   {
    //     distance:     dis.to_i,
    //     baseline:     base.chomp!.to_f,
    //     skill_level:  @skill_level,
    //     lie:          @lie,
    //   }
    // end
  }

  // #
  // # get the location of where to write files to
  // #
  get_write_file(format) {
    console.log(format);

    // if format.to_s =~ /yaml/i
    //   Load.yaml_data(lie, @skill_level)
    // else
    //   Load.json_data(lie, @skill_level)
    // end
  }
}
