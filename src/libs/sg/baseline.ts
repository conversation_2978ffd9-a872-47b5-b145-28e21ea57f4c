import { SgLoad } from './load';

export class SgBaseline {
  // #
  // # pga: Boolean
  // #
  // #   * Is the user a pro or not
  // #
  skill_level: any;
  constructor(skill_level: '') {
    // @skill_level = skill_level.to_s
    this.skill_level = skill_level;
  }
  // initialize(skill_level: '') {
  //   // @skill_level = skill_level.to_s
  // }

  // #
  // # gets the baseline for a specific lie and distance to the pin
  // #
  async get_baseline_for(lie, distance_to_pin) {
    //   distance = lie.to_s =~ /putting|green/i ? distance_to_pin.to_f * 3 * 12 : distance_to_pin
    //   return 0 if distance.to_i.zero?
    //   raise InvalidDistance, "'#{distance}' is not a valid distance for #{lie.to_s} lie condition" if distance.to_i.zero?
    //   load_data( lie )[:baselines].find do |baseline|
    //     baseline[:distance] == distance.round(0)
    //   end.fetch(:baseline, nil)
    // rescue
    //   0

    let distance = ['putting', 'green'].includes(lie?.toLowerCase()?.trim())
      ? distance_to_pin * 3 * 12
      : distance_to_pin;
    // console.log({ lie, distance });

    if (distance == 0) {
      return 0;
    }
    distance = +distance.toFixed(0);

    // raise InvalidDistance, "'#{distance}' is not a valid distance for #{lie.to_s} lie condition" if distance.to_i.zero?

    // this.load_data( lie )[:baselines].find do |baseline|
    //   baseline[:distance] == distance.round(0)
    // end.fetch(:baseline, nil)

    const dataLine = await this.load_data(lie);

    if (dataLine) {
      const baseLines = dataLine?.baselines;

      const baseLine = baseLines.find((b: any) => {
        return +b.distance == +distance.toFixed(0);
      });
      // fs.appendFileSync('get_baseline_for.log', JSON.stringify({ lie, distance, distance_to_pin, baseLine }) + '\n');

      return baseLine?.baseline || null;
    } else {
      return null;
    }
  }

  // #
  // # private methods
  // #
  // private

  // #
  // # loads the JSON data
  // #
  // # using JSON since JSON loading/parsing is so much faster
  // # than YAML
  // #
  async load_data(lie) {
    return await new SgLoad().read_json_data(lie, this.skill_level);
    // Load.read_json_data( lie: lie, skill_level: @skill_level )
  }
}
