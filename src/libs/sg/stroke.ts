// #
// # a = Sg::Stroke.new("pga" => true, "start_lie" => "Tee", "start_distance" => 380.9551886457532, "end_lie" => "Rough", "end_distance" => 148.19060394848077)
// #
import { Logger } from '@nestjs/common';
import { Stats } from 'src/utils/smart-golf/stats';
import { includeStr } from 'src/utils/utils';
import { SgBaseline } from './baseline';
import { SgHelper } from './helper';

// #
export class SgStroke extends SgHelper {
  private readonly logger = new Logger(SgStroke.name);
  // #
  // # A stroke needs to consist of the following:
  // #
  // #   * pga: <PERSON><PERSON><PERSON>            - A pro or not (not == scratch)
  // #   * starts_penalty: <PERSON><PERSON>an - Did the shot start as a penalty shot
  // #   * ends_penalty: <PERSON><PERSON><PERSON>   - Did the shot end as a penalty shot
  // #   * difficult: Boolean      - Was this a difficult shot
  // #   * start lie: String       - What was the shot's starting lie condition
  // #   * end lie: String         - What was the shot's ending lie condition
  // #   * start distance: Float   - What is the starting distance to the pin
  // #   * end distance: Float     - What is the ending distance to the pin
  // #
  // #
  // class Stroke
  //   include Helper
  //   attr_reader :start_lie, :end_lie, :start_distance,
  //     :end_distance, :last_shot, :starts_penalty, :ends_penalty
  options: any;
  start_lie: any;
  end_lie: any;
  start_distance: any;
  end_distance: any;
  starts_penalty: any;
  ends_penalty: any;
  last_shot: any;
  constructor(options: any) {
    super();
    this.options = JSON.parse(JSON.stringify(options)); // # simple stringify_keys
    this.start_lie = this.format_lie(this.options['start_lie']);
    this.end_lie = this.format_lie(this.options['end_lie']);
    this.start_distance = this.options['start_distance'];
    this.end_distance = this.options['end_distance'];
    this.starts_penalty = this.options['starts_penalty'];
    this.ends_penalty = this.options['ends_penalty'];
    this.last_shot = this.end_distance == 0;
  }
  // #
  // # options: Hash
  // #
  // initialize(options = {}) {
  //   // @options = ::JSON.parse(options.to_json) # simple stringify_keys
  //   // @start_lie      = format_lie( @options["start_lie"] )
  //   // @end_lie        = format_lie( @options["end_lie"] )
  //   // @start_distance = @options["start_distance"]
  //   // @end_distance   = @options["end_distance"]
  //   // @starts_penalty = !!@options["starts_penalty"]
  //   // @ends_penalty   = !!@options["ends_penalty"]
  //   // @last_shot      = @end_distance.to_f.zero?
  // }

  // #
  // # get the starting strokes gained
  // #
  async start_sg() {
    // @start_sg ||= begin
    //   base = get_baseline_number(start_lie, start_distance)
    //   base.zero? ? 1 : base
    // end
    try {
      // fs.appendFileSync(
      //   'get_baseline_for.log',
      //   JSON.stringify({ start_lie: this.start_lie, start_distance: this.start_distance }) + '\n'
      // );
      const base = await this.get_baseline_number(this.start_lie, this.start_distance);
      return base == 0 ? 1 : +base;
    } catch (error) {
      this.logger.error(error.message);
      return 1;
    }
  }

  // #
  // # get the ending strokes gained
  // #
  async end_sg() {
    // @end_sg ||= last_shot ? 0 : get_baseline_number(end_lie, end_distance)
    // fs.appendFileSync(
    //   'get_baseline_for.log',
    //   JSON.stringify({ end_lie: this.end_lie, end_distance: this.end_distance }) + '\n'
    // );
    return this.last_shot ? 0 : await this.get_baseline_number(this.end_lie, this.end_distance);
  }

  // #
  // # calculate the strokes gained
  // #
  async calculate_sg() {
    // return 0 if starts_penalty || start_lie =~ /hazard/i
    // base = start_sg - end_sg - 1.0
    // base += 0.3 if difficult?
    // base -= 1.0 if ends_penalty
    // base
    if (this.starts_penalty || includeStr(this.start_lie, 'hazard')) {
      return 0;
    }
    let base = (await this.start_sg()) - (await this.end_sg()) - 1.0;
    if (this.difficult()) {
      base += 0.3;
    }
    if (this.ends_penalty) {
      base -= 1.0;
    }
    return base;
  }
  // alias_method :baseline, :calculate_sg

  // #
  // # What this shot a difficult shot
  // #
  difficult() {
    // @difficult ||= !!@options.fetch("difficult", false)
    return this.options?.difficult || false;
  }

  // alias_method :difficult?, :difficult

  // #
  // # The user's skill level
  // #
  skill_level() {
    // @skill_level ||= @options["skill_level"] || ""
    return this.options['skill_level'] || '';
  }

  // #
  // # gets the baseline number
  // #
  async get_baseline_number(lie, distance) {
    // formatted_distance = format_distance(lie, distance)
    // # distance_in_yards = distance.to_f * 1.09361
    // Baseline.new(skill_level: skill_level).get_baseline_for(lie, formatted_distance)
    const formatted_distance = this.format_distance(lie, distance);
    // # distance_in_yards = distance.to_f * 1.09361
    const baseLine = new SgBaseline(this.skill_level());
    const baseLineFor = await baseLine.get_baseline_for(lie, formatted_distance);

    return +baseLineFor;
  }

  // #
  // # distance comes in meters, no matter what. need to convert
  // #
  format_distance(lie, distance) {
    const distance_in_yards = +distance * Stats.METERS_TO_YARDS;
    return +distance_in_yards;
  }
}
