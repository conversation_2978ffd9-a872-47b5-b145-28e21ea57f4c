export class SgHelper {
  // #
  // # format the lie conditions
  // #
  format_lie(lie_condition) {
    // formatted_lie = lie_condition.to_s.split("_").last
    // case formatted_lie.to_s
    // when /tee/i           then "tee"
    // when /tee boundary/i  then "tee"
    // when /fairway/i       then "fairway"
    // when /sand/i          then "sand"
    // when /bunker/i        then "sand"
    // when /rough/i         then "rough"
    // when /putting/i       then "putting"
    // when /green/i         then "putting"
    // when /recovery/i      then "recovery"
    // when /hazard/i        then "recovery"
    // when /water/i         then "recovery"
    // else
    //   "rough"
    // end

    if (lie_condition == undefined) {
      return 'rough';
    }
    const formatted_lie = lie_condition?.split('_').pop() || '';

    switch (formatted_lie.toLowerCase()) {
      case 'tee':
        return 'tee';
      case 'fairway':
        return 'fairway';
      case 'sand':
      case 'bunker':
        return 'sand';
      case 'rough':
        return 'rough';
      case 'putting':
      case 'green':
        return 'putting';
      case 'recovery':
      case 'hazard':
      case 'water':
        return 'recovery';
      default:
        return 'rough';
    }
  }

  get_lie(filename) {
    // # fifteen_roughShotValue.txt
    // name = filename.to_s.split('_').last
    // name = name.split("ShotValue.txt").first
    // format_lie(name)
    let name = filename?.toString().split('_').pop();
    name = name.split('ShotValue.txt').first;
    return this.format_lie(name);
  }

  determine_skill_level(filename) {
    // return "" unless ::File.basename(filename).include?("_")
    // ::File.basename(filename).to_s.split('_').first
    if (!filename.includes('_')) {
      return '';
    }
    return filename.split('_')[0];
  }

  // #
  // # determines what units should be used
  // #
  determine_unit(lie) {
    // lie.to_s =~ /putting/i ? 'inches' : 'yards'
    return lie?.toString()?.toLowerCase().includes('putting') ? 'inches' : 'yards';
  }
}
