import { SgStroke } from './sg/stroke';

export class Sg {
  // #
  // # place to put the options for start/end/calculate strokes gains
  // #
  new(options) {
    // Stroke.new(options)
    return new SgStroke(options);
  }

  // #
  // # Calculates the Strokes Gained
  // #
  // # Options: Hash
  // #
  // #   * pga: <PERSON><PERSON><PERSON>            - A pro or not (not == scratch)
  // #   * starts_penalty: <PERSON><PERSON><PERSON> - Did the shot start as a penalty shot
  // #   * ends_penalty: <PERSON><PERSON><PERSON>   - Did the shot end as a penalty shot
  // #   * difficult: <PERSON><PERSON><PERSON>      - Was this a difficult shot
  // #   * start lie: String       - What was the shot's starting lie condition
  // #   * end lie: String         - What was the shot's ending lie condition
  // #   * start distance: Float   - What is the starting distance to the pin
  // #   * end distance: Float     - What is the ending distance to the pin
  // #
  async calculate(options) {
    return await new SgStroke(options).calculate_sg();
  }

  // #
  // # Calculates the Strokes Gained
  // #
  // # Options: Hash
  // #
  // #   * pga: Boolean            - A pro or not (not == scratch)
  // #   * starts_penalty: <PERSON><PERSON>an - Did the shot start as a penalty shot
  // #   * ends_penalty: <PERSON><PERSON><PERSON>   - Did the shot end as a penalty shot
  // #   * difficult: <PERSON><PERSON><PERSON>      - Was this a difficult shot
  // #   * start lie: String       - What was the shot's starting lie condition
  // #   * end lie: String         - What was the shot's ending lie condition
  // #   * start distance: Float   - What is the starting distance to the pin
  // #   * end distance: Float     - What is the ending distance to the pin
  // #
  async startSg(options) {
    // Stroke.new(options).start_sg
    // console.log({ options });
    return await new SgStroke(options).start_sg();
  }

  // #
  // # Calculates the Strokes Gained
  // #
  // # Options: Hash
  // #
  // #   * pga: Boolean            - A pro or not (not == scratch)
  // #   * starts_penalty: Boolean - Did the shot start as a penalty shot
  // #   * ends_penalty: Boolean   - Did the shot end as a penalty shot
  // #   * difficult: Boolean      - Was this a difficult shot
  // #   * start lie: String       - What was the shot's starting lie condition
  // #   * end lie: String         - What was the shot's ending lie condition
  // #   * start distance: Float   - What is the starting distance to the pin
  // #   * end distance: Float     - What is the ending distance to the pin
  // #
  async endSg(options) {
    return await new SgStroke(options).end_sg();
  }

  // #
  // # Import txt file data into a specific format: JSON/YAML
  // #
  import(format = ':yaml') {
    console.log(format);

    // Import.all(format)
  }
}
