import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional } from 'class-validator';

export class MappingCourseDto {
  @ApiProperty({ name: 'tee' })
  @IsNotEmpty()
  tee: string;

  @ApiProperty({ name: 'course_par' })
  @IsOptional()
  course_par: number;

  @ApiProperty({ name: 'course_name' })
  @IsNotEmpty()
  course_name: string;

  @ApiProperty({ name: 'igolf_course_id' })
  @IsNotEmpty()
  igolf_course_id: string;

  @ApiProperty({ name: 'gender' })
  @IsNotEmpty()
  gender: string;
}
