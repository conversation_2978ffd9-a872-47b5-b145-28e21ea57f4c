import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

export class CreateGhinDto {
  @ApiProperty({ example: 'string' })
  @IsNotEmpty()
  igolf_course_id: string;

  @ApiProperty({ example: '32018' })
  @IsNotEmpty()
  ghin_course_id: string;

  @ApiProperty({ example: 'Hill Course' })
  @IsNotEmpty()
  ghin_course_name: string;

  @ApiProperty({ example: 'Long Thanh Golf Club & Residential Estate - Hill Course' })
  @IsNotEmpty()
  ghin_course_full_name: string;
}
