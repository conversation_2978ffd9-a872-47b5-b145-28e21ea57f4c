import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

export class UpdateScoreGhinDto {
  @ApiProperty({ example: '32018' })
  @IsNotEmpty()
  ghin_course_id: string;

  @ApiProperty({ example: 'B COURSE' })
  @IsNotEmpty()
  ghin_course_name: string;

  @ApiProperty({ example: '867632474' })
  @IsNotEmpty()
  ghin_round_id: string;

  @ApiProperty({ example: '615348' })
  @IsNotEmpty()
  ghin_tee_set_id: string;

  @ApiProperty({ example: 'GOLD' })
  @IsNotEmpty()
  ghin_tee_set_name: string;

  @ApiProperty({ example: '50' })
  @IsNotEmpty()
  adjusted_gross_score: string;

  @ApiProperty({ example: '2023-04-05' })
  @IsNotEmpty()
  played_at: string;
}
