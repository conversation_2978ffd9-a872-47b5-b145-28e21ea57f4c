import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

export class MatchingGHINCourseDto {
  @ApiProperty({ name: 'ghin_tee_set_name' })
  @IsNotEmpty()
  ghin_tee_set_name: string;

  @ApiProperty({ name: 'ghin_tee_set_id' })
  @IsNotEmpty()
  ghin_tee_set_id: string;

  @ApiProperty({ name: 'ghin_course_name' })
  @IsNotEmpty()
  ghin_course_name: string;

  @ApiProperty({ name: 'ghin_course_id' })
  @IsNotEmpty()
  ghin_course_id: string;
}
