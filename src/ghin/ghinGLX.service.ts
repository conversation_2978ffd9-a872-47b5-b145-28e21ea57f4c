import { BadRequestException, CACHE_MANAGER, Inject, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import { Cache } from 'cache-manager';
import ms from 'ms';
import { MytmService } from 'src/mytm/mytm.service';
import { GHIN_APIS, GHIN_CONST } from './ghin.const';

@Injectable()
export class GhinGLXService {
  private readonly logger = new Logger(GhinGLXService.name);
  ghinEndpoint: null;
  constructor(
    private configService: ConfigService,
    private myTMService: MytmService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache
  ) {
    this.ghinEndpoint = configService.get('ghin.endpoint');
  }
  /**
   * getRequestHeaderConfigs
   * @returns token
   */
  async getRequestHeaderConfigs() {
    let token = await this.getGhinToken();
    if (!token) {
      token = await this.handleRefreshGhinToken();
    }
    return {
      headers: { Authorization: `Bearer ${token}` },
    };
  }
  async getGhinToken() {
    return await this.cacheManager.get(GHIN_CONST.CACHE_GHIN_TOKEN);
  }
  async removeGhinToken() {
    return await this.cacheManager.del(GHIN_CONST.CACHE_GHIN_TOKEN);
  }

  /**
   * handleRefreshGhinToken
   *
   * @returns
   */
  async handleRefreshGhinToken() {
    this.logger.debug(`HANDLE REFRESH GHIN AUTH TOKEN`);
    const urlLogin = `${this.ghinEndpoint}${GHIN_APIS.LOGIN}`;
    this.logger.debug(`GHIN LOGIN URL: ${urlLogin}`);
    try {
      const payload = {
        user: {
          email: this.configService.get('ghin.email'),
          password: this.configService.get('ghin.password'),
          remember_me: 'true',
        },
      };
      const response = await axios.post(urlLogin, payload);
      const token = response.data?.token;
      this.logger.debug(`GHIN TOKEN: ${token}`);
      const ttl = ms(`${this.configService.get('app.cacheTTL')}`) / 1000;
      await this.cacheManager.set(GHIN_CONST.CACHE_GHIN_TOKEN, token, { ttl });

      return token;
    } catch (error) {
      console.error(error);
      return null;
    }
  }

  /**
   * getTees
   *
   * @param ghinCourseId
   * @param isRetry
   * @returns
   */
  async getGhinTees(ghinCourseId: string, isRetry = true) {
    let urlGetScoreDetail = `${this.ghinEndpoint}${GHIN_APIS.GET_LIST_TEE_RATINGS}`;
    urlGetScoreDetail = urlGetScoreDetail.replace('COURSE_ID', ghinCourseId);
    try {
      const response = await axios.get(urlGetScoreDetail, await this.getRequestHeaderConfigs());
      return response.data;
    } catch (error) {
      if (error.response.status == 401 && isRetry) {
        await this.removeGhinToken();
        return await this.getGhinTees(ghinCourseId, false);
      }
      console.error(error?.response.data);
      throw new BadRequestException(`Can't get tees!`);
    }
  }
}
