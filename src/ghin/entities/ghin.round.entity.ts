import { ApiProperty } from '@nestjs/swagger';
import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

@Entity({ name: 'ghin_rounds' })
export class GhinRound {
  @PrimaryGeneratedColumn()
  @ApiProperty({ example: 1 })
  id: number;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  user_id: number;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  ghin_id: number;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  ghin_round_id: number;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  ghin_parent_round_id: number;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  ghin_course_id: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  ghin_course_name: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  ghin_tee_set_id: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  ghin_tee_set_name: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  ghin_tee_set_side: string;

  @Column({ type: 'float' })
  @ApiProperty({ example: 1.0 })
  course_rating: number;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  score: number;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 9 })
  number_of_holes_played: number;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  slope_rating: number;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  score_type: string;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 0 })
  score_to_par: number;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  generated_by: string;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 0 })
  course_par: number;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 0 })
  course_yards: number;

  @ApiProperty({ example: new Date().toISOString() })
  @Column({ type: 'timestamptz' })
  played_on: Date;

  @CreateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  created_at: Date;

  @UpdateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  updated_at: Date;
}
