import { ApiProperty } from '@nestjs/swagger';
import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

@Entity({ name: 'ghin_score_logs' })
export class GhinScoreLogEntity {
  @PrimaryGeneratedColumn()
  @ApiProperty({ example: 1 })
  id: number;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  round_id: number;

  @Column({ type: 'integer' })
  golfer_id: number;

  @Column({ type: 'jsonb' })
  score_detail: object;

  @Column({ type: 'jsonb' })
  score_with_hole_detail: object;

  @Column({ type: 'boolean' })
  is_post_hole_by_hole: boolean;

  @Column({ type: 'jsonb' })
  response: object;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
