export interface GhinSearchGeo {
  currentLatitude: number;
  currentLongitude: number;
  distance: number;
  distanceUnitOfMeasure: string;
  courseStatus: string;
  facilityStatus: string;
}
export interface GhinSearchName {
  course_status: string;
  facility_status: string;
  name: string;
  include_tee_sets?: boolean;
}
export interface IGolfCourse {
  tee: string;
  course_par: number;
  course_name: string;
  igolf_course_id: string;
  gender: string;
}
export interface GHINCourseDto {
  ghin_tee: string;
  ghin_course_name: string;
  ghin_tee_id: string;
  ghin_course_id: string;
}
export interface GHINCourseHandicap {
  golfer_id: string;
  handicap_index: string;
  course_id: string;
  played_at?: string;
}
export const DRIVE_ACCURACY = {
  MISSED_LEFT: 0,
  MISSED_RIGHT: 1,
  MISSED_LONG: 2,
  MISSED_SHORT: 3,
};
export const APPROACH_SHOT_ACCURACY = {
  MISSED_LEFT: 0,
  MISSED_RIGHT: 1,
  MISSED_LONG: 2,
  MISSED_SHORT: 3,
};
