import { Body, Controller, Delete, Get, HttpCode, HttpStatus, Param, Patch, Post, Query } from '@nestjs/common';
import { ApiBody, ApiParam, ApiQuery, ApiTags } from '@nestjs/swagger';
import { getOptionsPaging } from 'src/utils/infinity-pagination';
import { MappingCourseDto } from './dto/mapping-course.dto';
import { MatchingGHINCourseDto } from './dto/matching-ghin-course.dto';
import { GhinService } from './ghin.service';

@Controller('ghin')
@ApiTags('GHIN')
export class GhinController {
  constructor(private readonly ghinService: GhinService) {}

  @Get()
  @ApiParam({ name: 'id', example: 41428 })
  @ApiQuery({ name: 'page', example: 1, required: false })
  @ApiQuery({ name: 'per', example: 10, required: false })
  findAll(@Param('id') id: number, @Query() query?: any) {
    if (!query.per) {
      query['per'] = 30;
    }
    const { page, limit } = getOptionsPaging(query.page, query.per);
    const pagingOption = { limit, page };

    return this.ghinService.findAll(pagingOption);
  }

  @Post('mapping')
  @HttpCode(HttpStatus.OK)
  @ApiBody({ type: MappingCourseDto })
  mappingCourse(@Body() mappingCourseDto: MappingCourseDto) {
    return this.ghinService.mappingIGolfGhinCourse(mappingCourseDto);
  }
  @Post('match_igolf_course')
  @HttpCode(HttpStatus.OK)
  @ApiBody({ type: MatchingGHINCourseDto })
  matchingCourseIGolf(@Body() payload: MatchingGHINCourseDto) {
    return this.ghinService.matchingCourseIGolf(payload);
  }

  @Get('search')
  @HttpCode(HttpStatus.OK)
  searchByName(
    @Query('name') course_name: string,
    @Query('latitude') latitude: number,
    @Query('longitude') longitude: number
  ) {
    if (latitude && longitude) {
      return this.ghinService.searchGHINCoursesByGEO(latitude, longitude);
    }
    return this.ghinService.searchGHINCoursesByName(course_name);
  }

  @Get('course_detail/:id')
  @HttpCode(HttpStatus.OK)
  getCourseDetail(@Param('id') id: string) {
    return this.ghinService.getGHINCourseDetail(id);
  }

  @Get(':userId/course_recent')
  @ApiParam({ name: 'userId', example: '41428' })
  @HttpCode(HttpStatus.OK)
  async getUserCourseRecent(@Param('userId') userId: string) {
    return await this.ghinService.getGolferGHINCourseRecent(+userId);
  }

  @Get('webhook/golfer')
  @HttpCode(HttpStatus.OK)
  getWebhookGolfer(@Query() query: any) {
    console.log(`GET: WEBHOOK/GOLFER: ${JSON.stringify(query)}`);
    console.log(query);
    return query;
  }

  @Post('webhook/golfer')
  @HttpCode(HttpStatus.OK)
  postWebhookGolfer(@Body() payload: any) {
    console.log(`POST: WEBHOOK/GOLFER: ${JSON.stringify(payload)}`);
    return this.ghinService.processRequestGPA(payload);
  }

  @Get('user/webhook_settings')
  @HttpCode(HttpStatus.OK)
  getWebhookSettings() {
    return this.ghinService.getWebhookSettings();
  }

  @Patch('user/webhook_settings')
  @HttpCode(HttpStatus.OK)
  updateWebhookSettings(@Body() payload: any) {
    return this.ghinService.updateWebhookSettings(payload);
  }

  @Delete('user/webhook_settings')
  @HttpCode(HttpStatus.NO_CONTENT)
  deleteWebhookSettings() {
    return this.ghinService.deleteWebhookSettings();
  }

  @Get('user/webhook_settings/test')
  @HttpCode(HttpStatus.OK)
  testWebhookSettings(@Query('type') type: string) {
    return this.ghinService.testWebhookSettings(type);
  }

  // @Post()
  // create(@Body() createGhinDto: CreateGhinDto) {
  //   return this.ghinService.create(createGhinDto);
  // }

  // @Patch(':id')
  // update(@Param('id') id: string, @Body() updateGhinDto: UpdateGhinDto) {
  //   return this.ghinService.update(+id, updateGhinDto);
  // }

  // @Delete(':id')
  // remove(@Param('id') id: string) {
  //   return this.ghinService.remove(+id);
  // }
}
