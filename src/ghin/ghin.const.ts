export const GHIN_APIS = {
  LOGIN: '/v1/users/login.json',

  SEARCH_COURSE_BY_GEO: '/v1/Courses/SearchGeo.json',

  SEARCH_COURSE_BY_NAME: '/v1/courses/search.json',

  // Replace COURSE_ID with value need search.
  GET_COURSE_DETAIL: '/v1/courses/COURSE_ID.json?include_altered_tees=true&tee_set_state=Active',

  GET_COURSE_HANDICAP: '/v1/course_handicaps.json?',

  // Replace TEE_ID with value need search.
  GET_TEE_DETAIL: '/v1/TeeSetRatings/TEE_ID.json?include_altered_tees=true',

  // Replace COURSE_ID with value need search.
  // Can search with query parameters: gender (M,F), tee_set_status: Active, number_of_holes: 18,9
  GET_LIST_TEE_RATINGS: '/v1/courses/COURSE_ID/tee_set_ratings.json?tee_set_status=Active',

  POST_SCORE_HOLE_BY_HOLE: '/v1/scores/hbh.json',

  POST_SCORE_9_HOLES: '/v1/scores/18h9and9.json',

  POST_SCORE_TOTAL_SCORE: '/v1/scores/adjusted.json',

  UPDATE_SCORE: '/v1/scores/GHIN_ROUND_ID/update.json',

  GET_SCORE_DETAIL: '/v1/scores/GHIN_ROUND_ID.json',

  GET_GOLFER_HANDICAP: '/v1/golfers/GHIN_ID/handicap_history_count.json?rev_count=10',

  GET_GOLFER_INFO:
    '/v1/golfers/search.json?per_page=1&page=1&golfer_id=GHIN_ID&sorting_criteria=id&order=ASC&status=Active',

  GET_GOLFER_SCORE: '/v1/golfers/GHIN_ID/scores.json',

  GET_GOLFER_ALL_SCORES: '/v1/scores/get_all.json',

  GET_GOLFER_COURSE_RECENT: '/v1/golfers/GHIN_ID/golfer_most_recent_courses.json?scores_to_use=',

  GET_GOLFER_SCORE_LIST: '/v1/scores/search.json?golfer_id=GHIN_ID&per_page=2&page=1&date_ranges=created_at',

  USER_REQUEST_ACCESS: '/v1/users/golfers/GHIN_ID/request_golfer_product_access.json',

  USER_REVOKE_ACCESS: '/v1/users/golfers/GHIN_ID/revoke_golfer_product_access.json',

  USER_WEB_HOOK_SETTINGS: '/v1/user/webhook_settings.json',

  USER_WEB_HOOK_SETTINGS_TEST: '/v1/user/webhook_settings/test.json',
};
export const GHIN_CONST = {
  CACHE_GHIN_TOKEN: 'CACHE_GHIN_TOKEN',
  RADIUS_DISTANCE_SEARCH: 20,
  SEARCH_DISTANCE_UNITS: 'Miles',
};
export const GHIN_SOURCE_TYPES = {
  GHIN_COM: 'GHINcom',
  IOS_GHIN_MOBILE_APP: 'iOS GHIN Mobile App',
  ANDROID_GHIN_MOBILE_APP: 'Android GHIN Mobile App',
};
export const GHIN_SCORE_TYPES = {
  TOURNAMENT: 'T',
  HOME: 'H',
  AWAY: 'A',
};
export const GHIN_ERROR_CODES = {
  ERROR_MAP_TEE_SET_RATINGS: 'ERROR_MAP_TEE_SET_RATINGS',
  ERROR_MAP_GHIN_COURSE: 'ERROR_MAP_GHIN_COURSE',
  ERROR_MAP_IGOLF_COURSE: 'ERROR_MAP_IGOLF_COURSE',
  ERROR_MAP_IGOLF_COURSE_TEE: 'ERROR_MAP_IGOLF_COURSE_TEE',
  ERROR_NOT_FOUND_GOLFER_PROFILE: 'ERROR_NOT_FOUND_GOLFER_PROFILE',
  ERROR_GPA_NOT_ACCEPTED: 'ERROR_GPA_NOT_ACCEPTED',
  GHIN_ID_CONNECTED_WITH_OTHER_USER: 'GHIN_ID_CONNECTED_WITH_OTHER_USER',
  ERROR_MAP_NUMBER_HOLES_PLAYED: 'ERROR_MAP_NUMBER_HOLES_PLAYED',
  ERROR_GET_HANDICAP_INDEX: 'ERROR_GET_HANDICAP_INDEX',
  ERROR_INVALID_DATA: 'ERROR_INVALID_DATA',
};
