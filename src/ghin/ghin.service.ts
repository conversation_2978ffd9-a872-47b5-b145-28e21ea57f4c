import { InjectQueue } from '@nestjs/bull';
import {
  BadRequestException,
  CACHE_MANAGER,
  Inject,
  Injectable,
  Logger,
  UnauthorizedException,
  forwardRef,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import axios from 'axios';
import { Queue } from 'bull';
import { Cache } from 'cache-manager';
import { plainToClass } from 'class-transformer';
import { getDistance } from 'geolib';
import _, { isEmpty } from 'lodash';
import ms from 'ms';
import { IPaginationOptions, paginateRaw } from 'nestjs-typeorm-paginate';
import { Repository } from 'typeorm';
import { GhinTeeRating } from 'src/ghin-tee-rating/entities/ghin-tee-rating.entity';
import { GhinTeeRatingService } from 'src/ghin-tee-rating/ghin-tee-rating.service';
import { MytmService } from 'src/mytm/mytm.service';
import { GhinScoreDto } from 'src/rounds/dto/ghin-score.dto';
import { OPTIONS_JOB_DEFAULT } from 'src/rounds/round.const';
import { RequestAccessGHINDto } from 'src/users/dto/update-user.dto';
import { User } from 'src/users/entities/user.entity';
import { transformDataPaging } from 'src/utils/infinity-pagination';
import { isValidId } from 'src/utils/utils';
import { PROCESSORS, PROCESS_QUEUE_NAMES } from 'src/workers/jobs/job.constant';
import { ThreePartyCourseService } from '../igolf/threePartyCourse.service';
import { isProduction } from '../utils/cron';
import { CreateGhinDto } from './dto/create-ghin.dto';
import { GetAllScoreDto } from './dto/get-all-score.dto';
import { MatchingGHINCourseDto } from './dto/matching-ghin-course.dto';
import { UpdateGhinDto } from './dto/update-ghin.dto';
import { UpdateScoreGhinDto } from './dto/update-score-ghin.dto';
import { GhinScoreLogEntity } from './entities/ghin-score-log.entity';
import { IGolfGhinCourse } from './entities/ghin.entity';
import { GHIN_APIS, GHIN_CONST, GHIN_ERROR_CODES, GHIN_SCORE_TYPES } from './ghin.const';
import { GHINCourseHandicap, GhinSearchGeo, GhinSearchName, IGolfCourse } from './ghin.types';

@Injectable()
export class GhinService {
  private readonly logger = new Logger(GhinService.name);
  ghinEndpoint: null;
  constructor(
    private configService: ConfigService,
    private myTMService: MytmService,
    private ghinTeeRatingService: GhinTeeRatingService,
    @Inject(forwardRef(() => ThreePartyCourseService)) private threePartyCourseService: ThreePartyCourseService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    @InjectRepository(IGolfGhinCourse)
    private iGolfGhinRepository: Repository<IGolfGhinCourse>,
    @InjectRepository(GhinScoreLogEntity)
    private ghinScoreLogRepository: Repository<GhinScoreLogEntity>,
    @InjectQueue(PROCESSORS.SyncUpdateGhinNumberJob) private updateGhinNumberJobQueue: Queue,
    @InjectRepository(User)
    private userRepo: Repository<User>
  ) {
    this.ghinEndpoint = configService.get('ghin.endpoint');
  }
  /**
   * getRequestHeaderConfigs
   * @returns token
   */
  async getRequestHeaderConfigs() {
    let token = await this.getGhinToken();
    if (!token) {
      token = await this.handleRefreshGhinToken();
    }
    return {
      headers: { Authorization: `Bearer ${token}` },
    };
  }
  async getGhinToken() {
    return await this.cacheManager.get(GHIN_CONST.CACHE_GHIN_TOKEN);
  }
  async removeGhinToken() {
    return await this.cacheManager.del(GHIN_CONST.CACHE_GHIN_TOKEN);
  }

  /**
   * handleRefreshGhinToken
   *
   * @returns
   */
  async handleRefreshGhinToken() {
    this.logger.debug(`HANDLE REFRESH GHIN AUTH TOKEN`);
    const urlLogin = `${this.ghinEndpoint}${GHIN_APIS.LOGIN}`;
    this.logger.debug(`GHIN LOGIN URL: ${urlLogin}`);
    try {
      const payload = {
        user: {
          email: this.configService.get('ghin.email'),
          password: this.configService.get('ghin.password'),
          remember_me: 'true',
        },
      };
      const response = await axios.post(urlLogin, payload);
      const token = response.data?.token;
      this.logger.debug(`GHIN TOKEN: ${token}`);
      const ttl = ms(`${this.configService.get('app.cacheTTL')}`) / 1000;
      await this.cacheManager.set(GHIN_CONST.CACHE_GHIN_TOKEN, token, { ttl });

      return token;
    } catch (error) {
      console.error(error);
      return null;
    }
  }

  /**
   *
   * @param scoreDto Dto post score with total score
   * @param scoreWithHoleDetails Dto post score  with holes details
   * @param isPostHoleByHole Post score hole by hole
   * @param isRetry Retry if Unauthorized
   * @returns
   */
  async postScoreToGHIN(
    scoreDto: GhinScoreDto,
    scoreWithHoleDetails: GhinScoreDto,
    isPostHoleByHole = true,
    isRetry = true,
    roundId = null
  ) {
    let urlPostScore = `${this.ghinEndpoint}${GHIN_APIS.POST_SCORE_TOTAL_SCORE}`;
    let urlPostScore9Holes = `${this.ghinEndpoint}${GHIN_APIS.POST_SCORE_9_HOLES}`;
    const urlPostScoreHBH = `${this.ghinEndpoint}${GHIN_APIS.POST_SCORE_HOLE_BY_HOLE}`;
    scoreDto.override_confirmation = isProduction ? false : true;
    if (this.isPostScore9Holes(scoreDto) && isRetry) {
      if (scoreDto.tee_set_side == 'F9') {
        scoreDto.front9_adjusted = scoreDto.adjusted_gross_score;
      } else {
        scoreDto.back9_adjusted = scoreDto.adjusted_gross_score;
      }
    }
    const queryParams = new URLSearchParams(scoreDto as any).toString();

    urlPostScore = `${urlPostScore}?${queryParams}`;
    urlPostScore9Holes = `${urlPostScore9Holes}?${queryParams}`;

    this.logger.log(`PARAMS POST SCORE:`);
    this.logger.log(JSON.stringify(scoreDto));
    try {
      let response = null;
      if (this.isPostScore9Holes(scoreDto)) {
        this.logger.log(`URL POST SCORE 9 HOLES: ${urlPostScore9Holes}`);
        response = await axios.post(urlPostScore9Holes, {}, await this.getRequestHeaderConfigs());
      } else {
        if (isPostHoleByHole) {
          response = await axios.post(urlPostScoreHBH, scoreWithHoleDetails, await this.getRequestHeaderConfigs());
        } else {
          this.logger.log(`URL POST TOTAL SCORE: ${urlPostScore}`);
          response = await axios.post(urlPostScore, {}, await this.getRequestHeaderConfigs());
        }
      }
      //save log score
      this.ghinScoreLogRepository
        .save({
          round_id: roundId || null,
          golfer_id: parseInt(scoreDto.golfer_id, 10),
          score_detail: scoreDto,
          score_with_hole_detail: scoreWithHoleDetails,
          is_post_hole_by_hole: isPostHoleByHole,
          response: response?.data?.score,
        })
        .catch((e) => e);
      return response.data.score;
    } catch (error) {
      if (error.response.status == 401 && isRetry) {
        await this.removeGhinToken();
        return await this.postScoreToGHIN(scoreDto, scoreWithHoleDetails, isPostHoleByHole, false);
      }
      this.logger.error(`POST SCORE TO GHIN FAILED:`);
      this.logger.error(error?.response?.data);
      this.logger.error(`URL POST SCORE: ${urlPostScore}`);
      // Switch to submit total score
      const errorTeeSetId = error?.response?.data?.errors?.tee_set_id;
      if (errorTeeSetId && errorTeeSetId[0].includes('post a total score') && isPostHoleByHole) {
        this.logger.debug(`RESUBMIT SCORE WITH TOTAL SCORE`);
        return await this.postScoreToGHIN(scoreDto, scoreWithHoleDetails, false, false);
      }
      // ReSubmit score if wrong side hole for 9 holes played.
      if (this.isPostScore9Holes(scoreDto)) {
        const errorTeeSetSide = error?.response?.data?.errors['tee_set_side'];
        if (errorTeeSetSide) {
          if (scoreDto.tee_set_side == 'F9') {
            scoreDto.tee_set_side = 'B9';
            scoreDto.back9_adjusted = scoreDto.front9_adjusted;
            delete scoreDto.front9_adjusted;
          } else {
            scoreDto.tee_set_side = 'F9';
            scoreDto.front9_adjusted = scoreDto.back9_adjusted;
            delete scoreDto.back9_adjusted;
          }

          this.logger.debug(`RESUBMIT SCORE FOR ROUND 9 HOLES TEE SIDE: ${scoreDto.tee_set_side}`);
          return await this.postScoreToGHIN(scoreDto, scoreWithHoleDetails, false, false);
        }
      }
      // save log score error
      this.ghinScoreLogRepository
        .save({
          roundId: roundId || null,
          golfer_id: parseInt(scoreDto.golfer_id, 10),
          score_detail: scoreDto,
          score_with_hole_detail: scoreWithHoleDetails,
          is_post_hole_by_hole: isPostHoleByHole,
          response: error,
        })
        .catch((e) => e);
      throw new BadRequestException(JSON.stringify(error?.response?.data) || error?.message);
    }
  }
  private isPostScore9Holes(scoreDto: GhinScoreDto) {
    ['F9', 'B9'].includes(scoreDto.tee_set_side);
    return false;
  }

  /**
   * getHandicapIndex
   *
   * @param userId
   * @returns
   */
  async getHandicapIndex(userId) {
    if (isValidId(userId)) {
      const user = await this.getUser(userId);
      return await this.getGolferHandicapIndex(user.ghin_id);
    }
    this.throwExceptionUserNotFound();
  }
  /**
   * requestAccessGHIN
   *
   * @param userId
   * @param requestAccessDto
   * @returns
   */
  async requestAccessGHIN(userId, requestAccessDto: RequestAccessGHINDto) {
    if (isValidId(userId)) {
      // check ghin_id has map with user
      const user = await this.userRepo.findOne({
        where: {
          ghin_id: requestAccessDto.ghin_id,
          ghin_gpa_status: true,
        },
      });
      if (user) {
        throw new BadRequestException({
          message: 'Handicap I.D. (GHIN #) already connected to TaylorMade Account.',
          errorCode: GHIN_ERROR_CODES.GHIN_ID_CONNECTED_WITH_OTHER_USER,
          ghin_id: requestAccessDto.ghin_id,
        });
      }
      const resultRequestGPA = await this.requestAccessToGHIN(requestAccessDto);
      if (resultRequestGPA.success) {
        // create request other user
        await this.userRepo.update(
          { ghin_id: requestAccessDto.ghin_id },
          { ghin_email: null, ghin_id: null, ghin_gpa_status: false }
        );
        await this.userRepo.update(
          { id: userId },
          {
            ghin_id: requestAccessDto.ghin_id,
            ghin_gpa_status: false,
            ghin_email: requestAccessDto.email?.toLowerCase()?.trim(),
          }
        );
      }
      return resultRequestGPA;
    }
    this.throwExceptionUserNotFound();
  }

  private throwExceptionUserNotFound() {
    throw new BadRequestException('User not found');
  }

  /**
   * revokeAccessToGHIN
   *
   * @param userId
   * @returns
   */
  async revokeAccessToGHIN(userId) {
    if (isValidId(userId)) {
      const user = await this.getUser(userId, false);
      await this.revokeUserAccessToGHIN(user.ghin_id);
      await this.userRepo.update({ id: userId }, { ghin_id: null, ghin_gpa_status: false, ghin_email: null });
      return { success: true };
    }
    this.throwExceptionUserNotFound();
  }

  /**
   * revokeUserAccessToGHIN
   *
   * @param ghinId
   * @param isRetry
   * @returns
   */
  async revokeUserAccessToGHIN(ghinId: string, isRetry = true) {
    let urlRevokeAccess = `${this.ghinEndpoint}${GHIN_APIS.USER_REVOKE_ACCESS}`;
    urlRevokeAccess = urlRevokeAccess.replace('GHIN_ID', ghinId);
    try {
      const response = await axios.delete(urlRevokeAccess, await this.getRequestHeaderConfigs());
      return response.data;
    } catch (error) {
      if (error.response.status == 401 && isRetry) {
        await this.removeGhinToken();
        return await this.revokeUserAccessToGHIN(ghinId, false);
      }
      console.error(error);
      throw new BadRequestException(JSON.stringify(error?.response.data) || error.message);
    }
  }

  /**
   * requestAccessToGHIN
   *
   * @param requestAccessDto
   * @param isRetry
   * @returns
   */
  async requestAccessToGHIN(requestAccessDto: RequestAccessGHINDto, isRetry = true) {
    let urlRequestAccess = `${this.ghinEndpoint}${GHIN_APIS.USER_REQUEST_ACCESS}`;
    urlRequestAccess = urlRequestAccess.replace('GHIN_ID', requestAccessDto.ghin_id);
    try {
      const response = await axios.post(
        urlRequestAccess,
        { email: requestAccessDto.email },
        await this.getRequestHeaderConfigs()
      );
      return response.data;
    } catch (error) {
      if (error.response.status == 401 && isRetry) {
        await this.removeGhinToken();
        return await this.requestAccessToGHIN(requestAccessDto, false);
      }
      console.error(error);
      throw new BadRequestException(JSON.stringify(error?.response.data) || error.message);
    }
  }

  /**
   * getGolferProfile
   *
   * @param userId
   * @returns
   */
  async getGolferProfile(userId) {
    if (isValidId(userId)) {
      const user = await this.getUser(userId);
      return await this.getGHINGolferProfile(user.ghin_id);
    }
    this.throwExceptionUserNotFound();
  }
  /**
   * getGHINScores
   *
   * @param userId
   * @returns
   */
  async getGHINScores(userId) {
    if (isValidId(userId)) {
      const user = await this.getUser(userId);
      return await this.getGolferGHINScores(user.ghin_id);
    }
    this.throwExceptionUserNotFound();
  }

  /**
   * getGHINAllScores
   *
   * @param userId
   * @param getAllScoreDto
   * @returns
   */
  async getGHINAllScores(userId, getAllScoreDto: GetAllScoreDto) {
    if (isValidId(userId)) {
      const user = await this.getUser(userId);
      getAllScoreDto.golfer_id = user.ghin_id;
      return await this.getAllScores(getAllScoreDto);
    }
    this.throwExceptionUserNotFound();
  }

  /**
   * getUser
   *
   * @param userId
   * @param isCheckGPAStatus
   * @returns
   */
  private async getUser(userId: any, isCheckGPAStatus = true) {
    const user = await this.userRepo.findOne({
      where: { id: userId },
      select: ['id', 'ghin_id', 'ghin_gpa_status', 'ghin_email'],
    });
    if (!user) {
      this.throwExceptionUserNotFound();
    }
    if (!user.ghin_id) {
      throw new UnauthorizedException({ message: `User don't have GHIN id`, errorCode: 'ERROR_USER_NOT_HAVE_GHIN_ID' });
    }
    if (!user.ghin_gpa_status && isCheckGPAStatus) {
      throw new UnauthorizedException({
        message: `User need to acknowledge request golfer product access!`,
        ghin_id: user.ghin_id,
        ghinEmail: user.ghin_email,
        errorCode: GHIN_ERROR_CODES.ERROR_GPA_NOT_ACCEPTED,
      });
    }
    return user;
  }

  /**
   * getGolferGHINScores
   *
   * @param ghinId
   * @param isRetry
   * @returns
   */
  async getGolferGHINScores(ghinId: string, isRetry = true) {
    let urlGolferScoreList = `${this.ghinEndpoint}${GHIN_APIS.GET_GOLFER_SCORE}`;
    urlGolferScoreList = urlGolferScoreList.replace('GHIN_ID', ghinId);
    try {
      const response = await axios.get(urlGolferScoreList, await this.getRequestHeaderConfigs());
      return response.data;
    } catch (error) {
      if (error.response.status == 401 && isRetry) {
        await this.removeGhinToken();
        return await this.getGolferGHINScores(ghinId, false);
      }
      console.error(error);
      throw new BadRequestException(`Can't get scores!`);
    }
  }

  /**
   * getAllScores
   *
   * @param getAllScoreDto
   * @param isRetry
   * @returns
   */
  async getAllScores(getAllScoreDto: GetAllScoreDto, isRetry = true) {
    let urlGetAllScore = `${this.ghinEndpoint}${GHIN_APIS.GET_GOLFER_ALL_SCORES}`;
    const queryParams = new URLSearchParams(getAllScoreDto as any).toString();
    urlGetAllScore = `${urlGetAllScore}?${queryParams}`;
    try {
      const response = await axios.get(urlGetAllScore, await this.getRequestHeaderConfigs());
      return response.data;
    } catch (error) {
      if (error.response.status == 401 && isRetry) {
        await this.removeGhinToken();
        return await this.getAllScores(getAllScoreDto, false);
      }
      console.error(error);
      return [];
    }
  }
  /**
   * getGolferGHINCourseRecent
   *
   * @param ghinId
   * @param isRetry
   * @returns
   */
  async getGolferGHINCourseRecent(userId: number, isRetry = true) {
    const user = await this.getUser(userId);
    const listScores = 40;
    let urlGolferCourseRecent = `${this.ghinEndpoint}${GHIN_APIS.GET_GOLFER_COURSE_RECENT}${listScores}`;
    urlGolferCourseRecent = urlGolferCourseRecent.replace('GHIN_ID', user.ghin_id);
    console.log({ urlGolferCourseRecent });

    try {
      const response = await axios.get(urlGolferCourseRecent, await this.getRequestHeaderConfigs());
      return response.data;
    } catch (error) {
      if (error.response.status == 401 && isRetry) {
        await this.removeGhinToken();
        return await this.getGolferGHINCourseRecent(userId, false);
      }
      console.error(error);
      throw new BadRequestException(error?.response?.data?.error || error.message);
    }
  }

  /**
   * getGHINGolferProfile
   *
   * @param ghinId
   * @param isRetry
   * @returns
   */
  async getGHINGolferProfile(ghinId: string, isRetry = true) {
    let urlGolferProfile = `${this.ghinEndpoint}${GHIN_APIS.GET_GOLFER_INFO}`;
    urlGolferProfile = urlGolferProfile.replace('GHIN_ID', ghinId);
    try {
      const response = await axios.get(urlGolferProfile, await this.getRequestHeaderConfigs());
      if (response.data?.golfers.length > 0) {
        this.logger.log(response.data);
        return response.data?.golfers[0];
      } else {
        return {};
      }
    } catch (error) {
      if (error.response.status == 401 && isRetry) {
        await this.removeGhinToken();
        return await this.getGHINGolferProfile(ghinId, false);
      }
      console.error(error);
      throw new BadRequestException({
        message: `Can't get golfer profile!`,
        errorCode: GHIN_ERROR_CODES.ERROR_NOT_FOUND_GOLFER_PROFILE,
      });
    }
  }

  /**
   * getGolferHandicapIndex
   *
   * @param ghinId
   * @param isRetry
   * @returns
   */
  async getGolferHandicapIndex(ghinId: string, isRetry = true) {
    let urlGetHandicapIndex = `${this.ghinEndpoint}${GHIN_APIS.GET_GOLFER_HANDICAP}`;
    urlGetHandicapIndex = urlGetHandicapIndex.replace('GHIN_ID', ghinId);
    try {
      const response = await axios.get(urlGetHandicapIndex, await this.getRequestHeaderConfigs());
      if (response.data.handicap_revisions.length > 0) {
        this.logger.log(response.data);
        return response.data?.handicap_revisions[0];
      } else {
        return {};
      }
    } catch (error) {
      if (error.response.status == 401 && isRetry) {
        await this.removeGhinToken();
        return await this.getGolferHandicapIndex(ghinId, false);
      }
      console.error(error);
      throw new BadRequestException({
        message: `Can't get handicap index!`,
        errorCode: GHIN_ERROR_CODES.ERROR_GET_HANDICAP_INDEX,
      });
    }
  }

  /**
   * deleteScore
   *
   * @param ghinRoundId
   * @param isRetry
   * @returns
   */
  async deleteScore(ghinRoundId: string, isRetry = true) {
    let urlDeleteScore = `${this.ghinEndpoint}${GHIN_APIS.GET_SCORE_DETAIL}`;
    urlDeleteScore = urlDeleteScore.replace('GHIN_ROUND_ID', ghinRoundId);
    try {
      const response = await axios.delete(urlDeleteScore, await this.getRequestHeaderConfigs());
      return response.data;
    } catch (error) {
      if (error.response.status == 401 && isRetry) {
        await this.removeGhinToken();
        return await this.deleteScore(ghinRoundId, false);
      }
      console.log(error);
      console.error(error?.response.data);
      throw new BadRequestException(`Can't delete score!`);
    }
  }

  /**
   * getScoreDetail
   *
   * @param ghinRoundId
   * @param isRetry
   * @returns
   */
  async getScoreDetail(ghinRoundId: string, isRetry = true) {
    let urlGetScoreDetail = `${this.ghinEndpoint}${GHIN_APIS.GET_SCORE_DETAIL}`;
    urlGetScoreDetail = urlGetScoreDetail.replace('GHIN_ROUND_ID', ghinRoundId);
    try {
      const response = await axios.get(urlGetScoreDetail, await this.getRequestHeaderConfigs());
      return response.data;
    } catch (error) {
      if (error.response.status == 401 && isRetry) {
        await this.removeGhinToken();
        return await this.getScoreDetail(ghinRoundId, false);
      }
      console.error(error?.response.data);
      throw new BadRequestException(`Can't get score!`);
    }
  }

  /**
   * updateScore
   *
   * @param updateScoreGHINDto
   * @param isRetry
   * @returns
   */
  async updateScore(updateScoreGHINDto: UpdateScoreGhinDto, isRetry = true) {
    let urlGetScoreDetail = `${this.ghinEndpoint}${GHIN_APIS.UPDATE_SCORE}`;
    urlGetScoreDetail = urlGetScoreDetail.replace('GHIN_ROUND_ID', updateScoreGHINDto.ghin_round_id);
    try {
      const ghinScoreDetail = await this.getScoreDetail(updateScoreGHINDto.ghin_round_id);
      let score: any = {
        golfer_id: '',
        course_id: '',
        tee_set_id: '',
        course_name: '',
        played_at: '',
        number_of_holes: '',
        number_of_played_holes: '',
        gender: '',
        adjusted_gross_score: '',
        score_type: '',
      };
      const keys = Object.keys(score);
      score = _.extend(score, _.pick(ghinScoreDetail.scores, keys));
      score.adjusted_gross_score = updateScoreGHINDto.adjusted_gross_score;
      score.course_id = updateScoreGHINDto.ghin_course_id;
      score.course_name = updateScoreGHINDto.ghin_course_name;
      score.tee_set_id = updateScoreGHINDto.ghin_tee_set_id;
      score.played_at = updateScoreGHINDto.played_at;
      score.score_type = GHIN_SCORE_TYPES.HOME;

      const response = await axios.patch(urlGetScoreDetail, { score }, await this.getRequestHeaderConfigs());
      return response.data;
    } catch (error) {
      if (error.response.status == 401 && isRetry) {
        await this.removeGhinToken();
        return await this.updateScore(updateScoreGHINDto, false);
      }
      console.error(error?.response.data);
      throw new BadRequestException(`Can't update score!`);
    }
  }

  /**
   * searchGHINCourses
   *
   * @param searchDto GhinSearchGeo
   * @param isRetry
   * @returns
   */
  async searchGHINCourses(searchDto: GhinSearchGeo, isRetry = true) {
    let urlSearchGhinByGEO = `${this.ghinEndpoint}${GHIN_APIS.SEARCH_COURSE_BY_GEO}`;
    const queryParams = new URLSearchParams(searchDto as any).toString();
    urlSearchGhinByGEO = `${urlSearchGhinByGEO}?${queryParams}`;
    try {
      const response = await axios.get(urlSearchGhinByGEO, await this.getRequestHeaderConfigs());
      return response.data;
    } catch (error) {
      if (error.response.status == 401 && isRetry) {
        await this.removeGhinToken();
        return await this.searchGHINCourses(searchDto, false);
      }
      console.error(error);
      return [];
    }
  }

  /**
   * searchGHINCoursesByGEO
   *
   * @param latitude
   * @param longitude
   * @returns
   */
  async searchGHINCoursesByGEO(latitude: number, longitude: number) {
    const ghinSearchDto: GhinSearchGeo = {
      currentLatitude: latitude,
      currentLongitude: longitude,
      distance: GHIN_CONST.RADIUS_DISTANCE_SEARCH,
      distanceUnitOfMeasure: GHIN_CONST.SEARCH_DISTANCE_UNITS,
      courseStatus: 'Active',
      facilityStatus: 'Active',
    };
    return await this.searchGHINCourses(ghinSearchDto);
  }

  /**
   * searchGHINCoursesByName
   *
   * @param courseName
   * @param isRetry
   * @returns
   */
  async searchGHINCoursesByName(courseName: string, isRetry = true) {
    const searchDto: GhinSearchName = {
      name: courseName,
      facility_status: 'Active',
      course_status: 'Active',
      include_tee_sets: true,
    };
    let urlSearchGhinByName = `${this.ghinEndpoint}${GHIN_APIS.SEARCH_COURSE_BY_NAME}`;
    const queryParams = new URLSearchParams(searchDto as any).toString();
    urlSearchGhinByName = `${urlSearchGhinByName}?${queryParams}`;
    try {
      const response = await axios.get(urlSearchGhinByName, await this.getRequestHeaderConfigs());
      return response.data;
    } catch (error) {
      if (error.response.status == 401 && isRetry) {
        await this.removeGhinToken();
        return await this.searchGHINCoursesByName(courseName, false);
      }
      console.error(error);
      return [];
    }
  }

  /**
   * getGHINCourseDetail
   *
   * @param ghinCourseId
   * @param isRetry
   * @returns
   */
  async getGHINCourseDetail(ghinCourseId: string, isRetry = true) {
    let urlGhinCourseDetail = `${this.ghinEndpoint}${GHIN_APIS.GET_COURSE_DETAIL}`;
    urlGhinCourseDetail = urlGhinCourseDetail.replace('COURSE_ID', ghinCourseId);
    try {
      const response = await axios.get(urlGhinCourseDetail, await this.getRequestHeaderConfigs());
      return response.data;
    } catch (error) {
      if (error.response.status == 401 && isRetry) {
        await this.removeGhinToken();
        return await this.getGHINCourseDetail(ghinCourseId, false);
      }
      console.error(error);
      throw new BadRequestException(error?.response?.data?.error || error.message);
    }
  }

  /**
   * getGHINCourseTeeDetail
   *
   * @param teeSetId
   * @param isRetry
   * @returns
   */
  async getGHINCourseTeeDetail(teeSetId: string, isRetry = true) {
    let urlGhinCourseTeeDetail = `${this.ghinEndpoint}${GHIN_APIS.GET_TEE_DETAIL}`;
    urlGhinCourseTeeDetail = urlGhinCourseTeeDetail.replace('TEE_ID', teeSetId);
    try {
      const response = await axios.get(urlGhinCourseTeeDetail, await this.getRequestHeaderConfigs());
      return response.data;
    } catch (error) {
      if (error.response.status == 401 && isRetry) {
        await this.removeGhinToken();
        return await this.getGHINCourseTeeDetail(teeSetId, false);
      }
      console.error(error);
      return null;
    }
  }

  /**
   * getGHINCourseHandicap
   *
   * @param userId
   * @param courseId
   * @param isRetry
   * @returns
   */
  async getGHINCourseHandicap(userId: string, courseId: string, isRetry = true) {
    let urlGhinCourseHandicap = `${this.ghinEndpoint}${GHIN_APIS.GET_COURSE_HANDICAP}`;
    const user = await this.getUser(userId);
    const golferProfile = await this.getGHINGolferProfile(user.ghin_id);
    if (!golferProfile || isEmpty(golferProfile)) {
      throw new BadRequestException({
        message: `Can't get golfer profile!`,
        errorCode: GHIN_ERROR_CODES.ERROR_NOT_FOUND_GOLFER_PROFILE,
      });
    }
    const handicapIndex = +golferProfile.handicap_index;
    const payload: GHINCourseHandicap = {
      golfer_id: user.ghin_id,
      course_id: courseId,
      handicap_index: isNaN(handicapIndex) ? '' : `${handicapIndex}`,
    };
    urlGhinCourseHandicap += new URLSearchParams(payload as any);
    this.logger.log(`URL GHIN COURSE HANDICAP: ${urlGhinCourseHandicap}`);
    try {
      const response = await axios.get(urlGhinCourseHandicap, await this.getRequestHeaderConfigs());
      return response.data;
    } catch (error) {
      if (error.response.status == 401 && isRetry) {
        await this.removeGhinToken();
        return await this.getGHINCourseHandicap(userId, courseId, false);
      }
      console.error(error);
      return null;
    }
  }

  /**
   * processRequestGPA
   *
   * @param payload
   * @returns
   */
  async processRequestGPA(payload: any) {
    // const payload =
    // {
    //   object: { id: '12054793', user: { user_id: '2878485' }, golfer_product_access: { gpa_status: 'approved' } },
    //   object_type: 'golfer',
    //   action: 'update',
    //   webhook_key: '7Fa3Qw59gV5g59k7zLQUyw',
    //   webhook_id: 85245970,
    //   webhook_sent_at: '2023-05-10T03:53:26.178Z',
    //   environment: 'staging',
    // };
    const result = await this.updateUserGhinGPAStatus(payload);
    return result;
  }

  /**
   * updateUserGhinGPAStatus
   *
   * @param payload
   * @returns
   */
  private async updateUserGhinGPAStatus(payload: any) {
    const isUserAccepted = await this.userRepo.count({
      where: { ghin_id: payload?.object?.id, ghin_gpa_status: true },
    });
    if (isUserAccepted && isUserAccepted > 0) {
      return { success: false, message: `Have user approved GPA` };
    }
    const users = await this.userRepo.find({
      where: { ghin_id: payload?.object?.id, ghin_gpa_status: false },
      select: ['id', 'email', 'ghin_id'],
      order: { updated_at: 'DESC' },
      take: 1,
    });
    let user = null;
    if (users && users.length > 0) {
      user = users[0];
    }
    if (user && payload?.object?.golfer_product_access?.gpa_status == 'approved' && payload?.object_type == 'golfer') {
      this.logger.log(`UPDATE GHIN GPA STATUS: true USER: ${user.id}`);
      await this.userRepo.update({ id: user.id }, { ghin_gpa_status: true });
      try {
        await this.myTMService.triggerAddPointUSGA(user.id);
        await this.updateGhinNumberJobQueue.add(
          PROCESS_QUEUE_NAMES.SYNC_UPDATE_GHIN_NUMBER,
          { userId: user.id },
          OPTIONS_JOB_DEFAULT
        );
      } catch (error) {
        console.log(error);
      }
      return { success: true };
    }
    return { success: false, message: 'NotFound User' };
  }

  /**
   * getWebhookSettings
   *
   * @param isRetry
   * @returns
   */
  async getWebhookSettings(isRetry = true) {
    const urlGhinWebhookSettings = `${this.ghinEndpoint}${GHIN_APIS.USER_WEB_HOOK_SETTINGS}`;
    try {
      const response = await axios.get(urlGhinWebhookSettings, await this.getRequestHeaderConfigs());
      return response.data;
    } catch (error) {
      if (error.response.status == 401 && isRetry) {
        await this.removeGhinToken();
        return await this.getWebhookSettings(false);
      }
      console.error(error);
      return null;
    }
  }

  /**
   * updateWebhookSettings
   *
   * @param payload
   * @param isRetry
   * @returns
   */
  async updateWebhookSettings(payload: any, isRetry = true) {
    const urlGhinWebhookSettings = `${this.ghinEndpoint}${GHIN_APIS.USER_WEB_HOOK_SETTINGS}`;
    try {
      const response = await axios.patch(urlGhinWebhookSettings, payload, await this.getRequestHeaderConfigs());
      return response.data;
    } catch (error) {
      if (error.response.status == 401 && isRetry) {
        await this.removeGhinToken();
        return await this.updateWebhookSettings(payload, false);
      }
      console.error(error);
      throw new BadRequestException(JSON.stringify(error.response.data) || error.message);
    }
  }

  /**
   * deleteWebhookSettings
   *
   * @param isRetry
   * @returns
   */
  async deleteWebhookSettings(isRetry = true) {
    const urlGhinWebhookSettings = `${this.ghinEndpoint}${GHIN_APIS.USER_WEB_HOOK_SETTINGS}`;
    try {
      const response = await axios.delete(urlGhinWebhookSettings, await this.getRequestHeaderConfigs());
      return response.data;
    } catch (error) {
      if (error.response.status == 401 && isRetry) {
        await this.removeGhinToken();
        return await this.deleteWebhookSettings(false);
      }
      console.error(error);
      throw new BadRequestException(JSON.stringify(error.response.data) || error.message);
    }
  }

  /**
   * testWebhookSettings
   *
   * @param type
   * @param isRetry
   * @returns
   */
  async testWebhookSettings(type: string, isRetry = true) {
    const urlGhinWebhookSettingsTest = `${this.ghinEndpoint}${GHIN_APIS.USER_WEB_HOOK_SETTINGS_TEST}?type=${type}`;
    try {
      const response = await axios.get(urlGhinWebhookSettingsTest, await this.getRequestHeaderConfigs());
      return response.data;
    } catch (error) {
      if (error.response.status == 401 && isRetry) {
        await this.removeGhinToken();
        return await this.testWebhookSettings(type, false);
      }
      console.error(error);
      throw new BadRequestException(JSON.stringify(error.response.data) || error.message);
    }
  }

  /**
   * mappingIGolfGhinCourse
   *
   * @param iGolfCourse IGolfCourse
   * @returns
   */
  async mappingIGolfGhinCourse(iGolfCourse: IGolfCourse) {
    const iGolfGhinCourse = await this.findOne({ igolf_course_id: iGolfCourse.igolf_course_id?.trim() });
    if (iGolfGhinCourse) {
      const lstTeeSetGHIN = await this.ghinTeeRatingService.findBy({ ghin_course_id: iGolfGhinCourse.ghin_course_id });
      if (!lstTeeSetGHIN || isEmpty(lstTeeSetGHIN)) {
        throw new BadRequestException({
          message: 'Tee set rating for course not found!',
          errorCode: GHIN_ERROR_CODES.ERROR_MAP_TEE_SET_RATINGS,
          ghinCourseId: null,
        });
      }
      const teeSetRating = lstTeeSetGHIN.find((tee) => {
        const iGolfGender = ['men', 'male', `men's`].includes(iGolfCourse.gender.toLowerCase().trim())
          ? 'male'
          : 'female';
        return (
          tee.tee_set_rating_name?.toLowerCase() == iGolfCourse.tee?.toLowerCase() &&
          tee.gender.toLowerCase() == iGolfGender
        );
      });
      if (!teeSetRating) {
        throw new BadRequestException({
          message: 'Tee set rating for course not found!',
          errorCode: GHIN_ERROR_CODES.ERROR_MAP_TEE_SET_RATINGS,
          ghinCourseId: iGolfGhinCourse.ghin_course_id,
        });
      }
      return { igolf_ghin: iGolfGhinCourse, tee_set_rating: teeSetRating };
    }
    // eslint-disable-next-line @typescript-eslint/no-var-requires
    const FlexSearch = require('flexsearch');
    const searchIndex = new FlexSearch.Index('match');
    const iGolfCourseDetail = await this.threePartyCourseService.getCourseDetail(iGolfCourse.igolf_course_id);
    const ghinSearchDto: GhinSearchGeo = {
      currentLatitude: iGolfCourseDetail.latitude,
      currentLongitude: iGolfCourseDetail.longitude,
      distance: this.configService.get('ghin.radiusDistanceSearchUnitMiles'),
      distanceUnitOfMeasure: GHIN_CONST.SEARCH_DISTANCE_UNITS,
      courseStatus: 'Active',
      facilityStatus: 'Active',
    };
    const ghinCourses = await this.searchGHINCourses(ghinSearchDto);
    if (ghinCourses.length == 0) {
      throw new BadRequestException({
        message: 'GHIN course not found!',
        errorCode: GHIN_ERROR_CODES.ERROR_MAP_GHIN_COURSE,
        ghinCourseId: null,
      });
    }
    for (const idx in ghinCourses) {
      searchIndex.add(idx, ghinCourses[idx].FullName.toLowerCase());
    }
    const resultIds = await searchIndex.search({
      query: iGolfCourseDetail.courseName?.trim().toLowerCase(),
      // When suggestion is enabled all results will be filled up (until limit, default 1000) with similar matches ordered by relevance.
      suggest: true,
    });
    let idxBets = 0;
    if (resultIds.length > 0) {
      idxBets = resultIds[0];
    }
    const courseGHINBase = ghinCourses[idxBets];
    const courseGHINDetail = await this.getGHINCourseDetail(courseGHINBase.CourseID);
    const ghinTeeRatingActive = courseGHINDetail.TeeSets.filter((t) => t.TeeSetState.toLowerCase() == 'active');
    let teeMap = null;
    if (ghinTeeRatingActive.length > 0) {
      teeMap = ghinTeeRatingActive.find((tee) => {
        const iGolfGender = ['men', 'male', `men's`].includes(iGolfCourse.gender.toLowerCase().trim())
          ? 'male'
          : 'female';
        return (
          tee.TeeSetRatingName.toLowerCase().trim().includes(iGolfCourse.tee.toLowerCase().trim()) &&
          tee.Gender.toLowerCase().trim() == iGolfGender
        );
      });
    }
    if (!teeMap) {
      throw new BadRequestException({
        message: 'Tee set rating for course not found!',
        ghinCourseId: courseGHINDetail.CourseId,
        errorCode: GHIN_ERROR_CODES.ERROR_MAP_TEE_SET_RATINGS,
      });
    }
    if (courseGHINBase.DistanceMiles >= 4) {
      throw new BadRequestException({
        message: 'GHIN course not found!',
        errorCode: GHIN_ERROR_CODES.ERROR_MAP_GHIN_COURSE,
        ghinCourseId: null,
      });
    }
    const ghinDto: CreateGhinDto = {
      igolf_course_id: iGolfCourse.igolf_course_id,
      ghin_course_id: courseGHINBase.CourseID,
      ghin_course_name: courseGHINDetail.CourseName,
      ghin_course_full_name: courseGHINBase.FullName,
    };
    const igolfGhin = await this.create(ghinDto);
    let teeSetRatingMapping = null;
    for (const teeRatting of ghinTeeRatingActive) {
      teeRatting['ghin_course_id'] = courseGHINBase.CourseID;
      teeRatting['tee_set_rating_name'] = teeRatting.TeeSetRatingName;
      teeRatting['ghin_tee_rating_id'] = teeRatting.TeeSetRatingId;
      teeRatting['gender'] = teeRatting.Gender;
      teeRatting['holes_number'] = teeRatting.HolesNumber;
      teeRatting['total_yard_age'] = teeRatting.TotalYardage;
      teeRatting['total_par'] = teeRatting.TotalPar;
      teeRatting['tee_rating_detail'] = JSON.stringify(teeRatting);

      const teeRattingDto = plainToClass(GhinTeeRating, teeRatting);
      const result = await this.ghinTeeRatingService.create(teeRattingDto);
      if (teeRatting.TeeSetRatingId == teeMap.TeeSetRatingId) {
        teeSetRatingMapping = result;
      }
    }

    return { igolf_ghin: igolfGhin, tee_set_rating: teeSetRatingMapping };
  }

  /**
   *
   * @param GHINCourseDto
   * @returns
   */
  async matchingCourseIGolf(GHINCourseDto: MatchingGHINCourseDto) {
    const iGolfGhinCourse = await this.findOne({ ghin_course_id: GHINCourseDto.ghin_course_id?.trim() });
    if (iGolfGhinCourse) {
      return await this.matchingCourseInOC(iGolfGhinCourse, GHINCourseDto);
    }
    const ghinCourseDetail = await this.getGHINCourseDetail(GHINCourseDto.ghin_course_id);
    if (!ghinCourseDetail) {
      this.throwGHINCourseNotFound();
    }

    // eslint-disable-next-line @typescript-eslint/no-var-requires
    const FlexSearch = require('flexsearch');
    const searchIndex = new FlexSearch.Index('match');
    const payloadSearchCourse = {
      active: 1,
      radius: 5,
      referenceLongitude: ghinCourseDetail.Facility.GeoLocationLongitude,
      referenceLatitude: ghinCourseDetail.Facility.GeoLocationLatitude,
    };
    const listCourseIGolf = await this.threePartyCourseService.searchCourseFromGhin(payloadSearchCourse, 'GHIN');

    if (!listCourseIGolf || listCourseIGolf?.totalCourses == 0) {
      this.throwGHINCourseNotFound();
    }
    const courseList = listCourseIGolf.courseList;
    for (const idx in courseList) {
      searchIndex.add(idx, courseList[idx].courseName.toLowerCase());
    }
    const resultIds = await searchIndex.search({
      query: `${ghinCourseDetail?.Facility.FacilityName} - ${ghinCourseDetail?.CourseName}`?.trim().toLowerCase(),
      suggest: true,
    });
    let idxBets = 0;
    if (resultIds.length > 0) {
      idxBets = resultIds[0];
    }
    const courseIGolfBase = courseList[idxBets];
    const [courseIGolfDetail, teesIGolfDetail] = await Promise.all([
      this.threePartyCourseService.getCourseDetail(courseIGolfBase.id_course),
      this.threePartyCourseService.getCourseTeeDetail(courseIGolfBase.id_course),
    ]);

    if (!courseIGolfDetail || !teesIGolfDetail) {
      this.throwIGolfCourseNotFound(GHINCourseDto.ghin_course_id);
    }
    const ghinTeeRating = ghinCourseDetail?.TeeSets?.find((tee) => tee.TeeSetRatingId == GHINCourseDto.ghin_tee_set_id);

    const ghinTeeRatingActive = ghinCourseDetail.TeeSets.filter((t) => t.TeeSetState.toLowerCase() == 'active');
    let teeMap = null;
    const teesList = teesIGolfDetail.teesList;
    teeMap = teesList.find((tee) => {
      const iGolfGender = ['men', 'male', `men's`, 'mens'].includes(tee.gender.toLowerCase().trim())
        ? 'male'
        : 'female';
      const lstHoles = tee?.ydsHole?.filter((yard) => yard > 0);
      return (
        ghinTeeRating.TeeSetRatingName.toLowerCase().trim().includes(tee.teeName.toLowerCase().trim()) &&
        ghinTeeRating.Gender.toLowerCase().trim() == iGolfGender &&
        ghinTeeRating.HolesNumber == lstHoles.length
      );
    });
    if (!teeMap) {
      throw new BadRequestException({
        message: 'Tee set rating for course not found!',
        errorCode: GHIN_ERROR_CODES.ERROR_MAP_TEE_SET_RATINGS,
        ghinCourseId: GHINCourseDto.ghin_course_id,
      });
    }
    // Check distance
    const locationIGolfCourse = { latitude: courseIGolfBase.latitude, longitude: courseIGolfBase.longitude };
    const locationGhinCourse = {
      latitude: payloadSearchCourse.referenceLatitude,
      longitude: payloadSearchCourse.referenceLongitude,
    };
    const distance = getDistance(locationIGolfCourse, locationGhinCourse) / 1000; // meter to km
    if (distance >= 4.5) {
      throw new BadRequestException({
        message: 'iGolf course not found!',
        errorCode: GHIN_ERROR_CODES.ERROR_MAP_IGOLF_COURSE,
        ghinCourseId: null,
      });
    }
    const ghinDto: CreateGhinDto = {
      igolf_course_id: courseIGolfBase.id_course,
      ghin_course_id: ghinCourseDetail.CourseId,
      ghin_course_name: ghinCourseDetail.CourseName,
      ghin_course_full_name: `${ghinCourseDetail.Facility.FacilityName} - ${ghinCourseDetail.CourseName}`,
    };
    try {
      await this.create(ghinDto);
      await Promise.all(
        ghinTeeRatingActive.map(async (teeRatting) => {
          teeRatting['ghin_course_id'] = ghinCourseDetail.CourseId;
          teeRatting['tee_set_rating_name'] = teeRatting.TeeSetRatingName;
          teeRatting['ghin_tee_rating_id'] = teeRatting.TeeSetRatingId;
          teeRatting['gender'] = teeRatting.Gender;
          teeRatting['holes_number'] = teeRatting.HolesNumber;
          teeRatting['total_yard_age'] = teeRatting.TotalYardage;
          teeRatting['total_par'] = teeRatting.TotalPar;
          teeRatting['tee_rating_detail'] = JSON.stringify(teeRatting);

          const teeRattingDto = plainToClass(GhinTeeRating, teeRatting);
          await this.ghinTeeRatingService.create(teeRattingDto);
        })
      );
    } catch (error) {
      await this.iGolfGhinRepository.delete({ ghin_course_id: ghinCourseDetail.CourseId });
      await this.ghinTeeRatingService.remove({ ghin_course_id: ghinCourseDetail.CourseId });
    }

    return {
      igolf_course_id: courseIGolfBase.id_course,
      teeName: teeMap.teeName,
      course_name: courseIGolfBase.courseName,
    };
  }

  /**
   * matchingCourseInOC
   *
   * @param iGolfGhinCourse
   * @param GHINCourseDto
   * @returns
   */
  async matchingCourseInOC(iGolfGhinCourse, GHINCourseDto) {
    const igolfCourseId = iGolfGhinCourse.igolf_course_id;
    const teeSetRating = await this.ghinTeeRatingService.findOne({
      ghin_course_id: iGolfGhinCourse.ghin_course_id,
      ghin_tee_rating_id: GHINCourseDto.ghin_tee_set_id,
    });
    if (!teeSetRating) {
      throw new BadRequestException({
        message: 'Tee set rating for course not found!',
        errorCode: GHIN_ERROR_CODES.ERROR_MAP_TEE_SET_RATINGS,
        ghinCourseId: iGolfGhinCourse.ghin_course_id,
      });
    }
    const iGolfCourseDetail = await this.threePartyCourseService.getCourseDetail(igolfCourseId);
    const iGolfTeeDetail = await this.threePartyCourseService.getCourseTeeDetail(igolfCourseId);
    if (iGolfTeeDetail) {
      const { teesList } = iGolfTeeDetail;
      let teeMap = null;
      const ghinTeeDetail = JSON.parse(teeSetRating.tee_rating_detail);
      teeMap = teesList.find((tee) => {
        const iGolfGender = ['men', 'male', `men's`, 'mens'].includes(tee.gender.toLowerCase().trim())
          ? 'male'
          : 'female';
        const lstHoles = tee?.ydsHole?.filter((yard) => yard > 0);
        return (
          ghinTeeDetail.TeeSetRatingName.toLowerCase().trim().includes(tee.teeName.toLowerCase().trim()) &&
          ghinTeeDetail.Gender.toLowerCase().trim() == iGolfGender &&
          ghinTeeDetail.HolesNumber == lstHoles.length
        );
      });
      if (!teeMap) {
        throw new BadRequestException({
          message: 'Tee set rating for course not found!',
          errorCode: GHIN_ERROR_CODES.ERROR_MAP_TEE_SET_RATINGS,
          ghinCourseId: iGolfGhinCourse.ghin_course_id,
        });
      }
      return { igolf_course_id: igolfCourseId, teeName: teeMap.teeName, course_name: iGolfCourseDetail.courseName };
    } else {
      this.throwIGolfCourseNotFound(iGolfGhinCourse.ghin_course_id);
    }
  }

  private throwIGolfCourseNotFound(ghin_course_id: any) {
    throw new BadRequestException({
      message: 'The iGolf course not found!',
      errorCode: GHIN_ERROR_CODES.ERROR_MAP_IGOLF_COURSE,
      ghinCourseId: ghin_course_id,
    });
  }

  throwGHINCourseNotFound() {
    throw new BadRequestException({
      message: 'GHIN course not found!',
      errorCode: GHIN_ERROR_CODES.ERROR_MAP_GHIN_COURSE,
      ghinCourseId: null,
    });
  }

  create(createGhinDto: CreateGhinDto) {
    return this.iGolfGhinRepository.save(this.iGolfGhinRepository.create(createGhinDto));
  }

  async findAll(pagingOptions: IPaginationOptions, options?: any) {
    const queryBuilderPaging = this.iGolfGhinRepository.createQueryBuilder('igolf_ghin_courses');
    if (options) {
      queryBuilderPaging.where(options);
    }
    queryBuilderPaging.orderBy('created_at', 'DESC');
    let dataPaging = await paginateRaw(queryBuilderPaging, pagingOptions);
    dataPaging = transformDataPaging(dataPaging);
    return dataPaging;
  }

  findOne(field: any) {
    return this.iGolfGhinRepository.findOneBy(field);
  }

  update(field: any, updateGhinDto: UpdateGhinDto) {
    return this.iGolfGhinRepository.update(field, updateGhinDto);
  }

  remove(field: any) {
    return this.iGolfGhinRepository.delete(field);
  }
}
