import { Controller, Get } from '@nestjs/common';
import { HealthService } from './health.service';

@Controller('health')
export class HealthController {
  constructor(private healthService: HealthService) {}

  @Get('status')
  async status(): Promise<any> {
    const databasePingStatus = await this.healthService.pingDatabaseConnection();
    const redisPingStatus = await this.healthService.pingRedisConnection();
    return {
      healthy: databasePingStatus && redisPingStatus,
      databaseEstablished: databasePingStatus,
      redisPingStatus: redisPingStatus,
    };
  }
}
