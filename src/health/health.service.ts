import { CACHE_MANAGER, Inject, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Cache } from 'cache-manager';
import { Connection } from 'typeorm';

// const DEFAULT_PING_TIMEOUT = 30000;

@Injectable()
export class HealthService {
  private readonly logger = new Logger(HealthService.name);
  constructor(
    private readonly config: ConfigService,
    private connection: Connection,
    @Inject(CACHE_MANAGER) private cacheManager: Cache
  ) {
    this.config = config;
  }

  async pingDatabaseConnection() {
    const queryRunner = this.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.release();
    return true;
  }

  async pingRedisConnection() {
    try {
      await this.cacheManager.set('TEST_CONNECTION', 1);
      await this.cacheManager.get('TEST_CONNECTION');
      await this.cacheManager.del('TEST_CONNECTION');
      return true;
    } catch (e) {
      return false;
    }
  }
}
