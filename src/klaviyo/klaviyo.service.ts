import { InjectQueue } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Queue } from 'bull';
import { KLAVIYO_TRACK_PROCESS_NAME, KLAVIYO_TRACK_QUEUE_NAME } from 'src/workers/klaviyo/klaviyo.constant';

export enum KlaviyoTrackEvents {}

@Injectable()
export class KlaviyoService {
  private readonly logger = new Logger(KlaviyoService.name);
  constructor(
    private readonly config: ConfigService,
    @InjectQueue(KLAVIYO_TRACK_QUEUE_NAME) private klaviyoTrackQueue: Queue
  ) {}
  async track(email: string, event: KlaviyoTrackEvents, properties: any = {}, customerProperties: any = {}) {
    try {
      return this.klaviyoTrackQueue.add(KLAVIYO_TRACK_PROCESS_NAME, {
        event,
        email,
        properties,
        customerProperties: {
          $email: email,
          ...customerProperties,
        },
      });
    } catch (e) {
      this.logger.error(e);
    }
  }

  async trackWithMyTM(url: string, body: any) {
    try {
      return this.klaviyoTrackQueue.add(KLAVIYO_TRACK_PROCESS_NAME, {
        url,
        body,
        withMyTM: true,
      });
    } catch (e) {
      this.logger.error(e);
    }
  }
}
