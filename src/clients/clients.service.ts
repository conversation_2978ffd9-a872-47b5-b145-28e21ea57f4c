import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EntityCondition } from '../utils/types/entity-condition.type';
import { CreateClientDto } from './dto/create-client.dto';
import { UpdateClientDto } from './dto/update-client.dto';
import { Client } from './entities/client.entity';

@Injectable()
export class ClientsService {
  constructor(
    @InjectRepository(Client)
    private readonly clientRepository: Repository<Client>
  ) {}

  create(createClientDto: CreateClientDto) {
    console.log({ createClientDto });

    return 'This action adds a new client';
  }

  findAll() {
    return `This action returns all clients`;
  }

  findOne(fields: EntityCondition<Client>) {
    return this.clientRepository.findOne({
      where: fields,
    });
  }

  count(fields: EntityCondition<Client>) {
    return this.clientRepository.count({
      where: fields,
    });
  }

  update(id: number, updateClientDto: UpdateClientDto) {
    console.log({ updateClientDto });
    return `This action updates a #${id} client`;
  }

  remove(id: number) {
    return `This action removes a #${id} client`;
  }
}
