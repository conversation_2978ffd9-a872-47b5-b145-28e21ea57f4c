import { ApiProperty } from '@nestjs/swagger';
import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

@Entity({ name: 'clients' })
export class Client {
  @PrimaryGeneratedColumn()
  @ApiProperty({ example: 1 })
  id: number;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  email: string;

  @Column({ type: 'timestamp' })
  @ApiProperty({ example: new Date().toISOString() })
  current_sign_in_at: Date;

  @Column({ type: 'timestamp' })
  @ApiProperty({ example: new Date().toISOString() })
  last_sign_in_at: Date;

  @CreateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  created_at: Date;

  @UpdateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  updated_at: Date;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  api_token: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  api_secret: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  company: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  encrypted_password: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  reset_password_token: string;

  @Column({ type: 'timestamp' })
  @ApiProperty({ example: new Date().toISOString() })
  reset_password_sent_at: Date;

  @Column({ type: 'timestamp' })
  @ApiProperty({ example: new Date().toISOString() })
  remember_created_at: Date;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  sign_in_count: number;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  current_sign_in_ip: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  last_sign_in_ip: string;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  failed_attempts: number;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  unlock_token: string;

  @Column({ type: 'timestamp' })
  @ApiProperty({ example: new Date().toISOString() })
  locked_at: Date;
}
