import { ApiProperty } from '@nestjs/swagger';
import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { EntityHelper } from 'src/utils/entity-helper';

@Entity({ name: 'oauth_access_tokens' })
export class OauthAccessTokenEntity extends EntityHelper {
  @PrimaryGeneratedColumn()
  @ApiProperty({ example: 1 })
  id: number;

  @Column({ type: 'integer', nullable: false })
  @ApiProperty({ example: 180947 })
  resource_owner_id: number;

  @Column()
  @ApiProperty({ example: 180947 })
  application_id: number;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: '8ac4da7253aaf66d07cc63afa6106e3e8a432393bb9e5d4d0417c36d77b7466f' })
  token: string;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: '8ac4da7253aaf66d07cc63afa6106e3e8a432393bb9e5d4d0417c36d77b7466f' })
  refresh_token: string;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 36000 })
  expires_in: number;

  @Column({ type: 'timestamp' })
  @ApiProperty({ example: new Date().toISOString() })
  revoked_at: Date;

  @CreateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  created_at: Date;

  @Column('varchar', { default: '' })
  @ApiProperty({ example: '' })
  scopes: string;
}
