import { Modu<PERSON> } from '@nestjs/common';
import { IsExist } from 'src/utils/validators/is-exists.validator';
import { IsNotExist } from 'src/utils/validators/is-not-exists.validator';
import { SharedModule } from './../shared/shared.module';
import { AuthController } from './auth.controller';
import { AnonymousStrategy } from './strategies/anonymous.strategy';
import { JwtStrategy } from './strategies/jwt.strategy';

@Module({
  imports: [SharedModule],
  controllers: [AuthController],
  providers: [IsExist, IsNotExist, JwtStrategy, AnonymousStrategy],
  exports: [],
})
export class AuthModule {}
