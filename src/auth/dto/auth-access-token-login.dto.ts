import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsN<PERSON>ber, IsOptional, IsString } from 'class-validator';

export class AuthAccessTokenLoginDto {
  @ApiProperty({
    example:
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c',
  })
  @IsNotEmpty()
  @IsString()
  access_token: string;
}

export class AuthAccessTokenRegisterDto {
  @ApiProperty({
    example:
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c',
  })
  @IsNotEmpty()
  @IsString()
  access_token: string;
}

export class AuthEmailRegisterDto {
  @ApiProperty({
    example: '<EMAIL>',
  })
  @IsNotEmpty()
  @IsString()
  @IsEmail()
  email: string;
}
export class AddSIEmailDto {
  @ApiProperty({
    example: '<EMAIL>',
  })
  @IsNotEmpty()
  @IsString()
  @IsEmail()
  email: string;

  @ApiProperty({
    example: 'Male',
  })
  @IsOptional()
  @IsString()
  gender: string;

  @ApiProperty({
    example: 'Join',
  })
  @IsOptional()
  @IsString()
  firstName: string;

  @ApiProperty({
    example: 'Doe',
  })
  @IsOptional()
  @IsString()
  lastName: string;

  @ApiProperty({
    example: 'scratch',
  })
  @IsOptional()
  @IsString()
  strokesGainedBaseline: string;
}

export class UpdateSIEmailDto {
  @ApiProperty({
    example: '<EMAIL>',
  })
  @IsOptional()
  @IsString()
  @IsEmail()
  email: string;

  @ApiProperty({
    example: '1',
  })
  @IsNotEmpty()
  @IsNumber()
  userId: number;

  @ApiProperty({
    example: 'Male',
  })
  @IsOptional()
  @IsString()
  gender: string;

  @ApiProperty({
    example: 'Join',
  })
  @IsOptional()
  @IsString()
  firstName: string;

  @ApiProperty({
    example: 'Doe',
  })
  @IsOptional()
  @IsString()
  lastName: string;

  @ApiProperty({
    example: 'scratch',
  })
  @IsOptional()
  @IsString()
  strokesGainedBaseline: string;
}
