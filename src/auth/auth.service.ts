import { InjectQueue } from '@nestjs/bull';
import { Inject, Injectable, forwardRef } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { ManagementClient } from 'auth0';
import axios from 'axios';
import { Queue } from 'bull';
import { plainToClass } from 'class-transformer';
import request from 'request';
import secureRandom from 'secure-random';
import { IsNull, Repository } from 'typeorm';
import { CdmService } from 'src/cdm/cdm.service';
import { OPTIONS_JOB_DEFAULT } from 'src/rounds/round.const';
import { User } from 'src/users/entities/user.entity';
import { UsersService } from 'src/users/users.service';
import { StatsHelper } from 'src/utils/helper/stats_helper';
import { detectBaseline } from 'src/utils/utils';
import { PROCESSORS, PROCESS_QUEUE_NAMES } from 'src/workers/jobs/job.constant';
import { MAIL_QUEUE_NAME } from 'src/workers/mail/mail.constant';
import { ROLES, USER_STATUS } from './auth.const';
import {
  AddSIEmailDto,
  AuthAccessTokenLoginDto,
  AuthAccessTokenRegisterDto,
  AuthEmailRegisterDto,
  UpdateSIEmailDto,
} from './dto/auth-access-token-login.dto';
import { LoginWithEmailPasswordDto } from './dto/auth-email-login.dto';
import { OauthAccessTokenEntity } from './entities/oauth-access-token.entity';

@Injectable()
export class AuthService {
  auth0Management: ManagementClient;
  private readonly statsHelper = new StatsHelper();
  constructor(
    private jwtService: JwtService,
    private config: ConfigService,
    private usersService: UsersService,
    @InjectRepository(OauthAccessTokenEntity) private oauthAccessTokenRepository: Repository<OauthAccessTokenEntity>,
    @Inject(forwardRef(() => CdmService)) private cdmService: CdmService,
    @InjectQueue(MAIL_QUEUE_NAME) private mailQueue: Queue,
    @InjectQueue(PROCESSORS.SyncUpdateMrpIdJob) private syncUpdateMrpIdJob: Queue,
    @InjectQueue(PROCESSORS.SyncUpdateClubsFromCdmJob) private syncUpdateClubsFromCdmJob: Queue
  ) {
    this.auth0Management = new ManagementClient({
      domain: this.config.get('auth0.apiDomain'),
      clientId: this.config.get('auth0.clientId'),
      clientSecret: this.config.get('auth0.clientSecret'),
      scope: this.config.get('auth0.managementScope'),
    });
  }

  getAuth0LoginOptions(email: string, password: string) {
    return {
      method: 'POST',
      json: true,
      url: `https://${this.config.get('auth0.domain')}/oauth/token`,
      headers: {
        'content-type': 'application/x-www-form-urlencoded',
      },
      form: {
        grant_type: 'password',
        username: email,
        password,
        audience: `https://${this.config.get('auth0.apiDomain') || this.config.get('auth0.domain')}/api/v2/`,
        scope: 'openid email profile offline_access',
        client_id: this.config.get('auth0.clientId'),
        client_secret: this.config.get('auth0.clientSecret'),
      },
    };
  }

  async getAuth0TokenInfo(token: string) {
    try {
      const response = await axios.post(`https://${this.config.get('auth0.apiDomain')}/userinfo`, {
        access_token: token,
      });
      return [null, response.data];
    } catch (e) {
      return [e, null];
    }
  }

  async loginWithAuth0AccessToken(loginDto: AuthAccessTokenLoginDto, isOAuthRequest: boolean) {
    const [tokenInfoError, tokenInfo] = await this.getAuth0TokenInfo(loginDto.access_token);
    if (tokenInfoError) {
      return [tokenInfoError, null];
    }
    const email = tokenInfo.email;
    const consumer = await this.cdmService.getConsumer(email);
    let user = await this.usersService.findOne({ email });
    if (!user && consumer) {
      const userPayload = CdmService.mapConsumerToUser(consumer);
      user = plainToClass(User, { ...userPayload, cdm_id: consumer.id, start_mytm: new Date() });
      user = await this.usersService.createWithEntity(user);
      try {
        await this.syncUpdateMrpIdJob.add(
          PROCESS_QUEUE_NAMES.SYNC_UPDATE_MRP_ID,
          { userId: user.id },
          OPTIONS_JOB_DEFAULT
        );
        await this.syncUpdateClubsFromCdmJob.add(
          PROCESS_QUEUE_NAMES.SYNC_UPDATE_CLUBS_FROM_CDM,
          { userId: user.id },
          OPTIONS_JOB_DEFAULT
        );
      } catch (error) {
        console.log(error);
      }
    }
    if (user && !user.start_mytm) {
      await this.usersService.updateStartMyTm(user.id, new Date());
    }
    if (user && !consumer?.tmUserIds?.mrp) {
      try {
        await this.syncUpdateMrpIdJob.add(
          PROCESS_QUEUE_NAMES.SYNC_UPDATE_MRP_ID,
          { userId: user.id },
          OPTIONS_JOB_DEFAULT
        );
      } catch (error) {
        console.log(error);
      }
    }
    let accessToken;
    if (isOAuthRequest) {
      accessToken = await this.createOAuthAccessToken(user.id);
    }
    return [null, AuthService.transformLoginResponse(user, consumer?.golferProfile, accessToken)];
  }

  async registerWithAuth0AccessToken(registerDto: AuthAccessTokenRegisterDto) {
    const [tokenInfoError, tokenInfo] = await this.getAuth0TokenInfo(registerDto.access_token);
    if (tokenInfoError) {
      return [tokenInfoError, null, 401];
    }
    const email = tokenInfo.email;
    let user = await this.usersService.findOne({ email });
    if (user) {
      return [this.emailAlreadyExistError(), null, 422];
    }
    const consumer = await this.cdmService.getConsumer(email);
    if (!consumer) {
      return [this.consumerNotFoundError(), null, 422];
    }
    const userPayload = CdmService.mapConsumerToUser(consumer);
    user = plainToClass(User, { ...userPayload, cdm_id: consumer.id, start_mytm: new Date() });
    user = await this.usersService.createWithEntity(user);
    try {
      await this.syncUpdateMrpIdJob.add(
        PROCESS_QUEUE_NAMES.SYNC_UPDATE_MRP_ID,
        { userId: user.id },
        OPTIONS_JOB_DEFAULT
      );
      await this.syncUpdateClubsFromCdmJob.add(
        PROCESS_QUEUE_NAMES.SYNC_UPDATE_CLUBS_FROM_CDM,
        { userId: user.id },
        OPTIONS_JOB_DEFAULT
      );
    } catch (error) {
      console.log(error);
    }
    return [null, user, 200];
  }

  async registerWithEmail(registerDto: AuthEmailRegisterDto): Promise<[Error, User | { user_id: number }, number]> {
    let user = await this.usersService.findOne({ email: registerDto.email });
    if (user) {
      return [null, { user_id: user.id }, 200];
    }
    const consumer = await this.cdmService.getConsumer(registerDto.email);
    if (!consumer) {
      return [this.consumerNotFoundError(), null, 422];
    }
    const userPayload = CdmService.mapConsumerToUser(consumer);
    user = plainToClass(User, { ...userPayload, cdm_id: consumer.id, start_mytm: new Date() });
    user = await this.usersService.createWithEntity(user);
    try {
      await this.syncUpdateMrpIdJob.add(
        PROCESS_QUEUE_NAMES.SYNC_UPDATE_MRP_ID,
        { userId: user.id },
        OPTIONS_JOB_DEFAULT
      );
      await this.syncUpdateClubsFromCdmJob.add(
        PROCESS_QUEUE_NAMES.SYNC_UPDATE_CLUBS_FROM_CDM,
        { userId: user.id },
        OPTIONS_JOB_DEFAULT
      );
    } catch (error) {
      console.log(error);
    }

    user.avatar = user.avatar != null ? user.avatar : '/images/original/missing.png';
    user.role = ROLES[user.role] || 'user';
    user.status = USER_STATUS[user.status] || USER_STATUS[0];
    user.delete_data = user.delete_data == null ? false : user.delete_data;
    return [null, user, 200];
  }
  async registerWithSIEmail(registerDto: AddSIEmailDto): Promise<[Error, User | { id: number }, number]> {
    let user = await this.usersService.findOne({ email: registerDto.email });
    if (user) {
      return [null, { id: user.id }, 200];
    }
    user = plainToClass(User, {
      email: registerDto.email,
      first_name: registerDto.firstName ?? '',
      name: registerDto.lastName ?? '',
      gender: registerDto.gender ?? 'male',
      strokes_gained_baseline: registerDto.strokesGainedBaseline ?? 'scratch',
    });
    user = await this.usersService.createWithEntity(user);

    user.avatar = user.avatar != null ? user.avatar : '/images/original/missing.png';
    user.role = ROLES[user.role] || 'user';
    user.status = USER_STATUS[user.status] || USER_STATUS[0];
    user.delete_data = user.delete_data == null ? false : user.delete_data;
    return [null, user, 200];
  }

  async updateSIAccount(payload: UpdateSIEmailDto): Promise<[Error, User | { success: boolean }, number]> {
    const user = await this.usersService.findOne({ id: payload.userId });
    if (!user) {
      const error = this.userNotFoundError();
      return [error, null, 400];
    }
    try {
      const sgBaseLine = detectBaseline(payload.strokesGainedBaseline ?? user.strokes_gained_baseline);

      const options = {
        email: payload.email ?? user.email,
        first_name: payload.firstName ?? user.first_name,
        name: payload.lastName ?? user.name,
        gender: payload.gender ?? user.gender,
        strokes_gained_baseline: sgBaseLine,
      };
      await this.usersService.updateSIAccount(payload.userId, options);
      return [null, { success: true }, 200];
    } catch (error) {
      console.log(error);
      return [null, { success: false }, 400];
    }
  }

  async getAuth0AccessToken(email: string, password: string): Promise<any> {
    return new Promise((resolve) => {
      request(this.getAuth0LoginOptions(email, password), (error, response, body) => {
        if (response.statusCode === 403) {
          return resolve([new Error(body.error_description || body.error), null]);
        }
        if (error) {
          return resolve([error, null]);
        }
        return resolve([null, body.access_token]);
      });
    });
  }

  async isEmailAlreadyExists(email: string) {
    const users = await this.auth0Management.getUsersByEmail(email);
    return users.length > 0;
  }

  async createAuth0User(email: string, password: string) {
    try {
      const user = await this.auth0Management.createUser({
        email,
        password,
        connection: this.config.get('auth0.connection'),
        verify_email: false,
      });
      return [null, user];
    } catch (e) {
      return [e, null];
    }
  }

  async validateLogin(loginDto: LoginWithEmailPasswordDto, isOAuthRequest: boolean): Promise<[Error, User]> {
    const error = this.invalidEmailOrPasswordError();
    const user = await this.usersService.findOne({
      email: loginDto.user.email,
    });
    if (!user) {
      return [error, null];
    }
    const [errorLogin] = await this.getAuth0AccessToken(loginDto.user.email, loginDto.user.password);
    if (errorLogin) {
      return [errorLogin, null];
    }
    // TODO: return cannot login
    // if (user.deactivated) {
    //   return [this.accountHasBeenDeleted(), null];
    // }

    // const isValidPassword = await bcrypt.compare(
    //   `${loginDto.user.password}${this.config.get('auth.bcryptPepper')}`,
    //   user.encrypted_password
    // );
    const isValidPassword = true;
    if (isValidPassword) {
      let accessToken;
      if (isOAuthRequest) {
        accessToken = await this.createOAuthAccessToken(user.id);
      }
      const isNewFromAuth0 = this.isNewFromAuth0(user);
      const transformedUser = (await this.transformEmailLoginResponse(
        user,
        accessToken,
        isNewFromAuth0
      )) as unknown as User;
      return [null, transformedUser];
    } else {
      return [error, null];
    }
  }

  private isNewFromAuth0(user: User) {
    try {
      const name = user.first_name + user.name;
      return !(user.country && name?.toString().trim().length > 0 && user.birthday);
    } catch (error) {
      console.log(error);
      return true;
    }
  }

  static transformLoginResponse(user: User, golferProfile, accessToken: OauthAccessTokenEntity) {
    let isNewFromAuth0 = false;
    const name = user.first_name + user.name;
    if (!(user.country && user.birthday && name?.toString().trim().length > 0)) {
      isNewFromAuth0 = true;
    }
    let handicap = golferProfile?.newHandicap?.userInputHandicap || 0;
    if (parseFloat(handicap) < 0) {
      handicap = `+${handicap * -1}`;
    }
    return {
      id: user.id,
      email: user.email,
      handed: user.handed,
      birthday: user.birthday,
      name: user.name,
      first_name: user.first_name,
      country: user.country,
      avatar: user.avatar || '/images/original/missing.png',
      gender: user.gender,
      third_party_email_opt_in: user.third_party_email_opt_in,
      tmag_email_opt_in: user.tmag_email_opt_in,
      accepted_terms_on: user.accepted_terms_on,
      accepted_privacy_on: user.accepted_privacy_on,
      token: accessToken?.token || user.token,
      postal_code: user.postal_code,
      iGolf_home_course_id: user.iGolf_home_course_id || '',
      home_course_name: user.home_course_name || '',
      cdm_id: user.cdm_id,
      start_mytm: user.start_mytm,
      handicap: handicap?.toString(),
      is_new_from_auth0: isNewFromAuth0,
      strokes_gained_baseline: StatsHelper.to_baseline_number(golferProfile?.strokesGainedBaseline),
    };
  }

  async transformEmailLoginResponse(user: User, accessToken?: OauthAccessTokenEntity, is_new_from_auth0?: boolean) {
    const deviceTokens = await this.usersService.getDeviceTokens(user.id);
    return {
      id: user.id,
      email: user.email,
      handed: user.handed,
      birthday: user.birthday,
      name: user.name,
      first_name: user.first_name,
      country: user.country,
      avatar: user.avatar || '/images/original/missing.png',
      gender: user.gender,
      third_party_email_opt_in: user.third_party_email_opt_in,
      tmag_email_opt_in: user.tmag_email_opt_in,
      accepted_terms_on: user.accepted_terms_on,
      accepted_privacy_on: user.accepted_privacy_on,
      token: accessToken?.token || user.token,
      postal_code: user.postal_code,
      iGolf_home_course_id: user.iGolf_home_course_id || '',
      home_course_name: user.home_course_name || '',
      cdm_id: user.cdm_id,
      start_mytm: user.start_mytm,
      handicap: user.handicap?.replace('-', '+'),
      is_new_from_auth0,
      device_tokens: deviceTokens?.map((deviceToken) => deviceToken.device_token),
      strokes_gained_baseline: StatsHelper.to_baseline_number(user.strokes_gained_baseline),
    };
  }

  getUserById(id: number): Promise<User> {
    return this.usersService.findOne({
      id,
    });
  }

  getUserByToken(token: string): Promise<User> {
    return this.usersService.findOne({
      token,
    });
  }

  async createOAuthAccessToken(userId: number) {
    const token = secureRandom.randomBuffer(32).toString('hex');
    return this.oauthAccessTokenRepository.save(
      this.oauthAccessTokenRepository.create({
        token,
        resource_owner_id: userId,
        created_at: new Date(),
      })
    );
  }

  async changeUserPassword(uid: number, password: string, newPassword: string): Promise<any> {
    let accessToken;
    const user = await this.usersService.findOne({ id: uid });
    try {
      const [error, auth0AccessToken] = await this.getAuth0AccessToken(user.email, password);
      if (error) {
        return [this.currentPasswordIsNotCorrectError(), null, 422];
      }
      accessToken = auth0AccessToken;
    } catch (e) {
      return [this.currentPasswordIsNotCorrectError(), null, 422];
    }
    try {
      const response = await axios.post(`https://${this.config.get('auth0.domain')}/userinfo`, {
        access_token: accessToken,
      });
      await this.auth0Management.updateUser(
        { id: response.data.sub },
        {
          password: newPassword,
          connection: 'Username-Password-v2',
        }
      );
      return [null, this.usersService.transformUser(user), 200];
    } catch (e) {
      return [e, null, 422];
    }
  }

  async revokeOAuthAccessToken(token: string, userId: number) {
    await this.oauthAccessTokenRepository.delete({ token, resource_owner_id: userId });
  }

  async getOAuthAccessTokenByToken(token: string) {
    return this.oauthAccessTokenRepository.findOne({ where: { token, revoked_at: IsNull() } });
  }

  async me(user: User): Promise<User> {
    return this.usersService.findOne({
      id: user.id,
    });
  }

  userNotFoundError() {
    return new Error('User notfound.');
  }

  invalidEmailOrPasswordError() {
    return new Error('Invalid email or password.');
  }

  accountHasBeenDeleted() {
    return new Error('Account has been deleted.');
  }

  emailAlreadyExistError() {
    return new Error('Register email already existed');
  }

  currentPasswordIsNotCorrectError() {
    return new Error('is invalid');
  }

  consumerNotFoundError() {
    return new Error('Consumer not found!');
  }
}
