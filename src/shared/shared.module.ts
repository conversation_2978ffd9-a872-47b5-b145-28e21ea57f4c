import { CacheModule } from '@nestjs/cache-manager';
import { CACHE_MANAGER, Inject, Logger, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { TypeOrmModule } from '@nestjs/typeorm';
import * as redisStore from 'cache-manager-ioredis';
import { AuthService } from 'src/auth/auth.service';
import { OauthAccessTokenEntity } from 'src/auth/entities/oauth-access-token.entity';
import { AverageScoresService } from 'src/average-scores/average-scores.service';
import { AverageScoreClassic } from 'src/average-scores/entities/average-score.classic.entity';
import { AverageScore } from 'src/average-scores/entities/average-score.entity';
import { CdmCacheService } from 'src/cdm/cdm.cache.service';
import { CdmService } from 'src/cdm/cdm.service';
import { ClientsService } from 'src/clients/clients.service';
import { Client } from 'src/clients/entities/client.entity';
import { ClubsService } from 'src/clubs/clubs.service';
import { AvailableClubEntity } from 'src/clubs/entities/available-club.entity';
import { Club } from 'src/clubs/entities/club.entity';
import { CountriesService } from 'src/countries/countries.service';
import { CountryIso } from 'src/countries/entities/country-iso.entity';
import { Country } from 'src/countries/entities/country.entity';
import { GhinTeeRating } from 'src/ghin-tee-rating/entities/ghin-tee-rating.entity';
import { GhinTeeRatingService } from 'src/ghin-tee-rating/ghin-tee-rating.service';
import { IGolfGhinCourse } from 'src/ghin/entities/ghin.entity';
import { GhinRound } from 'src/ghin/entities/ghin.round.entity';
import { GhinService } from 'src/ghin/ghin.service';
import { GhinGLXService } from 'src/ghin/ghinGLX.service';
import { CourseErrorEntity } from 'src/glx/entity/course-error.entity';
import { GlxService } from 'src/glx/glx.service';
import { GolfNetTeeRating } from 'src/golfnet/entities/golfnet-tee-rating.entity';
import { IGolfGolfNetCourse } from 'src/golfnet/entities/igolf-golfnet-course.entity';
import { GolfNetService } from 'src/golfnet/golfnet.service';
import { HolePlayed } from 'src/holes-played/entities/hole-played.entity';
import { HolesPlayedService } from 'src/holes-played/holes-played.service';
import { IgolfLogRequests } from 'src/igolf/entity/ghin.log.request.entity';
import { UserLogCourseEntity } from 'src/igolf/entity/user-log-course.entity';
import { IGolfService } from 'src/igolf/igolf.service';
import { ThreePartyCourseService } from 'src/igolf/threePartyCourse.service';
import { KeyConfigEntity } from 'src/key-config/entities/key-config.entity';
import { KlaviyoService } from 'src/klaviyo/klaviyo.service';
import { MailModule } from 'src/mail/mail.module';
import { MytmService } from 'src/mytm/mytm.service';
import { PlayerScore } from 'src/players/entities/player-score.entity';
import { Player } from 'src/players/entities/player.entity';
import { PlayersService } from 'src/players/players.service';
import { RoundAuditImportMobileService } from 'src/round-audit-import-mobile/round-audit-import-mobile.service';
import { RoundAuditUpdateService } from 'src/round-audit-update/round-audit-update.service';
import { RoundAuditUtilsService } from 'src/round-audit-util/round-audit-util.service';
import { RoundAudit } from 'src/round-audit/entities/round-audit.entity';
import { RoundAuditService } from 'src/round-audit/round-audit.service';
import { Round } from 'src/rounds/entities/round.entity';
import { RoundCronService } from 'src/rounds/round.cron.service';
import { RoundService } from 'src/rounds/rounds.service';
import { State } from 'src/states/entities/state.entity';
import { StatesService } from 'src/states/states.service';
import { StatsApproachService } from 'src/stats/stats-approarch.service';
import { StatsClubService } from 'src/stats/stats-club.service';
import { StatsDrivingService } from 'src/stats/stats-driving.service';
import { StatsOverallService } from 'src/stats/stats-overall.service';
import { StatsPuttingService } from 'src/stats/stats-putting.service';
import { StatsShortService } from 'src/stats/stats-short.service';
import { StrokeBaseline } from 'src/stroke-baselines/entities/stroke-baseline.entity';
import { StrokeBaselinesService } from 'src/stroke-baselines/stroke-baselines.service';
import { StrokePlayed } from 'src/strokes-played/entities/stroke-played.entity';
import { StrokesPlayedService } from 'src/strokes-played/strokes-played.service';
import { StrokeStat } from 'src/strokes-stats/entities/stroke-stat.entity';
import { StrokesStatsService } from 'src/strokes-stats/strokes-stats.service';
import { DeviceToken } from 'src/users/entities/device-token.entity';
import { User } from 'src/users/entities/user.entity';
import { UsersService } from 'src/users/users.service';
import redisInstance from 'src/utils/redis';
import { WhitelistIp } from 'src/whitelist-ip/entities/whitelist-ip.entity';
import { WorkersRegistryModule } from 'src/workers/workers.registry.module';
import { GhinScoreLogEntity } from '../ghin/entities/ghin-score-log.entity';
import { GolfNetRoundEntity } from '../golfnet/entities/golf-net-round.entity';

const JWT = JwtModule.registerAsync({
  imports: [ConfigModule],
  inject: [ConfigService],
  useFactory: (configService: ConfigService) => ({
    secret: configService.get('auth.secret'),
    signOptions: {
      expiresIn: configService.get('auth.expires'),
    },
  }),
});

const getCacheModuleConfig = () => {
  const options: any = {
    store: redisStore,
    redisInstance,
  };
  return options;
};

@Module({
  imports: [
    TypeOrmModule.forFeature([
      User,
      Round,
      GhinRound,
      Country,
      CountryIso,
      State,
      StrokeStat,
      StrokePlayed,
      HolePlayed,
      Player,
      StrokeBaseline,
      Client,
      RoundAudit,
      Club,
      DeviceToken,
      PlayerScore,
      AverageScore,
      AverageScoreClassic,
      AvailableClubEntity,
      OauthAccessTokenEntity,
      IGolfGhinCourse,
      GhinTeeRating,
      IGolfGolfNetCourse,
      GolfNetTeeRating,
      GolfNetService,
      IgolfLogRequests,
      KeyConfigEntity,
      CourseErrorEntity,
      UserLogCourseEntity,
      GolfNetRoundEntity,
      GhinScoreLogEntity,
      WhitelistIp,
    ]),
    MailModule,
    WorkersRegistryModule,
    JWT,
    CacheModule.registerAsync({
      useFactory: getCacheModuleConfig,
      inject: [ConfigService],
    }),
  ],
  providers: [
    AuthService,
    UsersService,
    RoundService,
    CountriesService,
    StatesService,
    StrokesStatsService,
    StrokesPlayedService,
    HolesPlayedService,
    PlayersService,
    ClientsService,
    ClubsService,
    StrokeBaselinesService,
    CdmService,
    CdmCacheService,
    KlaviyoService,
    RoundAuditService,
    RoundAuditImportMobileService,
    RoundAuditUtilsService,
    IGolfService,
    ThreePartyCourseService,
    AverageScoresService,
    RoundAuditUpdateService,
    MytmService,
    StatsOverallService,
    StatsClubService,
    StatsDrivingService,
    StatsPuttingService,
    StatsShortService,
    StatsApproachService,
    RoundCronService,
    GhinService,
    GhinGLXService,
    GhinTeeRatingService,
    GolfNetService,
    GlxService,
  ],
  exports: [
    TypeOrmModule.forFeature([
      User,
      Round,
      Country,
      CountryIso,
      State,
      StrokeStat,
      StrokePlayed,
      HolePlayed,
      Player,
      StrokeBaseline,
      Client,
      RoundAudit,
      Club,
      PlayerScore,
      AverageScore,
      AverageScoreClassic,
      AvailableClubEntity,
      OauthAccessTokenEntity,
      IGolfGhinCourse,
      GhinTeeRating,
      IGolfGolfNetCourse,
      GolfNetTeeRating,
      IgolfLogRequests,
      KeyConfigEntity,
      CourseErrorEntity,
      UserLogCourseEntity,
      GolfNetRoundEntity,
      GhinScoreLogEntity,
    ]),
    AuthService,
    UsersService,
    RoundService,
    StatesService,
    CountriesService,
    StrokesStatsService,
    StrokeBaselinesService,
    StrokesPlayedService,
    HolesPlayedService,
    PlayersService,
    ClientsService,
    ClubsService,
    IGolfService,
    ThreePartyCourseService,
    RoundAuditService,
    RoundAuditUpdateService,
    RoundAuditImportMobileService,
    RoundAuditUtilsService,
    MailModule,
    JWT,
    CdmService,
    KlaviyoService,
    WorkersRegistryModule,
    CdmCacheService,
    AverageScoresService,
    MytmService,
    StatsOverallService,
    StatsClubService,
    StatsDrivingService,
    StatsPuttingService,
    StatsShortService,
    StatsApproachService,
    RoundCronService,
    GhinService,
    GhinGLXService,
    GhinTeeRatingService,
    GolfNetService,
    GlxService,
    CacheModule.registerAsync({
      useFactory: getCacheModuleConfig,
      inject: [ConfigService],
    }),
  ],
})
export class SharedModule {
  private readonly logger = new Logger(SharedModule.name);
  constructor(
    @Inject(CACHE_MANAGER) cacheManager,
    private cdmCacheService: CdmCacheService,
    private cdmService: CdmService
  ) {
    const client = cacheManager.store.getClient();
    client.on('error', (error) => {
      console.log(client.options);
      console.error({ error });
    });
    client.on('connect', async (error) => {
      console.log(`connect success....`);

      if (!error) {
        const handleCaches = [];
        await this.cdmCacheService.initialCaches();
        if (this.cdmCacheService.caches.clubModels.length === 0) {
          handleCaches.push(this.cdmService.cacheClubModels());
        }
        if (this.cdmCacheService.caches.clubHands.length === 0) {
          handleCaches.push(this.cdmService.cacheClubHands());
        }
        if (this.cdmCacheService.caches.clubLofts.length === 0) {
          handleCaches.push(this.cdmService.cacheClubLofts());
        }
        if (this.cdmCacheService.caches.clubShaftFlex.length === 0) {
          handleCaches.push(this.cdmService.cacheClubShaftFlex());
        }
        if (this.cdmCacheService.caches.clubLoftAdjustment.length === 0) {
          handleCaches.push(this.cdmService.cacheClubLoftAdjustment());
        }
        if (this.cdmCacheService.caches.clubShaftLength.length === 0) {
          handleCaches.push(this.cdmService.cacheClubShaftLength());
        }
        if (this.cdmCacheService.caches.clubLies.length === 0) {
          handleCaches.push(this.cdmService.cacheClubLies());
        }
        if (this.cdmCacheService.caches.clubBrands.length === 0) {
          handleCaches.push(this.cdmService.cacheClubBrands());
        }
        if (this.cdmCacheService.caches.clubCategories.length === 0) {
          handleCaches.push(this.cdmService.cacheClubCategories());
        }
        if (this.cdmCacheService.caches.clubCategoriesTypes.length === 0) {
          handleCaches.push(this.cdmService.cacheClubCategoriesTypes());
        }
        if (this.cdmCacheService.caches.regions.length === 0) {
          handleCaches.push(this.cdmService.cacheRegions());
        }
        Promise.all(handleCaches).then((r) => r);
        this.logger.log(`Connect to Redis successfully!`);
      }
    });
  }
}
