import { InjectQueue } from '@nestjs/bull';
import { Inject, Injectable, Logger, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Queue } from 'bull';
import { isNumberString } from 'class-validator';
import { Point } from 'geojson';
import { isEmpty } from 'lodash';
import { Repository } from 'typeorm';
import { ClubsService } from 'src/clubs/clubs.service';
import { ThreePartyCourseService } from 'src/igolf/threePartyCourse.service';
import { PlayerScoreDto } from 'src/players/dto/player-score.dto';
import { PlayerDto } from 'src/players/dto/player.dto';
import { Round } from 'src/rounds/entities/round.entity';
import { OPTIONS_JOB_COMPLETED_ROUND, OPTIONS_JOB_DEFAULT, ROUND } from 'src/rounds/round.const';
import { coordsDistance, deleteValueBlank } from 'src/utils/utils';
import { PROCESSORS, PROCESS_QUEUE_NAMES } from 'src/workers/jobs/job.constant';

@Injectable()
export class RoundAuditUtilsService {
  private readonly logger = new Logger(RoundAuditUtilsService.name);
  private round: any;
  constructor(
    @InjectRepository(Round)
    private roundRepository: Repository<Round>,
    private readonly clubsService: ClubsService,
    @Inject(forwardRef(() => ThreePartyCourseService))
    private readonly threePartyCourseService: ThreePartyCourseService,
    @InjectQueue(PROCESSORS.IGolfRoundCompleteJob) private iGolfRoundCompleteJobQueue: Queue,
    @InjectQueue(PROCESSORS.CalcHandicapJob) private calcHandicapJobQueue: Queue,
    @InjectQueue(PROCESSORS.SimpleScoreToParJob) private simpleScoreToParJobQueue: Queue,
    @InjectQueue(PROCESSORS.CalculateAverageScoreJob) private calculateAverageScoreJobQueue: Queue
  ) {}
  async queueCompleteRound(round: any, holesPlayedModify = [], totalStrokes?: any) {
    if (!round.id) {
      return;
    }
    this.logger.log(`============ START QUEUE COMPLETE JOB ROUND ID: ${round.id} ============`);
    if (round.round_mode == ROUND.ROUND_MODE_ADVANCED) {
      this.roundRepository.update({ id: round.id }, { driving_stat_complete: false, stats_completed: false });
      const data = {
        roundId: round.id,
        holesPlayedModify,
        totalStrokes,
      };

      await this.addJobCalculateRoundStats(data);
      // if (round.completed) {
      //   await this.addJobCalculateAverageScore(round);
      //   // Pending Calculate HandicapIndex
      //   // await this.addJobCalculateHandicapIndex(round);
      // }
    } else {
      if (round.completed) {
        await this.addJobCalculateAverageScore(round);
        //TODO: Pending Calculate HandicapIndex
        // if (round.round_mode != ROUND.ROUND_MODE_MULTIPLAYER) {
        //   await this.addJobCalculateHandicapIndex(round);
        // }
        if (round.round_mode == ROUND.ROUND_MODE_SIMPLE) {
          await this.addJobSimpleScoreToPar(round);
        }
      }
    }
  }

  private async addJobCalculateRoundStats(data: { roundId: any; holesPlayedModify: any[]; totalStrokes: any }) {
    // const lstJobActive = await this.iGolfRoundCompleteJobQueue.getActive();
    // const lstJobWaiting = await this.iGolfRoundCompleteJobQueue.getWaiting();
    // const lstJob = [...lstJobActive, ...lstJobWaiting];
    // const isExisted = lstJob.some((job) => job.data.roundId == data.roundId);
    // if (isExisted) {
    //   return;
    // }
    try {
      await this.iGolfRoundCompleteJobQueue.add(
        PROCESS_QUEUE_NAMES.IGOLF_ROUND_COMPLETE,
        data,
        OPTIONS_JOB_COMPLETED_ROUND
      );
    } catch (error) {
      console.log(error);
    }
  }

  private async addJobSimpleScoreToPar(round: any) {
    const lstJobActive = await this.simpleScoreToParJobQueue.getActive();
    const lstJobWaiting = await this.simpleScoreToParJobQueue.getWaiting();
    const lstJob = [...lstJobActive, ...lstJobWaiting];
    const isExisted = lstJob.some((job) => job.data.roundId == round.id);
    if (isExisted) {
      return;
    }
    try {
      await this.simpleScoreToParJobQueue.add(PROCESS_QUEUE_NAMES.SIMPLE_SCORE_TO_PAR, { roundId: round.id });
    } catch (error) {
      console.log(error);
    }
  }

  private async addJobCalculateHandicapIndex(round: any) {
    const lstJobActive = await this.calcHandicapJobQueue.getActive();
    const lstJobWaiting = await this.calcHandicapJobQueue.getWaiting();
    const lstJob = [...lstJobActive, ...lstJobWaiting];
    const isExisted = lstJob.some((job) => job.data.userId == round.user_id);
    if (isExisted) {
      return;
    }
    await this.calcHandicapJobQueue.add(PROCESS_QUEUE_NAMES.CALC_HANDICAP, {
      userId: round.user_id,
    });
  }

  private async addJobCalculateAverageScore(round: any) {
    const lstJobActive = await this.calculateAverageScoreJobQueue.getActive();
    const lstJobWaiting = await this.calculateAverageScoreJobQueue.getWaiting();
    const lstJob = [...lstJobActive, ...lstJobWaiting];
    const isExisted = lstJob.some((job) => job.data.userId == round.user_id);
    if (isExisted) {
      // Remove all current job
      for (const job of lstJob) {
        try {
          this.logger.debug(`FORCE MOVE JOB CALCULATE AVG TO COMPLETED: ${job.id}`);
          await job.moveToCompleted('Force move completed!');
        } catch (error) {
          this.logger.error(`ERROR FORCE MOVE JOB CALCULATE AVG TO COMPLETED: ${job.id}`);
          this.logger.error(error);
        }
      }
    }
    try {
      await this.calculateAverageScoreJobQueue.add(
        PROCESS_QUEUE_NAMES.CALCULATE_AVERAGE_SCORE,
        {
          userId: round.user_id,
        },
        OPTIONS_JOB_DEFAULT
      );
    } catch (error) {
      console.log(`ADD QUEUE CALCULATE_AVERAGE_SCORE FAIL`);
      console.log(error);
    }
  }

  async createHoleAttributes(hole: any, round, forceMapCourse = null) {
    let fairway_center,
      tee_center,
      green_center = null;
    if (
      (round.round_mode === ROUND.ROUND_MODE_ADVANCED || round.round_mode === ROUND.ROUND_MODE_MULTIPLAYER) &&
      !forceMapCourse
    ) {
      [fairway_center, tee_center, green_center] = await Promise.all([
        this.getCenterFor('fairway_center', hole['number'], round['igolf_course_id'], round['play_client']),
        this.getCenterFor('tee_center', hole['number'], round['igolf_course_id'], round['play_client']),
        this.getCenterFor('green_center', hole['number'], round['igolf_course_id'], round['play_client']),
      ]);
    } else {
      fairway_center = null;
      tee_center = null;
      green_center = null;
    }

    let pin_location = null;
    if (hole.pin_location) {
      pin_location = {
        type: 'Point',
        coordinates: hole.pin_location,
      };
    } else {
      pin_location = null;
    }

    let holeData = {
      round_id: round.id,
      score: hole['score'],
      par: hole['par'],
      name: +hole['number'],
      lowest_heartrate: hole['lowest_heartrate'],
      average_heartrate: hole['avg_heartrate'],
      peak_heartrate: hole['peak_heartrate'],
      shots_removed: hole['strokes_removed'],
      calories_burned: hole['calories_burned'],
      steps_count: hole['steps_count'],
      stroke_index: hole['stroke_index'],
      handicap_index: hole['handicap'] || hole['stroke_index'],
      pin_location,

      fairway_center, //: await this.getCenterFor('fairway_center', hole['number'], round['igolf_course_id']),
      tee_center, //: await this.getCenterFor('tee_center', hole['number'], round['igolf_course_id']),
      green_center, //: await this.getCenterFor('green_center', hole['number'], round['igolf_course_id']),

      // Classic round
      putts_number: hole['putts_number'] || 0,
      fw_stats: hole['fw_stats'] || '',
      gr_stats: hole['gr_stats'] || '',
      bunker_hit: hole['bunker_hit'] || false,
      penalty_hit: hole['penalty_hit'] || false,
      yards: hole['yards'] || null,
      created_at: new Date(),
      updated_at: new Date(),
    };
    holeData = deleteValueBlank(holeData);
    return holeData;
  }

  //
  // creates a new stroke for the given hole
  //
  // TODO: Need monitor get list club in hole.
  async createStrokeAttributes(stroke: any, roundId: number, holePlayedId: number, pinLocation: any) {
    let club_id = !stroke['club_id'] ? '' : stroke['club_id'];
    const club_id_is_number = isNumberString(club_id + '');
    club_id = await this.getClubId(club_id, club_id_is_number);
    const distance_from_pin = coordsDistance(
      stroke['coords'], //lat, lon
      [pinLocation[1], pinLocation[0]] // lat, lon
    );
    // const distance_from_pin = coordsDistance(
    //   [stroke['coords'][1], stroke['coords'][0]], //lon, lat
    //   [pinLocation[1], pinLocation[0]]
    // );
    let strokeData = {
      hole_played_id: holePlayedId,
      round_id: roundId,
      distance: stroke['distance'],
      timestamp: new Date(),
      record_id: stroke['record_id'],
      distance_from_pin,
      ordinal: +stroke['ordinal'],
      coords: this.buildPoint(stroke['coords']),
      lie: stroke['starting_lie'],
      subsequent_lie: stroke['ending_lie'],
      club_id: club_id, //stroke["club_id"],
      recovery: !!stroke['recovery'],
      penalty: !!stroke['penalty'],
      difficult: !!stroke['difficult'],
      auto_detected: !!stroke['auto_detected'],
      created_at: new Date(),
      updated_at: new Date(),
    };
    strokeData = deleteValueBlank(strokeData);

    return strokeData;
  }

  private async getClubId(club_id: any, club_id_is_number: boolean) {
    // this.logger.debug(`GET CLUB ID`);
    // this.logger.debug({ club_id, club_id_is_number });

    if (club_id != '' && !club_id_is_number) {
      const club = await this.clubsService.findClubBy({
        where: { cdm_witb_id: club_id },
        select: ['id'],
      });
      club_id = club.length > 0 ? club[0].id : '';
    }
    return club_id;
  }

  // FOR ROUND_MODE: Multiplayer
  //
  //creates player scores
  //
  createPlayerScoreAttributes(hole, player_score, roundId): PlayerScoreDto {
    let playerScoreData = {
      round_id: roundId,
      hole_played_id: hole.id,
      hole_number: hole['name'],
      hole_score: player_score['hole_score'],
      player_id: player_score['player_id'],
      created_at: new Date(),
      updated_at: new Date(),
    };
    playerScoreData = deleteValueBlank(playerScoreData);
    return playerScoreData as PlayerScoreDto;
  }

  // FOR ROUND_MODE: Multiplayer
  //
  //creates player scores
  //
  createPlayerAttributes(player: any, roundId: number): PlayerDto {
    let playerDto = {
      round_id: roundId,
      player_name: player['player_name'],
      player_id: player['player_id'],
      team: player['team'],
      user_id: player['user_id'] ?? null,
      player_email: player['player_email'] ?? null,
      player_initials: player['player_initials'],
      stroke_received: player['stroke_received'],
      created_at: new Date(),
      updated_at: new Date(),
    } as PlayerDto;
    playerDto = deleteValueBlank(playerDto);

    return playerDto;
  }

  //
  // gets the facility
  //
  facility() {
    // @facility ||= ::Facility.find_by(
    //     course_id: data["course_id"]
    //   )
  }

  //
  // get the course
  //
  async course() {
    // let round_data;
    // let round;
    // let data;

    // if (round) round_data = round;
    // else round_data = data;
    const course = await this.threePartyCourseService.getCourseDetail(this.round.igolf_course_id);
    return course;
  }

  //
  // assign pin location to an array
  //
  buildPoint(coord: any) {
    if (!coord || coord.length == 0) {
      return null;
    }
    const pointObject: Point = {
      type: 'Point',
      coordinates: [coord[1], coord[0]],
    };
    return pointObject;
  }

  //
  // gets center
  //
  async getCenterFor(location, holeNumber, igolfCourseId, client?: string) {
    let coords: any;
    switch (location) {
      case 'fairway_center':
        coords = await this.threePartyCourseService.coordinatesFor(holeNumber, 'fairway', igolfCourseId, client);
        break;
      case 'tee_center':
        coords = await this.threePartyCourseService.coordinatesFor(holeNumber, 'tee', igolfCourseId, client);
        break;
      case 'green_center':
        coords = await this.threePartyCourseService.coordinatesFor(holeNumber, 'green', igolfCourseId, client);
        break;
    }
    if (!coords || isEmpty(coords)) {
      return null;
    }
    const coordinates = coords[coords.length - 1][0];
    const pointObject: Point = {
      type: 'Point',
      coordinates,
    };
    return pointObject;
  }

  //
  // simple factory
  //
  factory() {
    // @factory ||= ::RGeo::Geos.factory(srid: 4326)
  }
}
