import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional } from 'class-validator';
import { Point } from 'geojson';

export class CreateStrokesPlayedDto {
  @ApiProperty({ example: 1 })
  @IsNotEmpty()
  hole_played_id: number;

  @IsOptional()
  @ApiProperty({ example: 1.0 })
  distance: number;

  @IsOptional()
  @ApiProperty({ example: new Date().toISOString() })
  timestamp: Date;

  @IsOptional()
  @ApiProperty({ example: 1 })
  record_id: number;

  @IsOptional()
  @ApiProperty({ example: false })
  auto_detected: boolean;

  @IsOptional()
  @ApiProperty({ example: false })
  penalty: boolean;

  @IsOptional()
  @ApiProperty({ example: 1.0 })
  distance_from_pin: number;

  @IsOptional()
  @ApiProperty({ example: new Date().toISOString() })
  created_at: Date;

  @IsOptional()
  @ApiProperty({ example: new Date().toISOString() })
  updated_at: Date;

  @IsOptional()
  @ApiProperty({ example: 1 })
  ordinal: number;

  @IsOptional()
  coords: Point;

  @IsOptional()
  @ApiProperty({ example: 'string' })
  lie: string;

  @IsOptional()
  @ApiProperty({ example: 1 })
  round_id: number;

  @IsOptional()
  @ApiProperty({ example: false })
  difficult: boolean;

  @IsOptional()
  @ApiProperty({ example: 1 })
  club_id: number;

  @IsOptional()
  @ApiProperty({ example: false })
  recovery: boolean;

  @IsOptional()
  @ApiProperty({ example: false })
  manually_added: boolean;

  @IsOptional()
  @ApiProperty({ example: 1.0 })
  azimuth_to_pin: number;

  @IsOptional()
  @ApiProperty({ example: 1.0 })
  shot_azimuth: number;

  @IsOptional()
  @ApiProperty({ example: 1.0 })
  result_from_pin: number;

  @IsOptional()
  @ApiProperty({ example: 1.0 })
  result_angle_from_pin: number;

  @IsOptional()
  @ApiProperty({ example: 1.0 })
  deviance: number;

  @IsOptional()
  @ApiProperty({ example: false })
  final_stroke: boolean;

  @IsOptional()
  @ApiProperty({ example: 'string' })
  subsequent_lie: string;

  @IsOptional()
  @ApiProperty({ example: 1.0 })
  center_angle: number;

  @IsOptional()
  @ApiProperty({ example: 1.0 })
  strokes_gained: number;

  @IsOptional()
  @ApiProperty({ example: 'string' })
  meta: string;

  @IsOptional()
  @ApiProperty({ example: false })
  auto_moved: boolean;

  @IsOptional()
  @ApiProperty({ example: false })
  manually_moved: boolean;
}
