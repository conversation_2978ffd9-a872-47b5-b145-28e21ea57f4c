import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { Point } from 'geojson';
import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { HolePlayed } from 'src/holes-played/entities/hole-played.entity';
import { StrokeStat } from 'src/strokes-stats/entities/stroke-stat.entity';

@Entity({ name: 'strokes_played' })
export class StrokePlayed {
  @PrimaryGeneratedColumn()
  @ApiProperty({ example: 1 })
  @Index()
  id: number;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  @Index()
  hole_played_id: number;

  @Column({ type: 'float' })
  @ApiProperty({ example: 1.0 })
  distance: number;

  @Column({ type: 'timestamp' })
  @ApiProperty({ example: new Date().toISOString() })
  timestamp: Date;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  record_id: number;

  @Column({ type: 'boolean' })
  @ApiProperty({ example: false })
  auto_detected: boolean;

  @Column({ type: 'boolean' })
  @ApiProperty({ example: false })
  penalty: boolean;

  @Column({ type: 'float' })
  @ApiProperty({ example: 1.0 })
  distance_from_pin: number;

  @CreateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  created_at: Date;

  @UpdateDateColumn()
  @ApiProperty({ example: new Date().toISOString() })
  updated_at: Date;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  ordinal: number;

  @Column({
    type: 'geography',
    spatialFeatureType: 'Point',
    srid: 4326,
    nullable: true,
  })
  @Transform(({ value }) => value?.coordinates?.reverse())
  coords: Point;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  lie: string;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  @Index()
  round_id: number;

  @Column({ type: 'boolean' })
  @ApiProperty({ example: false })
  difficult: boolean;

  @Column({ type: 'integer' })
  @ApiProperty({ example: 1 })
  @Index()
  club_id: number;

  @Column({ type: 'boolean' })
  @ApiProperty({ example: false })
  recovery: boolean;

  @Column({ type: 'boolean' })
  @ApiProperty({ example: false })
  manually_added: boolean;

  @Column({ type: 'float' })
  @ApiProperty({ example: 1.0 })
  azimuth_to_pin: number;

  @Column({ type: 'float' })
  @ApiProperty({ example: 1.0 })
  shot_azimuth: number;

  @Column({ type: 'float' })
  @ApiProperty({ example: 1.0 })
  result_from_pin: number;

  @Column({ type: 'float' })
  @ApiProperty({ example: 1.0 })
  result_angle_from_pin: number;

  @Column({ type: 'float' })
  @ApiProperty({ example: 1.0 })
  deviance: number;

  @Column({ type: 'boolean' })
  @ApiProperty({ example: false })
  final_stroke: boolean;

  @Column({ type: 'varchar' })
  @ApiProperty({ example: 'string' })
  subsequent_lie: string;

  @Column({ type: 'float' })
  @ApiProperty({ example: 1.0 })
  center_angle: number;

  @Column({ type: 'float' })
  @ApiProperty({ example: 1.0 })
  strokes_gained: number;

  @Column({ type: 'json' })
  @ApiProperty({ example: 'string' })
  meta: string;

  @Column({ type: 'boolean' })
  @ApiProperty({ example: false })
  auto_moved: boolean;

  @Column({ type: 'boolean' })
  @ApiProperty({ example: false })
  manually_moved: boolean;

  @OneToOne(() => StrokeStat, (strokeStats) => strokeStats.stroke_played_id, {
    eager: false,
  })
  @JoinColumn({ name: 'id', referencedColumnName: 'stroke_played_id' })
  stroke_stat?: StrokeStat;

  @OneToOne(() => HolePlayed, (holePlayed) => holePlayed.id, {
    eager: false,
  })
  @JoinColumn({ name: 'hole_played_id', referencedColumnName: 'id' })
  hole_played?: HolePlayed;
}
