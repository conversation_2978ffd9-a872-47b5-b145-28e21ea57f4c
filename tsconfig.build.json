{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "es2017", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "esModuleInterop": true, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false}, "include": ["**/*.ts", "**/*.tsx"], "exclude": ["node_modules", "test", "dist", "**/*spec.ts"]}