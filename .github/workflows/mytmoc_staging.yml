# Docs for the Azure Web Apps Deploy action: https://github.com/Azure/webapps-deploy
# More GitHub Actions for Azure: https://github.com/Azure/actions

name: Build Azure MyTMOC Staging

on:
  push:
    branches:
      - staging

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@master

      - name: Set up Node.js version
        uses: actions/setup-node@v1
        with:
          node-version: '16.x'

      - name: Copy env files
        run: cp .env.staging .env

      - name: Install dependencies
        run: yarn

      - name: Build
        run: yarn build

      - name: Clean dependencies
        run: |
          rm -rf node_modules

      - name: Install prod dependencies
        run: yarn --prod

      - name: 'Deploy to Azure Web App 1'
        uses: azure/webapps-deploy@v2
        with:
          app-name: 'app-dev-mytmoc-app-001'
          slot-name: 'production'
          publish-profile: ${{ secrets.AZURE_WEB_APP_PUBLISH_PROFILE_STG_001 }}

      - name: Update .env for cron jobs
        run: sed -i 's/CRON_JOB_HANDLERS=disabled/CRON_JOB_HANDLERS=enabled/g' .env

      - name: Update .env for workers
        run: sed -i 's/APP_TAG=APIS/APP_TAG=JOBS/g' .env

      - name: 'Deploy to Azure Job App 1'
        uses: azure/webapps-deploy@v2
        with:
          app-name: 'app-dev-mytmocjobs-app-001'
          slot-name: 'production'
          publish-profile: ${{ secrets.AZURE_JOB_APP_PUBLISH_PROFILE_STG_001 }}
