{"printWidth": 120, "trailingComma": "es5", "tabWidth": 2, "semi": true, "singleQuote": true, "bracketSpacing": true, "arrowParens": "always", "endOfLine": "auto", "proseWrap": "preserve", "quoteProps": "as-needed", "useTabs": false, "htmlWhitespaceSensitivity": "css", "importOrder": ["^@nestjs/(.*)$", "<THIRD_PARTY_MODULES>", "^src/(.*)$", "^[./]"], "importOrderSeparation": false, "importOrderParserPlugins": ["typescript", "classProperties", "decorators-legacy"], "importOrderSortSpecifiers": true}